const fs = require('fs')
const path = require('path')

 
const localesBasePath = path.resolve(`${__dirname}/../locales`)
const locales = fs.readdirSync(localesBasePath)
const avaiableLocales = locales.map(localeFileName => localeFileName.split('.')[0])

 
console.log({ localesBasePath, locales, avaiableLocales })

const addTranslations = [
  // { key: 'tran', en: 'translated', vi: 'đã dịch' },
]

/**
 * @type {String[]}
 */
const delTranslations = [
]

if (delTranslations.length) {
  for (const locale of locales) {
    const filePath = `${localesBasePath}${path.sep}${locale}`
    const file = JSON.parse(fs.readFileSync(filePath))

    for (const key of delTranslations) {
      if (!(key in file)) { continue }

      delete file[key]
       
      console.log(`Deleted ${key} @ ${filePath}`)
    }

    fs.writeFileSync(filePath, JSON.stringify(file, null, 2) + '\n', { encoding: 'utf-8' })
  }
}

if (addTranslations.length) {
  for (const obj of addTranslations) {
    if (!obj.key) {
       
      console.log('Missing translation "key", Skipping...')
      continue
    }

    for (const locale in obj) {
      if (locale === 'key') { continue }

      const filePath = `${localesBasePath}${path.sep}${locale}.json`
      const file = JSON.parse(fs.readFileSync(filePath))

      if (file[obj.key]) {
         
        console.log(`Key ${obj.key} is exists @ ${filePath}, Skipping...`)
        continue
      }

      file[obj.key] = obj[locale]

      fs.writeFileSync(filePath, JSON.stringify(file, null, 2) + '\n', { encoding: 'utf-8' })
       
      console.log(`Added ${obj.key} to ${filePath}`)
    }
  }
}
