<script setup lang="ts">
const props = defineProps({
  isModal: <PERSON>olean,
  product: Object as PropType<Product>,
  url: String,
  isHidden: <PERSON><PERSON><PERSON>,
  isActive: <PERSON><PERSON><PERSON>,
  currentOptions: Object as PropType<{ [key: string]: string }>
})

const baseClass = computed(() => {
  let base = 'h-[100px] w-[80px] min-w-[80px] m-1 hover:shadow-custom cursor-pointer'
  if (props.isActive) {
    base += ' border border-primary'
  }
  if (props.isHidden) {
    base += ' md:hidden'
  }

  return base
})
</script>

<template>
  <template v-if="isModal || !url">
    <div
      sp-action="change_product"
      data-test-id="change-product"
      :class="baseClass"
      @click.prevent.stop="$emit('onChangeProduct', product)"
    >
      <common-image
        :image="{ path: product?.thumb_url }"
        :alt="product?.name"
      />
    </div>
  </template>
  <template v-else>
    <nuxt-link
      :to="url"
      sp-action="change_product"
      data-test-id="change-product"
      class="product-item overflow-hidden"
      :class="baseClass"
      @click.prevent.stop="$emit('onChangeProduct', product)"
    >
      <common-image
        :image="{ path: product?.thumb_url, color: currentOptions?.color }"
        :alt="product?.name"
      />
    </nuxt-link>
  </template>
</template>
