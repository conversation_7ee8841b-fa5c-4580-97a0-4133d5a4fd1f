<script lang="ts" setup>
const props = defineProps({
  isDraggable: {
    default: false,
    type: Boolean
  }
})

const dragging = ref(false)
const startX = ref(0)
const startY = ref(0)
const currentX = ref(0)
const currentY = ref(0)

function dragStart (event:TouchEvent) {
  dragging.value = true
  startX.value = event.touches[0].clientX - currentX.value
  startY.value = event.touches[0].clientY - currentY.value
}

function drag (event:TouchEvent) {
  if (props.isDraggable) {
    event.preventDefault()
  }
  if (!dragging.value || !props.isDraggable) {
    return
  }
  currentX.value = event.touches[0].clientX - startX.value
  currentY.value = event.touches[0].clientY - startY.value
}

function dragEnd () {
  dragging.value = false
}
</script>

<template>
  <div
    :class="{border: isDraggable}"
    :style="isDraggable ? `transform: translate(${currentX}px, ${currentY}px)` :''"
    @touchstart="dragStart"
    @touchmove="drag"
    @touchend="dragEnd"
    @mouseleave="dragEnd"
  >
    <slot />
  </div>
</template>

<style>
</style>
