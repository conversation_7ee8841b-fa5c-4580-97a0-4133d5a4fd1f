<script lang="ts" setup>
import { useStoreInfo } from '~/store/storeInfo'

const { forPosition } = defineProps({
  forPosition: {
    type: String,
    default: ''
  }
})

const storeInfoHeadTags = headTagFilter(useStoreInfo().getStoreHeadTags[forPosition])

const tags = (!forPosition || !storeInfoHeadTags) ? [] : [...storeInfoHeadTags]
  .sort((a: HeadTag, b: HeadTag) => b.priority - a.priority)
</script>
<template>
  <div>
    <div v-for="tag in tags" :key="tag.name" v-html="tag.code" />
  </div>
</template>
