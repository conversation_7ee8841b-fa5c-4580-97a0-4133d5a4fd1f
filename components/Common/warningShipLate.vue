<script setup lang="ts">
/*
* <PERSON><PERSON><PERSON> tra xem có hiển thị thông báo ship late hay không
* Hiển thị từ ngày 9/12 đến hết 25/12 hàng năm
*/
function isShowWarningShipLate () {
  const currentDate = new Date()
  const start = new Date(currentDate.getFullYear(), 11, 9)
  const end = new Date(currentDate.getFullYear(), 11, 26)

  start.setHours(0, 0, 0, 0)
  end.setHours(0, 0, 0, 0)

  return currentDate >= start && currentDate <= end
}
</script>

<template>
  <div v-if="isShowWarningShipLate()" class="text-sm text-orange-500">
    {{ $t('* Order may arrive post-Christmas due to peak season.') }}
  </div>
</template>