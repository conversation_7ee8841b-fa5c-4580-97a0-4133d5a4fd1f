<script lang="ts" setup>
import { useUserSession } from '~/store/userSession'

defineProps({
  btnClass: {
    default: 'border',
    type: String
  },
  showOnHover: {
    default: true,
    type: Boolean
  },
  showDropdownIcon: {
    default: true,
    type: <PERSON>olean
  },
  showFlagInButton: {
    default: true,
    type: Boolean
  }
})

const { currencies: currencyList } = generalSettings()
const userSession = useUserSession()
const currentCurrency = computed(() => userSession.currentCurrency)
</script>

<template>
  <common-dropdown
    dropdown-id="currencySelect"
    :btn-class="btnClass"
    :show-on-hover="showOnHover"
    :show-dropdown-icon="showDropdownIcon"
  >
    <div class="flex items-center p-1 min-w-20" data-test-id="currency-select-btn">
      <span v-if="showFlagInButton" :class="`vti__flag ${currentCurrency?.locale === 'en' ? 'us' : currentCurrency?.locale.slice(-2).toLowerCase()}`" />
      <span>{{ $getCurrencySymbol(currentCurrency?.locale === 'en' ? undefined : currentCurrency?.locale, currentCurrency?.code) }}</span>
      <span class="ml-2">{{ currentCurrency ? $t(currentCurrency?.name) : $t('Currency') }}</span>
    </div>
    <template #content>
      <div
        v-for="(currencyItem, index) in currencyList"
        :key="index"
        class="btn-text flex items-center px-4 w-full text-black"
        :class="{
          'bg-primary !text-white': currencyItem.code === currentCurrency?.code,
          [currencyItem.code]: true
        }"
        data-test-id="currency-select-currency"
        @click="userSession.update('userBehavior', { currencyCode: currencyItem.code });userSession.update('visitInfo', { currency_code: currencyItem.code })"
      >
        <span :class="`vti__flag ${currencyItem?.locale === 'en' ? 'us' : currencyItem?.locale.slice(-2).toLowerCase()}`" />
        <span class="ml-2">{{ $getCurrencySymbol(currencyItem?.locale === 'en' ? undefined : currencyItem?.locale, currencyItem?.code) }}</span>
        <span class="ml-2 w-max">{{ $t(currencyItem?.name) }}</span>
      </div>
    </template>
  </common-dropdown>
</template>
