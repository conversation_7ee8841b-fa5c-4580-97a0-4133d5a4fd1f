<script lang="ts" setup>
const props = defineProps({
  dataDescription: {
    default: undefined,
    type: String
  },
  dataProductDetail: {
    default: undefined,
    type: String
  },
  titleClass: {
    default: 'text-gray-500 btn-text font-medium',
    type: String
  },
  collapseClass: {
    default: '',
    type: String
  },
  collapseClassExtended: {
    default: '',
    type: String
  },

  defaultExpanded: {
    default: '',
    type: [Array, String]
  },
  multiExpand: {
    default: false,
    type: Boolean
  }
})

const showTab = ref(props.defaultExpanded)

function toggleTab(tabName: string) {
  if (!props.multiExpand) {
    showTab.value = tabName
  }

  const copied = [...showTab.value]

  if (copied.includes(tabName)) {
    copied.splice(copied.indexOf(tabName), 1)
  }
  else {
    copied.push(tabName)
  }

  showTab.value = copied
}
</script>

<template>
  <div class=" mt-4">
    <div v-if="dataDescription">
      <div
        class="cursor-pointer"
        :class="titleClass"
        @click="toggleTab('campaignDescription')"
      >
        <span class="capitalize">
          {{ $t('Description') }}
        </span>
        <span
          class="float-right transition-default transform"
          :class="[
            (showTab.includes('campaignDescription')) ? 'rotate-180' : 'rotate-0'
          ]"
        >
          <i class="icon-sen-chevron-down p-3 max-h-50 overflow-y-auto" />
        </span>
      </div>
      <common-collapse
        :when="showTab.includes('campaignDescription')"
        class="transition-default pr-3 max-h-50 overflow-y-auto"
        :class="{
          [collapseClass]: true,
          [collapseClassExtended]: showTab.includes('campaignDescription')
        }"
      >
        <div v-html="dataDescription" />
      </common-collapse>
    </div>

    <div v-if="dataProductDetail">
      <div
        class="cursor-pointer"
        :class="titleClass"
        @click="toggleTab('dataProductDetail')"
      >
        <span class="capitalize">
          {{ $t('Item Details') }}
        </span>
        <span
          class="float-right transition-default transform"
          :class="[
            (showTab.includes('dataProductDetail')) ? 'rotate-180' : 'rotate-0'
          ]"
        >
          <i class="icon-sen-chevron-down p-3 max-h-50 overflow-y-auto" />
        </span>
      </div>
      <common-collapse
        :when="showTab.includes('dataProductDetail')"
        class="transition-default pr-3 max-h-50 overflow-y-auto"
        :class="{
          [collapseClass]: true,
          [collapseClassExtended]: showTab.includes('dataProductDetail')
        }"
      >
        <div v-html="dataProductDetail" />
      </common-collapse>
    </div>

    <div>
      <div
        class="cursor-pointer"
        :class="titleClass"
        @click="toggleTab('shippingInfo')"
      >
        <span class="capitalize">
          {{ $t('Delivery') }}
        </span>
        <span
          class="float-right transition-default transform"
          :class="[
            (showTab.includes('shippingInfo')) ? 'rotate-180' : 'rotate-0'
          ]"
        >
          <i class="icon-sen-chevron-down p-3 max-h-50 overflow-y-auto" />
        </span>
      </div>

      <common-collapse
        :when="showTab.includes('shippingInfo')"
        class="transition-default pr-3 max-h-50 overflow-y-auto"
        :class="{
          [collapseClass]: true,
          [collapseClassExtended]: showTab.includes('shippingInfo')
        }"
      >
        <p>{{ $t('Production time') }}: </p>
        <ul>
          <li>
            {{ $t('Printed apparel (normal/partial printing)') }}:  <strong>3-5 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('Special for Canvas/Poster printing') }}:  <strong>5-7 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('All Over Print apparel and products (Full Prints)') }}: <strong>7-10 {{ $t('business days') }}</strong>
          </li>
        </ul>
        <p>{{ $t('Shipping time') }}: </p>
        <ul>
          <li>
            {{ $t('Printed apparel (normal/partial printing)') }}:  <strong>5-7 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('All Over Print apparel and products (Full Prints)') }}: <strong>11-14 {{ $t('business days') }}</strong>
          </li>
          <li>
            {{ $t('International orders may take an additional') }}: <strong>1-2 {{ $t('weeks') }}</strong>.
          </li>
        </ul>
        <p>
          <strong>{{ $t('Note') }}: </strong>
          <span>{{ $t('Shipping time can take from') }}</span>
          <strong>&nbsp;{{ $t('21 to 25 business days') }}</strong>
          <span>, {{ $t('the tracking is normal that there might not be timely updates of your parcel, therefore please wait patiently.') }}.</span>
        </p>
      </common-collapse>
    </div>

    <div>
      <div
        class="cursor-pointer"
        :class="titleClass"
        @click="toggleTab('refundInfo')"
      >
        <span class="capitalize">
          {{ $t('Shipping and return policies') }}
        </span>
        <span
          class="float-right transition-default transform"
          :class="[
            (showTab.includes('refundInfo')) ? 'rotate-180' : 'rotate-0'
          ]"
        >
          <i class="icon-sen-chevron-down p-3 max-h-50 overflow-y-auto" />
        </span>
      </div>

      <common-collapse
        :when="showTab.includes('refundInfo')"
        class="transition-default pr-3 max-h-50 overflow-y-auto"
        :class="{
          [collapseClass]: true,
          [collapseClassExtended]: showTab.includes('refundInfo')
        }"
      >
        <p>{{ $t('While we want every order to be perfect, mistakes can happen occasionally. We can offer reproductions and refunds for your orders if there are order mistakes. If you are submitting a reproduction or refund request, please include photo evidence of your product in your order.') }}</p>
        <p>{{ $t('Cases where we cover the Reprinting cost (Reproductions) or Refunding') }}:</p>
        <ul>
          <li>
            {{ $t('There is a manufacturing issue with your product.') }}
          </li>
          <li>
            {{ $t('The product in your order is broken or damaged during transit.') }}
          </li>
          <li>
            {{ $t('You receive the wrong product in your order.') }}
          </li>
          <li>
            {{ $t('The order is lost in transit, and the initial shipping address was correct.') }}
          </li>
          <li>
            {{ $t('The order is lost in transit, and the actual shipping time exceeds the general shipping time frame for orders.') }}
          </li>
        </ul>
        <p>{{ $t('If the goods delivered to you fall under one of the criteria noted above, please follow below steps to request from our customer service') }}: </p>
        <ul>
          <li>
            {{ $t('You must submit a request for a return within') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>&nbsp;{{ $t('after you receive your goods.') }}
          </li>
          <li>
            {{ $t('After') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>,&nbsp;{{ $t('no return will be accepted. Also, if goods are returned prior to submitting your request, those goods are forfeited.') }}
          </li>
          <li>
            {{ $t('Please note that customers are responsible for return shipping labels. We will send you return instructions separately by email.') }}
          </li>
          <li>
            {{ $t('You must submit a request for a return within') }}&nbsp;<strong>14 {{ $t('business days') }}</strong>&nbsp;{{ $t('after you receive your goods.') }}
          </li>
          <li>
            {{ $t('It will take approximately') }}&nbsp;<strong>5-10 {{ $t('business days') }}</strong>&nbsp;{{ $t('to process your refund.') }}
          </li>
        </ul>
      </common-collapse>
    </div>
  </div>
</template>

<style scoped>
ul {
  list-style: disc;
  padding-left: 20px;
}

p {
  margin-top: 8px;
  margin-bottom: 8px;
}
</style>
