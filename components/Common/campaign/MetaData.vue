<script setup lang="ts">
defineProps({
  campaignData: {
    type: Object,
    required: true,
  },
  campaignDescription: {
    type: String,
    required: true,
  },
  thumbnail: {
    type: String,
    required: true,
  },
  reviewSummary: {
    type: Object,
    required: false,
  },
  userCampaignOption: {
    type: Object,
    required: true,
  },
})


</script>

<template>
  <div itemscope itemtype="http://schema.org/Product">
    <meta itemprop="brand" :content="campaignData.store_name">
    <meta itemprop="name" :content="campaignData.name">
    <meta itemprop="description" :content="campaignDescription">
    <meta itemprop="productID" :content="String(campaignData.id)">
    <meta itemprop="url" :content="`https://${storeInfo().domain}/${campaignData.slug}`">
    <meta itemprop="image" :content="thumbnail">
    <div v-if="reviewSummary && storeInfo().product_review_display !== 'disable' && reviewSummary.review_count > 0" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
      <meta itemprop="ratingValue" :content="String(reviewSummary.average_rating)">
      <meta itemprop="reviewCount" :content="String(reviewSummary.review_count)">
    </div>
    <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
      <link itemprop="availability" href="http://schema.org/InStock">
      <link itemprop="itemCondition" href="http://schema.org/NewCondition">
      <meta itemprop="price" :content="String(userCampaignOption.currentProduct?.price)">
      <meta itemprop="priceCurrency" :content="userCampaignOption.currentProduct?.currency_code">
    </div>
  </div>
</template>
