<script lang="ts" setup>
const props = defineProps({
  customImageItem: {
    default: () => {},
    type: Object as PropType<CustomImageItem>
  }
})

const {
  imageResolution,
  controlList,
  controlCustomImage,
  startIntervalControl,
  removeIntervalControl
} = useControlCustomImage(props.customImageItem)
</script>

<template>
  <div v-if="customImageItem.canvasObject" class="control-design">
    <div class="flex justify-center">
      <button
        v-for="controlItem in controlList"
        :id="`controlDesign-${controlItem.key}`"
        :key="controlItem.key"
        class="btn-border center-flex mx-1 h-7 w-7 text-xl md:(h-10 w-10 text-3xl mx-2)"
        @click="controlCustomImage(controlItem.key)"
        @mousedown="startIntervalControl(controlItem.key)"
        @mouseleave="removeIntervalControl"
        @mouseup="removeIntervalControl"
      >
        <i :class="controlItem.icon" :style="controlItem.style || ''" />
      </button>
    </div>
    <div
      v-if="!imageResolution || customImageItem.isImageLowQuality"
      class="<md:text-sm mt-5 text-center text-red-500 block"
    >
      {{ $t('Image resolution is low. Please upload higher resolution image, minimum size is heightxwidth px', imageResolution) }}
    </div>
  </div>
</template>
