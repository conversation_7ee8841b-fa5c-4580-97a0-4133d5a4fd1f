<script lang="ts" setup>
defineProps({
  active: {
    type: Boolean,
    default: false
  },
  background: {
    type: String,
    default: 'bg-primary'
  },
})

</script>

<template>
  <div
    class="h-5 w-5 p-1 rounded-full border center-flex"
    :class="{ [background]: active }"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 512 512"
      class="w-full h-full rounded-full fill-white"
    >
      <path
        d="M470.6 105.4c12.5 12.5 12.5 32.8 0 45.3l-256 256c-12.5 12.5-32.8 12.5-45.3 0l-128-128c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0L192 338.7 425.4 105.4c12.5-12.5 32.8-12.5 45.3 0z"
      />
    </svg>
  </div>
</template>

<style lang="scss" scoped>
</style>
