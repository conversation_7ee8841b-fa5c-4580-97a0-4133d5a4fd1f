<script lang="ts" setup>

defineProps({
  currentPage: {
    type: Number,
    default: 1
  },
  lastPage: {
    type: Number,
    default: 1
  },
})
</script>

<template>
  <nav v-if="currentPage" class="-space-x-px flex">
    <template v-if="currentPage > 1">
      <template v-if="currentPage > 2">
      <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-r-none" @click="$emit('input', 1)">
        <i class="icon-sen-chevron-double-left" />
      </span>
      <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-l-none" @click="$emit('input', currentPage - 1)">
        <i class="icon-sen-chevron-left" />
      </span>
      <span class="px-3 h-10 flex items-center">...</span>
    </template>
      <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-r-none" @click="$emit('input', currentPage - 1)">
        {{ currentPage - 1 }}
      </span>
    </template>
    <span
      class="px-3 h-10 border border-primary bg-primary text-contrast flex items-center"
      :class="{
        '!rounded-l-none': currentPage > 1,
        '!rounded-r-none': currentPage < lastPage
      }"
    >{{ currentPage }}</span>
    <template v-if="currentPage < lastPage">
      <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-l-none" @click="$emit('input', currentPage + 1)">
        {{ currentPage + 1 }}
      </span>
      <template v-if="currentPage < lastPage - 1">
        <span class="px-3 h-10 flex items-center">...</span>
        <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-r-none" @click="$emit('input', currentPage + 1)">
          <i class="icon-sen-chevron-right" />
        </span>
        <span class="px-3 h-10 btn-border-fill border-gray-300 flex items-center !rounded-l-none" @click="$emit('input', lastPage)">
          <i class="icon-sen-chevron-double-right" />
        </span>
      </template>
    </template>
  </nav>
</template>

<style scoped>
.btn-border-fill {
  border-radius: var(--sp-border-radius-1);
}

.btn-border-fill:nth-child(2) + .btn-border-fill {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-border-fill:nth-child(2):has(+ .btn-border-fill) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
