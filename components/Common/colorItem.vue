<script lang="ts" setup>
const props = defineProps({
  color: {
    required: true,
    type: String
  },
  size: {
    default: 'md',
    type: String
  },
  active: {
    default: false,
    type: Boolean
  },
  parentClass: {
    default: 'm-0.5 p-0.5',
    type: String
  }
})

const colorInfo = computed(() => {
  return getColorInfo(props.color)
})

const hexColor = computed(() => {
  return colorVal(props.color)
})

const isMultiColorMode = computed(() => {
  return colorInfo.value.multi_color_mode === 1
})

const colorParts = computed(() => {
  if (!isMultiColorMode.value) {
    return [hexColor.value]
  }

  const colors = colorInfo.value.hex_code.split('-').filter(color => color.trim())
  return colors.length > 0 ? colors : [hexColor.value]
})

const gradientStyle = computed(() => {
  if (!isMultiColorMode.value || colorParts.value.length <= 1) {
    return `background: ${hexColor.value}`
  }

  const colors = colorParts.value
  const segmentSize = 100 / colors.length

  const gradientStops = []
  for (let i = 0; i < colors.length; i++) {
    const startPercent = i * segmentSize
    const endPercent = (i + 1) * segmentSize
    gradientStops.push(`${colors[i]} ${startPercent}%`)
    gradientStops.push(`${colors[i]} ${endPercent}%`)
  }

  return `background: linear-gradient(180deg, ${gradientStops.join(', ')})`
})
</script>

<template>
  <div
    class="rounded-full border-2 hover:border-primary"
    :class="parentClass + (active ? 'border-primary' : ' border-white')"
  >
    <div
      class="rounded-full shadow-color relative overflow-hidden"
      :class="{
        'h-4 w-4': size === 'xs',
        'h-5 w-5': size === 'sm',
        'h-6 w-6': size === 'md',
        'h-7 w-7': size === 'lg',
        'h-8 w-8': size === 'xl'
      }"
    >
      <!-- Color display (supports single and multi-color) -->
      <div
        class="absolute inset-0 rounded-full"
        :style="gradientStyle"
      />

      <i v-if="active" class="z-1 absolute position-center icon-sen-check" :class="isLightColor(hexColor) ? 'text-black' : 'text-white'" />
    </div>
  </div>
</template>
