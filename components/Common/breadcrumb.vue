<script lang="ts" setup>
interface Crumb {
  url?: string,
  text: string
}

const { items } = defineProps({
  items: {
    default: (): Array<Crumb> => {
      return []
    },
    type: Array<Crumb>
  },
  separatorIcon: {
    default: '',
    type: String,
  },
  crumbClass: {
    default: 'font-medium text-gray-700 dark:text-gray-400 dark:hover:text-white',
    type: String,
  },
  crumbClassUrl: {
    default: 'font-medium text-gray-500 dark:text-gray-400 hover:text-blue-600',
    type: String,
  }
})
</script>
<template>
  <nav class="flex">
    <ol class="flex items-center gap-2">
      <template v-for="(crumb, index) in items" :key="index">
        <li>
          <a v-if="crumb.url" :href="crumb.url" :class="[crumbClass, crumbClassUrl]">
            {{ crumb.text }}
          </a>
          <span v-else :class="crumbClass">{{ crumb.text }}</span>
        </li>
        <span v-if="!separatorIcon && index < (items.length - 1)" class="mx-1 text-gray-400">/</span>
        <i v-else-if="separatorIcon && index < (items.length - 1)" :class="separatorIcon" />
      </template>
    </ol>
  </nav>
</template>
