<script lang="ts" setup>
import type { Locale, LocaleObject } from '@nuxtjs/i18n/dist/runtime/composables'
import { useUserSession } from '~~/store/userSession'

const props = defineProps({
  showLanguage: {
    type: Boolean,
    default: true
  },
  useSvg: {
    type: Boolean,
    default: false
  },
  wrapperBorder: {
    type: Boolean,
    default: true
  },
  expandableLang: {
    type: Boolean,
    default: false
  },
  btnClass: {
    type: String,
    default: () => 'border'
  },
  showOnHover: {
    default: true,
    type: Boolean
  },
  showDropdownIcon: {
    default: true,
    type: Boolean
  },
  showFlagInButton: {
    default: true,
    type: Boolean
  }
})

const { $i18n, $switchLocalePath } = useNuxtApp()
const isLangExpanded = ref(false)
const locales = computed(() => {
  const primaryLang = ['en', 'de', 'es', 'fr', 'zh']

  const preComputedLang: Record<string, LocaleObject[]> = {
    primary: [],
    expanded: []
  };

  ($i18n.locales as (string[] | LocaleObject[]) & Ref<LocaleObject[]>).value.forEach((locale) => {
    if (primaryLang.includes(locale.code) || locale.code === $i18n.locale.value) {
      preComputedLang.primary.push(locale)
    }
    else {
      preComputedLang.expanded.push(locale)
    }
  })

  if (props.expandableLang && !isLangExpanded.value) {
    return preComputedLang.primary
  }
  if (isLangExpanded.value) {
    return [
      ...preComputedLang.primary,
      ...preComputedLang.expanded
    ]
  }

  return ($i18n.locales as (string[] | LocaleObject[]) & Ref<LocaleObject[]>).value
})
const currentLocale = computed(() => {
  return locales.value.find(item => item.code === ($i18n.locale as (Locale) & Ref<Locale>).value)
})

const userCountryCode = computed(() => {
  const preferredCountry = useUserSession().userInfo?.country || ''
  if (preferredCountry !== '') {
    return preferredCountry
  }
  return useUserSession().visitInfo.country
})

function setDocumentLanguage(newCode: string) {
  // this function is for using fonts based on language
  document.body.setAttribute('lang', newCode)
}

onMounted(() => {
  setDocumentLanguage(currentLocale.value.code)
})
watch(currentLocale, (val) => {
  setDocumentLanguage(val.code)
})
</script>

<template>
  <common-dropdown
    dropdown-id="languageSelect"
    :btn-class="btnClass"
    :show-dropdown-icon="showDropdownIcon"
    :show-on-hover="showOnHover"
    @hidden="isLangExpanded = false"
  >
    <div class="flex items-center p-1" data-test-id="language-select-btn">
      <img v-if="showFlagInButton && props.useSvg && userCountryCode" class="country-flag border-[#e8e8e8] border-[0.5px] <md:min-w-[1.8rem] min-w-[2.5rem]" :alt="`flag-${userCountryCode}`" :src="`${cdnURL}images/country-flag/${userCountryCode.toLowerCase()}.svg`">
      <span v-else-if="showFlagInButton" :class="`vti__flag ${currentLocale?.countryCode[0].toLowerCase()}`" />

      <span v-if="props.showLanguage">{{ currentLocale?.name || $t('Language') }}</span>
      <slot name="btn" />
    </div>
    <template #content>
      <nuxt-link
        v-for="(localeItem, index) in locales"
        :key="index"
        :to="$switchLocalePath(localeItem.code)"
        class="btn-text flex items-center px-4 py-1 w-full text-black"
        :class="{
          'bg-primary !text-contrast': localeItem.code === currentLocale?.code,
          [localeItem.code]: true
        }"
        data-test-id="language-select-lang"
      >
        <span :class="`vti__flag ${localeItem?.countryCode[0].toLowerCase()}`" />
        <span class="w-max">{{ localeItem?.name }}</span>
      </nuxt-link>
      <div v-if="expandableLang && !isLangExpanded" class="btn-text flex items-center justify-center px-4 w-full" data-test-id="language-select-expand-btn" @click.stop="isLangExpanded = true">
        . . .
      </div>
    </template>
  </common-dropdown>
</template>
