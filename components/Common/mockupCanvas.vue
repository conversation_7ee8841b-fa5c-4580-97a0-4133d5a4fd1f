<script lang="ts" setup>
const props = defineProps({
  personalize: {
    default: undefined,
    type: Object as PropType<Personalize>
  },
  color: {
    default: undefined,
    type: String
  },
  isShowDesign: {
    default: false,
    type: <PERSON><PERSON>an
  }
})

const mockupCanvas = shallowRef<any>()
const wrapperElement = ref<HTMLElement>()
const oldWidth = ref(0)

onMounted(() => {
  loadMockup()
  oldWidth.value = wrapperElement.value?.clientWidth ?? 0
  window.addEventListener('resize', resize)
  window.addEventListener('scroll', resize)
})

onUnmounted(() => {
  window.removeEventListener('resize', resize)
  window.removeEventListener('scroll', resize)
})

watch(() => props.color, () => {
  changeColor(props.color)
})

watch(() => props.personalize, () => {
  loadMockup()
})

function resize() {
  console.log('resize', wrapperElement.value?.clientWidth)
  mockupCanvas.value?.setDisplaySize(wrapperElement.value?.clientWidth || 500)
}

async function loadMockup() {
  const canvasEL = document.getElementById(props.personalize?.personalizeKey as string) as HTMLCanvasElement
  if (!mockupCanvas?.value) {
    mockupCanvas.value = new window.DesignCanvas.BaseMockup(canvasEL, {
      selection: false,
      controlsAboveOverlay: true,
      centeredScaling: true,
      allowTouchScrolling: true
    // enableRetinaScaling: false
    })
    mockupCanvas.value.locked = true

    props.personalize!.mockupCanvas = unref(mockupCanvas)
  }
  resetMockupSize()
  if (props.personalize?.baseMockup?.design_json) {
    await mockupCanvas.value.loadFromJSONPromise(props.personalize?.baseMockup?.design_json)
  }

  changeColor(props.color)
  loadDesign()

  function resetMockupSize() {
    const width = wrapperElement.value?.clientWidth || 500
    mockupCanvas.value.setWidth(width)
    mockupCanvas.value.setHeight(width * 1.25)
    mockupCanvas.value.renderAll()
  }
}

function changeColor(color?: string) {
  color = colorVal(color)
  if (color) {
    mockupCanvas.value?.setMockupColor(color)
  }
}

function loadDesign() {
  if (props.personalize?.personalized === 1) {
    if (props.personalize.designCanvas) {
      if (props.personalize.designCanvas.hasDesign()) {
        mockupCanvas.value.viewDesign(props.personalize.designCanvas.exportCanvas())
      }
    }
  }
  else if (props.personalize?.personalized === 2) {
    if (props.personalize.designCanvas) {
      mockupCanvas.value.viewDesign(props.personalize.designCanvas)
    }
  }
}
</script>

<template>
  <div ref="wrapperElement" class="w-full">
    <div v-show="isShowDesign">
      <canvas :id="personalize?.personalizeKey" class="w-full" />
    </div>
  </div>
</template>
