<script lang="ts" setup>
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  side: {
    default: 'left',
    type: String as PropType<'left' | 'right'>,
  },

  additionalContainerClass: {
    default: 'z-1',
    type: String
  },

  closeButtonClass: {
    default: '',
    type: String,
  },
  closeButtonStyle: {
    default: '',
    type: String,
  },
  closeButtonIcon: {
    default: '',
    type: String,
  },
})

const emit = defineEmits(['update:visible'])
</script>
<template>
  <div
    class="fixed top-0 w-full h-full bg-[#0e0e0ebf] transition-all opacity-100"
    :class="{
      'invisible opacity-0': !visible,
      [additionalContainerClass]: true,
    }"
  >
    <div
      class="absolute h-full w-[25%] bg-white transition-all <md:(h-[93vh] w-full bottom-0) md:w-[60%] lg:w-[25%]"
      :class="{ 'right-0': side === 'right' }"
    >
      <slot />
    </div>
    <div
      class="absolute right-0 top-0 mt-4"
      :class="{
        'md:(left-[61%]) lg:(left-[26%])': side === 'left',
        'md:(right-[61%]) lg:(right-[26%])': side === 'right',
      }"
    >
      <a
        class="relative text-white text-2xl lg:text-3xl px-2 pt-2 transition-all cursor-pointer"
        :class="closeButtonClass"
        :style="closeButtonStyle"
        @click="emit('update:visible', false)"
      >
        <i :class="closeButtonIcon" />
      </a>
    </div>
  </div>
</template>
