<script lang="ts" setup>
/**
 * ReCaptcha Setup
 */
const config = useRuntimeConfig()

const reCaptchaConfig: ReCaptchaOptions = {
  version: 2,
  siteKey: config.public.recaptchaSiteKey,
}

class ReCaptcha {
  API_URL = 'https://www.recaptcha.net/recaptcha/api.js'

  _elements: { [key: string]: HTMLElement } = {}
  _grecaptcha: any = null

  _eventBus: { [key in ReCaptchaCustomEvents]: Function } = {
    'recaptcha-success': (_key: string) => null,
    'recaptcha-error': (_msg: string) => null,
    'recaptcha-expired': () => null
  }

  _ready: boolean | null = null
  _readyPromise: Promise<unknown> | null = null

  hideBadge = false
  language = ''

  siteKey = ''
  version = 2
  size = 'normal'

  mode = 'base'

  constructor ({ hideBadge = false, language = '', mode = 'base', siteKey, version = 2, size = 'normal' }: ReCaptchaOptions) {
    if (!siteKey) {
      throw new Error('ReCaptcha error: No key provided')
    }

    if (!version) {
      throw new Error('ReCaptcha error: No version provided')
    }

    this._elements = {}
    this._grecaptcha = null

    this._ready = null

    this.hideBadge = hideBadge
    this.language = language

    this.siteKey = siteKey
    this.version = version
    this.size = size

    this.mode = mode
  }

  init () {
    if (this._ready) {
      // make sure caller waits until recaptcha get ready
      return this._ready
    }

    // Default events callback
    window.recaptchaErrorCallback = () => this._eventBus['recaptcha-error']('Failed to execute')
    window.recaptchaExpiredCallback = () => this._eventBus['recaptcha-expired']()
    window.recaptchaSuccessCallback = (token: string) => this._eventBus['recaptcha-success'](token)

    //  Create elements
    this._elements = {
      script: document.createElement('script'),
      style: document.createElement('style')
    }
    const { script, style } = this._elements

    let scriptUrl = this.API_URL
    const params = []
    if (this.version === 3) { params.push(`render=${this.siteKey}`) }
    if (this.language) { params.push(`hl=${this.language}`) }
    if (this.mode === 'enterprise') {
      scriptUrl = scriptUrl.replace('api.js', 'enterprise.js')
      params.push(`render=${this.siteKey}`)
    }

    script.setAttribute('async', '')
    script.setAttribute('defer', '')
    script.setAttribute('src', `${scriptUrl}?${params.join('&')}`)

    this._readyPromise = new Promise((resolve, reject) => {
      script.addEventListener('error', () => {
        document.head.removeChild(script)
        reject(new Error('ReCaptcha error: Failed to load script'))
      })

      script.addEventListener('load', () => {
        if (this.version === 3 && this.hideBadge) {
          style.innerHTML = '.grecaptcha-badge { display: none; }'
          document.head.appendChild(style)
        } else if (this.version === 2 && this.hideBadge) {
          // display: none DISABLES the spam checking!
          // ref: https://stackoverflow.com/questions/44543157/how-to-hide-the-google-invisible-recaptcha-badge
          style.innerHTML = '.grecaptcha-badge { visibility: hidden; }'
          document.head.appendChild(style)
        }

        this._grecaptcha = window.grecaptcha.enterprise || window.grecaptcha
        this._grecaptcha.ready(resolve)
      })

      document.head.appendChild(script)
    }).then((isInitSuccess) => {
      this._ready = isInitSuccess as boolean
    }).catch((_initError) => {
      this._ready = null
    })

    return this._readyPromise
  }

  destroy (): void {
    if (this._ready) {
      this._ready = false

      const { head } = document
      const { style } = this._elements

      // @ts-ignore
      const scripts = [...document.head.querySelectorAll('script')].filter(script => script.src.includes('recaptcha'))
      if (scripts.length) {
        scripts.forEach(script => head.removeChild(script))
      }

      if (head.contains(style)) {
        head.removeChild(style)
      }

      const badge = document.querySelector('.grecaptcha-badge')
      if (badge) {
        badge.remove()
      }
    }
  }

  async execute (action: any): Promise<any> {
    try {
      await this.init()

      if ('grecaptcha' in window) {
        return this._grecaptcha.execute(
          this.siteKey,
          { action }
        )
      }
    } catch (error) {
      throw new Error(`ReCaptcha error: Failed to execute ${error}`)
    }
  }

  getResponse (widgetId?: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!('grecaptcha' in window)) {
        reject(new Error('ReCaptcha Error: Failed to get response (no grecaptcha)'))
      }

      if (this.size === 'invisible') {
        window.recaptchaErrorCallback = (error: string) => {
          this._eventBus['recaptcha-error'](error)
          reject(error)
        }
        window.recaptchaSuccessCallback = (token: string) => {
          this._eventBus['recaptcha-success'](token)
          resolve(token)
        }

        this._grecaptcha.execute(widgetId)
      } else {
        const response = this._grecaptcha.getResponse(widgetId)

        if (response) {
          this._eventBus['recaptcha-success'](response)
          resolve(response)
        } else {
          const errMsg = 'Failed to execute'

          this._eventBus['recaptcha-error'](errMsg)
          reject(errMsg)
        }
      }
    })
  }

  on (event: ReCaptchaCustomEvents, callback: Function): void {
    this._eventBus[event] = callback
  }

  reset (widgetId?: any): void {
    if (this.version === 2 || typeof widgetId !== 'undefined') {
      this._grecaptcha.reset(widgetId)
    }
  }

  render (reference: any, { siteKey, theme }: Partial<ReCaptchaOptions>) {
    return this._grecaptcha.render(reference.$el || reference, { siteKey, theme })
  }
}

const reCaptchaInstance = new ReCaptcha(reCaptchaConfig)
/**
 * ReCaptcha Setup End
 */

const props = defineProps({
  siteKey: {
    type: String,
    default: ''
  },
  dataTheme: {
    type: String,
    default: 'light',
    validator: (value: string) => ['dark', 'light'].includes(value)
  },
  dataSize: {
    type: String,
    default: 'normal',
    validator: (value: string) => ['compact', 'normal', 'invisible'].includes(value)
  },
  dataBadge: {
    type: String,
    default: 'bottomright',
    validator: (value: string) => ['bottomright', 'bottomleft', 'inline'].includes(value)
  },
  dataTabindex: {
    type: Number,
    default: 0
  }
})

const computedSiteKey = computed(() => {
  return props.siteKey || config.public.recaptchaSiteKey
})

const $emit = defineEmits(['success', 'error', 'expired'])
function onSuccess (key: string) {
  $emit('success', key)
}
function onError (message: any) {
  $emit('error', message)
}
function onExpired (expired: any) {
  $emit('expired', expired)
}
async function getResponse () {
  return await reCaptchaInstance.getResponse()
}
function reset () {
  reCaptchaInstance.reset()
}

onBeforeUnmount(() => {
  reCaptchaInstance.destroy()
})

onMounted(() => {
  reCaptchaInstance.init()

  reCaptchaInstance.on('recaptcha-success', onSuccess)
  reCaptchaInstance.on('recaptcha-error', onError)
  reCaptchaInstance.on('recaptcha-expired', onExpired)
})

defineExpose({ getResponse, reset })
</script>
<template>
  <div
    class="g-recaptcha"

    :data-sitekey="computedSiteKey"
    :data-theme="props.dataTheme"
    :data-badge="props.dataBadge"
    :data-tabindex="props.dataTabindex"
    :data-size="props.dataSize"

    data-callback="recaptchaSuccessCallback"
    data-expired-callback="recaptchaExpiredCallback"
    data-error-callback="recaptchaErrorCallback"
  />
</template>
