<script lang="ts" setup>
import type { FileSelectorEmits, FileSelectorProps } from '~/components-logic/FileSelector/type'
import { getDefaultsPropsValue, useFileSelector } from '~/components-logic/FileSelector'

const props = withDefaults(defineProps<FileSelectorProps>(), getDefaultsPropsValue())
const emit = defineEmits<FileSelectorEmits>()

const { fileInput, resetFilesInput, filesName, multiple, internalDisable, onInputChange, isDroping, onDragging, onDrop } = useFileSelector(props, emit)

defineExpose({ resetFilesInput })
</script>

<template>
  <div>
    <span
      v-if="label && inputType === 2"
      :class="labelClass"
    >
      {{ label }}
    </span>
    <div class="flex relative w-full select-none" :class="{ disabled: internalDisable, [selectorClass]: true }">
      <input
        :id="id"
        ref="fileInput"
        class="-z-5 relative w-full hidden"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="internalDisable"
        @change="onInputChange"
      >

      <slot name="before" />
      <label
        :for="id"
        class="w-full border justify-between relative cursor-pointer"
        :class="[
          inputClass,
          state === 'warning' ? '!border-orange-500' : '',
          state === true ? '!border-green-500' : '',
          state === false ? '!border-red-500' : '',
          inputType === 2 ? 'h-10' : 'h-12'
        ]"
        @dragover.prevent="onDragging"
        @dragenter.prevent="onDragging"
        @drop.prevent="onDrop"
      >
        <span
          v-if="label && inputType === 1"
          class="absolute label px-3 transition-all duration-150 text-overflow-hidden w-full capitalize z-1 text-sm"
        >{{ label }}</span>
        <div
          class="px-3 text-overflow-hidden"
          :class="[
            label && inputType === 1 ? 'pt-2 leading-12' : 'leading-10'
          ]"
        >
          {{ fileUpload || (isDroping && $t(dropMsg)) || filesName || $t(placeholder) }}
        </div>
        <div class="absolute right-0 top-0 h-full border-left bg-gray-200 text-gray-00 px-3 center-flex rounded-r-[var(--sp-border-radius-2)]">
          <common-loading-dot v-if="isLoading" />
          <template v-else>
            <i class="icon-sen-camera" />
            <span class="ml-1">{{ $t(browseText) }}</span>
          </template>
        </div>
        <slot />
      </label>

      <span
        v-if="state !== undefined && message"
        class="mt-1 text-sm"
        :class="[
          state === null ? '' : state === true ? '!text-green-500' : '!text-red-500']"
      >{{ $t(message) }}</span>
      <slot />
    </div>
  </div>
</template>
