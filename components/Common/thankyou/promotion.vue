<script lang="ts" setup>
import { useStoreInfo } from '~~/store/storeInfo'

defineProps({
  btnClass: {
    default: '',
    type: String,
  },
})

const storeInfo = useStoreInfo()
const localePath = useLocalePath()
const router = useRouter()

function handleCoupon() {
  router.push(localePath('/collection'))
}
</script>

<template>
  <div v-if="storeInfo.promotion_title && storeInfo.discount_code" class="border relative border-dashed border-2 p-4 border-radius-override">
    <h6 class="bg-white absolute top-[-12px] font-medium px-2 uppercase">
      {{ $t('Thank you for your order') }}
    </h6>

    <form action="#" @submit.prevent="handleCoupon">
      <i>{{ storeInfo.promotion_title }}</i>
      <div class="my-4 flex">
        <input class="border border-gray-300 border-r-0 max-w-[300px] px-3 py-2 w-full focus:border-blue-500/50 !rounded-r-none" type="text" :value="storeInfo.discount_code">
        <input
          class="border px-3 py-2 cursor-pointer !rounded-l-none border-l-0"
          :class="btnClass"
          type="submit"
          :value="$t('Shop Now')"
        >
      </div>
    </form>
  </div>
</template>
