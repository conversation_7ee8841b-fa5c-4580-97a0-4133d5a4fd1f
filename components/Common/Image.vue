<script lang="ts" setup>
import { normalizeClass } from 'vue'

const props = defineProps({
  image: {
    required: true,
    type: Object as PropType<SImage>
  },
  imgClass: {
    default: undefined,
    type: [String, Object]
  },
  imgStyle: {
    default: undefined,
    type: [String, Object]
  },
  mobileType: {
    default: undefined,
    type: String
  },
  imgId: {
    default: undefined,
    type: String
  },
  alt: {
    default: undefined,
    type: String
  },
  title: {
    default: undefined,
    type: String
  },
  fetchpriority: {
    default: 'auto',
    type: String
  },
  isSquare: {
    default: false,
    type: Boolean
  }
})
const { $imgUrl, $getMobileOperatingSystem } = useNuxtApp()
const imageError = ref(0)
const imageRef = ref()

const url = computed<string>(() => {
  if (imageError.value > 1) {
    return `${cdnURL.value}images/no-image.webp`
  }
  if (imageError.value === 1) {
    return $imgUrl(props.image)
  }
  return $imgUrl({ ...props.image, format: 'webp' })
})

const urlMobile = computed<string>(() => {
  if (imageError.value > 1) {
    return `${cdnURL.value}images/no-image.webp`
  }
  if (imageError.value === 1) {
    return $imgUrl({ ...props.image, type: props.mobileType as SImageType })
  }
  return $imgUrl({ ...props.image, type: props.mobileType as SImageType, format: 'webp' })
})

const computedSize = computed(() => {
  const size = {
    width: '',
    height: ''
  }

  if ($getMobileOperatingSystem() === 'iOS') {
    return size
  }

  const extracted = /\/rx\/(\d*)(?:x|-)(\d*),/.exec(url.value)
  size.width = extracted?.[1] || ''
  size.height = extracted?.[2] || ''

  return size
})

// Add a computed class that combines user classes with square mode classes if needed
const combinedClasses = computed(() => {
  const baseClasses = props.isSquare ? 'square-image' : 'w-auto object-contain'
  return normalizeClass([baseClasses, props.imgClass])
})

onMounted(() => {
  const imgEl = imageRef.value
  if (imgEl.complete) {
    if (imgEl.naturalWidth === 0) {
      handleError()
    }
  }
})

function handleError() {
  imageError.value++
}
</script>

<template>
  <picture>
    <source v-if="mobileType" :srcset="urlMobile" media="(max-width: 767px)">
    <img
      :id="imgId"
      ref="imageRef"
      :src="url"
      :loading="(fetchpriority !== 'high') ? 'lazy' : 'eager'"
      :class="combinedClasses"
      :style="imgStyle"
      :alt="alt"
      :title="title"
      :fetchpriority="fetchpriority"
      :width="computedSize.width"
      :height="computedSize.height"
      @error="handleError"
    >
  </picture>
</template>
