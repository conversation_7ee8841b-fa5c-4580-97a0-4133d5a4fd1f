<script lang="ts" setup>
import VueSlider from 'vue-slider-component/dist-css/vue-slider-component.umd.min.js'
import 'vue-slider-component/dist-css/vue-slider-component.css'

const emits = defineEmits(['change', 'update:modelValue'])

const props = defineProps({
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 0,
  },
  modelValue: {
    type: Array<Number>,
    default: [undefined, undefined],
  },
})

const modelValueProxy = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emits('update:modelValue', value)
    emits('change', value)
  }
})

</script>
<template>
  <vue-slider
    v-model="modelValueProxy"
    :min="min"
    :max="max"
    :lazy="true"
    tooltip="none"
    class="mb-8"
  />
</template>
