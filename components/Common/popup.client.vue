<script lang="ts" setup>
const messageList = computed(() => {
  return uiManager().popup
})

</script>

<template>
  <Teleport to="body">
    <div class="fixed bottom-5 right-5 z-2">
      <div
        v-for="(pop, index) in messageList"
        :key="index"
        :class="`${(pop.type === 'success') ? 'bg-green-500' : 'bg-red-500'} text-white text-xl px-4 py-2 m-2 w-100 flex items-center animate__animated animate__headShake`"
      >
        <i v-if="pop.type === 'success'" class="icon-sen-checkmark text-4xl" />
        <i v-else class="icon-sen-alert-outline text-4xl" />
        <span class="ml-3">
          {{ pop.message }}
        </span>
      </div>
    </div>
  </Teleport>
</template>
