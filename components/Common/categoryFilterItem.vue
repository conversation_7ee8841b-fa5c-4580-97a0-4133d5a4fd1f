<script lang="ts" setup>
const { category, currentCategory } = defineProps({
  category: {
    default: undefined,
    type: Object as PropType<Category>
  },
  currentCategory: {
    default: undefined,
    type: String
  }
})

const isShowChildCategory = ref<boolean>(checkCurrentCategory(category, currentCategory))

function checkCurrentCategory (category?:Category, currentCategory?: string):boolean {
  if (currentCategory === category?.slug) {
    return true
  } else if (category?.child_menu) {
    return category?.child_menu.some(subCategory => checkCurrentCategory(subCategory, currentCategory))
  } else {
    return false
  }
}

</script>

<template>
  <div>
    <div class="flex justify-between items-center p-1">
      <nuxt-link
        :to="getCategoryFilterUrl(currentCategory===category?.slug ? '' : category?.slug)"
        class="btn-text-black pl-0 flex items-center"
        :class="{'!text-primary':currentCategory===category?.slug}"
      >
        <i
          class="text-xl"
          :class="currentCategory === category?.slug?'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank'"
        /><span class="ml-2">{{ category?.name }}</span>
      </nuxt-link>
      <i
        v-if="category?.child_menu && category?.child_menu.length"
        class="btn-text-black text-xl"
        :class="isShowChildCategory?'icon-sen-minus':'icon-sen-plus'"
        @click="isShowChildCategory = !isShowChildCategory"
      />
    </div>
    <div
      v-show="isShowChildCategory"
      class="sub-categories pl-3"
    >
      <common-category-filter-item
        v-for="(subCategory, index) in category?.child_menu"
        :key="index"
        :category="subCategory"
        :current-category="currentCategory"
      />
    </div>
  </div>
</template>
