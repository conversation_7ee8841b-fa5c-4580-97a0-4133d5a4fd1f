<script lang="ts" setup>

const spriteUrl = computed(() => {
  return `--sprite-url: url("${cdnURL.value}images/logo_cart_sprite.webp")`
})

const paymentsGatewayTypeAccept = computed(() => {
  const storePaymentGatewayType = storeInfo().payment_gateway_type
  if (storePaymentGatewayType === PAYMENT_GATEWAY_TYPE.STRIPE) {
    return ['visa', 'master', 'amex', 'sofort', 'giropay']
  }
  if (storePaymentGatewayType === PAYMENT_GATEWAY_TYPE.PAYPAL) {
    return storeInfo().paypal_enable_card ? ['paypal', 'visa', 'master', 'amex', 'sofort', 'giropay'] : ['paypal']
  }

  return ['paypal', 'visa', 'master', 'amex', 'sofort', 'giropay']
})

</script>

<template>
  <div class="payment-gateway-accept" :style="spriteUrl">
    <p class="text-sm font-semibold mt-4 mb-2 uppercase">
      {{ $t('We accept') }}:
    </p>
    <div class="icon-card-container">
      <div v-for="name in paymentsGatewayTypeAccept" :key="name" class="icon-card">
        <i :class="`payments-sprite payments-sprite-${name}`" />
      </div>
    </div>
  </div>
</template>
