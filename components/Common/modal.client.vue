<script lang="ts" setup>
const props = defineProps({
  modelValue: {
    default: false,
    type: [Boolean, Number, String, Object]
  },
  modalClass: {
    default: undefined,
    type: String
  },
  modalContainerClass: {
    default: undefined,
    type: String
  },
  title: {
    default: undefined,
    type: String
  },
  modalId: {
    default: undefined,
    type: String
  },

  titleContainerClass: {
    default: '',
    type: String,
  },
  titleClass: {
    default: 'text-xl font-medium',
    type: String
  },
  closeIcon: {
    default: 'icon-sen-close-circle',
    type: [String, Boolean],
  },
  closeBtnClass: {
    default: 'btn-text absolute right-2 top-2 text-2xl',
    type: String
  }
})

const $emit = defineEmits(['shown', 'hidden', 'closeModal', 'update:modelValue'])
const shawn = ref(props.modelValue)

watch(() => props.modelValue, () => {
  if (props.modalId) {
    useTracking().newCustomTracking({
      event: 'uiManager',
      actionName: props.modelValue ? 'modal_shown' : 'modal_hidden',
      elementName: props.modalId
    })
  }
  nextTick(() => {
    shawn.value = props.modelValue
    $emit(props.modelValue ? 'shown' : 'hidden')
  })
})

function onModalCloseProxy () {
  // if (!props.closeIcon) {
  //   // If explicitly set `closeIcon` to `false` then disable modal close by clicking outside of the modal
  //   return
  // }

  $emit('closeModal')
  $emit('update:modelValue', false)
}

</script>

<template>
  <Teleport v-if="modelValue" to="body">
    <Transition name="fade">
      <div v-show="shawn" :id="modalId" class="fixed top-0 left-0 w-full h-full z-3 flex" :class="modalContainerClass">
        <div class="relative w-full h-full">
          <div
            class="bg-gray-900 opacity-50 w-full h-full modal-bg"
            @click="onModalCloseProxy"
          />
          <div
            class="border absolute position-center bg-white modal-body"
            :class="modalClass"
          >
            <div v-if="title || closeIcon" :class="titleContainerClass">
              <span v-if="title" :class="titleClass">{{ title }}</span>
              <span
                v-if="closeIcon"
                :class="closeBtnClass"
                @click="onModalCloseProxy"
              ><i :class="closeIcon" /></span>
            </div>
            <slot />
          </div>

          <slot name="inside" />
        </div>
        <slot name="outside" />
      </div>
    </Transition>
  </Teleport>
</template>
