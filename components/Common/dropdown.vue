<script lang="ts" setup>
const props = defineProps({
  inputType: {
    default: 1,
    type: Number,
  },
  btnClass: {
    default: 'btn-text hover:(border-primary-hover)',
    type: String
  },
  showDataClass: {
    default: 'border-primary shadow-custom2',
    type: String,
  },
  dropdownClass: {
    default: undefined,
    type: String
  },
  closeOnClick: {
    default: true,
    type: Boolean
  },
  dropdownId: {
    default: undefined,
    type: String
  },
  showDropdownIcon: {
    type: Boolean,
    default: true
  },
  showOnHover: {
    type: Boolean,
    default: false
  },
  dropdownIcon: {
    type: String,
    default: 'icon-sen-menu-down text-2xl'
  },
})

defineExpose({
  hideData,
  toggleData,
})

const $emit = defineEmits(['shown', 'hidden'])
const $viewport = useViewport()

const isShowData = ref<boolean>(false)
const element = ref<HTMLElement>()
const showPositionX = ref<'left' | 'right'>('left')
const showPositionY = ref<'top' | 'bottom'>('bottom')

function hideData (): void {
  if (isShowData.value) {
    isShowData.value = false
  }
  nextTick(() => {
    $emit('hidden')
  })
}

function toggleData (): void {
  if (!isShowData.value) {
    const { x, y, height, width } = element.value?.getBoundingClientRect() as DOMRect
    const { innerHeight, innerWidth } = window
    showPositionX.value = (x + width / 2 >= innerWidth / 2) ? 'right' : 'left'
    showPositionY.value = (y + height / 2 >= innerHeight / 2) ? 'top' : 'bottom'
  }
  isShowData.value = !isShowData.value
  nextTick(() => {
    if (props.dropdownId) {
      useTracking().newCustomTracking({
        event: 'uiManager',
        actionName: isShowData.value ? 'dropdown_shown' : 'dropdown_hidden',
        elementName: props.dropdownId
      })
    }
    $emit(isShowData.value ? 'shown' : 'hidden')
  })
}

function hoverHandler () {
  if (!props.showOnHover || $viewport.isLessThan(VIEWPORT.desktop)) { return }

  toggleData()
}
</script>

<template>
  <div
    :id="dropdownId"
    ref="element"
    v-click-outside="hideData"
    class="dropdown relative"
    @mouseenter="hoverHandler"
    @mouseleave="hoverHandler"
  >
    <button
      type="button"
      class="relative"
      :class="[
        btnClass,
        isShowData ? showDataClass : '',
        props.showDropdownIcon ? 'pr-5.5' : ''
      ]"
      @click="toggleData"
    >
      <slot />
      <i
        v-if="props.showDropdownIcon"
        class="float-right absolute right-0 position-center-y z-0 transition"
        :class="[
          isShowData ? '-rotate-90' : 'rotate-0',
          dropdownIcon,
        ]"
      />
    </button>
    <client-only>
      <template v-if="$viewport.isGreaterOrEquals(VIEWPORT.tablet)">
        <div
          v-if="isShowData"
          class="absolute border bg-white z-3"
          :class="[showPositionX === 'right' ? 'right-0': 'left-0', showPositionY === 'top' ? 'transform -translate-y-[100%] top-0': '', dropdownClass]"
          @click="closeOnClick ? hideData() : ''"
        >
          <ul
            class="overflow-auto max-h-50"
            :class="[
              inputType === 1 ? 'my-2' : ''
            ]"
          >
            <slot name="content" />
          </ul>
        </div>
      </template>
      <Teleport v-else to="body">
        <div v-if="isShowData" class="relative z-3" @click="closeOnClick ? hideData() : ''">
          <div class="fixed position-center bg-gray-900 opacity-50 w-full h-full" />
          <div
            class="border fixed position-center bg-white"
            :class="dropdownClass"
            @click="closeOnClick ? hideData() : ''"
          >
            <ul class="overflow-auto max-h-[80vh] min-w-[50vw] my-2">
              <slot name="content" />
            </ul>
          </div>
        </div>
      </Teleport>
    </client-only>
  </div>
</template>

<style scoped>
</style>
