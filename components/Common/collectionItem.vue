<script lang="ts" setup>
const localePath = useLocalePath()

defineProps({
  value: {
    type: String,
    default: ''
  },
  active: {
    type: Boolean,
    default: false
  },
  slug: {
    type: String,
    default: ''
  }
})
</script>
<template>
  <nuxt-link
    :to="localePath({ path: slug })"
    :title="value"
    class="btn-border px-3 py-0 uppercase"
    :class="{'!text-primary !border-primary shadow-custom':active}"
  >
    <small class="font-semibold whitespace-nowrap">{{ value }}</small>
  </nuxt-link>
</template>
<style scoped>
</style>
