<script lang="ts" setup>
import VueEasyLightbox from 'vue-easy-lightbox'

const vueEasyLightboxRef = ref<InstanceType<typeof VueEasyLightbox>>()
const { $imgUrl } = useNuxtApp()
const imageUrls = computed(() => {
  return uiManager().viewImageUrl?.map(image => {
    if (image.startsWith('data:image/png')) {
      return image
    }
    return $imgUrl({ path: image, type: 'full_hd' })
  })
})

const currentImageIndex = computed(() => {
  return uiManager().viewImageIndex
})

</script>

<template>
  <vue-easy-lightbox
    ref="vueEasyLightboxRef"
    :visible="!!imageUrls?.length"
    :imgs="imageUrls"
    :index="currentImageIndex"
    @hide="uiManager().viewImage(undefined)"
  />
</template>

<style scoped>
</style>
