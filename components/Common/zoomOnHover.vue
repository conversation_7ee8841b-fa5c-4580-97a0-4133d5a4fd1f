<script lang="ts" setup>
const $viewport = useViewport()

const props = defineProps({
  scale: {
    default: 1,
    type: Number
  },
  image: {
    required: true,
    type: Object as PropType<SImage>
  },
  imageZoom: {
    default: undefined,
    type: Object as PropType<SImage>
  },
  disableZoom: {
    default: false,
    type: Boolean
  },
  index: {
    default: null,
    type: Number || null
  },
  alt: {
    default: undefined,
    type: String
  },
  preloadIndex: {
    default: 0,
    type: Number
  }
})

const disabled = computed(() => {
  return props.disableZoom || $viewport.isLessThan(VIEWPORT.desktop)
})

const $el = ref<HTMLElement>()
const imageNormalRef = ref()
const imageZoomRef = ref()
const zoomed = ref(false)

function touchZoom (event:Event) {
  if (disabled.value) { return }
  move(event as MouseEvent)
  zoomed.value = !zoomed.value
}

function zoom () {
  if (!disabled.value && $viewport.isGreaterOrEquals(VIEWPORT.tablet)) {
    zoomed.value = true
  }
}

function unZoom () {
  if (!disabled.value && $viewport.isGreaterOrEquals(VIEWPORT.tablet)) {
    zoomed.value = false
  }
}

function move (event:MouseEvent) {
  try {
    if (disabled.value || !zoomed.value || !$el.value) { return }
    const offset = pageOffset($el.value)
    const zoom = imageZoomRef?.value.$refs.imageRef
    const normal = imageNormalRef?.value.$refs.imageRef
    const relativeX = event.clientX - offset.x + window.pageXOffset - normal.offsetWidth / 2
    const relativeY = event.clientY - offset.y + window.pageYOffset - normal.offsetHeight / 2
    const normalFactorX = relativeX / normal.offsetWidth
    const normalFactorY = relativeY / normal.offsetHeight

    const x = normalFactorX * (zoom.offsetWidth * props.scale - normal.offsetWidth)
    const y = normalFactorY * (zoom.offsetHeight * props.scale - normal.offsetHeight)

    zoom.style.left = -x + 'px'
    zoom.style.top = -y + 'px'
  } catch (error) {
    console.log(error)
  }
}

function pageOffset (el:HTMLElement) {
  const rect = el.getBoundingClientRect()
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  return {
    y: rect.top + scrollTop,
    x: rect.left + scrollLeft
  }
}

</script>

<template>
  <div
    ref="$el"
    class="zoom-on-hover"
    :class="{zoomed, 'cursor-zoom-in': !disabled}"
    @touchstart="touchZoom"
    @mousemove="move"
    @mouseenter="zoom"
    @mouseleave="unZoom"
  >
    <common-image
      ref="imageNormalRef"
      img-class="normal"
      :image="image"
      :alt="alt"
      :fetchpriority="(index === preloadIndex) ? 'high' : ''"
    />
    <common-image
      v-if="!disabled"
      ref="imageZoomRef"
      :img-style="`scale: ${scale}`"
      img-class="zoom"
      :image="imageZoom || image"
      :alt="alt"
      :fetchpriority="(index === preloadIndex) ? 'high' : ''"
    />
  </div>
</template>

<style>
.cursor-zoom-in {
  cursor: zoom-in;
}

.zoom-on-hover {
  position: relative;
  overflow: hidden;
}

.zoom-on-hover .normal {
  width: 100%;
}

.zoom-on-hover .zoom {
  position: absolute;
  opacity: 0;
  transform-origin: center;
}

@media (min-width: 768px) {
  .zoom-on-hover.zoomed .zoom {
    opacity: 1;
  }

  .zoom-on-hover.zoomed .normal {
    opacity: 0;
  }
}

</style>
