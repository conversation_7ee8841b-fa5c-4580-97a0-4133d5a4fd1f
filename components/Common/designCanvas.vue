<script lang="ts" setup>
const { customImage } = defineProps({
  customImage: {
    default: undefined,
    type: Object as PropType<CustomImageItem>
  },
  color: {
    default: undefined,
    type: String
  }
})

const designBoxRef = ref<HTMLElement>()
const { personalized, designCanvas } = customImage?.personalize || {}

onMounted(async () => {
  await nextTick()
  loadDesign()
  window.addEventListener('resize', resize)
})

onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

watch(() => customImage, loadDesign)

function loadDesign() {
  if (personalized === 1 && designCanvas) {
    designBoxRef.value!.innerHTML = ''
    designBoxRef.value?.appendChild(designCanvas.wrapperEl)
    resize()

    if (!designCanvas.isLockEdit) {
      designCanvas.lockEdit()
    }
  }
}

function resize() {
  if (designBoxRef.value?.clientWidth) {
    designCanvas.setDisplaySize(designBoxRef.value?.clientWidth)
  }
}
</script>

<template>
  <div class="w-full">
    <div id="editDesignBox" ref="designBoxRef" class="w-full" />
  </div>
</template>
