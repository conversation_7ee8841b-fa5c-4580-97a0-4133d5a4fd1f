<script lang="ts" setup>

const { template, currentTemplate } = defineProps({
  template: {
    default: undefined,
    type: Object as PropType<Template>
  },
  currentTemplate: {
    default: undefined,
    type: String
  }
})
</script>

<template>
  <nuxt-link
    class="btn-text-black p-1 block flex items-center"
    :class="{ '!text-primary' : template?.id+'' === currentTemplate }"
    :to="getFilterUrl('product', template?.id+'' !== currentTemplate ? template?.id+'' : '')"
  >
    <i
      class="text-xl"
      :class="template?.id === parseInt(currentTemplate || '') ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank'"
    />
    <span class="ml-2 max-line-1 text-left">{{ template?.name }}</span>
  </nuxt-link>
</template>
