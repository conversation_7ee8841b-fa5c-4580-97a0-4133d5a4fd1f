<script setup lang="ts">
interface StepperProps {
  timeframe: Timeframe
}

interface Step {
  key: keyof Timeframe
  label: string
}

defineProps<StepperProps>()

const { $i18n } = useNuxtApp()

const steps: Step[] = [
  {
    key: 'received',
    label: 'Received'
  },
  {
    key: 'validate',
    label: 'Validated'
  },
  {
    key: 'print',
    label: 'Printed'
  },
  {
    key: 'package',
    label: 'Packaged'
  },
  {
    key: 'ship',
    label: 'On Delivery'
  }
]

function formatTime(dateString: string) {
  const { date: dateLocale } = $i18n.localeProperties
  return new Date(dateString).toLocaleDateString(dateLocale, { month: 'short', day: 'numeric' })
}

onMounted(() => {
  scrollToStep(steps[3])
})

function scrollToStep(step: Step) {
  const element = document.getElementById(`status-step-${step?.key}`)
  element?.scrollIntoView({
    behavior: 'smooth',
    block: 'center'
  })
}

function isCurrentDateLaterThan(date: string) {
  const currentDate = new Date()
  const targetDate = new Date(date)
  return currentDate > targetDate
}

function getNextStepKey(step: Step) {
  const index = steps.findIndex(s => s.key === step.key)
  if (index === steps.length - 1) {
    return step
  }

  return steps[index + 1]?.key
}
</script>

<template>
  <div class="progress__bar flex justify-center">
    <!-- Step Line -->
    <div class="h-20 px-10 overflow-x-auto">
      <div class="flex md:min-w-[687px] min-w-[500px] items-center justify-between w-full">
        <div
          v-for="(step, index) in steps"
          :id="`status-step-${step?.key}`"
          :key="index"
          class="flex items-center"
          :class="{
            'flex-grow': index !== steps.length - 1
          }"
        >
          <!-- Content -->
          <div
            class="relative"
          >
            <!-- Step circle -->
            <div
              class="w-7 h-7 rounded-full flex items-center justify-center z-0 text-white"
              :class="isCurrentDateLaterThan(timeframe[step.key] as string) ? 'bg-primary' : 'bg-[#F3F3F3]'"
            >
              <GivehugSharedIconChecked v-if="isCurrentDateLaterThan(timeframe[step.key] as string)" class="w-4" />
            </div>
            <!-- Step text -->
            <div class="absolute top-8 text-center flex flex-col items-center right-1/2 transform translate-x-[50%]">
              <div
                class="text-xs sm:text-sm"
                :class="
                  Boolean(timeframe[step.key])
                    ? 'text-black font-semibold'
                    : 'text-[#B2B2B2]'
                "
                style="text-wrap: nowrap;"
              >
                {{ $t(step.label) }}
              </div>
              <div v-if="timeframe[step.key]" class="text-muted text-xs sm:text-sm">
                {{ formatTime(timeframe[step.key] as string) }}
              </div>
            </div>
          </div>
          <!-- Connector line -->
          <div
            v-if="index !== steps.length - 1"
            class="flex-grow h-0.5"
            :class="isCurrentDateLaterThan(getNextStepKey(step) as string) ? 'bg-primary' : 'bg-[#F3F3F3]'"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.progress__bar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.progress__bar::-webkit-scrollbar {
  display: none;
}
</style>
