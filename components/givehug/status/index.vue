<script lang="ts" setup>
const {
  relatedProducts,

  getOrderStatus,
  getTimeTracking,
  compareTrackingTime,

  showConfirmDeleteRequestCancelOrder,
  showPendingCustomerAction,
  resumeOrder,
  cancelOrder,
  getTotalTrackingItemText,

  order,
  tracking,
  timeframe,

  hasMailboxNumber,
  hasHouseNumber,
  currentCountry,
  orderStatuses,
  cancelOrderMsgs,
  currentShippingMethod,
  isShowModal,
  canEditAddressInfo
} = await useOrderStatusPage()

const ONE_DAY = 60 * 60 * 24 * 1000

const { $i18n } = useNuxtApp()

onMounted(() => {
  const { query } = useRoute()
  if (query?.open_edit === 'true' && canEditAddressInfo.value) {
    isShowModal.value.editInfo = true
  }
})

async function confirmed() {
  const router = useRouter()
  await router.replace({ query: {} })
  router.go(0)
}

function groupProductInCombo(products?: OrderProduct[]) {
  if (!products)
    return []

  const comboProducts = products.filter(product => product.combo_id)
  const comboMap = new Map<string, OrderProduct[]>()
  comboProducts.forEach((product) => {
    const comboId = product.combo_id as string
    if (comboMap.has(comboId)) {
      comboMap.get(comboId)?.push(product)
    }
    else {
      comboMap.set(comboId, [product])
    }
  })

  return Array.from(comboMap.values())
}

const estimatedDeliveryDate = computed(() => {
  if (!currentShippingMethod.value) {
    return ''
  }

  const { shipping_time, printing_time } = currentShippingMethod.value
  const { date: dateLocale } = $i18n.localeProperties
  const date2 = new Date(Date.now() + ONE_DAY * (shipping_time[1] + printing_time))
  return date2.toLocaleDateString(dateLocale, { month: 'short', day: 'numeric', year: 'numeric' })
})

const products = computed(() => {
  if (!tracking)
    return []

  const products = []

  for (const fulfill_status in tracking) {
    const statusProducts = tracking[fulfill_status as keyof Fulfillments] || []

    products.push(...statusProducts.map(product => ({ ...product, options: JSON.parse(product.options) })))
  }

  return products
})
</script>

<template>
  <main class="cs-container" dusk="thank-you-page">
    <GivehugStatusProgressBar v-if="timeframe" :timeframe="timeframe" class="mt-7 md:mt-15 -mx-5" />
    <GivehugThankyouTitleWrapper>
      {{ $t('Your Order Confirmed') }}
    </GivehugThankyouTitleWrapper>

    <div
      class="mt-8 md:mt-15"
    >
      <GivehugThankyouOrderInfo
        :order="order"
        :current-shipping-method="currentShippingMethod"
      />
    </div>

    <div class="flex flex-wrap justify-between mt-12 md:mt-15 gap-y-12">
      <!-- Table Product -->
      <div class="w-full lg:w-[54%]">
        <!-- Header -->
        <div class="flex border-b border-[#F4F4F4] font-semibold text-black pb-3 gap-0 xl:gap-16">
          <div class="w-8/12 md:w-7/12 lg:w-8/12 xl:w-7/12">
            {{ $t('Product') }}
          </div>
          <div class="hidden md:grid md:w-5/12 lg:w-4/12 xl:w-5/12 w-4/12 grid-cols-1 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3">
            <div class="text-left hidden md:block xl:block">
              {{ $t('Price') }}
            </div>
            <div class="text-center hidden md:block lg:hidden xl:block">
              {{ $t('Quantity') }}
            </div>
            <div class="text-right">
              {{ $t('Total') }}
            </div>
          </div>
        </div>
        <!-- Body -->
        <GivehugThankyouTableItem
          v-for="(product, index) in products"
          :key="product.id"
          :thumb-url="product.thumb_url"
          :product-url="product.product_url"
          :color="product.options?.color"
          :quantity="product.quantity"
          :title="product.campaign_title"
          :product-name="product.product_name"
          :option-list="product.options"
          :current-options="product.options"
          :price="product.price"
          :total-amount="product.total_amount"
          :currency-rate="order.currency_rate"
          :currency-code="order.currency_code"
          :class="{ 'border-b border-[#F4F4F4]': index !== products.length - 1 }"
        />

        <GivehugThankyouTableItemMobile
          v-for="(product, index) in products"
          :key="product.id"
          :thumb-url="product.thumb_url"
          :product-url="product.product_url"
          :color="product.options?.color"
          :title="product.campaign_title"
          :product-name="product.product_name"
          :option-list="product.options"
          :current-options="product.options"
          :quantity="product.quantity"
          :price="product.price"
          :total-amount="product.total_amount"
          :currency-rate="order.currency_rate"
          :currency-code="order.currency_code"
          :class="{ 'border-b border-[#F4F4F4]': index !== products.length - 1 }"
        />
      </div>

      <div class="w-full lg:w-[calc(40%-8px)]">
        <GivehugThankyouOrderSummary
          :total-product-amount="order.total_product_amount"
          :total-shipping-amount="order.total_shipping_amount"
          :total-discount="order.total_discount"
          :total-amount="order.total_amount"
          :currency-rate="order.currency_rate"
          :currency-code="order.currency_code"
        />
      </div>
    </div>

    <div class="flex flex-wrap justify-between mt-12 sm:mt-15 md:mt-20 gap-y-12 sm:gap-y-15">
      <!-- Thank you for your order -->
      <div class="md:w-full lg:w-[54%] <md:-mx-5 <md:px-1">
        <GivehugThankyouPromotion />
      </div>

      <div class="w-full lg:w-[calc(40%-8px)] <sm:flex <sm:flex-col <sm:items-center">
        <div class="<sm:text-center">
          For any concerns regarding product quality or defects, kindly contact our customer support for prompt resolution and assistance.
        </div>
        <button
          class="mt-4 btn text-white bg-black hover:bg-black/80 trans__time flex items-center justify-center gap-1 w-62 h-10 rounded-full"
          @click="$openCrispChat()"
        >
          <GivehugSharedIconChat class="w-4 h-4" />
          Chat with us
        </button>
      </div>
    </div>

    <LazyGivehugSharedCarouselRoot
      v-if="relatedProducts?.length && !storeInfo().disable_related_product"
      class="mt-20"
      :products="relatedProducts"
      :title="$t(`Frequently bought together`)"
      :max-items="6"
    />

    <div class="mb-12 md:mb-40" />
  </main>
</template>
