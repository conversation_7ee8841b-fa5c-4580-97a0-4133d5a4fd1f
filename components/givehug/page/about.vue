<template>
  <div class="about-container">
    <div class="about-intro">
      <div class="about-title">
        <span>Givehug is a cross-border e-commerce platform owned by</span>
        <div class="about-logo-container">
          <img class="about-logo-img" src="/images/givehug/about/1.webp" alt="Givehug">
        </div>
        <span>VTT Global LLC. Givehug.com was born with the desire to sell gift products to family, friends and relatives. With the meaning of giving love through hugs from loved ones to each other.
        </span>
      </div>
      <div class="about-subtitle">
        We are a technology company specializing in developing and publishing high-quality video games and mobile applications on iOS, Android and PC platforms. With a team of experienced software engineers, designers and technology experts, VTT Global LLC constantly innovates to bring attractive, user-friendly and highly interactive entertainment products to users around the world.
      </div>
    </div>
    <div class="about-hero">
      <img src="/images/givehug/about/2.webp" alt="Givehug">
    </div>
    <div class="about-description">
      In addition to focusing on game and application development, the company has also expanded into the cross-border e-commerce sector, specializing in Print on Demand (POD) products for the international market. With a mission to bring innovative designs and highly personalized products to customers around the world, we aim to combine technology, art and commerce to create a unique and highly personalized shopping experience for every customer.
    </div>
    <div class="about-grid">
      <div class="about-grid-image">
        <img src="/images/givehug/about/3.webp" alt="Givehug" class="about-img-cover">
      </div>
      <div class="about-grid-image">
        <img src="/images/givehug/about/4.webp" alt="Givehug" class="about-img-cover">
      </div>
      <div class="about-grid-text">
        <div>
          <svg class="about-svg-logo" width="169" viewBox="0 0 125 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_2_5)">
              <path d="M19.7874 12.2201C19.7607 11.2323 19.2534 10.378 18.2119 10.1911C17.3841 10.0576 14.5535 10.0576 13.8592 10.4314C13.1649 10.8318 13.0314 11.5259 13.1115 12.2735C10.2809 8.48247 4.72656 9.33678 1.97608 12.8074C-1.06814 16.6518 -0.614177 23.1392 3.23115 26.2895C6.08845 28.6388 10.9485 28.7189 13.245 25.5687C13.3252 29.333 11.1622 30.6945 7.58386 30.3475C5.15382 30.1072 3.65841 27.8913 2.02949 30.9348C1.57553 31.7624 1.06816 32.8036 1.81586 33.6579C2.61697 34.5389 6.59582 35.3398 7.85089 35.4199C23.4191 36.5412 19.2 22.4985 19.7874 12.1934V12.2201ZM10.2542 22.9256C5.79471 23.2193 5.55438 15.6106 9.3463 14.9699C14.1797 14.1423 14.42 22.632 10.2542 22.9256Z" fill="#F1641E" />
              <path d="M124.813 11.5259C124.653 10.8852 124.092 10.3779 123.451 10.2178C122.677 10.0576 119.766 10.0576 119.072 10.4046C118.751 10.5648 118.431 10.9119 118.324 11.259L118.11 12.3001C116.401 9.41685 112.423 9.28336 109.645 10.725C107.963 11.606 106.788 13.0477 106.04 14.7563C106.04 13.8753 106.04 12.9676 106.094 12.1133C106.014 10.9386 105.319 10.2979 104.171 10.1911C103.317 10.111 102.088 10.2712 101.447 10.725C100.993 11.0454 100.566 12.1667 100.459 12.9409C100.085 16.0911 99.498 18.307 97.7088 20.1758C97.5219 20.3894 97.3617 20.5496 97.2014 20.7098C97.0145 21.2437 97.1213 21.7242 96.6674 22.2048C95.5992 23.3795 95.1185 22.0446 95.0651 22.0713C95.0651 22.098 94.6913 23.0858 93.8902 23.1659C92.9288 23.246 92.5283 22.5519 92.8487 21.7509L93.1158 21.2704C92.4749 21.5374 91.2732 21.6442 91.1397 20.8699C91.0596 20.2559 91.834 19.6686 92.3414 19.3749C91.8607 19.0812 91.4334 18.4405 91.7539 18.0133C92.3147 17.2124 93.009 17.426 93.73 17.0255C94.3975 15.931 95.2788 14.1956 95.1185 12.2467C95.0384 11.1789 94.5845 10.3512 93.4362 10.1911C92.5817 10.0843 89.7511 10.0843 89.0568 10.458C88.4159 10.8051 88.229 11.5526 88.1756 12.2467C88.0688 13.3947 88.0421 14.8898 88.0421 16.3848C87.962 15.5839 87.8818 14.8898 87.775 14.4359C86.9205 10.992 84.2234 9.31006 80.7253 9.97749C79.123 10.2712 78.1884 11.0454 77.147 12.1934V5.03851C77.147 5.03851 76.6396 4.10411 76.5595 4.02402C76.3726 3.86383 75.7584 3.65025 75.4913 3.62356C74.7436 3.54347 72.5272 3.54347 71.7795 3.65025C71.2454 3.73035 70.6046 4.1575 70.4443 4.69144L70.3375 26.0225C70.3375 26.4763 70.6313 27.0904 71.0318 27.3307C71.8329 27.8379 74.2363 27.8379 75.0374 27.384C75.6249 27.0637 75.8652 26.3696 75.9186 25.7555C76.1856 23.246 76.9333 20.1224 78.3219 17.7998C78.5089 17.4527 78.7225 17.0522 78.7492 16.8654C78.8026 16.278 78.5623 15.8242 78.8827 15.2368C79.6304 13.8219 80.4315 14.9966 80.4582 14.9699C80.4582 14.9432 80.565 13.902 81.3127 13.635C82.2207 13.3146 82.7814 13.902 82.6746 14.7296L82.5411 15.2635C83.1019 14.8364 84.2501 14.4359 84.5439 15.1567C84.7842 15.7174 84.17 16.4916 83.7695 16.9188C84.3036 17.0789 84.8643 17.5862 84.6774 18.0934C84.3303 19.0011 83.6093 18.9744 82.9951 19.5618C82.9684 19.5885 82.8349 19.802 82.728 19.9355C81.8201 21.6975 81.2593 23.7265 81.7667 26.2628C81.9536 27.1438 82.0871 27.5442 82.9417 27.6777C83.7962 27.7845 86.2262 27.7845 87.0006 27.4908C87.6949 27.2239 88.0154 26.6098 88.0688 25.9157C88.1756 24.9546 88.229 23.246 88.229 21.4039C88.6029 25.622 90.4454 28.4519 95.3055 27.9714C97.1747 27.7845 97.7355 27.037 98.9105 25.8623C99.0173 25.7555 99.0173 25.6754 99.2309 25.7288C99.1508 26.3696 99.4178 27.1171 100.005 27.4374C100.753 27.8379 103.423 27.8379 104.331 27.7044C105.426 27.5442 105.934 26.9035 106.04 25.8089C106.094 25.0881 106.094 24.3139 106.12 23.5664C106.414 24.1804 106.788 24.741 107.215 25.275C110.019 28.6121 115.307 29.1728 118.271 25.7555C118.404 30.2139 114.933 31.095 111.194 30.0805C110.126 29.7868 109.004 28.9058 107.936 29.7334C107.162 30.3207 106.147 32.5099 106.735 33.4443C107.429 34.5656 111.541 35.3398 112.877 35.4466C119.633 35.9271 124.386 33.3642 124.813 26.2094C125.107 21.4573 124.653 16.3047 124.706 11.5259H124.813ZM115.36 22.9256C110.874 23.2193 110.74 15.6106 114.452 14.9699C119.285 14.1422 119.552 22.6319 115.36 22.9256Z" fill="#F1641E" />
              <path d="M50.2564 17.9332C49.6956 25.1682 55.4636 28.5053 62.0594 27.9714C63.5815 27.8379 67.6138 26.77 67.9342 25.0614C68.1478 23.9134 66.5456 21.7776 65.2638 21.8577C64.2491 21.9378 62.9139 22.8455 61.5253 22.9256C59.7896 23.0324 56.7988 22.3383 56.6385 20.2559H68.3081C68.6018 20.2559 69.3495 19.7219 69.483 19.4283C69.8836 18.4672 69.3762 16.278 69.0291 15.3169C65.9047 6.93399 51.0041 8.32224 50.2564 17.9332ZM56.6652 17.0789C56.9056 13.7151 63.0474 13.8219 63.4747 17.0789H56.6652Z" fill="#F1641E" />
              <path d="M49.8024 10.3246C49.0013 10.0042 45.5298 10.0576 44.702 10.3513C43.7941 10.6983 43.6071 11.4191 43.26 12.2468C42.2453 14.783 41.4975 17.5595 40.5629 20.1224C40.376 20.1224 40.3493 19.9355 40.2959 19.8021C39.201 17.426 38.6403 14.3024 37.5988 11.8463C37.2784 11.1255 37.0914 10.6449 36.2903 10.3513C35.4892 10.0576 32.7387 10.0309 31.8308 10.1644C30.4689 10.3513 30.1218 11.2056 30.5757 12.4603L36.9579 26.5831C37.8658 28.0248 40.2692 27.8646 41.818 27.7311C43.8208 27.5709 44.0344 26.7166 44.7821 25.1148C46.7582 21.0034 48.3604 16.7052 50.3632 12.5938C50.737 11.7128 50.7637 10.725 49.749 10.3246H49.8024Z" fill="#F1641E" />
              <path d="M27.6383 10.2178C26.8372 10.0843 24.6475 10.0576 23.8731 10.1911C23.2589 10.2979 22.6447 10.6716 22.4311 11.2857L22.351 26.156C22.4044 26.7967 22.8851 27.3307 23.4726 27.5443C24.1668 27.7845 26.7838 27.8112 27.5315 27.6777C28.4127 27.5176 29.0536 26.9836 29.107 26.0492V11.4459C28.84 10.6983 28.4127 10.3513 27.6383 10.2178Z" fill="#F1641E" />
              <path d="M26.0361 5.78603C26.1696 3.70365 24.3805 2.60906 23.6595 2.92943C21.0959 4.05071 22.084 7.14758 25.3952 8.8295C25.6088 8.93629 25.8759 8.93629 26.0895 8.8562C29.2406 7.54804 31.9109 5.22538 29.4542 3.32989C28.4127 2.52897 26.6236 3.94392 26.0628 5.78603H26.0361Z" fill="#F1641E" />
            </g>
            <defs>
              <clipPath id="clip0_2_5">
                <rect width="125" height="35" fill="white" transform="translate(0 0.5)" />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
    </div>
    <div class="about-grid mt-5">
      <div class="about-product-info">
        <svg width="169" viewBox="0 0 125 36" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_2_5)">
            <path d="M19.7874 12.2201C19.7607 11.2323 19.2534 10.378 18.2119 10.1911C17.3841 10.0576 14.5535 10.0576 13.8592 10.4314C13.1649 10.8318 13.0314 11.5259 13.1115 12.2735C10.2809 8.48247 4.72656 9.33678 1.97608 12.8074C-1.06814 16.6518 -0.614177 23.1392 3.23115 26.2895C6.08845 28.6388 10.9485 28.7189 13.245 25.5687C13.3252 29.333 11.1622 30.6945 7.58386 30.3475C5.15382 30.1072 3.65841 27.8913 2.02949 30.9348C1.57553 31.7624 1.06816 32.8036 1.81586 33.6579C2.61697 34.5389 6.59582 35.3398 7.85089 35.4199C23.4191 36.5412 19.2 22.4985 19.7874 12.1934V12.2201ZM10.2542 22.9256C5.79471 23.2193 5.55438 15.6106 9.3463 14.9699C14.1797 14.1423 14.42 22.632 10.2542 22.9256Z" fill="#F1641E" />
            <path d="M124.813 11.5259C124.653 10.8852 124.092 10.3779 123.451 10.2178C122.677 10.0576 119.766 10.0576 119.072 10.4046C118.751 10.5648 118.431 10.9119 118.324 11.259L118.11 12.3001C116.401 9.41685 112.423 9.28336 109.645 10.725C107.963 11.606 106.788 13.0477 106.04 14.7563C106.04 13.8753 106.04 12.9676 106.094 12.1133C106.014 10.9386 105.319 10.2979 104.171 10.1911C103.317 10.111 102.088 10.2712 101.447 10.725C100.993 11.0454 100.566 12.1667 100.459 12.9409C100.085 16.0911 99.498 18.307 97.7088 20.1758C97.5219 20.3894 97.3617 20.5496 97.2014 20.7098C97.0145 21.2437 97.1213 21.7242 96.6674 22.2048C95.5992 23.3795 95.1185 22.0446 95.0651 22.0713C95.0651 22.098 94.6913 23.0858 93.8902 23.1659C92.9288 23.246 92.5283 22.5519 92.8487 21.7509L93.1158 21.2704C92.4749 21.5374 91.2732 21.6442 91.1397 20.8699C91.0596 20.2559 91.834 19.6686 92.3414 19.3749C91.8607 19.0812 91.4334 18.4405 91.7539 18.0133C92.3147 17.2124 93.009 17.426 93.73 17.0255C94.3975 15.931 95.2788 14.1956 95.1185 12.2467C95.0384 11.1789 94.5845 10.3512 93.4362 10.1911C92.5817 10.0843 89.7511 10.0843 89.0568 10.458C88.4159 10.8051 88.229 11.5526 88.1756 12.2467C88.0688 13.3947 88.0421 14.8898 88.0421 16.3848C87.962 15.5839 87.8818 14.8898 87.775 14.4359C86.9205 10.992 84.2234 9.31006 80.7253 9.97749C79.123 10.2712 78.1884 11.0454 77.147 12.1934V5.03851C77.147 5.03851 76.6396 4.10411 76.5595 4.02402C76.3726 3.86383 75.7584 3.65025 75.4913 3.62356C74.7436 3.54347 72.5272 3.54347 71.7795 3.65025C71.2454 3.73035 70.6046 4.1575 70.4443 4.69144L70.3375 26.0225C70.3375 26.4763 70.6313 27.0904 71.0318 27.3307C71.8329 27.8379 74.2363 27.8379 75.0374 27.384C75.6249 27.0637 75.8652 26.3696 75.9186 25.7555C76.1856 23.246 76.9333 20.1224 78.3219 17.7998C78.5089 17.4527 78.7225 17.0522 78.7492 16.8654C78.8026 16.278 78.5623 15.8242 78.8827 15.2368C79.6304 13.8219 80.4315 14.9966 80.4582 14.9699C80.4582 14.9432 80.565 13.902 81.3127 13.635C82.2207 13.3146 82.7814 13.902 82.6746 14.7296L82.5411 15.2635C83.1019 14.8364 84.2501 14.4359 84.5439 15.1567C84.7842 15.7174 84.17 16.4916 83.7695 16.9188C84.3036 17.0789 84.8643 17.5862 84.6774 18.0934C84.3303 19.0011 83.6093 18.9744 82.9951 19.5618C82.9684 19.5885 82.8349 19.802 82.728 19.9355C81.8201 21.6975 81.2593 23.7265 81.7667 26.2628C81.9536 27.1438 82.0871 27.5442 82.9417 27.6777C83.7962 27.7845 86.2262 27.7845 87.0006 27.4908C87.6949 27.2239 88.0154 26.6098 88.0688 25.9157C88.1756 24.9546 88.229 23.246 88.229 21.4039C88.6029 25.622 90.4454 28.4519 95.3055 27.9714C97.1747 27.7845 97.7355 27.037 98.9105 25.8623C99.0173 25.7555 99.0173 25.6754 99.2309 25.7288C99.1508 26.3696 99.4178 27.1171 100.005 27.4374C100.753 27.8379 103.423 27.8379 104.331 27.7044C105.426 27.5442 105.934 26.9035 106.04 25.8089C106.094 25.0881 106.094 24.3139 106.12 23.5664C106.414 24.1804 106.788 24.741 107.215 25.275C110.019 28.6121 115.307 29.1728 118.271 25.7555C118.404 30.2139 114.933 31.095 111.194 30.0805C110.126 29.7868 109.004 28.9058 107.936 29.7334C107.162 30.3207 106.147 32.5099 106.735 33.4443C107.429 34.5656 111.541 35.3398 112.877 35.4466C119.633 35.9271 124.386 33.3642 124.813 26.2094C125.107 21.4573 124.653 16.3047 124.706 11.5259H124.813ZM115.36 22.9256C110.874 23.2193 110.74 15.6106 114.452 14.9699C119.285 14.1422 119.552 22.6319 115.36 22.9256Z" fill="#F1641E" />
            <path d="M50.2564 17.9332C49.6956 25.1682 55.4636 28.5053 62.0594 27.9714C63.5815 27.8379 67.6138 26.77 67.9342 25.0614C68.1478 23.9134 66.5456 21.7776 65.2638 21.8577C64.2491 21.9378 62.9139 22.8455 61.5253 22.9256C59.7896 23.0324 56.7988 22.3383 56.6385 20.2559H68.3081C68.6018 20.2559 69.3495 19.7219 69.483 19.4283C69.8836 18.4672 69.3762 16.278 69.0291 15.3169C65.9047 6.93399 51.0041 8.32224 50.2564 17.9332ZM56.6652 17.0789C56.9056 13.7151 63.0474 13.8219 63.4747 17.0789H56.6652Z" fill="#F1641E" />
            <path d="M49.8024 10.3246C49.0013 10.0042 45.5298 10.0576 44.702 10.3513C43.7941 10.6983 43.6071 11.4191 43.26 12.2468C42.2453 14.783 41.4975 17.5595 40.5629 20.1224C40.376 20.1224 40.3493 19.9355 40.2959 19.8021C39.201 17.426 38.6403 14.3024 37.5988 11.8463C37.2784 11.1255 37.0914 10.6449 36.2903 10.3513C35.4892 10.0576 32.7387 10.0309 31.8308 10.1644C30.4689 10.3513 30.1218 11.2056 30.5757 12.4603L36.9579 26.5831C37.8658 28.0248 40.2692 27.8646 41.818 27.7311C43.8208 27.5709 44.0344 26.7166 44.7821 25.1148C46.7582 21.0034 48.3604 16.7052 50.3632 12.5938C50.737 11.7128 50.7637 10.725 49.749 10.3246H49.8024Z" fill="#F1641E" />
            <path d="M27.6383 10.2178C26.8372 10.0843 24.6475 10.0576 23.8731 10.1911C23.2589 10.2979 22.6447 10.6716 22.4311 11.2857L22.351 26.156C22.4044 26.7967 22.8851 27.3307 23.4726 27.5443C24.1668 27.7845 26.7838 27.8112 27.5315 27.6777C28.4127 27.5176 29.0536 26.9836 29.107 26.0492V11.4459C28.84 10.6983 28.4127 10.3513 27.6383 10.2178Z" fill="#F1641E" />
            <path d="M26.0361 5.78603C26.1696 3.70365 24.3805 2.60906 23.6595 2.92943C21.0959 4.05071 22.084 7.14758 25.3952 8.8295C25.6088 8.93629 25.8759 8.93629 26.0895 8.8562C29.2406 7.54804 31.9109 5.22538 29.4542 3.32989C28.4127 2.52897 26.6236 3.94392 26.0628 5.78603H26.0361Z" fill="#F1641E" />
          </g>
          <defs>
            <clipPath id="clip0_2_5">
              <rect width="125" height="35" fill="white" transform="translate(0 0.5)" />
            </clipPath>
          </defs>
        </svg>
        <div class="about-product-content">
          Our products include T-shirts, mugs, ceramic cups, tote bags, wall art and decorative posters — all printed and shipped only after an order is placed. This helps reduce waste, supports environmental sustainability and enhances personalization. We are committed to continuous innovation, listening to our customers and staying up to date with global shopping trends to deliver the best shopping experience possible.
        </div>
      </div>
      <div class="about-grid-image">
        <img src="/images/givehug/about/5.webp" alt="Givehug">
      </div>
    </div>
  </div>
</template>

<style scoped>
.about-container {
  max-width: 1000px;
  margin: 0 auto;
  --bg-foreground: #f3f3f3;
  --bd-radius: 1.2rem;
}

.font-bold {
  font-weight: 600;
}

.about-intro {
  max-width: 800px;
  margin: 0 auto;
}

.about-title {
  font-size: 36px;
  font-weight: 400;
  line-height: 53px;
  text-align: center;
}

.about-logo-container {
  display: inline-block;
  margin: -17px 10px;
  height: 55px;
  width: 105px;
  border-radius: 9999px;
  overflow: hidden;
}

.about-logo-img {
  position: relative;
  bottom: 10px;
}

.about-subtitle {
  font-size: 16px;
  line-height: 1.5rem;
  text-align: center;
  max-width: 85%;
  margin: 24px auto 0;
  font-weight: 400;
}

.about-hero {
  margin-top: 64px;
  border-radius: var(--bd-radius);
  overflow: hidden;
  aspect-ratio: 20/9;
}

.about-hero img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.about-description {
  font-size: 16px;
  line-height: 1.5rem;
  text-align: center;
  max-width: 80%;
  @media (max-width: 420px) {
    max-width: 90%;
  }
  margin: 48px auto;
  font-weight: 400;
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

.about-grid-image {
  border-radius: var(--bd-radius);
  overflow: hidden;
  aspect-ratio: 1/1;
}

.about-img-cover {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.about-grid-text {
  border-radius: var(--bd-radius);
  overflow: hidden;
  background-color: var(--bg-foreground);
  padding: 36px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-svg-logo {
  margin: 0 auto;
}

.mt-5 {
  margin-top: 1.25rem;
}

.about-product-info {
  display: flex;
  align-items: center;
  justify-content: center;
  grid-column: span 2;
  background: var(--bg-foreground);
  border-radius: var(--bd-radius);
  width: 100%;
  height: 100%;
}

.about-product-content {
  max-width: 75%;
  line-height: 1.5;
}

.about-product-info svg {
  display: none;
}

/* Responsive styles */
@media (max-width: 1199px) {
  .about-grid {
    grid-template-columns: 1fr 1fr;
  }

  .about-grid-image:last-child {
    grid-column: span 2;
    aspect-ratio: 2/1;
  }

  .about-product-content {
    max-width: 80%;
    margin: 0 auto;
  }

  .about-product-info {
    grid-column: span 2;
    padding: 20px 0;
  }

  .about-product-info {
    display: block;
    text-align: center;
    background: transparent;
  }

  .about-product-info svg {
    display: block;
    margin: 0 auto 1.25rem;
  }

  .about-grid-text {
    display: none;
  }
}

@media (max-width: 991px) {
  .about-title {
    font-size: 30px;
    line-height: 45px;
  }

  .about-description {
    margin: 40px auto;
  }
}

@media (max-width: 767px) {
  .about-grid {
    grid-template-columns: 1fr;
  }

  .about-grid-image:last-child {
    grid-column: span 1;
    aspect-ratio: 1/1;
  }

  .about-subtitle {
    max-width: 95%;
  }

  .about-hero {
    margin-top: 40px;
  }

  .about-product-info {
    grid-column: span 1;
  }
}

@media (max-width: 575px) {
  .about-title {
    font-size: 24px;
    line-height: 36px;
  }

  .about-logo-container {
    display: block;
    margin: 10px auto;
  }

  .about-subtitle,
  .about-description {
    font-size: 14px;
  }

  .about-description {
    margin: 30px auto;
  }

  .about-grid-text {
    padding: 24px;
  }

  .about-product-content {
    max-width: 90%;
    font-size: 14px;
  }
}
</style>
