<script lang="ts" setup>
const fileSelector = ref()

const {
  optionsSelect,
  contactForm,
  submitted,
  invalids,
  isUploadingFiles,
  recaptcha,

  successForm,
  warningForm,
  errorMsg,

  handleSubmit,
  handleStateChange
} = useContactUsForm(fileSelector)

const { lastOrderUrl } = useLastOrder()
const localePath = useLocalePath()
</script>

<template>
  <main class="py-15 givehug-contact-us-background">
    <div class="cs-container max-w-[1000px]">
      <h1 class="text-center text-3xl font-semibold leading-normal mb-[6px]">
        {{ $t('Get in Touch') }}
      </h1>
      <div class="flex justify-center mb-11">
        <div class="max-w-[414px] text-center text-sm text-givehug-gray-muted">
          {{ $t("Contact us using the information below. We'll respond promptly to your inquiries and feedback") }}
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-[18px] mb-[27px] <md:text-center">
        <div class="p-6 border">
          <div class="mb-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="<md:mx-auto">
              <path d="M17.1816 3.5H6.68164C4.4725 3.5 2.68164 5.29086 2.68164 7.5V16.5C2.68164 18.7091 4.4725 20.5 6.68164 20.5H17.1816C19.3908 20.5 21.1816 18.7091 21.1816 16.5V7.5C21.1816 5.29086 19.3908 3.5 17.1816 3.5Z" stroke="black" stroke-width="1.8" />
              <path d="M2.72852 7.59L9.93352 11.72C10.5373 12.0704 11.223 12.2549 11.921 12.2549C12.6191 12.2549 13.3047 12.0704 13.9085 11.72L21.1335 7.59" stroke="black" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
          <div class="mb-2 font-semibold">
            {{ $t('Send us an email') }}
          </div>
          <div class="mb-2 text-sm text-givehug-gray-muted">
            {{ $t('Responding in 24-48 hours') }}
          </div>
          <a href="mailto:<EMAIL>" class="underline text-sm btn-text font-semibold">
            {{ $t('<EMAIL>') }}
          </a>
        </div>
        <div class="p-6 border">
          <div class="mb-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none" class="<md:mx-auto">
              <path d="M12.5 2C18.023 2 22.5 6.477 22.5 12C22.5 17.523 18.023 22 12.5 22H4.5C3.96957 22 3.46086 21.7893 3.08579 21.4142C2.71071 21.0391 2.5 20.5304 2.5 20V12C2.5 6.477 6.977 2 12.5 2ZM12.5 4C10.3783 4 8.34344 4.84285 6.84315 6.34315C5.34285 7.84344 4.5 9.87827 4.5 12V20H12.5C14.6217 20 16.6566 19.1571 18.1569 17.6569C19.6571 16.1566 20.5 14.1217 20.5 12C20.5 9.87827 19.6571 7.84344 18.1569 6.34315C16.6566 4.84285 14.6217 4 12.5 4ZM12.5 14C12.7549 14.0003 13 14.0979 13.1854 14.2728C13.3707 14.4478 13.4822 14.687 13.4972 14.9414C13.5121 15.1958 13.4293 15.4464 13.2657 15.6418C13.1021 15.8373 12.8701 15.9629 12.617 15.993L12.5 16H9.5C9.24512 15.9997 8.99997 15.9021 8.81463 15.7272C8.6293 15.5522 8.51777 15.313 8.50283 15.0586C8.48789 14.8042 8.57067 14.5536 8.73426 14.3582C8.89785 14.1627 9.1299 14.0371 9.383 14.007L9.5 14H12.5ZM15.5 10C15.7652 10 16.0196 10.1054 16.2071 10.2929C16.3946 10.4804 16.5 10.7348 16.5 11C16.5 11.2652 16.3946 11.5196 16.2071 11.7071C16.0196 11.8946 15.7652 12 15.5 12H9.5C9.23478 12 8.98043 11.8946 8.79289 11.7071C8.60536 11.5196 8.5 11.2652 8.5 11C8.5 10.7348 8.60536 10.4804 8.79289 10.2929C8.98043 10.1054 9.23478 10 9.5 10H15.5Z" fill="black" />
            </svg>
          </div>
          <div class="mb-2 font-semibold">
            {{ $t('Chat with us') }}
          </div>
          <div class="mb-2 text-sm text-givehug-gray-muted">
            {{ $t('Speak to our friendly team') }}
          </div>
          <button class="underline text-sm btn-text font-semibold" @click="$openCrispChat()">
            {{ $t('Livechat') }}
          </button>
        </div>
        <div class="p-6 border">
          <div class="mb-8">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="<md:mx-auto">
              <path d="M12 1C18.0753 1 23 5.9247 23 12C23 18.0753 18.0753 23 12 23C5.9247 23 1 18.0753 1 12C1 5.9247 5.9247 1 12 1ZM12 3.2C9.66609 3.2 7.42778 4.12714 5.77746 5.77746C4.12714 7.42778 3.2 9.66609 3.2 12C3.2 14.3339 4.12714 16.5722 5.77746 18.2225C7.42778 19.8729 9.66609 20.8 12 20.8C14.3339 20.8 16.5722 19.8729 18.2225 18.2225C19.8729 16.5722 20.8 14.3339 20.8 12C20.8 9.66609 19.8729 7.42778 18.2225 5.77746C16.5722 4.12714 14.3339 3.2 12 3.2ZM12 16.4C12.2917 16.4 12.5715 16.5159 12.7778 16.7222C12.9841 16.9285 13.1 17.2083 13.1 17.5C13.1 17.7917 12.9841 18.0715 12.7778 18.2778C12.5715 18.4841 12.2917 18.6 12 18.6C11.7083 18.6 11.4285 18.4841 11.2222 18.2778C11.0159 18.0715 10.9 17.7917 10.9 17.5C10.9 17.2083 11.0159 16.9285 11.2222 16.7222C11.4285 16.5159 11.7083 16.4 12 16.4ZM12 5.95C12.9265 5.95003 13.8241 6.27269 14.5386 6.86256C15.253 7.45243 15.7398 8.27269 15.9152 9.18244C16.0907 10.0922 15.9438 11.0346 15.5 11.8479C15.0561 12.6611 14.3429 13.2944 13.4828 13.639C13.3554 13.6858 13.2406 13.7615 13.1473 13.8601C13.0989 13.9151 13.0912 13.9855 13.0923 14.0581L13.1 14.2C13.0997 14.4804 12.9923 14.75 12.7999 14.9539C12.6074 15.1578 12.3444 15.2805 12.0645 15.2969C11.7846 15.3133 11.509 15.2223 11.294 15.0423C11.079 14.8624 10.9408 14.6071 10.9077 14.3287L10.9 14.2V13.925C10.9 12.6567 11.923 11.8955 12.6644 11.5974C12.9661 11.4769 13.2294 11.2765 13.4258 11.0177C13.6222 10.7589 13.7445 10.4514 13.7794 10.1284C13.8142 9.80538 13.7605 9.47894 13.6238 9.18415C13.4872 8.88937 13.2728 8.63737 13.0038 8.45522C12.7347 8.27308 12.4211 8.16766 12.0967 8.1503C11.7722 8.13293 11.4492 8.20427 11.1622 8.35666C10.8753 8.50905 10.6352 8.73672 10.4679 9.01524C10.3006 9.29375 10.2123 9.61259 10.2125 9.9375C10.2125 10.2292 10.0966 10.509 9.89032 10.7153C9.68403 10.9216 9.40424 11.0375 9.1125 11.0375C8.82076 11.0375 8.54097 10.9216 8.33468 10.7153C8.12839 10.509 8.0125 10.2292 8.0125 9.9375C8.0125 8.87995 8.43261 7.86571 9.18041 7.11791C9.92821 6.37011 10.9424 5.95 12 5.95Z" fill="black" />
            </svg>
          </div>
          <div class="mb-2 font-semibold">
            {{ $t('Documentation') }}
          </div>
          <div class="mb-2 text-sm text-givehug-gray-muted">
            {{ $t('FAQs and guides.') }}
          </div>
          <nuxt-link :to="localePath('/page/faq')" class="underline text-sm btn-text font-semibold">
            {{ $t('Go to documentation') }}
          </nuxt-link>
        </div>
      </div>

      <div class="page-contact border p-12">
        <div class="text-xl font-semibold text-center">
          {{ $t("We'd love to help") }}
        </div>
        <div class="flex justify-center mb-9">
          <div class="max-w-[444px] text-center text-sm text-givehug-gray-muted">
            {{ $t("We're a full service agency with experts ready to help. We'll get in touch within 24 hours.") }}
          </div>
        </div>
        <givehug-common-input
          id="name"
          v-model:model-value="contactForm.fullName"
          autocomplete="name"
          :label="$t('Fullname')"
          :placeholder="$t('Your fullname')"
          required
          autofocus
        />

        <!-- Email -->
        <givehug-common-input
          id="email"
          v-model:model-value="contactForm.email"
          :label="$t('Email')"
          required
          :state="submitted && (invalids.email_required || invalids.email_invalid) ? false : undefined"
          :message="invalids.email_required ? $t('Email is required') : invalids.email_invalid ? $t('Email is invalid') : undefined"
          type="email"
          :placeholder="$t('Enter email')"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          class="mt-4"
        />
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <div>
            <label
              class="text-sm font-semibold mb-1 block"
            >
              <span>{{ $t('Subject') }}</span>
            </label>
            <common-dropdown
              dropdown-id="contactFormTypeDropdown"
              class="!block"
              dropdown-class="w-full"
              btn-class="w-full text-left h-[45px] bg-givehug-gray-with-hover border border-givehug-gray px-5 flex items-center gap-5 text-sm shadow-none focus:border-primary"
            >
              <span class="px-2 float-left" :class="contactForm.type ? '' : 'text-gray-400'">{{ contactForm.type || 'Subject' }}</span>
              <template #content>
                <div
                  v-for="msg in optionsSelect"
                  :key="msg"
                  sp-action="contactFormType"
                  class="capitalize py-1 px-4 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center cursor-pointer"
                  @click="contactForm.type = msg"
                >
                  {{ $t(msg) }}
                </div>
              </template>
            </common-dropdown>
          </div>
          <div class="relative">
            <givehug-common-input
              id="order"
              v-model:model-value="contactForm.order"
              autocomplete="order"
              :label="$t('Order number')"
              :placeholder="$t('Enter order number')"
              required
            />
            <button class="absolute right-0 top-0 btn-text text-xs flex gap-1 items-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                <path d="M14.875 3.5H6.125C5.1585 3.5 4.375 4.2835 4.375 5.25V16.625C4.375 17.5915 5.1585 18.375 6.125 18.375H14.875C15.8415 18.375 16.625 17.5915 16.625 16.625V5.25C16.625 4.2835 15.8415 3.5 14.875 3.5Z" stroke="currentColor" stroke-width="1.5" />
                <path d="M7.875 7.875H13.125M7.875 11.375H13.125M7.875 14.875H11.375" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" />
              </svg>
              <a :href="localePath('/order/track')" target="_blank" rel="noopener noreferrer" v-text="$t('Track your order')" />
            </button>
          </div>
        </div>

        <div class="mt-4">
          <p class="text-sm font-semibold mb-1 block">
            <span>{{ $t('File Attachment') }}</span>
            <span> ({{ $t('Upload limit is 5 files.') }})</span>
          </p>
          <givehug-common-file-selector ref="fileSelector" v-model="contactForm.attachFile" :limit="5" @on-state-change="handleStateChange" />
          <small class="text-[13px]">{{ $t('contact_us_text-3') }}</small>
        </div>

        <div class="mt-4">
          <label class="text-sm font-semibold mb-1 block">
            {{ $t('Your message') }}
          </label>
          <textarea
            id="message"
            v-model="contactForm.message"

            class="border border-givehug-gray bg-givehug-gray-with-hover p-5 w-full resize-none text-sm overflow-y-scroll focus:border-primary focus-visible:outline-none"
            rows="5"
            :class="`${(submitted && (invalids.message_length || invalids.message_required)) ? 'border-red-600' : 'border-gray-400'}`"
            :placeholder="$t('Your message')"
          />
          <div v-if="submitted" id="email-feedback">
            <span v-if="invalids.message_required" class="text-red-600 text-sm">{{ $t('Message is required') }}</span>
            <span v-else-if="invalids.message_length" class="text-red-600 text-sm">{{ $t('Message must have at least 10 characters') }}</span>
          </div>
        </div>

        <div class="center-flex mt-9">
          <common-recaptcha ref="recaptcha" />
        </div>

        <button
          class="mt-9 w-full h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-full"
          type="button"
          :disabled="isUploadingFiles"
          @click="handleSubmit"
        >
          {{ $t('Send over') }}
        </button>

        <div v-if="!(successForm || warningForm)" class="mt-2 text-center">
          <p><span>{{ $t('Please do not send multiple requests on the same issue.') }}</span></p>
        </div>

        <div v-if="successForm" class="mt-2 text-secondary">
          <strong>{{ $t('Success') }}!</strong> {{ $t('You will receive a reply from us within 1 business day') }}.
        </div>
        <div v-else-if="warningForm" class="mt-2 text-[#856404]">
          <strong>{{ $t('Error') }}!</strong> {{ errorMsg }}
        </div>
      </div>
    </div>
  </main>
</template>

<style scoped>
.givehug-contact-us-background {
  position: relative;
  /* các style khác */
}

.givehug-contact-us-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 229px;
  background: linear-gradient(180deg, rgba(var(--color-primary-rgb), 0.15) 0%, rgba(var(--color-primary-rgb), 0) 100%);
  pointer-events: none; /* không ảnh hưởng tới sự kiện chuột */
}
</style>
