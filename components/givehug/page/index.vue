<script lang="ts" setup>
const { slug } = defineProps({
  slug: {
    default: () => {
      return ''
    },
    type: String
  }
})
const pageData: Ref<PageData> = ref({
  id: 0,
  title: '',
  slug: '',
  content: ''
})

if (slug) {
  pageData.value = (await useCommonFooterPage(slug)).pageData.value
}

const needReverseResetCss = computed(() => {
  const whiteList = ['about']
  return !whiteList.includes(slug)
})

onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
})

const computedClass = computed(() => {
  let className = slug
  if (needReverseResetCss.value) {
    className = 'reverse-reset-css'
  }

  if (slug === 'terms-of-service') {
    className += ' terms-of-service'
  }
  return className
})
</script>

<template>
  <givehug-page-contact-us-form v-if="slug === 'contact-us' && storeInfo().enable_contract_form" />
  <main v-else class="page-container py-16 md:py-25">
    <div class="grid grid-cols-12 gap-1">
      <div :class="`col-span-12 ${(slug === 'contact-us') ? (storeInfo().enable_contract_form ? 'lg:col-span-7' : 'lg:col-span-12') : null}`">
        <h1 v-if="slug === 'about'" class="text-3xl mb-3 font-semibold text-center">
          {{ pageData.title }}
        </h1>
        <div v-else class="mb-12 -my-4">
          <h1 class="text-3xl font-semibold text-primary">
            {{ pageData.title }}
          </h1>
          <div class="text-muted">
            Lasted update 13 May 2025
          </div>
        </div>
        <LazyGivehugPageAbout v-if="slug === 'about'" />
        <div
          v-else
          :class="computedClass"
          v-html="pageData.content"
        />
      </div>
    </div>
  </main>
</template>

<style lang="scss">
.page-container {
  max-width: 1000px;
  margin: 0 auto;
  padding-left: 16px;
  padding-right: 16px;
}

#spemail {
  margin-left: 0.25rem;
}

span#spemail {
  display: inline-block;
}

span#spemail span {
  float: right;
  margin-left: 3px;
}

.reverse-reset-css {
  * {
    color: #000000 !important;
  }

  & h2:first-child {
    display: none;
  }

  h2 {
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.75rem;
    margin-bottom: 0.5rem;
  }

  strong {
    font-weight: 600;
  }

  u {
    text-decoration: none;
  }

  p:has(> u) {
    font-weight: 600;
  }

  ul,
  ol {
    padding-inline-start: 25px;
  }
  ul {
    list-style: disc;
  }
  ol {
    list-style: decimal;
  }
  li::marker {
    font-size: 0.8em;
  }

  li {
    padding-left: 0;
    text-indent: -3px;
  }

  a span {
    color: var(--color-primary) !important;
    transition: all 0.2s ease-in-out;
  }
  a:hover {
    opacity: 0.9;
  }

  a {
    color: var(--color-primary) !important;
  }

  h3:first-child {
    display: none;
  }
}
</style>
