<script lang="ts" setup>
defineProps({
  order: {
    required: true,
    type: Object as PropType<Order>
  }
})
</script>

<template>
  <div class="flex flex-col gap-3">
    <div class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted"> {{ $t('Subtotal') }}</span>
      <span data-test-id="summary-charge-subtotal" class="font-semibold">{{ $formatPriceByCurrency(order.total_product_amount, order.currency_rate, order.currency_code) }}  </span>
    </div>
    <div v-if="order.total_shipping_amount" class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted"> {{ $t('Shipping') }} </span>
      <span data-test-id="summary-charge-shipping" class="font-semibold">{{ $formatPriceByCurrency(order.total_shipping_amount, order.currency_rate, order.currency_code) }} </span>
    </div>
    <div v-if="order.insurance_fee" class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted">{{ $t('Shipping insurance') }}</span>
      <span data-test-id="summary-charge-insurance" class="font-semibold">{{ $formatPriceByCurrency(order.insurance_fee, order.currency_rate, order.currency_code) }} </span>
    </div>
    <div v-if="order.total_discount" class="flex justify-between text-sm text-secondary">
      <div>
        <span>
          {{ $t('Discount') }}
        </span>
        <span v-if="order.discount_code" class="uppercase bg-green-100 px-1 rounded ml-2">{{ order.discount_code }}</span>
      </div>
      <span class="font-semibold">- {{ $formatPriceByCurrency(order.total_discount, order.currency_rate, order.currency_code) }}</span>
    </div>
    <div v-if="order.payment_discount" class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted">{{ $t('Payment discount') }}</span>
      <span data-test-id="summary-charge-discount" class="font-semibold">{{ $formatPriceByCurrency(order.payment_discount, order.currency_rate, order.currency_code) }}</span>
    </div>
    <div v-if="order.total_tax_amount" class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted">{{ $t('Tax') }}</span>
      <span data-test-id="summary-charge-tax" class="font-semibold">{{ $formatPriceByCurrency(order.total_tax_amount, order.currency_rate, order.currency_code) }}</span>
    </div>
    <div v-if="order.tip_amount" class="flex justify-between text-sm">
      <span class="text-givehug-gray-muted">{{ $t('Tip') }}</span>
      <span data-test-id="summary-charge-tip" class="font-semibold">{{ $formatPriceByCurrency(order.tip_amount, order.currency_rate, order.currency_code) }}</span>
    </div>
    <div class="flex justify-between text-sm font-semibold">
      <span>{{ $t('Total') }}</span>
      <span data-test-id="summary-charge-total" class="text-primary">{{ $formatPriceByCurrency(order.total_amount, order.currency_rate, order.currency_code) }}</span>
    </div>
    <div v-if="order.currency_code !== 'USD' && order.currency_code !== 'EUR' && order.country !== 'GB'" class="text-center rounded-[10px] text-[#0c5460] text-sm py-2 bg-[#d1ecf1] mt-3">
      {{ $t('You will be charged in USD') }} <span class="font-weight-semibold">&nbsp;${{ Number(order.total_amount.toFixed(2)) }}</span>
    </div>
  </div>
</template>

<style>
</style>
