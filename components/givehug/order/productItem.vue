<script lang="ts" setup>
const props = defineProps({
  convertCurrencyRate: {
    default: 1,
    type: Number
  },
  convertCurrencyCode: {
    default: undefined,
    type: String as PropType<CurrencyCode>
  },
  product: {
    default: undefined,
    type: Object as PropType<OrderProduct>
  },
  orderStatus: {
    default: '',
    type: String
  },
  pos: {
    default: 0,
    type: Number
  },
  customerName: {
    default: '',
    type: String
  },
  page: {
    default: '',
    type: String
  },
  showImg: {
    default: true,
    type: Boolean
  }
})
const localePath = useLocalePath()
const options = JSON.parse(props.product?.options as string)
</script>

<template>
  <div
    class="py-2 flex products-center gap-3"
    :class="{ 'bg-[#fff6e6]': product?.fulfill_status === 'no_ship' }"
  >
    <common-image
      v-if="showImg"
      img-class="!w-25 min-w-25 aspect-square rounded-[10px]"
      :image="{
        path: product?.thumb_url,
        type: 'list',
        color: options?.color
      }"
      :alt="product?.campaign_title"
    />
    <div class="flex-grow relative flex flex-col justify-between overflow-hidden">
      <div>
        <GivehugSharedItemTitle
          :product-url="product?.product_url || ''"
          :campaign-title="product?.campaign_title || ''"
          :product-name="product?.product_name || ''"
          :option-list="JSON.parse(product?.options || '') || {}"
          :current-options="JSON.parse(product?.options || '') as Record<string, string> || {}"
        />
        <template v-if="product?.custom_options">
          <div v-if="product?.personalized === 3 || product?.full_printed === 5" class="col-12 text-sm">
            <ul v-for="(v, i) in JSON.parse(product?.custom_options)" :key="i" class="pl-2 mb-2 list-disc">
              <template v-for="(vC, iC) in v">
                <li v-if="vC" :key="iC" class="capitalize" style="font-size: 13px;">
                  <div>
                    {{ $t(vC.label) }}:
                    <a v-if="vC.type === 'image' && vC.value" :href="$imgUrl({ path: vC.value, type: 'full' })" target="_blank" class="btn-text text-overflow-hidden" @click.prevent="uiManager().viewImage(vC.value)">{{ $t('View image') }}</a>
                    <span v-else>{{ vC.value }}</span>
                  </div>
                </li>
              </template>
            </ul>
          </div>
          <div v-else-if="product?.personalized === 1" class="col-12 px-0 text-sm">
            <div v-for="(value, key) in JSON.parse(product?.custom_options)" :key="key" class="text-capitalize text-overflow-hidden">
              <span>{{ key.replace(/_/g, ' ') }}</span>: <span>{{ value }}</span>
            </div>
          </div>
        </template>
        <div v-if="product?.fulfill_status === 'no_ship'" class="text-sm text-[#ffc107]">
          <i class="icon-sen-alert mr-2" />
          <span>{{ $t("This product variant is not available. Please try other option/product.") }}</span>
          <span
            class="underline cursor-pointer text-red-500 float-right px-1"
            @click="$emit('removeProduct')"
          ><i class="icon-sen-delete" /></span>
          <nuxt-link
            :to="localePath('/cart')"
            :title="$t('Edit')"
          >
            <span class="underline cursor-pointer text-primary float-right px-1">{{ $t('Edit') }}</span>
          </nuxt-link>
        </div>
      </div>

      <div class="mb-1 font-semibold text-sm flex justify-between">
        <span>
          {{ product?.quantity }}x
        </span>
        <span class="ml-3">{{ $formatPriceByCurrency(product?.total_amount, convertCurrencyRate, convertCurrencyCode) }}</span>
      </div>
      <lazy-default-product-review-modal-review
        v-if="page !== 'checkout'"
        :order-status="orderStatus"
        :pos="pos"
        :item="product"
        :customer-name="customerName"
      />
    </div>
  </div>
</template>

<style>
</style>
