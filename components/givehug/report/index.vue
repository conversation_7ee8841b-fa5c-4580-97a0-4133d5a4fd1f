<script lang="ts" setup>
const countryFilter = ref()
const emailConfirm = ref()
const countryDropdownLabel = ref()
const {
  tabsList,
  selectedTab,
  campaignSlug,
  isFileUploading,

  userCountry,
  currentCountry,
  filterCountryText,
  filterCountryArray,
  formIntellectual,
  formPolicy,

  handleStateChange,
  submit,

  errors
} = useCampaignReportPage(emailConfirm, countryDropdownLabel)
</script>

<template>
  <main class="cs-container max-w-[1000px] border rounded-[20px] my-15 px-[66px] pt-10 pb-12" dusk="campaign-report-page">
    <div class="flex justify-center mb-14">
      <div class="bg-givehug-gray flex gap-3 p-2 rounded-[10px]">
        <button
          v-for="(item, index) in tabsList"
          :key="index"
          class="trans__time h-[45px] font-medium px-5 !shadow-none rounded-[10px]"
          :class="{
            'bg-black hover:bg-gray-800 text-white': selectedTab === item.value,
            'hover:bg-white text-black': selectedTab !== item.value
          }"
          @click="selectedTab = item.value"
        >
          {{ $t(item.name) }}
        </button>
      </div>
    </div>

    <form v-if="selectedTab === 'intellectual'" class="mt-4" @submit.prevent="submit">
      <givehug-common-input
        id="campaignUrl"
        v-model:model-value="campaignSlug"
        required
        class="mb-9"
        :placeholder="$t('Enter campaign URL')"
        :label="$t('Campaign URL')"
      />
      <h4 class="text-xl font-medium mb-6">
        {{ $t('Your Contact Information') }}
      </h4>

      <div class="grid grid-cols-2 gap-4 mb-9">
        <givehug-common-input
          id="first-name"
          v-model:model-value="formIntellectual.firstName"
          autocomplete="given-name"
          required
          :placeholder="$t('Enter first name')"
          class="col-span-2 md:col-span-1"
          :label="$t('First Name')"
        />

        <givehug-common-input
          id="last-name"
          v-model:model-value="formIntellectual.lastName"
          autocomplete="family-name"
          required
          :placeholder="$t('Enter last name')"
          class="col-span-2 md:col-span-1"
          :label="$t('Last Name')"
        />

        <givehug-common-input
          id="email"
          v-model:model-value="formIntellectual.email"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          autocomplete="email"
          required
          :placeholder="$t('Enter email')"
          :state="errors.unmatchedEmail ? false : undefined"
          class="col-span-2 md:col-span-1"
          :label="$t('Email')"
        />

        <givehug-common-input
          id="email-confirm"
          v-model:model-value="formIntellectual.emailConfirm"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          autocomplete="email"
          required
          :placeholder="$t('Confirm email')"
          :state="errors.unmatchedEmail ? false : undefined"
          :message="errors.unmatchedEmail ? $t('Email does not match') : undefined"
          class="col-span-2 md:col-span-1"
          :label="$t('Confirm email')"
        />

        <givehug-common-input
          id="phone"
          v-model:model-value="formIntellectual.phone"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          autocomplete="tel"
          required
          :placeholder="$t('Enter phone number')"
          class="col-span-2 md:col-span-1"
          :label="$t('Phone')"
        />

        <givehug-common-input
          id="address1"
          v-model:model-value="formIntellectual.address1"
          autocomplete="address-line1"
          required
          :placeholder="$t('Enter address (House number and Street name)')"
          class="col-span-2 md:col-span-1"
          :label="$t('Address')"
        />

        <givehug-common-input
          id="address2"
          v-model:model-value="formIntellectual.address2"
          autocomplete="address-line2"
          required
          :placeholder="$t('Enter address details')"
          class="col-span-2 md:col-span-1"
          :label="$t('Address Line 2')"
        />

        <givehug-common-input
          id="state"
          v-model:model-value="formIntellectual.state"
          autocomplete="state"
          required
          :placeholder="$t('Enter state')"
          class="col-span-2 md:col-span-1"
          :label="$t('State')"
        />

        <div class="col-span-2">
          <label
            class="text-sm font-semibold mb-1 block"
          >
            <span>{{ $t('Country') }}</span>
            <span class="text-red-500">*</span>
          </label>
          <common-dropdown
            dropdown-id="countryDropdown"
            btn-class="w-full text-left h-[45px] bg-givehug-gray-with-hover border border-givehug-gray px-5 flex items-center gap-5 text-sm font-medium"
            dropdown-class="max-w-[80vw] w-full pt-20"
            show-data-class=""
            :close-on-click="false"
            @shown="countryFilter.focus()"
          >
            <div class="h-[25px] w-[25px] rounded-full overflow-hidden">
              <img
                class="country-flag object-cover h-full w-full"
                :alt="`flag-${currentCountry?.code.toLowerCase()}`"
                :src="`${cdnURL}images/country-flag/${currentCountry?.code.toLowerCase()}.svg`"
              >
            </div>
            <span>
              {{ currentCountry?.name }}
            </span>
            <template #content>
              <givehug-common-input
                id="countryFilter"
                ref="countryFilter"
                v-model="filterCountryText"
                :label="$t('Find your country')"
                class="!absolute top-0 w-full px-5 py-1"
              />
              <template v-if="filterCountryArray?.length">
                <div
                  v-for="(country, index) in filterCountryArray"
                  :key="index"
                  class="btn-text py-1 px-3"
                  :class="{
                    'bg-primary text-contrast': country.code === currentCountry?.code
                  }"
                  tabindex="0"
                  @click="userCountry = country.code; "
                  @keyup.space="userCountry = country.code"
                  @keyup.enter="userCountry = country.code"
                >
                  <span class="vti__flag inline-block" :class="country.code.toLowerCase()" />
                  <span class="ml-2">
                    {{ country.name }}
                  </span>
                </div>
              </template>
              <div v-else class="text-center">
                {{ $t('Sorry, no matching country') }}
              </div>
            </template>
          </common-dropdown>
        </div>
      </div>

      <h4 class="text-xl font-medium mb-6">
        {{ $t('IP Claim Details') }}
      </h4>

      <div class="flex justify-between mb-6">
        <p class="font-medium text-sm">
          {{ $t('Are you the Rights Owner or an Agent') }}?
        </p>
        <div class="flex gap-9">
          <div class="col-span-2 md:col-span-1">
            <input id="userType1" v-model="formIntellectual.userType" type="radio" name="user-type" value="1" style="accent-color: var(--color-primary);">
            <label for="userType1" class="ml-2 text-sm">{{ $t('Right Owner') }}</label>
          </div>
          <div class="col-span-2 md:col-span-1">
            <input id="userType2" v-model="formIntellectual.userType" type="radio" name="user-type" value="2" style="accent-color:  var(--color-primary);">
            <label for="userType2" class="ml-2 text-sm">{{ $t('Agent') }}</label>
          </div>
        </div>
      </div>

      <givehug-common-input
        id="legal-name"
        v-model:model-value="formIntellectual.legalName"
        required
        class="mb-4"
        :placeholder="$t('Enter Ip')"
        :label="$t('IP Owner (Legal Name)')"
      />

      <givehug-common-textarea
        id="reason"
        v-model:model-value="formIntellectual.additionalInfo.specificConcern"
        required
        class="mb-4"
        :placeholder="$t('Enter reason')"
        :label="$t('The specific concern is')"
      />

      <givehug-common-textarea
        id="original"
        v-model:model-value="formIntellectual.additionalInfo.originalWork"
        required
        class="mb-4"
        :placeholder="$t('Enter original url(s) to work')"
        :label="$t('URL(s) to original work')"
      />

      <givehug-common-textarea
        id="original"
        v-model:model-value="formIntellectual.additionalInfo.otherInfo"
        class="mb-4"
        :placeholder="$t('Enter additional information')"
        :label="$t('Additional information')"
      />

      <div class="mb-4">
        <p class="text-sm font-semibold mb-1 block">
          <span class="font-medium">{{ $t('File Attachment') }}</span>
          <span> ({{ $t('Upload limit is 5 files.') }})</span>
        </p>
        <givehug-common-file-selector v-model="formIntellectual.attachFile" :limit="5" @on-state-change="handleStateChange" />
        <p class="text-[13px] leading-normal mb-0 mt-[5px]">
          {{ $t('fileUpload-text-1') }}.
        </p>
        <p class="text-[13px] leading-normal">
          {{ $t('fileUpload-text-2') }}.
        </p>
      </div>

      <h4 class="text-xl font-medium mb-4">
        {{ $t('Statements') }}
      </h4>
      <p class="text-[13px] leading-normal mb-0 mt-[5px]">
        {{ $t('Statements-text-1') }}.
      </p>
      <p class="text-[13px] leading-normal mb-0 mt-[5px]">
        {{ $t('Statements-text-2') }}.
      </p>

      <GivehugCommonCheckbox1 v-model:value="formIntellectual.acceptStatement" class="mt-4" :label="$t('Statements-text-3')" />

      <button type="submit" class="mt-6 w-full h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-full" :disabled="!formIntellectual.acceptStatement || isFileUploading">
        {{ $t('Submit Claim') }}
      </button>
    </form>
    <form v-if="selectedTab === 'policy'" @submit.prevent="submit">
      <givehug-common-input
        id="campaignUrl"
        v-model:model-value="campaignSlug"
        required
        class="mb-6"
        :placeholder="$t('Enter campaign URL')"
        :label="$t('Campaign URL')"
      />
      <div class="mt-4 flex flex-col gap-4">
        <GivehugCommonCheckbox1 id="hate-speech" v-model:value="formPolicy.hate" :label="$t('policy-text-1')" />
        <GivehugCommonCheckbox1 id="sexual" v-model:value="formPolicy.sexual" :label="$t('policy-text-2')" />
        <GivehugCommonCheckbox1 id="advocates-violence" v-model:value="formPolicy.violence" :label="$t('policy-text-3')" />
        <GivehugCommonCheckbox1 id="misleading" v-model:value="formPolicy.misleading" :label="$t('policy-text-4')" />
        <GivehugCommonCheckbox1 id="advocates-illegal" v-model:value="formPolicy.illegal" :label="$t('policy-text-5')" />
      </div>
      <button type="submit" class="mt-6 w-full h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-full">
        {{ $t('Report') }}
      </button>
    </form>
  </main>
</template>

<style scoped>
input[type='text'],
textarea {
  padding: 0.375rem 0.75rem;
}
</style>
