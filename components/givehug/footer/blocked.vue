<script lang="ts" setup>
const localePath = useLocalePath()

const {
  storeContact,
  logo_url: logoUrl,
  name: storeName
} = storeInfo()

const menu = {
  menu1: {
    title: 'Info & support',
    menuData: [{
      name: 'Track order',
      url: '/order/track'
    }, {
      name: 'Contact support',
      url: '/page/contact-us'
    }]
  }
}

const {
  contactPhoneList,
  currentContactPhoneIndex,
  enableContactForm,
  canHasExtraInfo,
  encode,
  decode
} = useFooter()
</script>

<template>
  <footer id="footer">
    <div class="container flex flex-wrap text-center lg:text-left mb-10">
      <div class="w-full lg:w-[40%] lg:order-2 flex flex-wrap text-center lg:text-left mt-10 ml-auto">
        <div class="pt-3 hidden">
          <nuxt-link exact :to="localePath('/bot')">
            {{ $t('Bot') }}
          </nuxt-link>
        </div>
        <div v-for="menuList in menu" :key="menuList.title" class="w-full md:w-1/2 <md:my-5">
          <p class="uppercase font-semibold">
            {{ $t(menuList.title) }}
          </p>
          <div
            v-for="(item, index) in menuList.menuData"
            :key="index"
            class="pt-3 capitalize"
          >
            <nuxt-link exact :to="localePath(item.url)" class="btn-text">
              {{ $t(item.name) }}
            </nuxt-link>
          </div>
        </div>
      </div>
      <div class="w-full md:w-1/2 lg:w-[30%] lg:order-1 text-sm mt-10">
        <div class="flex <lg:justify-center my-5">
          <nuxt-link :to="localePath('/')">
            <common-image
              v-if="logoUrl"
              :image="{
                path: logoUrl,
                type: 'logo'
              }"
              img-id="footerLogo"
              :alt="storeName"
              img-class="h-10 -z-1"
            />
            <div v-else>
              <span class="text-2xl md:text-3xl font-semibold">{{ storeName }}</span>
            </div>
          </nuxt-link>
        </div>
        <p v-if="canHasExtraInfo && storeContact.phone" class="font-medium mt-3">
          {{ storeName }}
        </p>
        <p v-if="canHasExtraInfo && storeContact.email_info">
          <span>{{ $t('Email') }}: </span>
          <span class="ml-2">{{ storeContact.email_info }}</span>
        </p>
        <p v-if="storeContact.address" class="mt-2">
          <span>{{ $t('Address') }}: </span>
          <span class="ml-2">{{ storeContact.address }}</span>
        </p>
        <div class="flex <lg:justify-center items-center">
          <span>{{ $t('Phone number') }}: </span>
          <span v-if="canHasExtraInfo && storeContact.phone" class="ml-2">
            {{ decode(encode(storeContact.phone)) }}
          </span>
          <common-dropdown
            v-else
            class="ml-0.5"
            dropdown-id="contactPhoneDropdown"
            btn-class="px-3 py-1"
          >
            <div class="flex items-center">
              <img
                v-for="(flag, flagIndex) in contactPhoneList[currentContactPhoneIndex].flags"
                :key="flagIndex"
                :src="`${cdnURL}${flag}`"
                alt="flag"
                class="mr-1 h-4"
                loading="lazy"
              >
              <span>{{ decode(encode(contactPhoneList[currentContactPhoneIndex].phone)) }}</span>
            </div>
            <template #content>
              <li
                v-for="(contactPhone, index) in contactPhoneList"
                :key="index"
                class="btn-text flex items-center px-4 min-w-full w-max py-1"
                :class="{ 'bg-primary !text-contrast': index === currentContactPhoneIndex }"
                @click="currentContactPhoneIndex = index"
              >
                <img
                  v-for="(flag, flagIndex) in contactPhone.flags"
                  :key="flagIndex"
                  :src="`${cdnURL}${flag}`"
                  alt="flag"
                  class="mr-1 h-4"
                  loading="lazy"
                >
                <span>{{ decode(encode(contactPhone.phone)) }}</span>
              </li>
            </template>
          </common-dropdown>
        </div>
        <div v-if="enableContactForm">
          <span>
            {{ $t('Need support?') }}
            <nuxt-link class="text-primary hover:text-primary-hover" :to="localePath('/page/contact-us')">{{ $t('Submit a ticket') }}</nuxt-link>
          </span>
        </div>
      </div>
    </div>
  </footer>
</template>
