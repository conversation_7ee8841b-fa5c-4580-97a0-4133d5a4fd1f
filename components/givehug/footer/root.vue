<script lang="ts" setup>
import CommonCurrencySelect from '~/components/givehug/common/currencySelect.client.vue'
import CommonLanguageSelect from '~/components/givehug/common/languageSelect.client.vue'
import Subscribe from '~/components/givehug/home/<USER>'
import ShopInfo from './shopInfo.vue'

const localePath = useLocalePath()

const {
  copyrightText
} = useFooter()

function getFooters() {
  const { t } = useI18n()
  const copiedFooter = _cloneDeep($menuFooter)

  const { footerMenu } = storeInfo()

  for (const item of footerMenu) {
    if (!item.url) { continue }

    // Check if item.url exists in menu URL mapping
    const urlKey = item.url as keyof typeof $menuFooterUrlsGiveHug
    if (Object.prototype.hasOwnProperty.call($menuFooterUrlsGiveHug, urlKey)) {
      const indexedFooterItem = $menuFooterUrlsGiveHug[urlKey]
      const menuKey = indexedFooterItem[0] as keyof typeof copiedFooter
      const menuIndex = indexedFooterItem[1] as number

      if (menuKey in copiedFooter
        && Array.isArray(copiedFooter[menuKey].menuData)
        && item.name) {
        copiedFooter[menuKey].menuData[menuIndex] = {
          name: item.name,
          url: item.url
        }
      }
      continue
    }

    if (item.name) {
      copiedFooter.menu1.menuData.push({
        name: item.name,
        url: item.url
      })
    }
  }

  copiedFooter.menu1.title = t('Help')

  const targetIndex = copiedFooter.menu1.menuData.findIndex((item: any) => item.name === 'Contact support')
  if (targetIndex !== -1) {
    copiedFooter.menu1.menuData[targetIndex].name = t('Contact us')
  }

  return copiedFooter
}
</script>

<template>
  <div>
    <Subscribe v-if="!storeInfo().disable_promotion" class="mt-12" />
    <footer id="footer" class="lg:min-h-[300px] md:min-h-[300px] <md:min-h-[600px] bg-[#D55C20] text-white">
      <div class="cs-container flex flex-wrap <md:text-center lg:text-left py-[50px] gap-y-8 xl:px-5">
        <div class="pt-3 hidden">
          <nuxt-link exact :to="localePath('/bot')">
            {{ $t('Bot') }}
          </nuxt-link>
        </div>
        <client-only>
          <div v-for="menuList in getFooters()" :key="menuList.title" class="w-full md:w-1/2 lg:w-1/4">
            <p class="uppercase font-semibold text-[15px] font-medium">
              {{ $t(menuList.title) }}
            </p>
            <div class="mt-2.5">
              <div
                v-for="(item, index) in menuList.menuData"
                :key="index"
                class="capitalize text-[13px] font-normal leading-[25px]"
              >
                <nuxt-link exact :to="localePath(item.url)" class="hover:text-white/80 transition">
                  {{ $t(item.name) }}
                </nuxt-link>
              </div>
            </div>
          </div>
        </client-only>
        <div class="hidden md:block md:w-1/2 lg:w-1/4" />
        <div class="md:w-1/2 lg:w-1/4 w-full order-3 <md:max-w-sm <md:mx-auto">
          <ShopInfo class="shop-info" />
        </div>
        <div class="md:hidden w-full flex justify-center items-center mt-5 flex-wrap space-x-2 <md:order-4">
          <CommonLanguageSelect class="px-1 rounded-md text-xs py-0.5" btn-class="rounded-md" :show-dropdown-icon="false" />
          <div class="w-[1px] h-4 bg-white" />
          <CommonCurrencySelect class="px-1 rounded-md text-xs py-0.5" btn-class="rounded-md text-white" :show-dropdown-icon="false" />
        </div>
      </div>
      <div id="copyright" class="<md:(hidden mb-20) bg-[#00000033]">
        <div class="cs-container flex flex-wrap md:justify-between items-center justify-center">
          <div class="my-3 flex flex-wrap items-center justify-center <md:( w-full order-0)">
            <CommonLanguageSelect class="-ml-4" btn-class="rounded-md text-white" :show-dropdown-icon="false" />
            <div class="w-[1px] h-4 bg-white" />
            <CommonCurrencySelect btn-class="rounded-md text-white" :show-dropdown-icon="false" />
          </div>
          <small class="<md:(order-1 w-full) text-center" v-html="copyrightText" />
        </div>
      </div>
    </footer>
  </div>
</template>
