<script setup lang="ts">
defineProps<{
  socialsLink: SocialsLink
}>()
</script>

<template>
  <div class="contact-icon text-xl space-x-6">
    <a
      v-if="socialsLink.instagram"
      :href="$getSocialLink(socialsLink.instagram)"
      target="_blank"
      aria-label="instagram"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-instagram" /></span>
    </a>
    <a
      v-if="socialsLink.skype"
      :href="$getSocialLink(socialsLink.skype)"
      target="_blank"
      aria-label="skype"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-skype-business" /></span>
    </a>
    <a
      v-if="socialsLink.pinterest"
      :href="$getSocialLink(socialsLink.pinterest)"
      target="_blank"
      aria-label="pinterest"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-pinterest" /></span>
    </a>
    <a
      v-if="socialsLink.twitter"
      :href="$getSocialLink(socialsLink.twitter)"
      target="_blank"
      aria-label="twitter"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-twitter" /></span>
    </a>
    <a
      v-if="socialsLink.google"
      :href="$getSocialLink(socialsLink.google)"
      target="_blank"
      aria-label="google"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-google" /></span>
    </a>
    <a
      v-if="socialsLink.youtube"
      :href="$getSocialLink(socialsLink.youtube)"
      target="_blank"
      aria-label="youtube"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-youtube" /></span>
    </a>
    <a
      v-if="socialsLink.facebook"
      :href="$getSocialLink(socialsLink.facebook)"
      target="_blank"
      aria-label="facebook"
      rel="noopener noreferrer nofollow"
      class="hover:text-white/80 transition"
    >
      <span><i class="icon-sen-facebook" /></span>
    </a>
  </div>
</template>
