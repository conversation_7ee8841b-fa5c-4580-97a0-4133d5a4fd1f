<script lang="ts" setup>
const localePath = useLocalePath()

const {
  storeContact
} = storeInfo()

const {
  contactPhoneList,
  currentContactPhoneIndex,
  enableContactForm,
  canHasExtraInfo,
  encode,
  decode
} = useFooter()

const {
  socialsLink
} = storeInfo()

const hasSocialsLink = computed(() => {
  return socialsLink?.facebook || socialsLink?.instagram || socialsLink?.skype || socialsLink?.pinterest || socialsLink?.twitter || socialsLink?.google || socialsLink?.youtube
})
</script>

<template>
  <div>
    <div class="text-sm space-y-2.5">
      <div class="text-[15px] font-medium">
        VTT Global LLC
      </div>
      <div class="space-y-1">
        <div class="text-[13px] leading-5">
          <p v-if="storeContact.address" class="max-w-sm">
            <span>{{ $t('Address') }}:</span>
            <span class="ml-1">{{ storeContact.address }}</span>
          </p>
        </div>
      </div>
      <div>
        <client-only>
          <div class="flex <md:justify-center items-center text-[13px] hover:opacity-80 trans__time">
            <svg class="w-3 h-3 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
              <path d="M164.9 24.6c-7.7-18.6-28-28.5-47.4-23.2l-88 24C12.1 30.2 0 46 0 64C0 311.4 200.6 512 448 512c18 0 33.8-12.1 38.6-29.5l24-88c5.3-19.4-4.6-39.7-23.2-47.4l-96-40c-16.3-6.8-35.2-2.1-46.3 11.6L304.7 368C234.3 334.7 177.3 277.7 144 207.3L193.3 167c13.7-11.2 18.4-30 11.6-46.3l-40-96z" />
            </svg>
            <span v-if="canHasExtraInfo && storeContact.phone">
              {{ decode(encode(storeContact.phone)) }}
            </span>
            <GivehugCommonDropdown
              v-else
              class="!bg-transparent -ml-1"
              dropdown-id="contactPhoneDropdown"
              btn-class="text-white !text-[13px]"
              dropdown-icon-class="w-2.5 h-2.5"
            >
              <div class="flex items-center">
                <span>{{ decode(encode(contactPhoneList[currentContactPhoneIndex].phone)) }}</span>
                <img
                  v-for="(flag, flagIndex) in contactPhoneList[currentContactPhoneIndex].flags"
                  :key="flagIndex"
                  :src="`${cdnURL}${flag}`"
                  alt="flag"
                  class="ml-1 h-4"
                  loading="lazy"
                >
              </div>
              <template #content>
                <li
                  v-for="(contactPhone, index) in contactPhoneList"
                  :key="index"
                  class="btn-text flex items-center px-4 min-w-full w-max py-2 group"
                  :class="{
                    'bg-primary !text-contrast': index === currentContactPhoneIndex,
                    '!text-black': index !== currentContactPhoneIndex
                  }"
                  @click="currentContactPhoneIndex = index"
                >
                  <img
                    v-for="(flag, flagIndex) in contactPhone.flags"
                    :key="flagIndex"
                    :src="`${cdnURL}${flag}`"
                    alt="flag"
                    class="mr-1 h-4"
                    loading="lazy"
                  >
                  <span
                    :class="{
                      'group-hover:text-primary': index !== currentContactPhoneIndex
                    }"
                  >{{ decode(encode(contactPhone.phone)) }}</span>
                </li>
              </template>
            </GivehugCommonDropdown>
          </div>
        </client-only>
        <div class="text-[13px] leading-5 flex items-center <md:justify-center gap-1">
          <svg class="w-3.5 h-3.5 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
            <path d="M256 0a256 256 0 1 1 0 512A256 256 0 1 1 256 0zM232 120l0 136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2 280 120c0-13.3-10.7-24-24-24s-24 10.7-24 24z" />
          </svg>
          <div> 9 AM to 5 PM, Mon-Sat</div>
        </div>
        <div v-if="enableContactForm" class="max-w-[200px] space-y-4 mt-4 <md:mx-auto">
          <button
            class="flex items-center justify-center gap-1 w-full bg-white hover:text-black/80 text-black rounded-full h-10 text-[13px] font-medium hover__scale"
            @click="$openCrispChat()"
          >
            <GivehugSharedIconChat class="w-4 h-4" />
            {{ $t('Chat With Us') }}
          </button>
        </div>
      </div>
    </div>
    <GivehugFooterSocialsLinks v-if="hasSocialsLink" class="mt-4" :socials-link="socialsLink" />
  </div>
</template>
