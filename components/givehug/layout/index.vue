<script lang="ts" setup>
import FooterCheckout from '~~/components/givehug/footer/checkout.vue'
import FooterRoot from '~~/components/givehug/footer/root.vue'
import HeaderCheckout from '~~/components/givehug/header/checkout.vue'
import HeaderRoot from '~~/components/givehug/header/root.vue'

import { handleDevtoolDetector } from '~~/composables/common'
import { useCampaignStore } from '~~/store/campaign'

const { isShowCookieConsent, confirmCookieConsent } = useCookieConsent()
const campaignStore = useCampaignStore()
const campaignSlug = computed(() => {
  return campaignStore.modalCampaignUrl
})

const keyedRendering = ref(0)
watch(isCheckoutPage, () => {
  keyedRendering.value += 1
})

onMounted(() => {
  keyedRendering.value += 1

  const modalCampaignUrl = useRoute().query.campaign as string
  if (modalCampaignUrl) {
    campaignStore.$patch({ modalCampaignUrl })
  }
  handleDevtoolDetector()
})
</script>

<template>
  <div id="default_layout" class="overflow-x-hidden">
    <HeaderCheckout v-if="isCheckoutPage" />
    <HeaderRoot v-else />
    <slot />
    <FooterCheckout v-if="isCheckoutPage" />
    <FooterRoot v-else />

    <lazy-cookie-consent v-if="isShowCookieConsent" @confirm="confirmCookieConsent" />
    <lazy-common-modal
      :key="campaignSlug"
      modal-class="<md:(w-full h-full) <lg:(w-[80vw]) lg:w-[798px] max-h-100vh md:max-h-[80vh]"
      :model-value="!!campaignSlug"
      @close-modal="campaignStore.$patch({ modalCampaignUrl: '' })"
    >
      <lazy-default-campaign :key="campaignSlug" :campaign-slug="campaignSlug" :is-modal="true" />
    </lazy-common-modal>
  </div>
</template>

<style>
@import url('~~/assets/themes/givehug/index.css');
</style>
