<script lang="ts" setup>
import Logo from '~/components/givehug/shared/logo.vue'
import Cart from './cart.vue'
import Search from './Search.vue'
import SearchMobile from './SearchMobile.vue'

const localePath = useLocalePath()
const { headerMenu, enable_search: enableSearch } = storeInfo()

const {
  activeMenu,
  showMenu
} = useHeader()

const scrollY = ref(0)
const lastScrollY = ref(0)
const scrollingDown = ref(true)
const menuHidden = ref(false)

// Use computed property with hysteresis to prevent oscillation
const hideMenu = computed(() => {
  // If scrolling down and past threshold, hide menu
  if (scrollingDown.value && scrollY.value > 200) {
    return true
  }
  // If scrolling up and significantly up from where we hid, show menu
  if (!scrollingDown.value && scrollY.value < 100) {
    return false
  }
  // Otherwise maintain current state to prevent oscillation
  return menuHidden.value
})

// Watch the computed property to update the state variable
watch(hideMenu, (newValue) => {
  menuHidden.value = newValue
})

function handleScroll() {
  // Determine scroll direction
  scrollingDown.value = scrollY.value > lastScrollY.value
  // Save last position for next comparison
  lastScrollY.value = scrollY.value
  // Update current position
  scrollY.value = window.scrollY
}

onMounted(() => {
  // Hotkey to focus search input
  document.body.addEventListener('keydown', (e) => {
    if (!window.searchInput) {
      window.searchInput = document.querySelector('#headerSearchInput')
    }

    if (e.ctrlKey && e.key === '/') {
      window.searchInput?.focus()
    }
  })

  window.addEventListener('scroll', () => {
    handleScroll()
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<template>
  <header
    id="PageHeader"
    class="sticky top-0 bg-white transition-default z-3"
    :class="{
      'show-menu': showMenu,
      'givehug__shadow': hideMenu
    }"
  >
    <nav class="z-2 bg-white relative cs-container flex gap-8 items-center justify-center md:justify-start py-3">
      <button
        id="headerShowMenu"
        type="button"
        aria-label="menu"
        class="md:hidden absolute left-2 btn-text text-2xl icon-sen-menu"
        @click="uiManager().toggleHeaderMenu()"
      />
      <nuxt-link id="headerLinkLogo" :to="localePath('/')" class="md:my-2">
        <Logo class="h-9" />
      </nuxt-link>
      <div v-if="enableSearch === 0" class="<md:hidden w-full" />
      <client-only>
        <Search :class="{ hidden: enableSearch === 0 }" />
        <div class="flex gap-6 items-center <md:(absolute right-3 mb-1.5)">
          <!-- <a
            id="headerSellerLogin"
            target="_blank"
            href="#"
            class="<md:hidden btn-text min-w-[fit-content] text-sm"
          >
            {{ $t('Sign in') }}
          </a> -->
          <button
            class="hidden md:flex items-center gap-1.5 btn-text text-sm btn-text trans__time"
            @click="$openCrispChat()"
          >
            <span class="hidden md:block text-sm font-medium">{{ $t('Chat With Us') }}</span>
            <GivehugSharedIconChat class="w-5 h-5" />
          </button>
          <Cart />
        </div>
      </client-only>
    </nav>

    <SearchMobile :class="{ hidden: enableSearch === 0 }" />

    <common-collapse
      id="headerMenuMobile"
      as="ul"
      :when="showMenu"
      class="absolute w-full bg-white shadow max-h-[70vh] overflow-auto transition-default transform md:hidden"
      :class="{ 'translate-y-[-42px]': showMenu }"
    >
      <div class="px-6 py-4 border-t">
        <givehug-header-menu-item-mobile
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
          class="md:mr-3"
          :active-header-menu="activeMenu === index"
          @update-active-header-menu="activeMenu = activeMenu === index ? false : index"
        />
      </div>
      <!-- <a
        id="headerSellerLogin"
        target="_blank"
        href="#"
        class="md:hidden btn-text text-lg mr-3"
      >
        {{ $t('Sign in') }}
      </a> -->
    </common-collapse>

    <div
      class="<md:hidden bg-[#F3F3F3] text-sm trans__time relative z-0"
      :class="{
        'max-h-0 opacity-0 overflow-hidden': hideMenu,
        'max-h-[100px] opacity-100': !hideMenu
      }"
    >
      <ul
        id="headerMenu"
        class="absolute cs-container w-full px-3 max-h-[70vh] transition-default transform relative flex justify-center py-1 gap-x-3 flex-wrap"
      >
        <givehug-header-menu-item
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
        />
      </ul>
    </div>
  </header>
</template>
