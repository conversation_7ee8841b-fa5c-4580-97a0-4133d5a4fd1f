<script setup lang="ts">
const {
  isSearching,
  searchKeyword,
  searchInputRef,
  searchAction
} = useHeader()
</script>

<template>
  <form
    class="<md:hidden flex flex-grow mx-8 relative text-sm"
    @submit.prevent.stop="searchAction"
  >
    <input
      id="headerSearchInput"
      ref="searchInputRef"
      v-model="searchKeyword"
      type="text"
      :placeholder="$t('Search for anything')"
      class="w-full py-3 px-5 pr-10 border border-black rounded-full"
      @mouseover="searchInputRef?.focus()"
    >
    <button
      id="headerSearchButton"
      aria-label="search"
      type="submit"
      style="top: 5px; right: 5px;"
      class="text-lg w-9 h-9 border-radius-override rounded-full absolute h-full px-4 btn-givehug text-white flex items-center justify-center"
    >
      <div
        class="flex items-center justify-center"
        :class="{
          'animate-spin': isSearching
        }"
      >
        <svg v-if="!isSearching" class="w-4 h-4" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12.8362 12.3361C15.4702 9.70112 15.5182 5.47712 12.9422 2.90112C10.3662 0.325116 6.14218 0.373116 3.50718 3.00712C0.873177 5.64212 0.825177 9.86612 3.40118 12.4421C5.97718 15.0181 10.2012 14.9701 12.8362 12.3361ZM12.8362 12.3361L17.5002 17.0001" stroke="white" stroke-width="2" />
        </svg>
        <span v-else class="icon-sen-loading" />
      </div>
    </button>
  </form>
</template>>
