<script setup lang="ts">
import { useCartStore } from '~/store/cart'

const localePath = useLocalePath()
const totalQuantity = computed(() => {
  return useCartStore().getTotalQuantity
})
</script>

<template>
  <nuxt-link
    id="headerCartButton"
    :to="localePath('/cart')"
    :title="$t('View cart')"
    data-test-id="header-cart-button"
    class="btn-text uppercase text-xl font-semibold relative trans__time group mr-1"
  >
    <svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.12579 3.92581C3.22367 2.34393 4.53515 1.11108 6.12006 1.11108H15.8798C17.4647 1.11108 18.7762 2.34393 18.8741 3.92581L19.6029 15.7036C19.7097 17.43 18.3383 18.8889 16.6086 18.8889H5.39131C3.66159 18.8889 2.29022 17.43 2.39704 15.7036L3.12579 3.92581Z" stroke="currentColor" stroke-width="1.8" />
      <path d="M7.69995 4.44446C7.69995 5.32851 8.04763 6.17636 8.6665 6.80148C9.28537 7.4266 10.1247 7.77779 11 7.77779C11.8752 7.77779 12.7145 7.4266 13.3334 6.80148C13.9523 6.17636 14.3 5.32851 14.3 4.44446" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
    </svg>

    <client-only>
      <div v-if="totalQuantity > 0" class="badge bg-primary -mt-1.25 mr-0.5 group-hover:bg-black trans__time">
        {{ totalQuantity }}
      </div>
    </client-only>
  </nuxt-link>
</template>
