<script lang="ts" setup>
defineProps({
  menu: {
    default: undefined,
    type: Object
  },
  menuLevel: {
    default: 0,
    type: Number
  }
})
const localePath = useLocalePath()
</script>

<template>
  <li class="relative rounded-full px-5 my-0.5 py-1.5 menu-item rippe-effect">
    <div class="flex justify-between items-center">
      <nuxt-link
        v-if="menu?.url.includes(`/${$getHost()}`) || menu?.url.startsWith('/')"
        :to="localePath(menu?.url)"
        class="w-full text-overflow-hidden py-1"
      >
        {{ $t(menu?.title) }}
      </nuxt-link>
      <span
        v-else-if="menu?.url === '#'"
        :to="localePath(menu?.url)"
        class="w-full text-overflow-hidden py-1"
      >
        {{ $t(menu?.title) }}
      </span>
      <a
        v-else
        :href="menu?.url"
        class="w-full text-overflow-hidden py-1"
      />
      <i
        v-if="menu?.submenu"
        class="text-2xl md:text-xl icon-sen-chevron-right trans__time min-w-[20px]"
        :class="{ 'transform rotate-90': !menuLevel }"
      />
    </div>
    <div class="!md:(pl-0 h-auto absolute max-h-[50vh] -left-4) hidden sub-menu-wrapper">
      <ul
        v-if="menu?.submenu"
        class="sub-menu rounded-lg mt-3 bg-white trans__time z-3 py-2.5 px-1.5"
        :class="{
          'left-[100%] top-0': menuLevel
        }"
      >
        <givehug-header-menu-item
          v-for="(menuItem, index) in menu?.submenu"
          :key="index"
          :menu="menuItem"
          :menu-level="menuLevel + 1"
          class="hover:bg-white"
        />
      </ul>
    </div>
  </li>
</template>
