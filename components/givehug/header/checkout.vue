<script lang="ts" setup>
const localePath = useLocalePath()
const { name: storeName, logo_url: logoUrl } = storeInfo()
</script>

<template>
  <header
    id="PageHeaderCheckout"
    class="sticky top-0 bg-white transition-default z-3 border-b border-[#EAEAEA]"
  >
    <nav class="z-1 bg-white relative cs-container flex items-center justify-center py-4">
      <nuxt-link :to="localePath('/')">
        <common-image
          img-class="h-6 md:h-8 rounded-none"
          img-id="headerLogo"
          :image="{ path: logoUrl, type: 'logo' }"
          :alt="storeName"
          aria-label="home"
        />
      </nuxt-link>
    </nav>
  </header>
</template>
