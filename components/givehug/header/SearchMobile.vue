<script setup lang="ts">
const {
  isSearching,
  searchKeyword,
  searchAction,
  showMenu
} = useHeader()
</script>

<template>
  <div
    class="border-t border-b md:hidden transform transition text-sm"
    :class="{ '<md:translate-y-[-100%]': showMenu }"
  >
    <form
      class="w-full flex focus-within:outline outline-1 outline-primary shadow-none focus-within:shadow-none container py-0.5"
      @submit.prevent.stop="searchAction"
    >
      <input
        id="headerSearchInput"
        v-model="searchKeyword"
        :placeholder="`${$t('Search for anything')}`"
        type="search"
        :enterkeyhint="`${$t('Search')}`"
        class="w-full bg-white py-2"
      >
      <button
        id="headerSearchButton"
        aria-label="search"
        type="submit"
        style="top: 2px; right: 4px;"
        class="text-lg w-9 h-9 border-radius-override rounded-full absolute h-full px-4 flex items-center justify-center opacity-30"
        :class="{
          '!opacity-100 bg__foreground': searchKeyword
        }"
      >
        <div
          class="flex items-center justify-center"
          :class="{
            'animate-spin': isSearching
          }"
        >
          <span :class="[(isSearching) ? 'icon-sen-loading' : 'icon-sen-search']" />
        </div>
      </button>
    </form>
  </div>
</template>
