<script setup lang="ts">
import CommonDropdown from '~/components/givehug/common/dropdown.vue'
import IconChecked from '~/components/givehug/shared/IconChecked.vue'

defineProps<{
  currentSortType: { text: string, value: string }
  filterValue: {
    sortTypeList: { text: string, value: string }[]
    sort: string
  }
}>()
</script>

<template>
  <div v-if="storeInfo().enable_search" class="flex items-center">
    <span class="font-semibold text-sm">{{ $t('Sort by') }}:</span>
    <CommonDropdown class="!bg-transparent" dropdown-icon-class="w-[5px]">
      <div>
        {{ $t(currentSortType?.text) }}
      </div>
      <template #content>
        <nuxt-link
          v-for="sortItem in filterValue.sortTypeList"
          :key="sortItem.value"
          sp-action="sort-listing"
          :to="getFilterUrl('sort', sortItem.value)"
          class="capitalize py-1 px-8 text-overflow-hidden hover:bg-gray-200 flex items-center relative text-sm"
        >
          <IconChecked v-if="filterValue.sort === sortItem.value" class="w-2.5 h-2.5 mr-2 absolute left-4" />{{ $t(sortItem.text) }}
        </nuxt-link>
      </template>
    </CommonDropdown>
  </div>
</template>
