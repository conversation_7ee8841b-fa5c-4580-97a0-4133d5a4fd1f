<script lang="ts" setup>
import Pagination from '~/components/givehug/shared/Pagination.vue'
import { getFilterUrl } from '~/composables/listing'
import ProductItem from '../shared/productItemListing.vue'
import FilterClear from './Filter/FilterClear.vue'
import FilterArea from './Filter/FilterRoot.vue'
import ListingHeader from './ListingHeader.vue'
import Sort from './Sort.vue'

const {
  filterValue,
  listingProduct,
  filterProduct,
  currentSortType,
  resetData,
  updatePrice
} = useListing()

await resetData()

function updatePriceFilter(value: [number, number]) {
  filterValue.priceFilter = value
  updatePrice()
}

const isShowFilter = ref(false)

const collectionSlug = computed(() => useRoute().params.collection) as Ref<string>
const collectionTitle = computed(() => {
  if (collectionSlug.value) {
    return $slugToName(collectionSlug.value)
  }
  return 'All Products'
})

const partDescription = computed(() => {
  if (collectionSlug.value) {
    return `this ${collectionTitle.value}`
  }
  return ''
})

const { collection_banners: collectionBanners } = storeInfo()
const FIXED_COLLECTION_DATA = $getFixedCollectionData()

const currentColor = computed(() => {
  if (collectionSlug.value) {
    const index = collectionBanners.findIndex((banner) => {
      const slug = banner.banner_link?.split('/').pop()
      return slug === collectionSlug.value
    })
    if (index !== -1) {
      return FIXED_COLLECTION_DATA[index].color
    }
  }
  return '241, 100, 30'
})

const router = useRouter()

async function gotoPage(page: number) {
  await router.push(getFilterUrl.value('page', page))
  window.scrollTo({ top: 0, behavior: 'smooth' })
}
</script>

<template>
  <main id="listingPage" :style="`--color-primary-rgb: ${currentColor}`">
    <ListingHeader
      :title="collectionTitle"
      :description="`Find the perfect way to show your love ${partDescription} with our specially selected gifts.`"
    />

    <!-- Related Collections -->
    <div class="cs-container">
      <div
        v-if="filterValue.pageType !== 'artist' && filterProduct.collection_group.length > 0 && !storeInfo().disable_related_collection"
        class="w-full flex gap-2 mt-8"
      >
        <div class="overflow-x-auto lg:ml-[20%] md:ml-[25%] no-scrollbar">
          <div class="gap-3 flex md:-ml-1">
            <GivehugCommonCollectionItem
              v-for="(collection, index) in filterProduct.collection_group"
              :key="index"
              :value="collection.name"
              :slug="useRoute().params.collection === collection.slug ? '/collection' : `/collection/${collection.slug}`"
              :active="useRoute().params.collection === collection.slug"
            />
          </div>
        </div>
      </div>
      <!-- Listing Total and Sort -->
      <div class="flex flex-wrap mt-8">
        <div class="w-full md:w-[25%] lg:w-[20%] md:sticky top-[140px] h-max md:pr-4">
          <common-collapse
            :when="isShowFilter || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
            class="transition-default"
            @expand="isShowFilter = true"
            @collapse="isShowFilter = false"
          >
            <FilterClear v-if="storeInfo().isShowFilterBox1" @toggle-filter="isShowFilter = !isShowFilter" />
            <FilterArea
              class="h-max mt-4"
              :filter-value="filterValue"
              :filter-product="filterProduct"
              @update:price-filter="updatePriceFilter"
            />
          </common-collapse>
        </div>

        <div :class="{ 'md:w-[75%] lg:w-[80%]': storeInfo().isShowFilterBox1 }" class="w-full md:pl-4">
          <!-- Listing Total and Sort -->
          <div class="flex items-center justify-between w-full">
            <div class="font-semibold text-sm flex gap-6 items-center" @click="isShowFilter = !isShowFilter">
              <div class="flex items-center gap-1 md:hidden uppercase">
                <GivehugSharedIconFilter />
                Filter
              </div>
              <div class="<md:font-normal">
                {{ listingProduct.total || 0 }} {{ $t('products') }}
              </div>
            </div>
            <div class="<md:ml-auto">
              <Sort
                :current-sort-type="currentSortType"
                :filter-value="filterValue"
              />
            </div>
          </div>

          <!-- Listing Products -->
          <div
            :class="{
              'lg:grid-cols-4 xl:grid-cols-5 md:grid-cols-3': !storeInfo().isShowFilterBox1,
              'xl:grid-cols-4 lg:grid-cols-3': storeInfo().isShowFilterBox1
            }"
            class="grid grid-cols-2 gap-4 md:mt-4 mt-2"
          >
            <div
              v-for="(product, index) in listingProduct.products"
              :key="index"
            >
              <ProductItem
                :product="product"
                :color="filterValue.color?.name"
              />
            </div>
          </div>
          <Pagination
            v-if="listingProduct"
            class="flex justify-center mt-9"
            :total-pages="listingProduct.lastPage"
            :current-page="listingProduct.currentPage"
            @update:current-page="gotoPage"
          />
        </div>
      </div>
    </div>
  </main>
</template>
