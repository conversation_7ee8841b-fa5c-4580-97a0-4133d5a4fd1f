<script lang="ts" setup>
import FilterItem from './FilterItem.vue'

const { category, currentCategory } = defineProps({
  category: {
    default: undefined,
    type: Object as PropType<Category>
  },
  currentCategory: {
    default: undefined,
    type: String
  }
})

const isShowChildCategory = ref<boolean>(checkCurrentCategory(category, currentCategory))

function checkCurrentCategory(category?: Category, currentCategory?: string): boolean {
  if (currentCategory === category?.slug) {
    return true
  }
  else if (category?.child_menu) {
    return category?.child_menu.some(subCategory => checkCurrentCategory(subCategory, currentCategory))
  }
  else {
    return false
  }
}

const hasSubCategory = computed(() => category?.child_menu && category?.child_menu.length)
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-2">
      <FilterItem
        v-if="category"
        :template="category"
        :is-active="currentCategory === category.slug"
        :url="getCategoryFilterUrl(currentCategory === category?.slug ? '' : category?.slug)"
        :class="{
          'min-w-1/2': hasSubCategory,
          'w-full': !hasSubCategory
        }"
      />
      <div
        v-if="hasSubCategory"
        class="flex-1 text-right group cursor-pointer"
        @click="isShowChildCategory = !isShowChildCategory"
      >
        <i
          class="btn-text-black text-xs group-hover:text-primary"
          :class="isShowChildCategory ? 'icon-sen-minus' : 'icon-sen-plus'"
        />
      </div>
    </div>
    <common-collapse :when="isShowChildCategory" class="transition-default">
      <div class="sub-categories pl-3">
        <givehug-listing-filter-category
          v-for="(subCategory, index) in category?.child_menu"
          :key="index"
          :category="subCategory"
          :current-category="currentCategory"
          :url="getCategoryFilterUrl(currentCategory === subCategory?.slug ? '' : subCategory?.slug)"
        />
      </div>
    </common-collapse>
  </div>
</template>
