<script lang="ts" setup>
defineEmits(['toggleFilter'])

const localePath = useLocalePath()
</script>

<template>
  <div class="flex items-center justify-between" @click="$emit('toggleFilter')">
    <div class="flex items-center gap-1 mt-1 font-semibold text-sm <md:-ml-0.5">
      <GivehugSharedIconFilter />
      <span class="uppercase">
        {{ $t('Filter') }}
      </span>
    </div>

    <nuxt-link
      :to="localePath($route.path)"
      class="text-xs underline text-gray-500 mt-1"
    >
      {{ $t('Clear all') }}
    </nuxt-link>
  </div>
</template>
