<script lang="ts" setup>
interface Props {
  template: {
    id: number
    name: string
  }
  isActive: boolean
  url: string
}

defineProps<Props>()
</script>

<template>
  <nuxt-link
    class="flex items-center gap-3 cursor-pointer group"
    :class="{ '!text-primary': isActive }"
    :to="url"
  >
    <div
      class="w-5 h-5 rounded flex items-center justify-center"
      :class="isActive ? 'bg-primary' : 'bg-[#f3f3f3]'"
    >
      <svg width="12.5" height="9.17" viewBox="0 0 12.5 9.17" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M4.16667 7.16667L1.25 4.25L0 5.5L4.16667 9.16667L12.5 2.5L11.25 1.25L4.16667 7.16667Z"
          :fill="isActive ? 'white' : '#f3f3f3'"
        />
      </svg>
    </div>
    <span class="text-[#595959] text-sm group-hover:text-primary">{{ template?.name }}</span>
  </nuxt-link>
</template>
