<script setup lang="ts">
import ColorItem from '@/components/givehug/common/colorItem2.vue'
import { useUserSession } from '~/store/userSession'
import FilterCategory from './FilterCategory.vue'
import FilterItem from './FilterItem.vue'

const props = defineProps<{
  filterValue: {
    filterCategories: any[]
    filterTemplates: any[]
    category: any
    template: any
    color: any
    priceFilter: [number, number]
  }
  filterProduct: {
    min_price: number
    max_price: number
    color_group: any[]
  }
}>()

const emit = defineEmits<{
  (e: 'update:price-filter', value: [number, number]): void
}>()

const { $convertPrice } = useNuxtApp()

const isColorExpanded = ref(true)
const isPriceExpanded = ref(true)
const isProductExpanded = ref(true)
const isCategoryExpanded = ref(true)

const localPriceFilter = ref<[number, number]>([0, 0])

watch(() => props.filterValue.priceFilter, (newVal) => {
  if (newVal && newVal.length === 2) {
    localPriceFilter.value = [newVal[0], newVal[1]]
  }
}, { immediate: true })

function updatePrice(index: number, event: Event) {
  const inputElement = event.target as HTMLInputElement
  if (inputElement) {
    const value = Number(inputElement.value)
    const newPrice: [number, number] = [...localPriceFilter.value] as [number, number]
    newPrice[index] = value
    localPriceFilter.value = newPrice
    emit('update:price-filter', newPrice)
  }
}

const userSession = useUserSession()
</script>

<template>
  <div v-if="storeInfo().isShowFilterBox1" class="select-none space-y-6">
    <!-- Filter Color -->
    <div
      v-if="filterProduct && filterProduct.color_group && filterProduct.color_group.length > 0"
      class="filter-color"
    >
      <div class="flex justify-between items-center group">
        <p class="<md:text-center font-semibold text-sm">
          {{ $t('Color') }}
        </p>
        <i
          class="btn-text-black text-xs cursor-pointer hover:text-primary group-hover:text-primary trans__time"
          :class="isColorExpanded ? 'icon-sen-minus' : 'icon-sen-plus'"
          @click="isColorExpanded = !isColorExpanded"
        />
      </div>
      <common-collapse :when="isColorExpanded" class="transition-default overflow-y-auto">
        <div class="gap-2 overflow-hidden grid grid-cols-[repeat(auto-fill,minmax(28px,1fr))] pt-2">
          <nuxt-link
            v-for="(color, index) in filterProduct.color_group"
            :key="index"
            :to="getFilterUrl('color', filterValue.color?.name === color ? '' : color)"
            sp-action="change_color"
            class="w-full aspect-square"
          >
            <ColorItem
              class="w-full h-full"
              :color="color"
              size="full"
              :active="filterValue.color?.name === color"
            />
          </nuxt-link>
        </div>
      </common-collapse>
    </div>

    <!-- Filter Price -->
    <div class="mt-6">
      <div class="flex justify-between items-center group">
        <p class="<md:text-center font-semibold text-sm">
          {{ $t('Price') }}
        </p>
        <i
          class="btn-text-black text-xs cursor-pointer group-hover:text-primary trans__time"
          :class="isPriceExpanded ? 'icon-sen-minus' : 'icon-sen-plus'"
          @click="isPriceExpanded = !isPriceExpanded"
        />
      </div>
      <common-collapse :when="isPriceExpanded" class="transition-default overflow-y-auto">
        <div class="mt-4 overflow-hidden">
          <div class="px-2">
            <common-listing-price-slider
              v-model="localPriceFilter"
              :min="filterProduct.min_price"
              :max="filterProduct.max_price"
              class="!mb-0"
              @change="$emit('update:price-filter', localPriceFilter)"
            />
          </div>
          <div class="mt-4 flex gap-3 justify-between text-sm text-[13px]">
            <div class="relative w-full bg-[#f3f3f3] rounded-[10px]">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-black">{{ userSession.currentCurrencySymbol }}</span>
              <input
                type="number"
                :value="localPriceFilter[0]"
                class="w-full h-full bg-transparent text-right outline-none py-3 pr-3"
                :min="$convertPrice(filterProduct.min_price).value"
                @input="updatePrice(0, $event)"
              >
            </div>
            <div class="relative w-full bg-[#f3f3f3] rounded-[10px]">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-black">{{ userSession.currentCurrencySymbol }}</span>
              <input
                type="number"
                :value="localPriceFilter[1]"
                class="w-full h-full bg-transparent text-right outline-none py-3 pr-3"
                :max="$convertPrice(filterProduct.max_price).value"
                @input="updatePrice(1, $event)"
              >
            </div>
          </div>
        </div>
      </common-collapse>
    </div>

    <!-- Filter Product -->
    <div
      v-if="filterValue.filterTemplates && filterValue.filterTemplates.length"
      class="mt-6"
    >
      <div
        class="flex justify-between items-center cursor-pointer group"
        @click="isProductExpanded = !isProductExpanded"
      >
        <p class="<md:text-center font-semibold text-sm">
          {{ $t('Product') }}
        </p>
        <i
          class="btn-text-black text-xs cursor-pointer group-hover:text-primary trans__time"
          :class="isProductExpanded ? 'icon-sen-minus' : 'icon-sen-plus'"
        />
      </div>
      <common-collapse :when="isProductExpanded" class="transition-default overflow-y-auto">
        <div class="space-y-2 overflow-hidden pt-2">
          <FilterItem
            v-for="(template, index) in filterValue.filterTemplates"
            :key="index"
            :template="template"
            :is-active="parseInt(filterValue.template || '') === template.id"
            :url="getFilterUrl('product', parseInt(filterValue.template || '') !== template.id ? `${template?.id}` : '')"
          />
        </div>
      </common-collapse>
    </div>

    <!-- Filter Category -->
    <div
      v-if="filterValue.filterCategories && filterValue.filterCategories.length"
      class="mt-6"
    >
      <div class="flex justify-between items-center mb-3">
        <p class="<md:text-center font-semibold text-sm">
          {{ $t('Explore') }}
        </p>
        <i
          class="btn-text-black text-xs cursor-pointer group-hover:text-primary trans__time"
          :class="isCategoryExpanded ? 'icon-sen-minus' : 'icon-sen-plus'"
          @click="isCategoryExpanded = !isCategoryExpanded"
        />
      </div>
      <common-collapse :when="isCategoryExpanded" class="transition-default overflow-y-auto">
        <div class="overflow-hidden">
          <FilterCategory
            v-for="(category, index) in filterValue.filterCategories"
            :key="index"
            :category="category"
            :current-category="filterValue.category"
            :url="getCategoryFilterUrl(filterValue.category === category?.slug ? '' : category?.slug)"
          />
        </div>
      </common-collapse>
    </div>
  </div>
</template>
