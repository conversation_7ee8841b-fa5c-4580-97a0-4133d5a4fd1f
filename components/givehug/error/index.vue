<script lang="ts" setup>
import type { NuxtError } from 'nuxt/app'

const props = defineProps({
  error: {
    default: undefined,
    type: Object as PropType<NuxtError>
  }
})
const { $i18n } = useNuxtApp()
const localePath = useLocalePath()

useHead({ title: `${$i18n.t(props.error?.message || '')} | ${storeInfo().name}` })
</script>

<template>
  <main id="errorPage" class="cs-container">
    <div class="max-w-6xl flex flex-col lg:flex-row lg:justify-between items-center lg:items-start mx-auto mt-16 lg:mt-34 mb-16 lg:mb-28 px-4 lg:px-0">
      <div class="w-full max-w-md lg:max-w-sm order-2 lg:order-1 text-center lg:text-left">
        <div class="text-xl lg:text-2xl mb-6 lg:mb-0">
          404 Error
        </div>
        <div class="space-y-4 lg:space-y-3 mt-6 lg:mt-9">
          <h2 class="text-3xl lg:text-4xl font-semibold leading-tight">
            {{ $t('Page not found') }}...
          </h2>
          <p class="text-base lg:text-base text-gray-600 leading-relaxed">
            {{ $t(`Sorry the page you are looking for doesn't exist or has been removed.`) }}
          </p>
          <div class="pt-2">
            <NuxtLink :to="localePath('/')" class="inline-flex items-center gap-2 bg-primary text-white px-6 lg:px-5 py-4 rounded-md text-sm hover:bg-primary/90 transition-colors">
              <svg width="16" height="11" viewBox="0 0 16 11" fill="none" xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0">
                <path d="M0.292969 4.79377C-0.0976562 5.1844 -0.0976562 5.81877 0.292969 6.2094L4.29297 10.2094C4.68359 10.6 5.31797 10.6 5.70859 10.2094C6.09922 9.81878 6.09922 9.1844 5.70859 8.79377L3.41484 6.50002H14.9992C15.5523 6.50002 15.9992 6.05315 15.9992 5.50002C15.9992 4.9469 15.5523 4.50002 14.9992 4.50002H3.41484L5.70859 2.20627C6.09922 1.81565 6.09922 1.18127 5.70859 0.790649C5.31797 0.400024 4.68359 0.400024 4.29297 0.790649L0.292969 4.79065V4.79377Z" fill="white" />
              </svg>
              {{ $t('Back to Home') }}
            </NuxtLink>
          </div>
        </div>
      </div>

      <div class="w-fit max-w-lg lg:max-w-none order-1 lg:order-2 mb-8 lg:mb-0 flex justify-center">
        <svg
          class="w-full h-auto max-w-sm lg:max-w-none lg:w-[486px] lg:h-[192px]"
          viewBox="0 0 486 192"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="xMidYMid meet"
        >
          <g clip-path="url(#clip0_1561_1970)">
            <path d="M245.142 177.394C287.098 177.394 321.111 143.437 321.111 101.548C321.111 59.6592 287.098 25.7017 245.142 25.7017C203.186 25.7017 169.174 59.6592 169.174 101.548C169.174 143.437 203.186 177.394 245.142 177.394Z" fill="currentColor" />
            <path d="M90.9286 153.972H8.70629L0.947266 120.795L88.9846 16.1919H136.522V118.86H158.105V153.972H136.522V186.904H90.923V153.972H90.9286ZM90.9286 119.104V70.4343L50.4284 119.104H90.9286Z" fill="#E4E4E4" />
            <path d="M418.818 153.972H336.601L328.842 120.801L416.879 16.1919H464.416V118.86H485.999V153.972H464.416V186.904H418.818V153.972ZM418.818 119.104V70.4343L378.312 119.104H418.818Z" fill="#E4E4E4" />
            <path d="M269.273 66.4249L249.595 66.4471L220.436 66.4804L183.68 183.061H249.595H306.624L269.273 66.4249Z" fill="#E4E4E4" />
            <path d="M245.152 92.1103C266.547 92.1103 283.891 74.7939 283.891 53.433C283.891 32.0721 266.547 14.7557 245.152 14.7557C223.756 14.7557 206.412 32.0721 206.412 53.433C206.412 74.7939 223.756 92.1103 245.152 92.1103Z" fill="#E4E4E4" />
            <path d="M283.814 53.4326C283.814 53.4326 270.934 61.3566 244.858 61.3566C218.782 61.3566 206.23 53.4326 206.23 53.4326C206.23 53.4326 161.447 66.3084 161.447 75.2194C161.447 84.1304 211.662 92.1154 244.853 92.1154C278.044 92.1154 328.424 84.1304 328.424 75.2194C328.424 66.3084 283.809 53.4326 283.809 53.4326H283.814Z" fill="#CACACA" />
            <path d="M204.412 53.921C204.412 53.921 208.811 76.9943 237.097 76.9943C265.384 76.9943 284.973 55.3184 284.973 55.3184C284.973 55.3184 272.71 59.139 244.851 59.139C216.992 59.139 204.406 53.921 204.406 53.921H204.412Z" fill="#CACACA" />
            <path d="M243.752 15.0437V3.95349" stroke="#E4E4E4" stroke-width="3.78947" stroke-miterlimit="10" />
            <path d="M243.968 8.1957C246.235 8.1957 248.072 6.36103 248.072 4.09785C248.072 1.83467 246.235 0 243.968 0C241.701 0 239.863 1.83467 239.863 4.09785C239.863 6.36103 241.701 8.1957 243.968 8.1957Z" fill="#D15B2D" />
            <path d="M245.152 192C279.102 192 306.624 187.963 306.624 182.984C306.624 178.004 279.102 173.967 245.152 173.967C211.202 173.967 183.68 178.004 183.68 182.984C183.68 187.963 211.202 192 245.152 192Z" fill="url(#paint0_linear_1561_1970)" />
            <path d="M270.738 135.218L246.656 153.284C245.967 153.8 244.99 153.661 244.468 152.974L224.062 125.864C223.546 125.176 223.684 124.2 224.373 123.679L244.162 108.834L254.11 110.537L271.044 133.039C271.56 133.727 271.422 134.702 270.733 135.224L270.738 135.218Z" fill="currentColor" />
            <path d="M244.168 108.834L254.115 110.537L249.794 114.08C249.228 114.546 248.389 114.446 247.95 113.858L244.174 108.834H244.168Z" fill="#7188C3" />
            <path d="M238.724 130.882C239.828 130.882 240.724 129.988 240.724 128.886C240.724 127.783 239.828 126.889 238.724 126.889C237.62 126.889 236.725 127.783 236.725 128.886C236.725 129.988 237.62 130.882 238.724 130.882Z" fill="#BFCBEB" />
            <path d="M249.626 122.708C250.731 122.708 251.626 121.815 251.626 120.712C251.626 119.61 250.731 118.716 249.626 118.716C248.522 118.716 247.627 119.61 247.627 120.712C247.627 121.815 248.522 122.708 249.626 122.708Z" fill="#BFCBEB" />
            <path d="M252.086 135.422C253.079 134.677 252.581 132.343 250.975 130.208C249.368 128.073 247.26 126.946 246.267 127.691C245.274 128.436 245.771 130.771 247.378 132.906C248.985 135.041 251.092 136.168 252.086 135.422Z" fill="black" />
            <path d="M289.649 120.168L280.468 121.172C280.007 121.222 279.785 120.628 280.168 120.368C281.868 119.22 284.317 116.963 286.028 112.843C287.738 108.723 287.416 106.145 287.2 105.307C287.15 105.113 287.244 104.914 287.422 104.814L295.747 100.283C296.02 100.139 296.353 100.3 296.397 100.605C296.614 102.069 296.925 105.984 295.109 110.703C293.17 115.732 290.532 119.253 289.949 119.996C289.877 120.091 289.766 120.152 289.649 120.163V120.168Z" fill="#BFCBEB" />
            <path d="M186.008 154.271C184.936 156.933 181.776 166.609 189.59 172.825C190.024 173.169 190.612 173.241 191.112 173.014L202.759 167.724C203.614 167.335 203.881 166.26 203.314 165.511C201.47 163.093 198.26 157.925 199.56 152.608C199.821 151.537 198.882 150.567 197.81 150.822L187.019 153.406C186.558 153.517 186.186 153.833 186.008 154.271Z" fill="#CACACA" />
            <path d="M208.7 83.0551C203.068 83.0551 198.48 78.4804 198.48 72.8521C198.48 67.2238 203.063 62.649 208.7 62.649C214.337 62.649 218.919 67.2238 218.919 72.8521C218.919 78.4804 214.337 83.0551 208.7 83.0551Z" fill="currentColor" />
            <path d="M281.602 83.0551C275.97 83.0551 271.383 78.4804 271.383 72.8521C271.383 67.2238 275.965 62.649 281.602 62.649C287.24 62.649 291.822 67.2238 291.822 72.8521C291.822 78.4804 287.24 83.0551 281.602 83.0551Z" fill="currentColor" />
            <path d="M281.602 80.6874C285.936 80.6874 289.45 77.1794 289.45 72.8521C289.45 68.5248 285.936 65.0168 281.602 65.0168C277.268 65.0168 273.754 68.5248 273.754 72.8521C273.754 77.1794 277.268 80.6874 281.602 80.6874Z" fill="#BFCBEB" />
            <path d="M245.075 87.1973C239.443 87.1973 234.855 82.6226 234.855 76.9943C234.855 71.366 239.438 66.7913 245.075 66.7913C250.712 66.7913 255.294 71.366 255.294 76.9943C255.294 82.6226 250.712 87.1973 245.075 87.1973Z" fill="currentColor" />
            <path d="M261.308 36.8526C264.633 36.8526 267.328 34.1615 267.328 30.8417C267.328 27.522 264.633 24.8308 261.308 24.8308C257.983 24.8308 255.287 27.522 255.287 30.8417C255.287 34.1615 257.983 36.8526 261.308 36.8526Z" fill="#D9E0F3" />
            <path d="M272.972 45.4421C274.607 45.4421 275.932 44.1189 275.932 42.4866C275.932 40.8543 274.607 39.531 272.972 39.531C271.337 39.531 270.012 40.8543 270.012 42.4866C270.012 44.1189 271.337 45.4421 272.972 45.4421Z" fill="#D9E0F3" />
            <path d="M243.752 43.1243C238.214 43.1243 236.182 45.9412 236.182 51.575C236.182 57.2089 241.725 61.1681 243.752 61.1681C245.779 61.1681 251.322 57.2089 251.322 51.575C251.322 45.9412 249.289 43.1243 243.752 43.1243Z" fill="#94A8D9" />
            <path d="M245.134 53.3439C245.134 53.3439 245.034 51.0371 246.534 49.9946C248.033 48.9521 250.244 49.7007 250.244 49.7007C250.244 49.7007 250.299 52.6285 248.644 53.3272C246.534 54.22 245.134 53.3439 245.134 53.3439Z" fill="#BFCBEB" />
            <path d="M242.452 53.344C242.452 53.344 242.552 51.0372 241.052 49.9947C239.553 48.9522 237.342 49.7008 237.342 49.7008C237.342 49.7008 237.286 52.6287 238.942 53.3273C241.052 54.2201 242.452 53.344 242.452 53.344Z" fill="#BFCBEB" />
            <path d="M244.857 63.5746C218.436 63.5746 205.578 55.6395 205.039 55.3068C204.001 54.6525 203.695 53.2829 204.351 52.2459C205.006 51.209 206.378 50.904 207.417 51.5583C207.533 51.6304 219.858 59.1441 244.857 59.1441C269.855 59.1441 282.53 51.6193 282.652 51.5417C283.696 50.904 285.062 51.2312 285.707 52.2736C286.345 53.3161 286.023 54.6802 284.979 55.3179C284.429 55.6562 271.261 63.5746 244.857 63.5746Z" fill="#CACACA" />
            <path d="M281.603 65.0225C281.22 65.0225 280.842 65.0613 280.476 65.1112L274.1 75.1257C274.611 76.8059 275.666 78.242 277.065 79.2346L285.469 66.0428C284.33 65.3995 283.014 65.0225 281.609 65.0225H281.603Z" fill="#D9E0F3" />
            <path d="M287.114 67.2792L278.889 80.1938C279.738 80.5098 280.649 80.6873 281.61 80.6873C281.727 80.6873 281.832 80.6762 281.949 80.6707L288.853 69.8355C288.447 68.8706 287.853 68.0056 287.12 67.2792H287.114Z" fill="#D9E0F3" />
            <path d="M244.946 84.829C249.28 84.829 252.793 81.321 252.793 76.9937C252.793 72.6664 249.28 69.1584 244.946 69.1584C240.611 69.1584 237.098 72.6664 237.098 76.9937C237.098 81.321 240.611 84.829 244.946 84.829Z" fill="#BFCBEB" />
            <path d="M244.947 69.1638C244.564 69.1638 244.186 69.2026 243.819 69.2525L237.443 79.267C237.954 80.9472 239.01 82.3834 240.409 83.376L248.813 70.1841C247.674 69.5409 246.358 69.1638 244.952 69.1638H244.947Z" fill="#D9E0F3" />
            <path d="M250.454 71.4207L242.229 84.3352C243.078 84.6513 243.989 84.8288 244.95 84.8288C245.067 84.8288 245.172 84.8177 245.289 84.8121L252.192 73.977C251.787 73.0121 251.193 72.1471 250.46 71.4207H250.454Z" fill="#D9E0F3" />
            <path d="M208.701 80.6874C213.036 80.6874 216.549 77.1794 216.549 72.8521C216.549 68.5248 213.036 65.0168 208.701 65.0168C204.367 65.0168 200.854 68.5248 200.854 72.8521C200.854 77.1794 204.367 80.6874 208.701 80.6874Z" fill="#BFCBEB" />
            <path d="M208.703 65.0222C208.32 65.0222 207.942 65.061 207.575 65.1109L201.199 75.1254C201.71 76.8056 202.765 78.2418 204.165 79.2344L212.568 66.0425C211.43 65.3993 210.113 65.0222 208.708 65.0222H208.703Z" fill="#D9E0F3" />
            <path d="M214.21 67.2789L205.984 80.1935C206.834 80.5096 207.745 80.687 208.706 80.687C208.822 80.687 208.928 80.676 209.045 80.6704L215.948 69.8352C215.543 68.8704 214.949 68.0053 214.215 67.2789H214.21Z" fill="#D9E0F3" />
            <path d="M245.151 14.7557C238.364 14.7557 231.988 16.5025 226.445 19.5634C226.945 19.3637 244.696 12.3935 262.447 22.2805C280.142 32.1397 283.708 50.5606 283.824 51.1872C282.658 30.8754 265.796 14.7557 245.151 14.7557Z" fill="#D9E0F3" />
            <path d="M328.429 75.2199C328.429 67.3403 293.522 56.3554 285.463 53.9266C285.463 53.9266 320.52 67.1795 321.364 73.9168C322.203 80.6541 303.43 86.0218 303.414 86.0218L316.182 82.8722C323.691 80.5211 328.424 77.9481 328.424 75.2199H328.429Z" fill="#CACACA" />
            <path d="M161.447 75.2199C161.447 67.3403 196.355 56.3554 204.413 53.9266C204.413 53.9266 169.356 67.1795 168.512 73.9168C167.673 80.6541 186.446 86.0218 186.463 86.0218L173.694 82.8722C166.185 80.5211 161.453 77.9481 161.453 75.2199H161.447Z" fill="#CACACA" />
          </g>
          <defs>
            <linearGradient id="paint0_linear_1561_1970" x1="245.152" y1="173.967" x2="245.152" y2="192" gradientUnits="userSpaceOnUse">
              <stop stop-color="white" />
              <stop offset="0.6" stop-color="#E6E9F2" />
              <stop offset="1" stop-color="#DBE0EC" />
            </linearGradient>
            <clipPath id="clip0_1561_1970">
              <rect width="485.053" height="192" fill="white" transform="translate(0.947266)" />
            </clipPath>
          </defs>
        </svg>
      </div>
    </div>
  </main>
</template>

<style scoped>
#errorPage svg {
  color: var(--color-primary);
}

/* Additional responsive styles */
@media (max-width: 1023px) {
  #errorPage .cs-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 767px) {
  #errorPage h2 {
    font-size: 2rem;
    line-height: 1.2;
  }

  #errorPage p {
    font-size: 0.95rem;
    line-height: 1.6;
  }
}
</style>
