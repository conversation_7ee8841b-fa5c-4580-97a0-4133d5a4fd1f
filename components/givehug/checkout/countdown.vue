<script lang="ts" setup>
const {
  timer,
  parsedTimer,
  runTimer
} = useCheckoutCountTimer()

onMounted(() => {
  runTimer()
})
</script>

<template>
  <div v-if="timer" class="text-primary flex gap-1 items-center">
    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
      <path d="M8.75 0.25C13.5826 0.25 17.5 4.16738 17.5 9C17.5 13.8326 13.5826 17.75 8.75 17.75C3.91738 17.75 0 13.8326 0 9C0 4.16738 3.91738 0.25 8.75 0.25ZM8.75 2C6.89348 2 5.11301 2.7375 3.80025 4.05025C2.4875 5.36301 1.75 7.14348 1.75 9C1.75 10.8565 2.4875 12.637 3.80025 13.9497C5.11301 15.2625 6.89348 16 8.75 16C10.6065 16 12.387 15.2625 13.6997 13.9497C15.0125 12.637 15.75 10.8565 15.75 9C15.75 7.14348 15.0125 5.36301 13.6997 4.05025C12.387 2.7375 10.6065 2 8.75 2ZM8.75 3.75C8.96432 3.75003 9.17117 3.82871 9.33133 3.97113C9.49148 4.11354 9.5938 4.30978 9.61888 4.52262L9.625 4.625V8.63775L11.9936 11.0064C12.1506 11.1638 12.2417 11.3751 12.2484 11.5973C12.2552 11.8195 12.1772 12.036 12.0302 12.2027C11.8831 12.3695 11.6781 12.474 11.4568 12.4951C11.2355 12.5162 11.0145 12.4522 10.8386 12.3162L10.7564 12.2436L8.13138 9.61862C7.99538 9.48252 7.90804 9.30538 7.88287 9.11462L7.875 9V4.625C7.875 4.39294 7.96719 4.17038 8.13128 4.00628C8.29538 3.84219 8.51794 3.75 8.75 3.75Z" fill="#F1641E" />
    </svg>
    <span class="md:hidden text-sm">
      {{ $t('Your order is reserved for') }} {{ parsedTimer.minutes }} {{ $t('m', { count: parsedTimer.minutes }) }} {{ parsedTimer.seconds }} {{ $t('s', { count: parsedTimer.seconds }) }}
    </span>
    <span class="<md:hidden">
      {{ $t('Your order is reserved for') }} {{ parsedTimer.minutes }} {{ $t('minute', { count: parsedTimer.minutes }) }} {{ parsedTimer.seconds }} {{ $t('second', { count: parsedTimer.seconds }) }}
    </span>
  </div>
  <button
    v-else
    class="btn-text text-blue-900"
    @click="timer = 600"
  >
    {{ $t('Reset order') }}
  </button>
</template>
