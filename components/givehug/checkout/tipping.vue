<script lang="ts" setup>
import { USD_CODE } from '~/utils/constant'

const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  }
})

const emit = defineEmits(['updateTip'])

const {
  currentTip,
  tipList,
  currentTipIndex
} = useCheckoutTip([0, 0.05, 0.1, 0.15, 0.2, 0.25])

const backCurrentTipToOriginalTimeout = ref<number | null>(null)
const isCustomTipFocus = ref(false)

const { $formatPriceByCurrency, $convertPrice } = useNuxtApp()

const currentTipText = computed({
  get() {
    if (isCustomTipFocus.value) {
      return currentTip.value
    }
    return $formatPriceByCurrency(currentTip.value, 1, props.order.currency_code)
  },
  set(value: number | string) {
    currentTip.value = Number(value)
  }
})

onMounted(() => {
  resetTip(props.order)
})

watch(props.order, () => {
  resetTip(props.order)
})

function resetTip(order: Order) {
  const index = tipList.findIndex(item => order?.tip_amount?.toFixed(2) === ((order?.total_product_amount || 0) * item).toFixed(2))
  currentTipIndex.value = (index < 0 ? false : index)
  currentTip.value = Number(order?.tip_amount || 0)
  backCurrentTipToOriginal()
}

function backCurrentTipToOriginal() {
  currentTip.value = $convertPrice(props.order.tip_amount, USD_CODE, props.order.currency_code).value
}

function onCustomTipFocusOut() {
  isCustomTipFocus.value = false
  backCurrentTipToOriginalTimeout.value = setTimeout(resetTip, 200)
}

function onUpdateCustomTip() {
  if (backCurrentTipToOriginalTimeout.value) {
    clearTimeout(backCurrentTipToOriginalTimeout.value)
  }
  emit('updateTip', {
    key: 'custom-tip',
    value: {
      tip_amount: $convertPrice(currentTip.value || 0, props.order.currency_code, USD_CODE).value.toFixed(2)
    }
  })
}
</script>

<template>
  <div>
    <p class="text-xl font-semibold mb-6">
      {{ $t('Tip (Optional)') }}
    </p>
    <div class="grid grid-cols-6 gap-3">
      <button
        v-for="(tip, index) in tipList"
        :key="index"
        class="bg-givehug-gray-with-hover rounded-[10px] border h-[45px] text-sm font-semibold"
        :class="{
          'cursor-not-allowed bg-gray': !!isLoading,
          'border-primary': currentTipIndex === index,
          'border-givehug-gray': currentTipIndex !== index
        }"
        @click="
          $emit('updateTip', {
            key: 'tip',
            value: {
              tip_amount: ((order?.total_product_amount || 0) * tip).toFixed(2)
            }
          });
          currentTipIndex = index"
      >
        <common-loading-dot v-if="isLoading === 'tip' && currentTipIndex === index" />
        <span v-else-if="tip === 0">
          {{ $t('No tip') }}
        </span>
        <div v-else>
          {{ tip * 100 }} %
        </div>
      </button>
    </div>
    <form
      action="#"
      class="flex mt-4"
      @submit.prevent.stop="onUpdateCustomTip"
    >
      <div class="w-full flex items-end">
        <givehug-common-input
          v-model="currentTipText"
          class="w-full"
          input-class="!rounded-r-none"
          :label="$t('Custom tip')"
          data-test-id="tipping-input-amount"
          @blur="onCustomTipFocusOut"
          @focus="isCustomTipFocus = true"
        />
        <button
          :disabled="!!isLoading"
          type="submit"
          :class="{ 'cursor-not-allowed bg-gray-400': isLoading }"
          class="min-w-max w-30 px-5 !rounded-l-none h-[45px] text-primary bg-givehug-gray-with-hover rounded-[10px] text-sm"
          data-test-id="tipping-update-tip-btn"
        >
          <common-loading-dot v-if="isLoading === 'custom-tip'" />
          <span v-else>
            {{ $t('Update tip') }}
          </span>
        </button>
      </div>
    </form>
  </div>
</template>

<style>
</style>
