<script lang="ts" setup>
import { useCheckoutDynamicForm } from '~/composables/checkout'
import { useUserSession } from '~/store/userSession'

interface ModalPolicies extends HTMLElement {
  toggleShow: Function
}

const token = useRoute().params.token as string
const route = useRoute()
const localePath = useLocalePath()
const $viewport = useViewport()
const modalPolicies = ref<ModalPolicies>()
const { $formatPriceNoUnit, $fetchDefault } = useNuxtApp()
const {
  order,
  shippingMethods,
  userInfo,
  currentShippingMethod,
  isNotOrderService,
  paymentGateways,
  isShowOrderSummary,
  countryDisabledCheckout,
  updateCheckoutDiscount,
  resetData,
  reloadOrder,
  updateOrder,
  removeProduct
} = await useCheckout(token as string)
const {
  userInfoForm,

  paymentDisplayType,
  isShowModalConfirmEmail,

  gateways,
  currentGateway,

  initPaymentGateways,
  submitCheckout,
  useCreditCardDiscount,

  paypalIframeFullScreen,
  paypalButtonContainer,
  paypalIsShowModalCreditCardDiscount,
  paypalModalConfirmed,

  stripeIframeContainer,
  stripeIframeEWalletContainer,
  stripeData,
  stripeProcessedBanks
} = useCheckoutPayment(token, userInfo, order, paymentGateways, updateOrder)

const {
  currentCountry
} = useCheckoutForm(userInfo)

onMounted(() => {
  initPaymentGateways()
  if (route.query.cart_key) {
    useUserSession().setOrderKey(order.access_token as string, route.query.cart_key as string)
    $fetchDefault(`/public/order/callback/abandoned/track?cartKey=${route.query.cart_key}`)
  }

  document.addEventListener('visibilitychange', reloadOrder)
  resetData()
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', reloadOrder)
})

watch(() => order.total_amount, (oldPrice, newPrice) => {
  if ($formatPriceNoUnit(oldPrice, order.currency_code, 'USD') === $formatPriceNoUnit(newPrice, order.currency_code, 'USD')) {
    return
  }
  initPaymentGateways()
})

let updateTimeout: any
const { isHasField } = useCheckoutDynamicForm(userInfo, false)
watch(userInfo, () => {
  if (!userInfoForm.value?.isFormValid) {
    return
  }
  if (!isHasField('state')) {
    userInfo.state = ''
  }
  if (!isHasField('address_2')) {
    userInfo.address_2 = ''
  }
  if (!isHasField('houseNumber', false)) {
    userInfo.house_number = ''
  }
  if (!isHasField('mailboxNumber', false)) {
    userInfo.mailbox_number = ''
  }
  if (!isHasField('deliveryNote', false)) {
    userInfo.note = ''
  }
  clearTimeout(updateTimeout)
  updateTimeout = setTimeout(() => {
    const isRequiredInfoFilled = (userInfo.email && userInfo.name) // Require at least email & name to make sure the request is succeed
    updateOrder('user-info', {
      order_token: token,
      ...(isRequiredInfoFilled) ? ({ user_info: { ...userInfo }, order_note: userInfo.note }) : ({}),
      email: userInfo.email,
      country: userInfo.country
    }, userInfo)
  }, 1000)
})

const { combos, productIdsInCombo } = campaignComboUI(toRef(order, 'products'), computed(() => order))

const products = computed(() => order.products.filter(product => !productIdsInCombo.value.includes(product.id)))

function getProductDB(product: CartItem) {
  return order.products.find(p => p.product_id === product.product_id) as OrderProduct
}

function isComboNoShip(combo: ComboItem) {
  return combo.items.some(item => getProductDB(item).fulfill_status === 'no_ship')
}
</script>

<template>
  <main id="checkoutPage" :style="`--sprite-url: url('${cdnURL}images/logo_checkout_sprite.webp')`" class="cs-container pb-8">
    <GivehugSharedProgressBar :current-step="1" class="mt-7 md:mt-15 mb-7 md:mb-15" />
    <div class="font-semibold flex gap-2 items-center mb-3 text-[30px] <md:justify-center">
      <nuxt-link v-if="isNotOrderService" :to="localePath('/cart')">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="21" viewBox="0 0 12 21" fill="none">
          <path d="M11.5411 20.5378C11.8349 20.2417 12 19.8402 12 19.4216C12 19.0029 11.8349 18.6014 11.5411 18.3054L3.78308 10.4905L11.5411 2.67556C11.8266 2.3778 11.9846 1.979 11.981 1.56505C11.9774 1.1511 11.8126 0.755126 11.522 0.462409C11.2314 0.169693 10.8383 0.00365782 10.4274 6.05794e-05C10.0165 -0.00353666 9.62058 0.155595 9.32499 0.44318L0.458878 9.37428C0.165058 9.67034 -4.77685e-07 10.0718 -4.59386e-07 10.4905C-4.41087e-07 10.9091 0.165058 11.3106 0.458878 11.6067L9.32499 20.5378C9.6189 20.8337 10.0175 21 10.4331 21C10.8486 21 11.2472 20.8337 11.5411 20.5378Z" fill="#222222" />
        </svg>
      </nuxt-link>
      <span>
        {{ $t('Checkout') }}
      </span>
    </div>
    <givehug-checkout-countdown v-if="isNotOrderService" class="mb-[46px] <md:justify-center" />
    <div class="grid grid-cols-12 md:gap-9 gap-y-9 flex mb-4">
      <div class="col-span-12 border md:(rounded-[20px] p-9 col-span-5 order-last sticky h-max top-40) <md:(rounded-[10px] py-3 px-5)">
        <div
          class="<md:(flex justify-between items-center cursor-pointer)"
          data-test-id="order-summary-btn"
          @click="isShowOrderSummary = !isShowOrderSummary"
        >
          <div class="md:hidden center-flex gap-1.5">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewBox="0 0 22 20" fill="none">
              <path d="M3.12603 3.92585C3.22391 2.34396 4.5354 1.11111 6.12031 1.11111H15.8801C17.465 1.11111 18.7765 2.34396 18.8744 3.92584L19.6031 15.7036C19.7099 17.43 18.3386 18.8889 16.6088 18.8889H5.39156C3.66183 18.8889 2.29046 17.43 2.39728 15.7036L3.12603 3.92585Z" stroke="black" stroke-width="1.8" />
              <path d="M7.7002 4.44444C7.7002 5.3285 8.04787 6.17634 8.66674 6.80147C9.28561 7.42659 10.125 7.77778 11.0002 7.77778C11.8754 7.77778 12.7148 7.42659 13.3336 6.80147C13.9525 6.17634 14.3002 5.3285 14.3002 4.44444" stroke="#222222" stroke-width="1.5" stroke-linecap="round" />
            </svg>
            <span class="text-sm font-semibold">
              {{ $formatPriceByCurrency(order.total_amount, order.currency_rate, order.currency_code) }}
            </span>
          </div>
          <h3 class="<md:(text-sm text-givehug-gray-muted font-medium) md:(text-xl font-semibold)">
            {{ $t('Review your cart') }}
            <span
              class="md:hidden"
              :class="isShowOrderSummary ? 'icon-sen-chevron-down' : 'icon-sen-chevron-up'"
            />
          </h3>
        </div>
        <client-only>
          <common-collapse :when="$viewport.isGreaterOrEquals(VIEWPORT.tablet) || isShowOrderSummary" class="transition-default">
            <div class="mt-9 flex flex-col">
              <lazy-default-campaign-combo-item
                v-for="combo in combos"
                :key="combo.id"
                :is-no-ship="isComboNoShip(combo)"
                :combo="combo"
                :title="combo.title"
                :thumb_url="combo.thumb_url"
                :price="combo.combo_price"
                :quantity="combo.quantity"
                :items="combo.items"
              />
              <givehug-order-product-item
                v-for="(product, index) in products"
                :key="index"
                :product="product"
                :convert-currency-code="order.currency_code"
                :convert-currency-rate="order.currency_rate"
                :show-img="isNotOrderService"
                page="checkout"
                class="mb-6"
                @remove-product="removeProduct(product)"
              />

              <givehug-checkout-discount
                v-if="isNotOrderService"
                class="md:(mb-20 mt-9)"
                :order="order"
                @update-checkout-discount="updateCheckoutDiscount"
              />

              <givehug-order-calculate-price class="mt-4 mb-2 md:my-9" :order="order" />
              <div v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.NULL" class="text-xl">
                <strong class="text-red-600">{{ $t('Error') }}:</strong> {{ $t('Please set up a payment gateway for processing transactions!') }}
              </div>

              <div
                v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT"
                id="paypal-button-container"
                ref="paypalButtonContainer"
                data-test-id="checkout-payment-type"
                data-test-prop="paypal-smartcheckout"
              />
              <div
                v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT_IFRAME"
                id="paypal-button-container"
                ref="paypalButtonContainer"
                data-test-id="checkout-payment-type"
                data-test-prop="paypal-smartcheckout"
                :class="(paypalIframeFullScreen) ? 'fixed h-[100vh] w-[100vw] z-99999 top-0 left-0' : 'mt-4 lg:h-[11rem] w-full'"
              />

              <div
                v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.LIST"
                class="<md:(bottom-fixed p-4 z-3) w-full bg-transparent"
                @click="
                  useTracking().customTracking({
                    event: 'checkout_place_order_button_click'
                  })
                "
              >
                <button
                  :disabled="
                    !!isLoading
                      || !currentGateway
                      || order.fulfill_status === 'no_ship'
                      || countryDisabledCheckout.includes(userInfo.country)
                  "
                  class="w-full py-4 font-semibold text-lg z-1 bg-black text-white rounded-full hover:opacity-80 trans__time"
                  :class="{
                    'cursor-not-allowed': !!isLoading || !currentGateway,
                    'hover:brightness-95 filter !bg-[#ffc439] text-black flex items-center justify-center': currentGateway === PAYMENT_METHOD.paypal
                  }"
                  data-test-id="checkout-submit-form"
                  @click="submitCheckout"
                >
                  <common-loading-spinner v-if="isLoading === 'place_order'" class="right-3" />
                  <span v-if="countryDisabledCheckout.includes(userInfo.country || '')">{{ $t('Cannot ship to', { country: currentCountry?.name }) }}</span>
                  <span v-else-if="[PAYMENT_METHOD.stripeCard, PAYMENT_METHOD.stripeEwallet].includes(currentGateway)" class="center-flex gap-1">
                    {{ $t('Place your order') }}
                  </span>
                  <span v-else-if="currentGateway === PAYMENT_METHOD.paypal" class="flex gap-1 normal-case text-gray-900">
                    {{ $t('Checkout with') }}
                    <div class="overflow-hidden" style="width: 94px;">
                      <i class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; margin-left: -11px; scale: 1.5;" />
                    </div>
                  </span>
                  <span v-else-if="currentGateway === PAYMENT_METHOD.stripeBank">
                    {{ $t('Continue With {gate}', { gate: stripeData.currentBank.name }) }}
                  </span>
                  <span v-else>{{ $t('Select a method') }}</span>
                </button>
              </div>

              <div v-if="isNotOrderService" class="<md:hidden mt-6 text-center text-[13px]">
                <div class="mb-6 leading-5">
                  <span>{{ $t('checkout_text_4') }}&nbsp;</span><a
                    class="underline font-semibold hover:text-primary"
                    :href="localePath('/page/terms-of-service')"
                    @click.prevent="modalPolicies?.toggleShow(true, 'terms-of-service')"
                    v-text="$t('terms of service')"
                  />.
                </div>
                <div class="flex items-center justify-center gap-1 text-sm font-semibold">
                  <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.86288 5.88129H14.1365C15.0061 5.88129 15.8401 6.22663 16.4551 6.84138C17.0702 7.45613 17.4159 8.28998 17.4163 9.15958V13.684C17.4163 14.5538 17.0708 15.3881 16.4557 16.0032C15.8406 16.6183 15.0063 16.9638 14.1365 16.9638H4.86288C3.99314 16.9638 3.15901 16.6184 2.54394 16.0035C1.92887 15.3885 1.58322 14.5545 1.58301 13.6847V9.15958C1.58364 8.29011 1.92947 7.45647 2.5445 6.84189C3.15953 6.22731 3.99342 5.88129 4.86288 5.88129ZM4.86288 7.46462C4.64014 7.46431 4.41951 7.50792 4.21364 7.59297C4.00777 7.67802 3.82069 7.80284 3.66311 7.96027C3.50553 8.1177 3.38054 8.30466 3.2953 8.51046C3.21006 8.71625 3.16624 8.93683 3.16634 9.15958V13.684C3.16634 14.6205 3.92634 15.3805 4.86288 15.3805H14.1365C15.0738 15.3805 15.833 14.6205 15.833 13.684V9.15958C15.833 8.22304 15.073 7.46383 14.1365 7.46383L4.86288 7.46462Z" fill="#222222" />
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.43625 2.01319C7.23504 1.65298 8.09083 1.58331 8.83737 1.58331H9.62904C10.2125 1.58331 10.8403 1.63635 11.4586 1.82873C12.0745 2.01952 12.669 2.29106 13.0427 2.89352C13.3895 3.45244 13.4583 4.16256 13.4583 4.9661V7.12498H11.875V4.9661C11.875 4.17998 11.7887 3.87519 11.6977 3.72873C11.6343 3.62581 11.5069 3.50152 10.9891 3.34081C10.5775 3.21256 10.123 3.16665 9.62904 3.16665H8.83737C8.16762 3.16665 7.58021 3.23473 7.08779 3.4564C6.79092 3.59098 6.6405 3.71765 6.54233 3.87677C6.43704 4.04935 6.33333 4.3589 6.33333 4.9661V7.12498H4.75V4.9661C4.75 4.19819 4.87746 3.56406 5.19254 3.05027C5.51554 2.52302 5.97471 2.2214 6.43625 2.01319ZM9.4715 10.009C9.68146 10.009 9.88283 10.0924 10.0313 10.2409C10.1798 10.3894 10.2632 10.5907 10.2632 10.8007V11.7618C10.2632 11.9717 10.1798 12.1731 10.0313 12.3216C9.88283 12.47 9.68146 12.5534 9.4715 12.5534C9.26154 12.5534 9.06017 12.47 8.91171 12.3216C8.76324 12.1731 8.67983 11.9717 8.67983 11.7618V10.8007C8.67983 10.5907 8.76324 10.3894 8.91171 10.2409C9.06017 10.0924 9.26154 10.009 9.4715 10.009Z" fill="#222222" />
                  </svg>
                  <span>{{ $t('Secure Checkout - SSL Encrypted') }}</span>
                </div>
                <div class="text-givehug-gray-muted text-sm mt-2">
                  <div class="max-w-full w-[335px] inline-block leading-5">
                    {{ $t('Ensuring your financial and personal details are secure during every transactions.') }}
                  </div>
                </div>
              </div>
            </div>
          </common-collapse>
        </client-only>
      </div>

      <div class="col-span-12 md:(col-span-7 order-first)">
        <givehug-checkout-form
          ref="userInfoForm"
          :user-info="userInfo"
          class="mb-9"
          :is-phone-number-required="order.shipping_method === SHIPPING_METHOD.express"
        >
          <template #post-phone-input>
            <GivehugCommonCheckbox1
              v-if="isNotOrderService && order.insurance_fee_2 && storeInfo().enable_insurance_fee"
              :value="!!order.insurance_fee"
              class="mt-2"
              :disabled="!!isLoading"
              data-test-id="shipping-insurance-checkbox"
              :label="`${$t('Add Delivery Insurance and SMS tracking for only')} ${$formatPriceByCurrency(order.insurance_fee_2, order.currency_rate, order.currency_code)}`"
              @update:value="updateOrder('insurance_fee', { delivery_insurance: !order.insurance_fee })"
            />
          </template>
        </givehug-checkout-form>

        <givehug-checkout-tipping
          v-if="isNotOrderService && storeInfo().show_tipping"
          :order="order"
          class="mb-9"
          @update-tip="updateOrder($event.key, $event.value)"
        />

        <div v-if="order.fulfill_status === 'no_ship'" class="bg-[#fff6e6] text-center my-4 py-5 rounded-[10px] text-sm">
          <div class="font-medium">
            {{ countryDisabledCheckout.includes(userInfo.country) ? $t(`no_ship_text1`, { country: currentCountry?.name }) : $t('We are sorry, but there are some product variants in your cart that are not available.') }}
          </div>
          <div>{{ countryDisabledCheckout.includes(userInfo.country) ? $t('no_ship_text2') : $t('Please remove it or edit to continue.') }}</div>
        </div>

        <givehug-checkout-shipping-methods
          v-else-if="isNotOrderService && shippingMethods && shippingMethods.length > 0"
          :shipping-methods="shippingMethods"
          :current-shipping-method="currentShippingMethod"
          class="mb-9"
          @update-shipping-method="(value) => { currentShippingMethod = value; }"
        />

        <template v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.LIST">
          <h6
            id="paymentInfo"
            class="font-semibold text-xl mb-4 flex flex-wrap gap-2 justify-between items-center"
          >
            {{ $t('Payment method') }}
            <span v-if="storeInfo().enable_payment_ssl_norton" class="md:flex flex-wrap gap-2 hidden">
              <i class="icons-sprite" style="--icon-height: 73;--icon-target-height: 30;" />
              <i class="icons-sprite" style="--icon-height: 80;--icon-target-height: 30;--icon-y-pos: 28;" />
            </span>
          </h6>
          <div>
            <!-- stripe-card -->
            <div
              v-if="gateways.stripeGateway"
              class="p-3 border cursor-pointer gateway"
              :class="{
                'bg-[#f2f2f2]': currentGateway === PAYMENT_METHOD.stripeCard
              }"
              data-test-id="checkout-payment-type"
              data-test-prop="stripe"
              @click="currentGateway = PAYMENT_METHOD.stripeCard"
            >
              <div class="flex">
                <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeCard" />
                <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                  <span class="capitalize">
                    {{ $t('Card') }}
                  </span>
                  <div class="flex flex-wrap gap-1">
                    <div
                      class="icon-card"
                      :class="stripeData.brandCard === 'visa' ? 'opacity-100' : 'opacity-60'"
                    >
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 42;" />
                    </div>
                    <div
                      class="icon-card"
                      :class="stripeData.brandCard === 'amex' ? 'opacity-100' : 'opacity-60'"
                    >
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 67;" />
                    </div>
                    <div
                      class="icon-card"
                      :class="stripeData.brandCard === 'mastercard' ? 'opacity-100' : 'opacity-60'"
                    >
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 92;" />
                    </div>
                    <div
                      class="icon-card"
                      :class="stripeData.brandCard === 'discover' ? 'opacity-100' : 'opacity-60'"
                    >
                      <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 117;" />
                    </div>
                  </div>
                </div>
              </div>

              <common-collapse
                :when="currentGateway === PAYMENT_METHOD.stripeCard"
                :class="{ 'mt-4': currentGateway === PAYMENT_METHOD.stripeCard }"
                class="transition-default grid grid-cols-2 gap-3"
              >
                <div v-if="stripeData.stripeGatewayError" class="mt-2 w-full text-sm text-red-500">
                  {{ stripeData.stripeGatewayError }}
                </div>
                <div
                  v-if="gateways.stripeGateway.checkout_domain"
                  id="stripe-iframe-container"
                  ref="stripeIframeContainer"
                  class="col-span-2 w-full"
                />
                <template v-else>
                  <div
                    class="col-span-2 p-3 border relative bg-white"
                    :class="{ 'input-error': stripeData.stripeGatewayError }"
                  >
                    <div id="card-payment-number" />
                    <i v-if="!stripeData.stripeGatewayError" class="absolute icon-sen-lock right-3 position-center-y z-1" />
                  </div>
                  <div
                    class="col-span-2 md:col-span-1 p-3 border relative bg-white"
                    :class="{ 'input-error': stripeData.stripeGatewayError }"
                  >
                    <div id="card-payment-expiry" />
                  </div>
                  <div
                    class="col-span-2 md:col-span-1 p-3 border relative border-radius-override-p2 bg-white"
                    :class="{ 'input-error': stripeData.stripeGatewayError }"
                  >
                    <div id="card-payment-cvc" />
                    <div v-if="!stripeData.stripeGatewayError" class="phone-tooltip z-1 absolute -right-1 h-full position-center-y">
                      <i class="icon-sen-info-outline absolute right-4 position-center-y rounded-full center-flex" />
                      <div class="tooltip-content">
                        <p class="mb-1 text-white">
                          {{ $t('3-digit security code usually found on the back of your card. American Express cards have a 4-digit code located on the front.') }}
                        </p>
                      </div>
                    </div>
                  </div>
                </template>
              </common-collapse>
            </div>
            <!-- stripe-ewallet -->
            <div
              v-if="gateways.stripeGateway"
              class="p-3 border cursor-pointer gateway"
              :class="{
                'bg-[#f2f2f2]': currentGateway === PAYMENT_METHOD.stripeEwallet
              }"
              data-test-id="checkout-payment-type"
              data-test-prop="stripe-ewallet"
              @click="currentGateway = PAYMENT_METHOD.stripeEwallet"
            >
              <div class="flex">
                <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeEwallet" />
                <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                  <span>Apple/Google Pay</span>
                  <div
                    class="flex flex-wrap gap-2"
                    :class="currentGateway === PAYMENT_METHOD.stripeEwallet ? 'opacity-100' : 'opacity-60'"
                  >
                    <i class="icon-card icons-sprite" style="--icon-height: 100;--icon-y-pos: 134; height: 16px;" />
                    <i class="icon-card icons-sprite" style="--icon-height: 90;--icon-y-pos: 167; height: 16px;" />
                  </div>
                </div>
              </div>
              <common-collapse
                :when="currentGateway === PAYMENT_METHOD.stripeEwallet"
                class="transition-default"
              >
                <div v-if="stripeData.stripeGatewayError" class="mt-3 w-full text-sm text-red-500">
                  {{ stripeData.stripeGatewayError }}
                </div>
                <div
                  v-if="gateways.stripeGateway.checkout_domain"
                  id="stripe-iframe-ewallet-container"
                  ref="stripeIframeEWalletContainer"
                  class="col-span-2 w-full mt-3"
                />
                <div v-else id="payment-element" class="mt-3" />
              </common-collapse>
            </div>
            <!-- paypal -->
            <div
              v-if="gateways.paypalGateway?.clientId"
              class="p-3 border cursor-pointer gateway"
              :class="{
                'bg-[#f2f2f2]': currentGateway === PAYMENT_METHOD.paypal
              }"
              data-test-id="checkout-payment-type"
              data-test-prop="paypal"
              @click="currentGateway = PAYMENT_METHOD.paypal"
            >
              <div class="flex">
                <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.paypal" />
                <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                  <span>PayPal</span>
                  <i :class="currentGateway === PAYMENT_METHOD.paypal ? 'opacity-100' : 'opacity-60'" class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371;" />
                </div>
              </div>
            </div>
            <!-- banks -->
            <div
              v-if="gateways.stripeGateway && currentCountry?.code !== 'US' && gateways.stripeGateway?.options?.other_payments"
              class="p-3 border cursor-pointer gateway"
              :class="{
                'bg-[#f2f2f2]': currentGateway === PAYMENT_METHOD.stripeBank
              }"
              data-test-id="checkout-payment-type"
              data-test-prop="stripe-bank"
              @click="currentGateway = PAYMENT_METHOD.stripeBank"
            >
              <div class="flex">
                <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeBank" />
                <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                  <span class="capitalize">
                    {{ $t('Other') }}
                  </span>
                  <div
                    class="flex flex-wrap gap-1"
                  >
                    <i
                      v-for="(bank, index) in stripeProcessedBanks"
                      :key="index"
                      class="icon-card icons-sprite" :class="[
                        `icons-sprite-stripe-${bank.name}`,
                        (currentGateway === PAYMENT_METHOD.stripeBank && stripeData.currentBank?.name === bank.name) ? 'opacity-100' : 'opacity-60'
                      ]"
                      @click="currentGateway = PAYMENT_METHOD.stripeBank; stripeData.currentBank = bank"
                    />
                  </div>
                </div>
              </div>
              <common-collapse
                :when="currentGateway === PAYMENT_METHOD.stripeBank"
                class="transition-default"
              >
                <div v-if="stripeData.stripeGatewayError" class="mt-2 w-full text-sm text-red-500">
                  {{ stripeData.stripeGatewayError }}
                </div>
                <common-dropdown
                  class="mt-3"
                  btn-class="border w-full p-2 bg-white"
                  dropdown-class="w-full"
                >
                  <span>{{ stripeData.currentBank.name }}</span>
                  <template #content>
                    <button
                      v-for="(bank, index) in stripeProcessedBanks"
                      :key="index"
                      :style="`--sprite-url: url('${cdnURL}images/logo_checkout_sprite.webp')`"
                      class="btn-text block px-3 py-1 w-full flex justify-between items-center hover:(bg-gray-300 text-white)"
                      :class="{ '!bg-primary !text-contrast': stripeData.currentBank?.name === bank.name }"
                      @click="currentGateway = PAYMENT_METHOD.stripeBank; stripeData.currentBank = bank"
                    >
                      <span>{{ bank.name }}</span>
                      <i
                        class="ml-1 icons-sprite icons-sprite-list-banks"
                        :class="`icons-sprite-${bank.name}`"
                      />
                    </button>
                  </template>
                </common-dropdown>
                <template v-if="stripeData.currentBank?.required">
                  <common-dropdown
                    v-if="stripeData.currentBank.required.includes('country')"
                    class="mt-3"
                    btn-class="w-full border p-2"
                    dropdown-class="max-w-[80vw] w-full text-center"
                  >
                    <template v-if="stripeData.billingDetails.country">
                      <span class="inline-block vti__flag" :class="stripeData.billingDetails.country.toLowerCase()" />
                      <span class="ml-2">
                        {{ countryByCode(stripeData.billingDetails.country)?.name }}
                      </span>
                    </template>
                    <span v-else>
                      {{ $t('Country') }}
                    </span>
                    <template #content>
                      <div
                        v-for="(countryCode, index) in stripeData.currentBank.countries[(gateways.stripeGateway.options.klarna_currency || stripeData.currentBank.currency)]"
                        :key="index"
                        class="btn-text py-1 px-3"
                        :class="{
                          'bg-gray-100 cursor-not-allowed hover:text-black': countryDisabledCheckout.includes(countryCode),
                          'bg-primary text-contrast': countryCode === userInfo?.country
                        }"
                        @click="stripeData.billingDetails.country = countryCode"
                      >
                        <span class="vti__flag inline-block" :class="countryCode.toLowerCase()" />
                        <span class="ml-2">
                          {{ countryByCode(countryCode)?.name }}
                        </span>
                      </div>
                    </template>
                  </common-dropdown>
                </template>

                <div class="w-full" :class="stripeData.currentBank.name_component ? 'block mt-3 border' : 'hidden'">
                  <div :id="`stripe-${stripeData.currentBank.name}`" />
                </div>
              </common-collapse>
            </div>
          </div>
        </template>
      </div>
    </div>

    <hr class="mt-4">
    <div class="mt-4 flex flex-wrap gap-4">
      <a
        class="text-xs text-primary hover:opacity-80 trans__time"
        :href="localePath('/page/return-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'return-policy')"
        v-text="$t('Return policy')"
      />
      <a
        class="text-xs text-primary hover:opacity-80 trans__time"
        :href="localePath('/page/shipping-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'shipping-policy')"
        v-text="$t('Shipping policy')"
      />
      <a
        class="text-xs text-primary hover:opacity-80 trans__time"
        :href="localePath('/page/privacy-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'privacy')"
        v-text="$t('Privacy policy')"
      />
    </div>

    <div v-if="isNotOrderService" class="md:hidden mt-6 text-center text-[13px]">
      <div class="mb-6 leading-5">
        <span>{{ $t('checkout_text_4') }}&nbsp;</span><a
          class="underline font-semibold hover:text-primary"
          :href="localePath('/page/terms-of-service')"
          @click.prevent="modalPolicies?.toggleShow(true, 'terms-of-service')"
          v-text="$t('terms of service')"
        />.
      </div>
      <div class="flex items-center justify-center gap-1 text-sm font-semibold">
        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M4.86288 5.88129H14.1365C15.0061 5.88129 15.8401 6.22663 16.4551 6.84138C17.0702 7.45613 17.4159 8.28998 17.4163 9.15958V13.684C17.4163 14.5538 17.0708 15.3881 16.4557 16.0032C15.8406 16.6183 15.0063 16.9638 14.1365 16.9638H4.86288C3.99314 16.9638 3.15901 16.6184 2.54394 16.0035C1.92887 15.3885 1.58322 14.5545 1.58301 13.6847V9.15958C1.58364 8.29011 1.92947 7.45647 2.5445 6.84189C3.15953 6.22731 3.99342 5.88129 4.86288 5.88129ZM4.86288 7.46462C4.64014 7.46431 4.41951 7.50792 4.21364 7.59297C4.00777 7.67802 3.82069 7.80284 3.66311 7.96027C3.50553 8.1177 3.38054 8.30466 3.2953 8.51046C3.21006 8.71625 3.16624 8.93683 3.16634 9.15958V13.684C3.16634 14.6205 3.92634 15.3805 4.86288 15.3805H14.1365C15.0738 15.3805 15.833 14.6205 15.833 13.684V9.15958C15.833 8.22304 15.073 7.46383 14.1365 7.46383L4.86288 7.46462Z" fill="#222222" />
          <path fill-rule="evenodd" clip-rule="evenodd" d="M6.43625 2.01319C7.23504 1.65298 8.09083 1.58331 8.83737 1.58331H9.62904C10.2125 1.58331 10.8403 1.63635 11.4586 1.82873C12.0745 2.01952 12.669 2.29106 13.0427 2.89352C13.3895 3.45244 13.4583 4.16256 13.4583 4.9661V7.12498H11.875V4.9661C11.875 4.17998 11.7887 3.87519 11.6977 3.72873C11.6343 3.62581 11.5069 3.50152 10.9891 3.34081C10.5775 3.21256 10.123 3.16665 9.62904 3.16665H8.83737C8.16762 3.16665 7.58021 3.23473 7.08779 3.4564C6.79092 3.59098 6.6405 3.71765 6.54233 3.87677C6.43704 4.04935 6.33333 4.3589 6.33333 4.9661V7.12498H4.75V4.9661C4.75 4.19819 4.87746 3.56406 5.19254 3.05027C5.51554 2.52302 5.97471 2.2214 6.43625 2.01319ZM9.4715 10.009C9.68146 10.009 9.88283 10.0924 10.0313 10.2409C10.1798 10.3894 10.2632 10.5907 10.2632 10.8007V11.7618C10.2632 11.9717 10.1798 12.1731 10.0313 12.3216C9.88283 12.47 9.68146 12.5534 9.4715 12.5534C9.26154 12.5534 9.06017 12.47 8.91171 12.3216C8.76324 12.1731 8.67983 11.9717 8.67983 11.7618V10.8007C8.67983 10.5907 8.76324 10.3894 8.91171 10.2409C9.06017 10.0924 9.26154 10.009 9.4715 10.009Z" fill="#222222" />
        </svg>
        <span>{{ $t('Secure Checkout - SSL Encrypted') }}</span>
      </div>
      <div class="text-givehug-gray-muted text-sm mt-2">
        <div class="max-w-full w-[335px] inline-block leading-5">
          {{ $t('Ensuring your financial and personal details are secure during every transactions.') }}
        </div>
      </div>
    </div>
  </main>

  <givehug-checkout-modal-policies ref="modalPolicies" />

  <common-modal
    modal-id="checkoutConfirmEmail"
    :model-value="isShowModalConfirmEmail"
    :close-icon="false"
  >
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="my-4 text-center">
        <h4>{{ $t('Payment confirmation with email address', { email: userInfo.email }) }}</h4>
      </div>
      <div class="flex justify-center gap-4">
        <button
          class="btn text-center border border-[#6c757d] hover:border-[#545b62] py-2 w-full text-white bg-[#6c757d] hover:bg-[#5a6268]"
          @click="() => { userInfoForm?.emailRef.focus(); isShowModalConfirmEmail = false; }"
        >
          {{ $t('Edit email') }}
        </button>
        <button
          class="btn text-center border border-[#007bff] hover:border-[#0062cc] py-2 w-full text-white bg-[#007bff] hover:bg-[#0069d9]"
          @click="() => { userInfoForm.advancedEmailChecking.serverValidate = true; isShowModalConfirmEmail = false; submitCheckout(); }"
        >
          {{ $t('Continue checkout') }}
        </button>
      </div>
    </div>
  </common-modal>
  <common-modal
    modal-id="checkoutCreditCardDiscountModal"
    :model-value="paypalIsShowModalCreditCardDiscount"
    :close-icon="false"
  >
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="my-4 text-center">
        <h4>{{ $t('Do you want to checkout with credit card for 2% discount?') }}</h4>
      </div>
      <div class="flex justify-center gap-4">
        <button
          class="btn text-center border border-[#6c757d] hover:border-[#545b62] py-2 w-full text-white bg-[#6c757d] hover:bg-[#5a6268]"
          @click="() => { paypalModalConfirmed = true; paypalIsShowModalCreditCardDiscount = false; submitCheckout(); }"
        >
          {{ $t('Checkout with') }}
          <div class="overflow-hidden" style="width: 94px;">
            <i class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; margin-left: -11px; scale: 1.5;" />
          </div>
        </button>
        <button
          class="btn text-center border border-[#007bff] hover:border-[#0062cc] py-2 w-full text-white bg-[#007bff] hover:bg-[#0069d9]"
          @click="() => { paypalIsShowModalCreditCardDiscount = false; useCreditCardDiscount(); }"
        >
          {{ $t('Use credit card') }}
        </button>
      </div>
    </div>
  </common-modal>

  <default-checkout-modal-confirm-product
    :order="order"
    @submit="reloadOrder"
  />
</template>

<style>
.icons-sprite {
  --icon-target-height: 25;
  --scale-fac: calc(var(--icon-height) / var(--icon-target-height));

  height: calc(var(--icon-target-height) * 1px);
  width: calc(150px / var(--scale-fac));
  background: var(--sprite-url) no-repeat;
  background-size: 100%;
  background-position-y: calc(var(--icon-y-pos) * -1px);
  display: block;
}

.icons-sprite-list-banks {
  --icon-target-height: 42;
}

.icons-sprite-stripe-iDEAL {
  --icon-height: 120;
  --icon-y-pos: 150;
}

.icons-sprite-stripe-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 193.5;
}

.icons-sprite-stripe-Sofort {
  --icon-height: 55;
  --icon-y-pos: 437;
}

.icons-sprite-stripe-Klarna {
  --icon-height: 148;
  --icon-y-pos: 172;
}

.icons-sprite-list-banks {
  --icon-target-height: 42;
}

.icons-sprite-iDEAL {
  --icon-height: 145;
  --icon-y-pos: 205;
}

.icons-sprite-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 325;
}

.icons-sprite-Sofort {
  --icon-height: 50;
  --icon-y-pos: 810;
}

.icons-sprite-Klarna {
  --icon-height: 145;
  --icon-y-pos: 295;
}

.tooltip-content {
  display: none;
  position: absolute;
  background-color: rgb(66, 66, 66);
  width: -moz-max-content;
  width: max-content;
  border-radius: 10px;
  padding: 0.5rem;
  color: white;
  transform: translate(-60%, -100%);
  max-width: 250px;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  bottom: -9px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgb(66, 66, 66);
  left: 50%;
  transform: translateX(-50%);
}

.phone-tooltip:hover .tooltip-content {
  display: block;
}

@media (max-width: 767.9px) {
  .tooltip-content {
    transform: translate(-100%, -100%);
  }
  .tooltip-content::after {
    left: 86%;
    transform: translateX(-0%);
  }
}

#checkoutCreditCardDiscountModal {
  z-index: 999;
}
</style>

<style scoped>
.gateway:not(:first-child) {
  border-top: 0px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.gateway:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
