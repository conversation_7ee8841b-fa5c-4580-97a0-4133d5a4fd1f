<script lang="ts" setup>
import type { PropType, Ref } from 'vue'
import { VueTelInput } from 'vue-tel-input'
import { useGeneralSettings } from '~/store/generalSettings'

const { userInfo, isPhoneNumberRequired } = defineProps({
  userInfo: {
    default: () => {},
    type: Object as PropType<Partial<UserInfo>>
  },
  isPhoneNumberRequired: {
    default: false,
    type: Boolean
  },
  isCountrySelectHidden: {
    default: false,
    type: Boolean
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const {
  advancedEmailChecking,
  computedEmailValidateText,
  isCountryUsingAlphanumericZipcode,
  filterCountryText,
  countryDropdown,
  phoneInput,
  warningAddress,
  filterCountryArray,
  isShowValidate,
  validate,

  currentCountry,
  computedStateSelect,
  countryState,

  selectCountry,
  checkPhone,
  updateUserInfo,
  onChangeEmailAddress
} = useCheckoutForm(userInfo)
const generalSettings = useGeneralSettings()

const countryFilter = ref()
const emailRef = ref()
const nameRef = ref()
const addressRef = ref()
const cityRef = ref()
const stateRef = ref()
const zipcodeRef = ref()
const phoneRef = ref()
const houseNumberRef = ref()
const inputRef: { [key: string]: Ref<any> } = {
  emailRef,
  nameRef,
  addressRef,
  cityRef,
  stateRef,
  zipcodeRef,
  phoneRef,
  houseNumberRef
}
function checkUserInfo() {
  isShowValidate.value = true

  const checkKeyArray = ['email', 'name', 'address', 'city', 'zipcode', 'phone']
  if (isHasField('state') && isRequired('state')) {
    checkKeyArray.push('state')
  }
  if (isRequired('houseNumber', false)) {
    checkKeyArray.push('house_number')
  }
  let check = true
  checkKeyArray.every((key) => {
    if (validate[key]) {
      check = false
      key = key === 'house_number' ? 'houseNumber' : key
      inputRef[`${key}Ref` as string]?.value.focus()
      return false
    }
    updateUserInfo(userInfo)
    return true
  })

  return check
}

function isFormValid() {
  const checkKeyArray = ['email', 'name', 'address', 'city', 'zipcode', 'phone']
  if (isHasField('state') && isRequired('state')) {
    checkKeyArray.push('state')
  }
  let check = true
  checkKeyArray.every((key) => {
    if (validate[key]) {
      check = false
      return false
    }
    return true
  })
  return check
}

const computedPhoneNumberRequired = computed(() => {
  if (isPhoneNumberRequired) {
    return true
  }

  return (userInfo.country !== 'US')
})
const {
  isHasField,
  isRequired,
  getClass,
  addressLabel2,
  stateLabel,
  cityLabel,
  zipcodeLabel,
  houseNumberLabel,
  mailboxLabel
} = useCheckoutDynamicForm(userInfo, computedPhoneNumberRequired.value, false)
onMounted(() => {
  if (userInfo.phone) {
    phoneInput.value = userInfo.phone.replace(/^\+/g, '')
  }

  if (userInfo.email) {
    nameRef.value.focus()
  }
  else {
    emailRef.value.focus()
  }
})

watch(currentCountry, (country) => {
  if (country?.code) {
    phoneRef.value.choose(country.code)
  }
})

function checkInput(string: string, { valid, number }: any) {
  checkPhone(string, { valid, number }, { isPhoneNumberRequired: computedPhoneNumberRequired.value })
}
defineExpose({
  checkUserInfo,
  isFormValid,
  advancedEmailChecking,
  emailRef
})

function handleClickFeedback() {
  if (!advancedEmailChecking.value.hasMistyped) {
    return
  }
  updateUserInfo({ email: advancedEmailChecking.value.mistypedAutocorrect })
  onChangeEmailAddress()
}
</script>

<template>
  <form id="CheckoutForm" action="#" @submit.prevent.stop>
    <h6 class="font-semibold text-xl mb-6">
      {{ $t('Contact information') }}
    </h6>
    <div class="grid lg:grid-cols-2 gap-4">
      <div>
        <givehug-common-input
          id="email"
          ref="emailRef"
          :label="$t('Email')"
          required
          :model-value="userInfo?.email"
          :state="computedEmailValidateText.level"
          :message="computedEmailValidateText.message"
          type="email"
          :placeholder="$t('Enter email')"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          @update:model-value="updateUserInfo({ email: $event })"
          @change="onChangeEmailAddress"
          @click:feedback="handleClickFeedback"
        />
        <GivehugCommonCheckbox1
          :value="!!userInfo.subscribed"
          class="mt-2"
          :label="$t('Email me with news and offers')"
          @update:value="updateUserInfo({ subscribed: $event ? 1 : 0 })"
        />
      </div>
      <div>
        <label class="text-sm font-semibold mb-1 block">
          <span>{{ $t('Phone') }}</span>
          <span v-if="computedPhoneNumberRequired" class="text-red-500">*</span>
        </label>
        <VueTelInput
          ref="phoneRef"
          v-model="phoneInput"
          :default-country="userInfo?.country"
          class="h-[45px] !border bg-givehug-gray-with-hover !rounded-[10px] !border-givehug-gray focus-within:border-primary"
          :class="{
            '!border-green-500': isShowValidate && !validate.phone && phoneInput,
            '!border-red-500': isShowValidate && validate.phone,
            '!border-gray-200': !isShowValidate
          }"
          :dropdown-options="({
            showDialCodeInSelection: true,
            showDialCodeInList: true,
            showFlags: true,
            showSearchBox: true,
            width: '400px'
          })"
          :input-options="({
            id: 'phone',
            name: 'phone',
            autocomplete: 'phone',
            styleClasses: 'bg-givehug-gray-with-hover !rounded-[10px] text-sm',
            placeholder: $t('Enter phone number'),
            required: computedPhoneNumberRequired,
            type: 'tel'
          })"
          @on-input="checkInput"
        >
          <template #icon-right>
            <div class="phone-tooltip relative z-1">
              <i class="icon-sen-info-outline absolute right-4 position-center-y center-flex" />
              <div class="tooltip-content">
                <p class="mb-1 text-white">
                  {{ $t('In case we need to contact') }} {{ $t('you about your order') }}
                </p>
              </div>
            </div>
          </template>
        </VueTelInput>
        <span
          v-if="isShowValidate && validate.phone"
          class="mt-1 text-sm text-red-500"
        >{{ $t(validate.phone) }}</span>
        <slot name="post-phone-input" />
      </div>
    </div>

    <givehug-common-input
      id="name"
      ref="nameRef"
      class="mt-4"
      :label="$t('Full Name')"
      :placeholder="$t('Enter full name')"
      required
      :model-value="userInfo?.name"
      :state="isShowValidate ? !validate.name : undefined"
      :message="validate.name"
      @update:model-value="updateUserInfo({ name: $event })"
    />

    <slot name="pre_shipping_info" />

    <h6 class="mt-9 font-semibold text-xl mb-6">
      {{ $t('Shipping details') }}
    </h6>
    <div
      class="grid gap-4" :class="{
        'md:grid-cols-2': !isCountrySelectHidden
      }"
    >
      <div v-if="!isCountrySelectHidden">
        <label
          class="text-sm font-semibold mb-1 block"
        >
          <span>{{ $t('Country') }}</span>
          <span class="text-red-500">*</span>
        </label>
        <common-dropdown
          ref="countryDropdown"
          dropdown-id="countryDropdown"
          btn-class="w-full text-left h-[45px] bg-givehug-gray-with-hover border border-givehug-gray px-5 flex items-center gap-5 text-sm font-medium"
          dropdown-class="max-w-[80vw] w-full pt-20"
          show-data-class=""
          :close-on-click="false"
          @shown="countryFilter.focus()"
        >
          <div class="h-[25px] w-[25px] rounded-full overflow-hidden">
            <img
              class="country-flag object-cover h-full w-full"
              :alt="`flag-${currentCountry?.code.toLowerCase()}`"
              :src="`${cdnURL}images/country-flag/${currentCountry?.code.toLowerCase()}.svg`"
            >
          </div>
          <span>
            {{ currentCountry?.name }}
          </span>
          <template #content>
            <givehug-common-input
              id="countryFilter"
              ref="countryFilter"
              v-model="filterCountryText"
              :label="$t('Find your country')"
              class="!absolute top-0 w-full px-5 py-1"
            />
            <template v-if="filterCountryArray?.length">
              <div
                v-for="(country, index) in filterCountryArray"
                :key="index"
                class="btn-text py-1 px-3"
                :class="{
                  'bg-gray-100 cursor-not-allowed hover:text-black': generalSettings.getCountryDisabledCheckout.includes(country.code),
                  'bg-primary text-contrast': country.code === userInfo?.country
                }"
                tabindex="0"
                @click="selectCountry(country.code)"
                @keyup.space="selectCountry(country.code)"
                @keyup.enter="selectCountry(country.code)"
              >
                <span class="vti__flag inline-block" :class="country.code.toLowerCase()" />
                <span class="ml-2">
                  {{ country.name }}
                </span>
              </div>
            </template>
            <div v-else class="text-center">
              {{ $t('Sorry, no matching country') }}
            </div>
          </template>
        </common-dropdown>
      </div>

      <givehug-common-input
        id="address"
        ref="addressRef"
        class="w-full"
        :label="$t('Address')"
        required
        :placeholder="$t('Enter address (House number and Street name)')"
        :model-value="userInfo?.address"
        :state="isShowValidate ? !validate.address : undefined"
        :message="validate.address"
        @update:model-value="updateUserInfo({ address: $event })"
      />
    </div>

    <div class="grid grid-cols-12 gap-4 mt-4">
      <div :class="getClass('address_1')">
        <givehug-common-input
          v-if="isHasField('address_2')"
          id="address_2"
          autocomplete="address-line2"
          :label="addressLabel2"
          :placeholder="$t('Enter Apt / Suite / Building / Other')"
          :model-value="userInfo?.address_2"
          :state="((warningAddress && !userInfo.address_2) ? 'warning' : undefined)"
          :message="$t('Please add your unit/room number if applicable')"
          @update:model-value="updateUserInfo({ address_2: $event })"
        />
      </div>
      <givehug-common-input
        v-if="isHasField('city')"
        id="city"
        ref="cityRef"
        :class="getClass('city')"
        :label="cityLabel"
        :required="isRequired('city')"
        :placeholder="$t('Enter city')"
        :model-value="userInfo?.city"
        autocomplete="address-level2"
        :state="isShowValidate ? !validate.city : undefined"
        :message="validate.city"
        @update:model-value="updateUserInfo({ city: $event })"
      />

      <template v-if="isHasField('state')">
        <template v-if="countryState?.length">
          <div class="relative" :class="getClass('state')">
            <label for="state" class="text-sm font-semibold mb-1 block">
              {{ stateLabel }}
              <span v-if="isRequired('state')" class="text-red-500">*</span>
            </label>
            <select
              id="state"
              ref="stateRef"
              v-model="computedStateSelect"
              name="state"
              autocomplete="address-level1"
              class="h-[45px] w-full bg-transparent border border-givehug-gray bg-givehug-gray-with-hover focus:outline-none focus:border-primary px-5 text-sm"
              :class="[
                (isShowValidate && validate.state) ? '!border-red-500' : (isShowValidate && !validate.state) ? '!border-green-500' : '',
                (userInfo.state) ? '' : 'text-gray-400'
              ]"
            >
              <option value="" disabled selected hidden>
                {{ $t('Select state') }}
              </option>
              <option
                v-for="(state, index) in countryState"
                :key="index"
                :value="state.value"
              >
                {{ state.text }}
              </option>
            </select>
            <span
              v-if="isShowValidate && validate.state"
              class="mt-1 text-sm"
              :class="[(isShowValidate && validate.state) ? '!text-red-500' : '']"
            >{{ $t(validate.state) }}</span>
          </div>
        </template>
        <template v-else>
          <givehug-common-input
            id="state"
            ref="stateRef"
            autocomplete="address-level1"
            :class="getClass('state')"
            :label="stateLabel"
            :required="isRequired('state')"
            :placeholder="$t('Enter state')"
            :model-value="userInfo?.state"
            :state="isShowValidate ? !validate.state : undefined"
            :message="validate.state"
            @update:model-value="updateUserInfo({ state: $event })"
          />
        </template>
      </template>

      <givehug-common-input
        id="zipcode"
        ref="zipcodeRef"
        :class="getClass('postCode')"
        :required="isRequired('postCode')"
        :placeholder="`Enter ${zipcodeLabel}`"
        autocomplete="postal-code"
        :label="zipcodeLabel"
        :model-value="userInfo?.zipcode || userInfo?.postcode"
        :state="isShowValidate ? !validate.zipcode : undefined"
        :message="validate.zipcode"
        :type="(isCountryUsingAlphanumericZipcode) ? '' : 'number'"
        pattern="^[a-zA-Z0-9-\s]+$"
        @update:model-value="updateUserInfo({ zipcode: $event })"
      />

      <givehug-common-input
        v-if="isHasField('houseNumber', false)"
        id="houseNumber"
        ref="houseNumberRef"
        :class="getClass('houseNumber')"
        :required="isRequired('houseNumber')"
        :placeholder="`Enter ${houseNumberLabel}`"
        autocomplete="house-number"
        :label="houseNumberLabel"
        :model-value="userInfo?.house_number"
        :state="isShowValidate ? !validate.house_number : undefined"
        :message="validate.house_number"
        @update:model-value="updateUserInfo({ house_number: $event })"
      />

      <givehug-common-input
        v-if="isHasField('mailboxNumber', false)"
        id="mailbox"
        :class="getClass('mailboxNumber')"
        :required="isRequired('mailboxNumber')"
        :placeholder="`Enter ${mailboxLabel}`"
        autocomplete="mailbox"
        :label="mailboxLabel"
        :model-value="userInfo?.mailbox_number"
        @update:model-value="updateUserInfo({ mailbox_number: $event })"
      />
    </div>

    <div v-if="isHasField('deliveryNote', false)" class="relative mt-5">
      <label v-if="!isModal" class="text-sm font-semibold mb-1 block">
        {{ $t('Order note') }}
      </label>
      <textarea
        class="border border-givehug-gray bg-givehug-gray-with-hover p-5 w-full resize-none text-sm overflow-y-scroll focus:border-primary focus-visible:outline-none"
        rows="3"
        maxlength="255"
        :placeholder="$t('Provide details such as building description, a nearby landmark, or other navigation instructions')"
        @update:model-value="updateUserInfo({ note: $event })"
      />
    </div>
  </form>
</template>

<style>
.vue-tel-input {
  border-radius: 0;
}

body.classic .vue-tel-input {
  border-radius: 0.3rem;
}

/* .vue-tel-input:focus-within {
  border-color: rgba(var(--color-primary), 0.3)!important;
} */

.tooltip-content {
  display: none;
  position: absolute;
  background-color: rgb(66, 66, 66);
  width: -moz-max-content;
  width: max-content;
  border-radius: 10px;
  padding: 0.5rem;
  color: white;
  transform: translate(-56%, -100%);
}

.tooltip-content::after {
  content: '';
  position: absolute;
  bottom: -9px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgb(66, 66, 66);
  left: 50%;
  transform: translateX(-50%);
}

.phone-tooltip:hover .tooltip-content {
  display: block;
}

@media (max-width: 767.9px) {
  .tooltip-content {
    transform: translate(-100%, -100%);
  }
  .tooltip-content::after {
    left: 91%;
    transform: translateX(-0%);
  }
}
</style>
