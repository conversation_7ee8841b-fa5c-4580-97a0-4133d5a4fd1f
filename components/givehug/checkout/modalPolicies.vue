<script lang="ts" setup>
const isShow = ref(false)
const pageData = ref<PageData>()

async function toggleShow(state: boolean, policyType: string) {
  if (state) {
    const { pageData: data } = await useCommonFooterPage(policyType)
    pageData.value = data.value
  }
  isShow.value = state
}

defineExpose({ toggleShow })
</script>

<template>
  <common-modal
    modal-id="checkoutPolicy-givehug"
    :model-value="isShow"
    @close-modal="isShow = false"
  >
    <div v-if="pageData" class="w-300 p-4 pt-3 max-w-[90vw]">
      <div class="flex justify-between items-center">
        <h3 class="font-semibold text-2xl">
          {{ $t(pageData.title || '') }}
        </h3>
      </div>
      <hr class="mt-3">
      <div class="overflow-auto max-h-[70vh] mt-3 pr-4" v-html="pageData.content" />
    </div>
  </common-modal>
</template>

<style lang="scss">
#checkoutPolicy-givehug {
  * {
    color: #000000 !important;
  }

  & h2:first-child {
    display: none;
  }

  h2 {
    font-weight: 600;
    font-size: 1.25rem;
    line-height: 1.75rem;
    margin-bottom: 0.5rem;
  }

  strong {
    font-weight: 600;
  }

  u {
    text-decoration: none;
  }

  p:has(> u) {
    font-weight: 600;
  }

  ul,
  ol {
    padding-inline-start: 25px;
  }
  ul {
    list-style: disc;
  }
  ol {
    list-style: decimal;
  }
  li::marker {
    font-size: 0.8em;
  }

  li {
    padding-left: 0;
    text-indent: -3px;
  }

  a span {
    color: var(--color-primary) !important;
    transition: all 0.2s ease-in-out;
  }
  a:hover {
    opacity: 0.9;
  }

  a {
    color: var(--color-primary) !important;
  }
}
</style>
