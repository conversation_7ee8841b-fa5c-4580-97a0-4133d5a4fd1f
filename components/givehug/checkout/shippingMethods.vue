<script lang="ts" setup>
const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  },
  shippingMethods: {
    type: Object,
    default: () => ({})
  },
  currentShippingMethod: {
    type: String,
    default: ''
  }
})
const $emit = defineEmits(['updateShippingMethod'])
const { shippingMethods, currentShippingMethod } = toRefs(props)
const { $i18n } = useNuxtApp()

const ONE_DAY = 60 * 60 * 24 * 1000
const dateFormat: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' }

const userTrigger = ref(false)
const computedShippingMethods = computed(() => {
  return shippingMethods.value.map((shippingMethod: any) => {
    const shippingTime = shippingMethod.shipping_time ?? [3, 10]
    const printingTime = shippingMethod.printing_time ?? 3

    let description = ''
    const { date: dateLocale } = $i18n.localeProperties
    if (shippingMethod.name === SHIPPING_METHOD.standard) {
      const date = new Date(Date.now() + ONE_DAY * printingTime)
      description = $i18n.t('Printed before _day', { day: date.toLocaleDateString(dateLocale, dateFormat) })
      // description += "<br><span class='note'>* Due to the peak season, we do not guarantee delivery before Christmas and New Year.</span>"
    }
    if (shippingMethod.name === SHIPPING_METHOD.express) {
      description = $i18n.t('We print your order immediately (no cancellation allowed)')
    }
    const date1 = new Date(Date.now() + ONE_DAY * (shippingTime[0] + printingTime))
    const date2 = new Date(Date.now() + ONE_DAY * (shippingTime[1] + printingTime))
    description += `. ${$i18n.t('Estimated delivery from _day1 to _day2.', { day1: date1.toLocaleDateString(dateLocale, dateFormat), day2: date2.toLocaleDateString(dateLocale, dateFormat) })}`
    shippingMethod.description = description

    return shippingMethod
  })
})

function updateShippingMethod(method: string) {
  if (isLoading.value) {
    return
  }

  userTrigger.value = true
  $emit('updateShippingMethod', method || SHIPPING_METHOD.standard)
}
</script>

<template>
  <div>
    <h6 class="font-semibold text-xl mb-6">
      {{ $t('Delivery options') }}
    </h6>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
      <div
        v-for="(shipping, index) in computedShippingMethods"
        :key="index"
        class="px-[24px] py-[22px] border cursor-pointer rounded-[10px] relative flex flex-col gap-[15px] overflow-hidden"
        :class="{
          'border-secondary bg-[#f7fff3]': currentShippingMethod === shipping?.name,
          'border-givehug-gray bg-givehug-gray-with-hover': currentShippingMethod !== shipping?.name,
          'bg-[rgba(233,233,233,0.7)]': isLoading
        }"
        @click="updateShippingMethod(shipping?.name)"
      >
        <div class="flex justify-between">
          <div class="capitalize font-semibold">
            <span> {{ shipping?.name }}</span>
            <span v-if="shipping?.shipping_cost"> - {{ $formatPrice(shipping?.shipping_cost, USD_CODE, order.currency_code) }}</span>
          </div>
          <i v-if="currentShippingMethod === shipping?.name" class="icon-sen-check text-secondary" />
        </div>
        <div
          v-if="storeInfo().show_checkout_shipping_info !== 0"
          class="text-sm"
          v-html="shipping?.description"
        />
        <div v-if="isLoading" class="absolute top-0 left-0 h-full w-full bg-[rgba(233,233,233,0.7)] center-flex">
          <common-loading-dot v-if="isLoading === `shipping_fee_${shipping?.name}`" variant="bg-black" class="ml-2" />
        </div>
      </div>
    </div>
  </div>
</template>
