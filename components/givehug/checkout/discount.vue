<script lang="ts" setup>
const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  }
})

const $emit = defineEmits(['updateCheckoutDiscount'])
const reEvaluateFlag: Ref<boolean> = ref(false)
const {
  discountList,
  DISCOUNT_RETURN_CODE,

  userTrigger,
  promotionShow,
  discountErrorCode,
  isShowModalDiscountList,
  discountCode,
  discountInput,
  getDiscountList,
  evaluateDiscountCode
} = useCheckoutDiscount(props.order)

function updateDiscount(discountCodeFromList?: string) {
  discountCode.value = discountCodeFromList || discountCode.value
  isShowModalDiscountList.value = false
  $emit('updateCheckoutDiscount', discountCode.value, reEvaluateFlag.value)
}

function reloadDiscountCode() {
  evaluateDiscountCode ()
}

watch(
  [
    () => props.order.discount_code,
    () => props.order.total_discount,
    () => reEvaluateFlag.value
  ],
  reloadDiscountCode
)

onMounted(async () => {
  getDiscountList()
  const { query } = useRoute()
  const discountCookie = useCookie('discount_code')
  if (query.discount || discountCookie.value) {
    discountCode.value = query.discount as string || discountCookie.value as string
    await nextTick()
    $emit('updateCheckoutDiscount', discountCode.value)
  }
  if (props.order.discount_code) {
    evaluateDiscountCode()
  }

  if (discountCookie.value) {
    discountCookie.value = undefined
  }
})
</script>

<template>
  <div id="coupon-form">
    <div class="text-sm font-semibold flex justify-between mb-[5px]">
      <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
          <g clip-path="url(#clip0_729_1275)">
            <path d="M9.5925 16.25L2.25 8.9075V10.4075C2.25 10.805 2.4075 11.1875 2.6925 11.465L8.535 17.3075C9.12 17.8925 10.0725 17.8925 10.6575 17.3075L15.315 12.65C15.9 12.065 15.9 11.1125 15.315 10.5275L9.5925 16.25Z" fill="black" />
            <path d="M8.535 13.5575C8.8275 13.85 9.21 14 9.5925 14C9.975 14 10.3575 13.85 10.65 13.5575L15.3075 8.9C15.8925 8.315 15.8925 7.3625 15.3075 6.7775L9.465 0.935C9.1875 0.6575 8.805 0.5 8.4075 0.5H3.75C2.925 0.5 2.25 1.175 2.25 2V6.6575C2.25 7.055 2.4075 7.4375 2.6925 7.715L8.535 13.5575ZM3.75 2H8.4075L14.25 7.8425L9.5925 12.5L3.75 6.6575V2Z" fill="black" />
            <path d="M5.4375 4.625C5.95527 4.625 6.375 4.20527 6.375 3.6875C6.375 3.16973 5.95527 2.75 5.4375 2.75C4.91973 2.75 4.5 3.16973 4.5 3.6875C4.5 4.20527 4.91973 4.625 5.4375 4.625Z" fill="black" />
          </g>
          <defs>
            <clipPath id="clip0_729_1275">
              <rect width="18" height="18" fill="white" transform="translate(0 0.5)" />
            </clipPath>
          </defs>
        </svg>
        <span>{{ $t('Discount code') }}</span>
      </div>
      <button
        v-if="discountList.length"
        class="bg-text hover:text-primary font-medium text-xs"
        type="button"
        @click="isShowModalDiscountList = true"
      >
        {{ $t('View code') }}
      </button>
    </div>
    <div class="flex text-[13px]">
      <input
        ref="discountInput"
        v-model="discountCode"
        class="min-w-1 flex-grow border border-givehug-gray bg-givehug-gray-with-hover border-givehug-gray rounded-l-[10px] px-5 h-[45px] text-givehug-gray-muted"
        :class="{
          'border-green-500': (discountErrorCode && discountErrorCode === 0),
          'border-red-600': (discountErrorCode && discountErrorCode > 0)
        }"
        type="text"
        :placeholder="$t('Discount Code')"
      >
      <button
        class="bg-givehug-gray-with-hover rounded-r-[10px] py-2 px-5 font-semibold"
        type="button"
        @click="updateDiscount()"
      >
        {{ $t('Apply') }}
      </button>
    </div>
    <div
      v-if="discountErrorCode !== undefined"
      class="mt-4 rounded-[10px] py-2 px-4 text-center text-[13px]"
      :class="discountErrorCode === DISCOUNT_RETURN_CODE.SUCCESS ? 'bg-[#D2FCBE] text-secondary' : 'bg-red-200 text-red-500'"
    >
      <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.SUCCESS">{{ $t('Discount activated') }}</span>
      <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.INVALID">{{ $t('Discount code is invalid') }}</span>
      <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.CONDITION_NOT_MET">{{ $t("Discount conditions have not been met") }}. {{ $t("Do you want to add more items?") }}</span>
      <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.USING_OTHER_DISCOUNT">{{ $t("Order has been discounted to the maximum, no further discount codes can be applied.") }}</span>
    </div>
    <lazy-common-modal
      :model-value="isShowModalDiscountList"
      :title="$t('Select a discount code')"
      modal-class="py-2 px-5 md:py-4 md:px-10 <md:w-full <lg:(w-[80vw]) lg:w-[798px] max-h-100vh max-h-[80vh]"
      modal-container-class="z-9999"
      @close-modal="isShowModalDiscountList = false"
    >
      <default-common-promotions-list
        v-if="discountList.length"
        :promotions-list="discountList"
        :is-checkout-page="true"
        class="mt-4 mb-8"
        @update-discount-apply="updateDiscount"
      />
    </lazy-common-modal>
  </div>
</template>
