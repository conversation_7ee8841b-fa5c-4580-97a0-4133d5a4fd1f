export function useThankyouTableItem(order: Order) {
  const products = computed(() => {
    return order.products.map((product) => {
      return { ...product, options: JSON.parse(product.options) }
    })
  })

  function getOptionsText(options: Record<string, string>) {
    return Object.entries(options).map(([_, value]) => `${value}`).join(', ')
  }

  return {
    products,
    getOptionsText,
  }
}
