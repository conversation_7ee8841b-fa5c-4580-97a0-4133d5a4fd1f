<script lang="ts" setup>
import type { PropType } from 'vue'

const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  index: {
    default: undefined,
    type: Number
  }
})

const { $formatPrice } = useNuxtApp()

const localePath = useLocalePath()
const $router = useRouter()
const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `/${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `?color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))

function goToPage() {
  $router.push(localePath(campaignUrl.value || '/'))
}

function productInfo() {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}

const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))
const showDiscount = computed(() => productOldPrice.value > productPrice.value && storeInfo().store_type !== 'google_ads')
</script>

<template>
  <div class="product-list-item rounded-[10px]" data-test-id="product-list-item">
    <div v-click-not-drag="goToPage" class="overflow-hidden relative">
      <nuxt-link :to="localePath(campaignUrl)">
        <div
          class="w-full pt-[125%]"
        >
          <common-image
            :image="{
              path: product?.thumb_url,
              type: 'list',
              color
            }"
            img-class="transition-transform duration-200 absolute top-0 left-0"
            :alt="product?.name"
            :title="productTitle"
            :fetchpriority="(index === 0) ? 'high' : ''"
          />
        </div>
      </nuxt-link>
    </div>
    <div class="p-2 md:p-4 space-y-2 givehug__background__description">
      <h5 v-click-not-drag="goToPage" class="text-overflow-hidden font-medium text-sm" :title="productTitle">
        <nuxt-link :to="localePath(campaignUrl)">
          {{ productTitle }}
        </nuxt-link>
      </h5>
      <div class="flex justify-between">
        <h6 class="block flex flex-wrap gap-x-2 items-end">
          <client-only>
            <span class="text-[13px] font-400" :class="{ 'text-secondary': showDiscount }">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
            <del v-if="showDiscount" class="text-[11px] text-gray-500">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
          </client-only>
        </h6>
        <div
          v-if="showDiscount" class="text-xs font-medium rounded-full px-2.5 py-0.5"
          style="background-color: rgba(210, 252, 190, 1); text-wrap: nowrap;"
        >
          {{ $formatPrice(productOldPrice - productPrice, product?.currency_code) }} Off
        </div>
      </div>
    </div>
    <default-common-personalize-tag v-if="product?.personalized" />
  </div>
</template>
