<script setup lang="ts">
interface Props {
  quantity: number
  minQuantity?: number
  maxQuantity?: number
  variant?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'normal',
  minQuantity: 1,
  maxQuantity: 50
})

const emit = defineEmits<{
  (e: 'updateQuantity', quantity: number): void
}>()

// Default values
const min = props.minQuantity || 1
const max = props.maxQuantity || 50

// Track quantity change event
function trackQuantityChange() {
  useTracking().customTracking({
    event: 'interact',
    data: {
      action: 'change_quantity'
    }
  })
}

// Decrement quantity
function decreaseQuantity() {
  if (props.quantity > min) {
    trackQuantityChange()
    emit('updateQuantity', props.quantity - 1)
  }
}

// Increment quantity
function increaseQuantity() {
  if (props.quantity < max) {
    trackQuantityChange()
    emit('updateQuantity', props.quantity + 1)
  }
}
</script>

<template>
  <div class="flex items-center bg-[#f3f3f3] rounded-[10px]" style="width: fit-content;">
    <!-- Minus button -->
    <button
      class="flex items-center justify-center text-lg hover__scale2 hover:text-primary"
      :class="{
        'opacity-50 cursor-not-allowed': quantity <= min,
        'w-12 h-12': variant === 'normal',
        'w-8 h-8': variant === 'xs'
      }"
      data-test-id="qty-decrease-btn"
      @click="decreaseQuantity"
    >
      <svg class="w-2.5 h-2.5" viewBox="0 0 10 2" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 0.5V1.5H0V0.5H10Z" fill="currentColor" />
      </svg>
    </button>

    <!-- Quantity display -->
    <div
      class="text-center font-medium"
      :class="{
        'w-14': variant === 'normal',
        'w-8': variant === 'xs'
      }"
      data-test-id="qty-value"
    >
      {{ quantity }}
    </div>

    <!-- Plus button -->
    <button
      class="flex items-center justify-center text-lg hover__scale2 hover:text-primary"
      :class="{
        'opacity-50 cursor-not-allowed': quantity >= max,
        'w-12 h-12': variant === 'normal',
        'w-8 h-8': variant === 'xs'
      }"
      data-test-id="qty-increase-btn"
      @click="increaseQuantity"
    >
      <svg class="w-2.5 h-2.5" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 4.5V5.5H0V4.5H10Z" fill="currentColor" />
        <path d="M5.5 10H4.5L4.5 0L5.5 4.37115e-08L5.5 10Z" fill="currentColor" />
      </svg>
    </button>
  </div>
</template>
