<script lang="ts" setup>
const route = useRoute()
const localePath = useLocalePath()

// Extract path segments from current route path
const pathSegments = computed(() => {
  // Remove initial slash and split by slashes
  const segments = route.path.replace(/^\//, '').split('/')
  return segments.filter(segment => segment !== '')
})

// Generate breadcrumb items with formatted names and paths
const breadcrumbs = computed(() => {
  const result = [
    // Home breadcrumb
    { name: 'HomePage', path: localePath('/') }
  ]

  // Add each segment as a breadcrumb
  let currentPath = ''
  for (const segment of pathSegments.value) {
    currentPath += `/${segment}`

    // Format segment name (capitalize and replace hyphens with spaces)
    const name = $slugToName(segment)

    result.push({
      name,
      path: localePath(currentPath)
    })
  }

  return result
})
</script>

<template>
  <div v-if="pathSegments.length > 0" class="flex items-center justify-center text-[#595959] text-[13px]">
    <template v-for="(item, index) in breadcrumbs" :key="index">
      <!-- Breadcrumb item -->
      <NuxtLink
        :to="item.path"
        class="hover:text-primary transition underline"
      >
        {{ item.name }}
      </NuxtLink>

      <!-- Separator (except for last item) -->
      <span v-if="index < breadcrumbs.length - 1" class="mx-2">
        &gt;
      </span>
    </template>
  </div>
</template>
