<script setup lang="ts">
interface StepperProps {
  currentStep?: number
  isFade?: boolean
}

// Use withDefaults for type-based props with defaults
withDefaults(defineProps<StepperProps>(), {
  currentStep: 0
})

const steps = ['Your Cart', 'Checkout', 'Complete']

const highlightLineMap = [
  [
    [true, false],
    [false, false]
  ],
  [
    [true, true],
    [false, false]
  ],
  [
    [true, true],
    [true, true]
  ]
]
</script>

<template>
  <div class="mx-auto relative pb-6" style="width: fit-content;">
    <!-- Step Line -->
    <div class="flex items-center justify-between">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="flex items-center"
      >
        <!-- Content -->
        <div
          class="relative"
        >
          <!-- Step circle -->
          <div
            class="w-[14px] h-[14px] rounded-full border-2 flex items-center justify-center z-0"
            :class="
              currentStep >= index
                ? (isFade ? 'border-[#F3F3F3]' : 'border-primary')
                : 'bg-[#F3F3F3] border-transparent'
            "
          />

          <!-- Step text -->
          <div
            class="absolute top-5 right-[-200%] -translate-x-1/2 text-xs sm:text-sm"
            :class="
              currentStep >= index
                ? 'text-black font-semibold'
                : 'text-[#B2B2B2]'
            "
            style="text-wrap: nowrap;"
          >
            {{ step }}
          </div>
        </div>

        <!-- Connector line -->
        <div
          v-if="index !== steps.length - 1"
          class="top-3.5 w-12 sm:w-16 md:w-22 h-0.5"
          :class="highlightLineMap[currentStep][index][0] && !isFade ? 'bg-primary' : 'bg-[#F3F3F3]'"
        />
        <div
          v-if="index !== steps.length - 1"
          class="top-3.5 w-12 sm:w-16 md:w-22 h-0.5"
          :class="highlightLineMap[currentStep][index][1] && !isFade ? 'bg-primary' : 'bg-[#F3F3F3]'"
        />
      </div>
    </div>
  </div>
</template>
