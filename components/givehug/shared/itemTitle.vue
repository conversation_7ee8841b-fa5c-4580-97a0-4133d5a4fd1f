<script setup lang="ts">
defineProps<{
  productUrl: string
  campaignTitle: string
  productName: string
  optionList: OptionsList
  currentOptions: Record<string, string>
}>()

const localePath = useLocalePath()
</script>

<template>
  <div class="w-full overflow-hidden max-w-full flex-grow-0">
    <nuxt-link
      :to="localePath(productUrl)"
      class="w-full block"
    >
      <h5 class="text-sm line-clamp-1" :title="campaignTitle">
        {{ campaignTitle }}
      </h5>
      <GivehugSharedItemSubTitle
        :product-name="productName"
        :option-list="optionList"
        :current-options="currentOptions"
      />
    </nuxt-link>
  </div>
</template>
