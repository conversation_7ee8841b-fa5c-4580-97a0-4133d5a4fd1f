<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

const { products } = defineProps({
  products: {
    default: (): Array<Product> => [],
    type: Array<Product>
  },
  title: {
    default: undefined,
    type: String
  },
  titleClass: {
    default: undefined,
    type: String
  },

  forceStaticGrid: {
    default: false,
    type: Boolean,
  },
  staticGrid: {
    default: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4',
    type: String,
  },
  splideSettings: {
    default: productSplideSetting,
    type: Object,
  },
  splideSlideClass: {
    default: 'p-1',
    type: String,
  },
  splideHideArrowOnEnds: {
    default: false,
    type: Boolean,
  },
})

const $viewport = useViewport()
const itemsToShow = ref<number>($viewport.isGreaterOrEquals(VIEWPORT.desktop) ? 4 : $viewport.isGreaterOrEquals(VIEWPORT.tablet) ? 3 : 4)
watch($viewport.breakpoint, () => {
  itemsToShow.value = $viewport.isGreaterOrEquals(VIEWPORT.desktop) ? 4 : $viewport.isGreaterOrEquals(VIEWPORT.tablet) ? 3 : 4
})
</script>

<template>
  <div>
    <h3 v-if="title" :class="titleClass">
      {{ $t(title) }}
    </h3>
    <div
      v-if="forceStaticGrid || (products.length <= itemsToShow)"
      :key="itemsToShow"
      class="grid justify-center"
      :class="staticGrid"
    >
      <div v-for="(product, index) in products" :key="index">
        <slot :product="product" :index="index" class="inline-block" />
      </div>
    </div>

    <Splide
      v-else
      :options="splideSettings"
      :extensions="splideExtensions"
      :class="{
        'arrow-on-ends': splideHideArrowOnEnds,
      }"
    >
      <SplideSlide v-for="(product, index) in products" :key="index" :class="splideSlideClass">
        <slot :product="product" :index="index" />
      </SplideSlide>
    </Splide>
  </div>
</template>
