<script lang="ts" setup>
import type { PropType } from 'vue'

const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  index: {
    default: undefined,
    type: Number
  }
})

const { $formatPrice } = useNuxtApp()

const localePath = useLocalePath()
const $router = useRouter()
const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `/${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `?color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))
const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))
const showDiscount = computed(() => productOldPrice.value > productPrice.value && storeInfo().store_type !== 'google_ads')

function goToPage() {
  $router.push(localePath(campaignUrl.value || '/'))
}

function productInfo() {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}
</script>

<template>
  <div
    class="relative product-list-item select-none overflow-hidden w-full rounded-[10px] cursor-pointer group"
    data-test-id="product-list-item"
  >
    <div v-click-not-drag="goToPage" class="overflow-hidden relative">
      <nuxt-link :to="localePath(campaignUrl)">
        <div class="w-full pt-[100%] aspect-square">
          <common-image
            :image="{
              path: product?.thumb_url,
              type: 'list',
              color
            } "
            img-class="transition-transform duration-200 absolute top-0 left-0 aspect-square" :alt="product?.name"
            :title="productTitle" :fetchpriority="(index === 0) ? 'high' : ''"
          />
        </div>
      </nuxt-link>
    </div>
    <div class="p-2.25 sm:p-3 md:p-4 md:space-y-1 givehug__background__description">
      <div class="flex items-center justify-between">
        <h6 class="block flex flex-wrap gap-x-0.5 sm:gap-x-1 md:gap-x-1.5 items-center">
          <client-only>
            <span class="text-secondary text-xs sm:text-lg lg:text-xl font-medium">{{ $formatPrice(productPrice, product?.currency_code)
            }}</span>
          </client-only>
          <del v-if="showDiscount" class="text-[9px] <md:mt-0.5 sm:text-[11px] text-gray-500">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
        </h6>
        <div
          v-if="showDiscount" class="text-[10px] sm:text-[11px] leading-4 font-medium rounded-full px-1 sm:px-1.5 md:px-2.5 lg:px-2 xl:px-2.5 py-0.5"
          style="background-color: rgba(210, 252, 190, 1); text-wrap: nowrap;"
        >
          <span class="inline-block sm:hidden">-</span> {{ $formatPrice(productOldPrice - productPrice, product?.currency_code) }} <span class="hidden sm:inline-block">Off</span>
        </div>
        <GivehugCommonPersonalizeTag v-if="product?.personalized && !showDiscount" />
      </div>
      <h5 v-click-not-drag="goToPage" class="text-overflow-hidden text-xs font-medium" :title="productTitle">
        <nuxt-link :to="localePath(campaignUrl)">
          {{ productTitle }}
        </nuxt-link>
      </h5>
    </div>
  </div>
</template>
