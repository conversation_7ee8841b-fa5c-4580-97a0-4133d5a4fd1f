<script setup lang="ts">
const props = defineProps<{
  productName: string
  optionList: OptionsList
  currentOptions: Record<string, string>
}>()
</script>

<template>
  <h5 class="text-sm font-medium w-full overflow-hidden" :title="productName">
    {{ productName }}&nbsp;
    <template v-for="key in Object.keys(optionList)" :key="key">
      <template v-if="!currentOptions[key].startsWith('__')">
        <span class="font-light opacity-30">-</span>
        <span class="uppercase">&nbsp;{{ currentOptions[key] }}&nbsp;</span>
      </template>
    </template>
  </h5>
</template>
