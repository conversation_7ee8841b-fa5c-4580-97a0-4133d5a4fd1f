<script setup lang="ts">
import CommonDropdown from '~/components/givehug/common/dropdown.vue'
import IconChecked from '~/components/givehug/shared/IconChecked.vue'

interface Props {
  list: Array<DropdownItemType>
  csKey?: string
  placeholder?: string
}

withDefaults(defineProps<Props>(), {
  placeholder: 'Select an option',
  csKey: 'option'
})

const current = defineModel<DropdownItemType>('current')
</script>

<template>
  <CommonDropdown
    btn-class="capitalize text-overflow-hidden w-full text-sm pl-2 py-1 font-semibold !gap-5"
    dropdown-class="md:min-w-full"
    data-test-id="cart-item-option"
    :data-test-prop="csKey"
  >
    <span class="w-full text-left py-1 pr-3 pl-1">{{ current?.label || placeholder }}</span>
    <template #content>
      <div
        v-for="(optionItem, optionIndex) in list"
        :key="optionIndex"
        :sp-action="`change_${csKey}`"
        class="capitalize py-1 px-8 text-overflow-hidden hover:bg-gray-200 flex items-center relative"
        :data-test-id="`cart-item-option-change-${csKey}`"
        @click="current = optionItem"
      >
        <IconChecked v-if="optionItem.value === current?.value" class="w-2.5 h-2.5 mr-2 absolute left-4" />{{ optionItem.label }}
      </div>
    </template>
  </CommonDropdown>
</template>
