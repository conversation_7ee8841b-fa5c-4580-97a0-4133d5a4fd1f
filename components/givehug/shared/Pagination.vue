<script setup>
import { computed } from 'vue'

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  maxVisiblePages: {
    type: Number,
    default: 5
  },
  shortMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:currentPage'])

const pages = computed(() => {
  // Short mode: only show current page to current + 4
  if (props.shortMode) {
    const start = props.currentPage
    const end = Math.min(props.currentPage + 4, props.totalPages)
    return Array.from({ length: end - start + 1 }, (_, i) => start + i)
  }

  if (props.totalPages <= props.maxVisiblePages) {
    // If we have fewer pages than the maximum visible, show all pages
    return Array.from({ length: props.totalPages }, (_, i) => i + 1)
  }

  // Always include first page, last page, current page, and pages adjacent to current page
  const pageNumbers = new Set([1, props.totalPages, props.currentPage])

  // Add one page before and after current page if they exist
  if (props.currentPage > 1) pageNumbers.add(props.currentPage - 1)
  if (props.currentPage < props.totalPages) pageNumbers.add(props.currentPage + 1)

  // Convert to array and sort
  const result = [...pageNumbers].sort((a, b) => a - b)

  // Now add ellipsis indicators where there are gaps
  const resultWithEllipsis = []
  for (let i = 0; i < result.length; i++) {
    resultWithEllipsis.push(result[i])

    // If there's a gap between this and the next number, add ellipsis
    if (i < result.length - 1 && result[i + 1] - result[i] > 1) {
      resultWithEllipsis.push('...')
    }
  }

  return resultWithEllipsis
})

const handlePageChange = (page) => {
  if (page >= 1 && page <= props.totalPages) {
    emit('update:currentPage', page)
  }
}
</script>

<template>
  <nav class="flex items-center gap-2.5">
    <!-- Previous page button -->
    <button
      class="text-[#222222] w-10 h-10 rounded-full flex items-center justify-center bg__foreground transition hover:bg-[#E2E2E2]"
      :disabled="currentPage === 1"
      @click="handlePageChange(currentPage - 1)"
    >

      <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_189_560)">
        <path d="M3.37852 9.44035C3.2301 9.5888 3.14673 9.79013 3.14673 10.0001C3.14673 10.21 3.2301 10.4113 3.37852 10.5598L7.85698 15.0382C7.93001 15.1138 8.01736 15.1741 8.11395 15.2156C8.21053 15.2571 8.31442 15.279 8.41953 15.2799C8.52465 15.2808 8.6289 15.2608 8.72619 15.221C8.82348 15.1811 8.91187 15.1224 8.98621 15.048C9.06054 14.9737 9.11932 14.8853 9.15913 14.788C9.19893 14.6907 9.21896 14.5865 9.21805 14.4814C9.21714 14.3762 9.1953 14.2724 9.15381 14.1758C9.11232 14.0792 9.052 13.9918 8.97639 13.9188L5.84931 10.7917H16.3334C16.5433 10.7917 16.7447 10.7083 16.8931 10.5598C17.0416 10.4114 17.125 10.21 17.125 10.0001C17.125 9.79009 17.0416 9.58873 16.8931 9.44026C16.7447 9.29179 16.5433 9.20839 16.3334 9.20839H5.84931L8.97639 6.0813C9.1206 5.93199 9.2004 5.73202 9.19859 5.52444C9.19679 5.31687 9.11353 5.11831 8.96675 4.97153C8.81997 4.82475 8.62141 4.74149 8.41384 4.73969C8.20626 4.73788 8.00629 4.81768 7.85698 4.96189L3.37852 9.44035Z" fill="currentColor"/>
        </g>
        <defs>
        <clipPath id="clip0_189_560">
        <rect width="19" height="19" fill="white" transform="matrix(0 -1 1 0 0.5 19.5)"/>
        </clipPath>
        </defs>
      </svg>

    </button>

    <!-- Page number buttons with ellipsis support -->
    <template v-for="(page, index) in pages" :key="index">
      <button
        v-if="page !== '...'"
        :class="[
          'w-10 h-10 rounded-full flex items-center justify-center transition hover:bg-[#E2E2E2]',
          page === currentPage ? 'bg-[#E2E2E2]' : 'bg__foreground'
        ]"
        @click="handlePageChange(page)"
      >
        {{ page }}
      </button>
      <span
        v-else
        class="w-10 flex items-center justify-center"
      >
        {{ page }}
      </span>
    </template>

    <!-- Next page button -->
    <button
      class="text-[#222222] w-10 h-10 rounded-full flex items-center justify-center bg__foreground transition hover:bg-[#E2E2E2]"
      :disabled="currentPage === totalPages"
      @click="handlePageChange(currentPage + 1)"
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_189_555)">
        <path d="M16.6215 10.5597C16.7699 10.4112 16.8533 10.2099 16.8533 9.99995C16.8533 9.79002 16.7699 9.5887 16.6215 9.44024L12.143 4.96178C12.07 4.88617 11.9826 4.82586 11.8861 4.78437C11.7895 4.74288 11.6856 4.72104 11.5805 4.72012C11.4753 4.71921 11.3711 4.73924 11.2738 4.77905C11.1765 4.81885 11.0881 4.87764 11.0138 4.95197C10.9395 5.0263 10.8807 5.11469 10.8409 5.21198C10.8011 5.30928 10.781 5.41352 10.7819 5.51864C10.7829 5.62376 10.8047 5.72764 10.8462 5.82422C10.8877 5.92081 10.948 6.00817 11.0236 6.0812L14.1507 9.20828H3.66665C3.45669 9.20828 3.25532 9.29169 3.10686 9.44015C2.95839 9.58862 2.87498 9.78998 2.87498 9.99995C2.87498 10.2099 2.95839 10.4113 3.10686 10.5597C3.25532 10.7082 3.45669 10.7916 3.66665 10.7916H14.1507L11.0236 13.9187C10.8794 14.068 10.7996 14.268 10.8014 14.4756C10.8032 14.6831 10.8865 14.8817 11.0333 15.0285C11.18 15.1753 11.3786 15.2585 11.5862 15.2603C11.7937 15.2621 11.9937 15.1823 12.143 15.0381L16.6215 10.5597Z" fill="currentColor"/>
        </g>
        <defs>
        <clipPath id="clip0_189_555">
        <rect width="19" height="19" fill="white" transform="matrix(0 1 -1 0 19.5 0.5)"/>
        </clipPath>
        </defs>
      </svg>
    </button>
  </nav>
</template>
