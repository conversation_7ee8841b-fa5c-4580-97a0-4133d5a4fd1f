<script lang="ts" setup>
import { Splide, SplideSlide, SplideTrack } from '@splidejs/vue-splide'
import ProductItem from './carouselItem.vue'

const props = defineProps({
  products: {
    type: Array as PropType<Product[]>,
    required: true
  },
  maxItems: {
    type: Number,
    default: 6
  },
  title: {
    type: String,
    default: null
  }
})

const $viewport = useViewport()
const itemsToShow = ref(props.maxItems)
watch($viewport.breakpoint, () => {
  if ($viewport.isGreaterOrEquals(1550)) {
    itemsToShow.value = props.maxItems
  }
  else if ($viewport.isGreaterOrEquals(1225)) {
    itemsToShow.value = 5
  }
  else if ($viewport.isGreaterOrEquals(950)) {
    itemsToShow.value = 4
  }
  else if ($viewport.isGreaterOrEquals(690)) {
    itemsToShow.value = 3
  }
})

const splideSettings = computed(() => {
  return {
    ...productSplideSetting,
    breakpoints: {
      690: {
        perPage: 1,
        grid: {
          rows: 2,
          cols: 2,
          gap: {
            row: '0.5rem',
            col: '1rem'
          }
        }
      },
      950: {
        perPage: 3,
        gap: '1rem'
      },
      1225: {
        perPage: 4,
        gap: '1rem'
      },
      1550: {
        perPage: 5,
        gap: '1rem'
      }
    },
    perPage: itemsToShow.value
  }
})
</script>

<template>
  <div>
    <div v-if="products.length <= itemsToShow">
      <div v-if="title" class="font-medium text-xl mb-4">
        {{ title }}
      </div>
      <div
        :key="itemsToShow"
        class="grid justify-center grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
      >
        <div v-for="(product, index) in products" :key="index">
          <ProductItem :product="product" :index="index" class="inline-block" />
        </div>
      </div>
    </div>

    <Splide
      v-else
      :has-track="false"
      :options="splideSettings"
      :extensions="splideExtensions"
      :class="{
        'arrow-on-ends': false
      }"
    >
      <div class="flex items-center justify-between mb-4">
        <div v-if="title" class="font-medium text-xl">
          {{ title }}
        </div>
        <div class="splide__arrows flex gap-2">
          <button class="splide__arrow splide__arrow--prev w-10 h-10 flex items-center justify-center rounded-full bg__foreground hover:bg-[#E2E2E2] trans__time">
            <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.49566 16C7.62707 16.0008 7.75735 15.972 7.87901 15.9152C8.00067 15.8583 8.11132 15.7745 8.20463 15.6686C8.29822 15.5624 8.37251 15.4361 8.4232 15.2968C8.4739 15.1576 8.5 15.0083 8.5 14.8575C8.5 14.7066 8.4739 14.5573 8.4232 14.4181C8.37251 14.2788 8.29822 14.1525 8.20463 14.0463L2.9123 8.00233L8.20463 1.95839C8.39266 1.74325 8.4983 1.45145 8.4983 1.1472C8.4983 0.996545 8.47236 0.847368 8.42197 0.708184C8.37159 0.568999 8.29773 0.442533 8.20463 0.336006C8.11153 0.229479 8.001 0.144977 7.87935 0.0873254C7.75771 0.0296734 7.62733 -7.62916e-08 7.49566 -8.78024e-08C7.22974 -1.1105e-07 6.97472 0.120865 6.78669 0.336006L0.795369 7.19114C0.701777 7.29735 0.62749 7.42371 0.576795 7.56294C0.5261 7.70217 0.499999 7.8515 0.499999 8.00233C0.499999 8.15315 0.5261 8.30249 0.576795 8.44171C0.62749 8.58094 0.701777 8.70731 0.795369 8.81352L6.78668 15.6686C6.87999 15.7745 6.99064 15.8583 7.11231 15.9152C7.23397 15.972 7.36424 16.0008 7.49566 16Z" fill="#222222" />
            </svg>
          </button>
          <button class="splide__arrow splide__arrow--next w-10 h-10 flex items-center justify-center rounded-full hover:opacity-90 bg__foreground hover:bg-[#E2E2E2] trans__time">
            <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1.50434 2.00272e-05C1.37293 -0.00084877 1.24265 0.0279703 1.12099 0.0848274C0.999331 0.141685 0.888675 0.225462 0.79537 0.331352C0.701777 0.437564 0.627491 0.563928 0.576796 0.703155C0.526101 0.842381 0.5 0.991715 0.5 1.14254C0.5 1.29337 0.526101 1.4427 0.576796 1.58193C0.627491 1.72116 0.701777 1.84752 0.79537 1.95373L6.0877 7.99767L0.79537 14.0416C0.607339 14.2568 0.501704 14.5485 0.501704 14.8528C0.501704 15.0035 0.527638 15.1526 0.578025 15.2918C0.628413 15.431 0.702267 15.5575 0.79537 15.664C0.888474 15.7705 0.999003 15.855 1.12065 15.9127C1.24229 15.9703 1.37267 16 1.50434 16C1.77026 16 2.02528 15.8791 2.21331 15.664L8.20463 8.80886C8.29822 8.70265 8.37251 8.57629 8.4232 8.43706C8.4739 8.29783 8.5 8.1485 8.5 7.99767C8.5 7.84685 8.4739 7.69751 8.4232 7.55828C8.37251 7.41906 8.29822 7.29269 8.20463 7.18648L2.21331 0.331352C2.12001 0.225462 2.00935 0.141685 1.88769 0.0848274C1.76603 0.0279703 1.63576 -0.00084877 1.50434 2.00272e-05Z" fill="#222222" />
            </svg>
          </button>
        </div>
      </div>

      <SplideTrack>
        <SplideSlide v-for="(product, index) in products" :key="index">
          <ProductItem :product="product" :index="index" class="inline-block" />
        </SplideSlide>
      </SplideTrack>
    </Splide>
  </div>
</template>
