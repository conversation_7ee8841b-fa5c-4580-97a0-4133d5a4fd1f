<script lang="ts" setup>
import type { PropType } from 'vue'

const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  index: {
    default: undefined,
    type: Number
  }
})

const { $formatPrice } = useNuxtApp()

const localePath = useLocalePath()
const $router = useRouter()
const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `/${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `?color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))

function goToPage() {
  $router.push(localePath(campaignUrl.value || '/'))
}

function productInfo() {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}

const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))
const showDiscount = computed(() => productOldPrice.value > productPrice.value && storeInfo().store_type !== 'google_ads')
</script>

<template>
  <nuxt-link :to="localePath(campaignUrl)" class="block relative product-list-item select-none overflow-hidden w-full overflow-hidden rounded-[10px]" data-test-id="product-list-item">
    <div v-click-not-drag="goToPage" class="relative overflow-hidden">
      <div
        class="w-full pt-[100%]"
      >
        <common-image
          :image="{
            path: product?.thumb_url,
            type: 'list',
            color
          }"
          img-class="transition-transform duration-200 absolute top-0 left-0 aspect-square"
          :alt="product?.name"
          :title="productTitle"
          :fetchpriority="(index === 0) ? 'high' : ''"
        />
      </div>
    </div>
    <div class="p-3 md:p-4 md:space-y-1 transition givehug__background__description">
      <div class="flex justify-between items-center">
        <h6 class="block flex flex-wrap gap-x-1 md:gap-x-2 items-center <sm:justify-between <sm:w-full">
          <client-only>
            <span class="text-sm sm:text-base md:text-lg font-medium" :class="{ 'text-secondary': showDiscount }">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
            <del v-if="showDiscount" class="text-[11px] md:text-sm text-[#595959]">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
          </client-only>
        </h6>
        <div
          v-if="showDiscount" class="text-[11px] hidden sm:block md:text-xs font-semibold rounded-full px-1.5 md:px-2.5 py-0.5"
          style="background-color: rgba(210, 252, 190, 1); text-wrap: nowrap;"
        >
          <span class="sm:hidden">-</span>{{ $formatPrice(productOldPrice - productPrice, product?.currency_code) }} <span class="hidden sm:inline-block">Off</span>
        </div>
        <GivehugCommonPersonalizeTag v-if="product?.personalized && !showDiscount" />
      </div>
      <h5 v-click-not-drag="goToPage" class="text-overflow-hidden font-medium text-sm" :title="productTitle">
        {{ productTitle }}
      </h5>
    </div>
  </nuxt-link>
</template>
