<script setup lang="ts">
interface Props {
  products: Product[]
  currentProduct?: Product
  campaignSlug?: string
  currentOptions: { [key: string]: string }
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'updateProduct', productName: string): void
}>()

const getProductUrl = computed(() => {
  return (productName?: string) => {
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName,
      query: useRoute().query
    })
  }
})
</script>

<template>
  <div class="grid gap-2.5 grid-cols-[repeat(auto-fill,minmax(130px,1fr))]">
    <NuxtLink
      v-for="product in products"
      :key="product.id"
      :to="getProductUrl(product.name)"
      class="text-[13px] leading-4  h-[45px] rounded-[10px] flex items-center justify-center block"
      :class="{
        'bg-primary text-white': product.id === currentProduct?.id,
        'hover:text-primary bg-[#F3F3F3]': product.id !== currentProduct?.id
      }"
      @click="emit('updateProduct', product.name || '')"
    >
      {{ product.name?.replace('Standard', '') }}
    </NuxtLink>
  </div>
</template>
