<script lang="ts" setup>
// import type DefaultCampaignBundleBox from '~/components/default/campaign/bundleBox.vue'

import type DefaultCampaignModalAddToCart from '~/components/default/campaign/modalAddToCart.vue'
import type DefaultCampaignModalConfirmDesign from '~/components/default/campaign/modalConfirmDesign.vue'
import type DefaultCampaignModalSelectSize from '~/components/default/campaign/modalSelectSize.vue'

import { useCampaignStore } from '~~/store/campaign'
import { useCartStore } from '~~/store/cart'

import CampaignDescription from '~/components/givehug/campaign/description.vue'
import RelatedCollections from '~/components/givehug/campaign/relatedCollections.vue'
import Breadcrumb from '~/components/givehug/shared/breadcrumb.vue'
import CarouselRoot from '~/components/givehug/shared/CarouselRoot.vue'
import Quantity from '~/components/givehug/shared/quantity.vue'

import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/utils/constant'

// const bundleBox = ref<InstanceType<typeof DefaultCampaignBundleBox>>()

const props = defineProps({
  campaignSlug: {
    type: String,
    required: true
  },
  isModal: {
    default: false,
    type: Boolean
  }
})
const cartStore = useCartStore()
const $viewport = useViewport()
const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
// eslint-disable-next-line
let isAddAllToCart:Boolean = false

const modalAddToCart = ref<InstanceType<typeof DefaultCampaignModalAddToCart>>()
const modalSelectSize = ref<InstanceType<typeof DefaultCampaignModalSelectSize>>()
const modalConfirmDesign = ref<InstanceType<typeof DefaultCampaignModalConfirmDesign>>()

const {
  campaignId,
  userCampaignOption,
  campaignData,
  isShowProductListDropdown,
  // similarProducts,
  relatedProducts,
  relatedCartProducts,

  productStats,
  campaignPromotions,

  dataDescription,
  dataProductDetail,

  updateProduct,
  updateOption,
  checkOptions,
  resetData,
  getRelatedProduct
} = useCampaign(props.campaignSlug, props.isModal)

await resetData()

const {
  // bundleProduct,
  currentBundleProduct,
  // totalBundleDiscount,
  // saveBundleDiscount,
  getDataBundle,
  checkBundleProduct,
  addBundleProductToCart
  // refetchDataBundle
} = useCampaignBundle(campaignId, false, modalConfirmDesign, BUNDLE_DISCOUNT_VIEW_PLACE.CAMPAIGN_DETAIL)

const {
  personalizeCustom,
  personalizePB,
  // personalizeCustomOptions,

  isShowDesign,
  currentPersonalize,
  showingDesign,
  showDesignHandler,
  getDataCustomDesign,
  updatePersonalizeCustom,
  selectCustomDesign,
  checkPersonalize,
  handlePersonalizeError,
  setCurrentPersonalize
} = useCampaignPersonalize(campaignData, userCampaignOption, props.isModal)

const customOptions = computed(() => userCampaignOption.currentProduct?.template_custom_options ?? userCampaignOption.currentProduct?.custom_options ?? campaignData.options)
const commonOptions = computed(() => userCampaignOption.currentProduct?.common_options ?? campaignData.common_options)
const {
  // extraCustomFee,
  groupCustomOptionsQuantity,
  userCustomOptions,
  userCommonOptions,
  totalCustomOptionFee,
  // updateCustomOptions,
  // updateCommonOptions,
  // requiredValue,
  // updateGroupCustomOptionsQuantity,
  checkPersonalizeCustomOptions,
  resetCommonOption,
  resetOption
} = useCampaignPersonalizeCustomOptions(customOptions, props.isModal, campaignData?.personalized, userCampaignOption.currentProduct?.full_printed, commonOptions)

if (import.meta.client && campaignData.id && (campaignData.personalized === 1 || campaignData.personalized === 2)) {
  // fix error after refresh page. wait new version of nuxt
  setTimeout(async () => {
    await getDataCustomDesign()
  })
}

if (import.meta.client && campaignData.id && !props.isModal) {
  const intervalTracking = setInterval(() => {
    if (window.userActivity) {
      clearInterval(intervalTracking)
      getDataBundle()
    }
  }, 500)
}

/**
 * @param [isConfirmDesign] Passed all 'personalize' checks
 * @param isCheckout
 * @param isForceOpenModal
 */
async function addToCart(isConfirmDesign = false, isCheckout = false, isForceOpenModal = false) {
  userCampaignOption.isAddToCart = true // for personalize_input check state
  if (checkOptions()) { // check normal options
    if (userCampaignOption.optionError === 'size') {
      modalSelectSize.value!.isShowModal = true
    }
    else {
      uiManager().createPopup($i18n.t('Please choose {option}', { option: userCampaignOption.optionError }))
    }
    return
  }

  if (!isConfirmDesign) { // check personalize options
    let designUrl: string[] = []
    if (userCampaignOption.currentProduct?.personalized === 1 || userCampaignOption.currentProduct?.personalized === 2) {
      const value = await checkPersonalize()
      if (value.success) {
        designUrl = designUrl.concat(value.designDataUrl || [])
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }
    else if (userCampaignOption.currentProduct?.personalized === 3 || userCampaignOption.currentProduct?.full_printed === 5) {
      const value = checkPersonalizeCustomOptions()
      if (!value.success) {
        return handlePersonalizeError({
          ...value,
          product: userCampaignOption.currentProduct
        }, currentBundleProduct)
      }
    }

    if (isAddAllToCart) {
      const value = await checkBundleProduct(isForceOpenModal)
      if (value.success) {
        designUrl = designUrl.concat(value?.designDataUrl || [])
        currentBundleProduct.value = undefined
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }

    if (designUrl.length) {
      modalConfirmDesign.value!.isShowModal = designUrl
      return
    }
  }

  const cartItem = cartStore.addCartItem(campaignData, userCampaignOption, {
    totalCustomOptionFee: totalCustomOptionFee.value,
    userCustomOptions: userCommonOptions.concat(userCustomOptions),
    groupCustomOptionsQuantity: groupCustomOptionsQuantity.value + (userCommonOptions?.length || 0)
  }, true)

  const listPersonalize: { [key: string]: Personalize } = {}
  if ((campaignData.personalized === 1 || campaignData.personalized === 2) && userCampaignOption?.currentProduct?.personalizeList?.length) {
    listPersonalize[cartItem.id] = userCampaignOption?.currentProduct?.personalizeList[0]
  }

  if (isAddAllToCart) {
    const cartItemBundle = await addBundleProductToCart()
    if (cartItemBundle) {
      cartItemBundle.forEach((cartItem) => {
        if ((cartItem.personalized === 1 || cartItem.personalized === 2) && cartItem.product.personalizeList?.length) {
          listPersonalize[cartItem.id] = cartItem?.product?.personalizeList[0]
        }
      })
    }
  }

  if (Object.keys(listPersonalize).length) {
    uiManager().$patch({ isUploadFile: true })
    window.loadingUploadImage = getPersonalizeUpload(listPersonalize)
    window.loadingUploadImage.then((uploadUrl) => {
      uiManager().$patch({ isUploadFile: false })
      Object.keys(uploadUrl).forEach((key) => {
        if (uploadUrl[key]) {
          cartStore.updateCartItemByID(key, { thumb_url: uploadUrl[key] })
        }
      })
    })
  }

  if (isCheckout) {
    await createOrder()
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return
  }

  if ($viewport.isLessThan(VIEWPORT.tablet) || isAddAllToCart) {
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return useRouter().push(localePath('/cart'))
  }

  uiManager().toggleCartDrawler(true)
}

const title = computed(() => {
  const campaignName = campaignData.name
  const productName = userCampaignOption.currentProduct?.name
  const enableProductNameAfter = storeInfo().enable_product_name_after

  if (enableProductNameAfter) {
    return `${campaignName} ${productName}`
  }
  return campaignName
})

useHead({
  title: computed(() => `${title.value} | ${storeInfo().name}`)
})

onMounted(() => {
  getRelatedProduct()
})

function updateSelectProduct(productName: string) {
  updateProduct(productName)
  if (userCampaignOption.currentProduct?.full_printed === 5) {
    resetOption()
    resetCommonOption()
  }
}

const { campaignDescription, thumbnail, reviewSummary } = useCampaignSchema(campaignData, userCampaignOption)

const { isHiddenBottomButton } = useHiddenBottomButton()
</script>

<template>
  <main
    v-if="campaignData.status !== 'blocked'"
    :id="isModal ? 'modalCampaignPage' : 'campaignPage'"
    data-test-id="campaign-container"
    class="cs-container"
    :class="{ 'flex flex-col max-h-100vh md:max-h-80vh ': isModal }"
  >
    <CommonCampaignMetaData
      :campaign-data="campaignData"
      :campaign-description="campaignDescription"
      :thumbnail="thumbnail"
      :review-summary="reviewSummary"
      :user-campaign-option="userCampaignOption"
    />
    <Breadcrumb class="my-8 <md:hidden" />
    <div
      class="flex flex-wrap"
      :class="{ 'overflow-y-auto mb-20 overflow-x-hidden': isModal }"
    >
      <div
        class="w-full top-0 md:(w-1/2 pr-6 sticky h-max)"
        :class="{ 'lg:w-14/24 md:top-15': !isModal }"
      >
        <givehug-campaign-view-box
          v-if="userCampaignOption.currentProduct"
          :is-modal="isModal"
          :campaign-data="campaignData"
          :images-list="userCampaignOption.imagesList"
          :current-product="userCampaignOption.currentProduct"
          :color="userCampaignOption.optionListFull.color?.length > 1 ? userCampaignOption.currentOptions.color : userCampaignOption.optionListFull.color?.[0]"

          :personalize="currentPersonalize"
          :is-show-design="isShowDesign"

          @change-image="isShowDesign = false"
        />
        <common-control-custom-image
          v-if="currentPersonalize?.customImageList?.[0]"
          :key="currentPersonalize.personalizeKey"
          :class="{ invisible: !isShowDesign }"
          class="my-3"
          :custom-image-item="currentPersonalize?.customImageList[0]"
        />
        <lazy-givehug-campaign-product-review-section
          v-if="storeInfo().product_review_display !== 'disable'"
          class="mt-20 <md:hidden mb-14"
          :current-product="userCampaignOption.currentProduct"
        />
      </div>
      <div
        class="w-full md:w-1/2 md:pl-6"
        :class="{ 'lg:w-10/24': !isModal }"
      >
        <givehug-campaign-general-info
          :campaign-data="campaignData"
          :user-campaign-option="userCampaignOption"
          :total-custom-option-fee="totalCustomOptionFee"
          :product-stats="productStats"
        />

        <div class="mt-6 space-y-3">
          <div class="flex gap-2.5">
            <div class="font-medium uppercase text-sm">
              {{ $t('Type') }}
            </div>
            <div class="text-sm text-option">
              {{ userCampaignOption.currentProduct?.name?.replace('Standard', '') }}
            </div>
          </div>
          <givehug-campaign-product-list
            v-if="campaignData.products && campaignData.system_type !== 'custom'"
            :is-modal="isModal"
            :products="campaignData.products"
            :current-product="userCampaignOption.currentProduct"
            :is-dropdown-type="isShowProductListDropdown"
            :campaign-slug="campaignData.slug"
            :current-options="userCampaignOption.currentOptions"
            :is-custom-campaign="campaignData.system_type === 'custom'"
            @update-product="updateSelectProduct"
          />

          <givehug-campaign-product-list-for-custom-campaign
            v-if="campaignData.products && campaignData.system_type === 'custom'"
            :products="campaignData.products"
            :current-product="userCampaignOption.currentProduct"
            :campaign-slug="campaignData.slug"
            :current-options="userCampaignOption.currentOptions"
            @update-product="updateSelectProduct"
          />
        </div>

        <div class="mt-6">
          <givehug-campaign-option-list
            :is-modal="isModal"
            :option-list="userCampaignOption.optionList"
            :current-product="userCampaignOption.currentProduct"
            :campaign-slug="campaignData.slug"
            :current-options="userCampaignOption.currentOptions"
            :option-error="userCampaignOption.optionError"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'option_click'
              }
            })"
            @update-option="updateOption"
          />
        </div>
        <client-only>
          <!-- <lazy-givehug-campaign-personalize-custom-options
            v-if="userCampaignOption.currentProduct?.personalized === 0 && userCampaignOption.currentProduct && userCampaignOption.currentProduct?.custom_options"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="userCampaignOption.currentProduct.template_custom_options"
            :common-options="userCampaignOption.currentProduct.common_options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :user-custom-options="userCustomOptions"
            :extra-custom-fee="extraCustomFee"
            :current-options="userCampaignOption.currentOptions"
            @required-value="requiredValue"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
          /> -->
          <lazy-givehug-campaign-personalize-custom-root
            v-if="userCampaignOption.currentProduct?.personalized === 1"
            ref="personalizeCustom"
            :is-modal="isModal"
            :product="userCampaignOption.currentProduct"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            class="mt-6"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-personalize-custom="updatePersonalizeCustom"
            @select-custom-design="selectCustomDesign"
            @show-design="showDesignHandler"
          />

          <lazy-givehug-campaign-personalize-pb
            v-if="userCampaignOption.currentProduct?.personalized === 2"
            ref="personalizePB"
            :is-modal="isModal"
            class="mt-6"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @show-design="isShowDesign = true"
          />

          <!-- <lazy-givehug-campaign-personalize-custom-options
            v-if="(userCampaignOption.currentProduct?.personalized === 3 || userCampaignOption.currentProduct?.system_type === 'custom' || userCampaignOption.currentProduct?.system_type === 'mockup') && (campaignData.options || campaignData.common_options)"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="campaignData.options"
            :common-options="campaignData.common_options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :user-custom-options="userCustomOptions"
            :user-common-options="userCommonOptions"
            :extra-custom-fee="extraCustomFee"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
          /> -->

          <lazy-givehug-campaign-promotions-list
            v-if="campaignPromotions?.length && !storeInfo().disable_promotion"
            :promotions-list="campaignPromotions"
            class="mt-6"
          />
          <div class="mt-6 flex items-center justify-between">
            <p>
              <span class="font-semibold">{{ $t('Quantity') }} </span>
              <span v-if="userCampaignOption.currentVariant?.out_of_stock" class="text-red-500 ml-2 font-weight-500">
                {{ $t('Out of stock') }}
              </span>
            </p>
            <Quantity
              v-if="!userCampaignOption.currentVariant?.out_of_stock"
              class="<md:mt-2"
              :quantity="userCampaignOption.quantity"
              @update-quantity="userCampaignOption.quantity = $event"
            />
          </div>

          <!-- Add to cart button -->
          <div class="flex gap-1 mt-6">
            <div
              class="flex w-full transition-all"
              :class="{
                'p-1 border w-full bg-white z-1 bottom-fixed': (isModal || !isHiddenBottomButton)
              }"
            >
              <button
                v-if="isModal"
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock)"
                class="<md:hidden uppercase font-semibold btn-givehug text-xl w-full h-15"
                data-test-id="buy-it-now"
                @click="isAddAllToCart = false; addToCart(false, true)"
              >
                {{ $t('Buy it now') }}
              </button>
              <button
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock)"
                class="uppercase font-semibold btn-givehug text-xl w-full h-15"
                :class="isModal ? 'md:ml-0.5' : ''"
                dusk="add-to-cart-button"
                data-test-id="campaign-add-to-cart"
                @click="isAddAllToCart = false; addToCart()"
              >
                <common-loading-dot v-if="isLoading" />
                <span v-else>{{ $t('Add to cart') }}</span>
              </button>
            </div>
          </div>
        </client-only>

        <template v-if="!isModal">
          <lazy-givehug-campaign-guaranteed-checkout
            v-if="storeInfo().store_type !== 'google_ads' && storeInfo().show_payment_button"
            class="mt-6"
          />
          <client-only>
            <!-- <lazy-givehug-campaign-bundle-box
              v-if="bundleProduct?.length && !storeInfo().disable_promotion"
              ref="bundleBox"
              class="mt-3"

              :bundle-product="bundleProduct"
              :user-campaign-option="userCampaignOption"
              :current-bundle-product="currentBundleProduct"

              :total-bundle-discount="totalBundleDiscount"
              :save-bundle-discount="saveBundleDiscount"
              :total-custom-option-fee="totalCustomOptionFee"

              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'bundle_box'
                }
              })"
              @select-bundle-product="currentBundleProduct = $event ? shallowReactive($event) : false"
              @submit="isAddAllToCart = true; addToCart(false, false, bundleProduct[0].personalized === 3 || bundleProduct[0].personalized === 5)"
              @modal-submit="isAddAllToCart ? addToCart():''; currentBundleProduct = undefined"
              @disable-add-all-to-cart="isAddAllToCart = false"
              @reset-bundle-product="productIdExclude => refetchDataBundle([productIdExclude])"
            /> -->

            <CampaignDescription
              class="mt-6"
              :data-description="dataDescription"
              :data-product-detail="dataProductDetail"
              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'description_box'
                }
              })"
            />

            <div class="mt-6">
              <nuxt-link :to="`/report?campaign=${campaignData.slug}`" class="flex gap-1 hover:text-primary trans__time">
                <span>{{ $t('Report a policy violation') }}?</span>
              </nuxt-link>
            </div>

            <RelatedCollections
              v-if="campaignData.collections?.length && !storeInfo().disable_related_collection"
              :collections="campaignData.collections"
              class="mt-6"
            />
          </client-only>
        </template>
      </div>
    </div>
    <lazy-givehug-campaign-product-review-section
      v-if="storeInfo().product_review_display !== 'disable'"
      class="mt-8 md:hidden mb-14"
      :current-product="userCampaignOption.currentProduct"
    />
    <div class="flex flex-wrap justify-between items-center <md:hidden mt-10">
      <givehug-campaign-share-box />
    </div>
    <template v-if="!isModal">
      <client-only>
        <CarouselRoot v-if="relatedProducts?.length && !storeInfo().disable_related_product" class="mt-16" :products="relatedProducts" :title="$t(`Frequently bought together`)" :max-items="6" />
        <div class="mt-3 flex justify-center md:hidden">
          <givehug-campaign-share-box />
        </div>
      </client-only>
    </template>

    <client-only>
      <lazy-default-campaign-modal-add-to-cart
        ref="modalAddToCart"
        :related-cart="relatedCartProducts"
      />
      <lazy-default-campaign-modal-select-size
        ref="modalSelectSize"
        :current-product="userCampaignOption.currentProduct"
        :option-list="userCampaignOption.optionList"
        @open-size-guide="uiManager().updateSizeGuideData(userCampaignOption.currentProduct, true)"
        @select-size="updateOption({ key: 'size', value: $event }); addToCart()"
      />
      <lazy-givehug-campaign-modal-confirm-design
        ref="modalConfirmDesign"
        @confirm-design="addToCart(true)"
      />
    </client-only>
  </main>
  <main v-else class="py-10 h-60vh">
    <div class="mt-5 pt-5 text-3xl text-center">
      <h2> {{ $t('This campaign was taken down due to a content violation') }} </h2>
    </div>
  </main>
</template>
