<script lang="ts" setup>
const props = defineProps({
  products: {
    required: true,
    type: Object as PropType<Array<Product>>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  isDropdownType: {
    default: undefined,
    type: <PERSON><PERSON><PERSON>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  isModal: {
    default: false,
    type: Boolean
  },
  additionalProductSelectorBtnClass: {
    default: '',
    type: String
  },
  additionalProductSelectorClass: {
    default: '',
    type: String
  },
  isCustomCampaign: {
    default: false,
    type: <PERSON>olean
  }
})
const showListingProduct = ref(false)

const getProductUrl = computed(() => {
  return (productName?: string) => {
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName,
      query: useRoute().query
    })
  }
})

function getThumbUrl(product: Product) {
  if (props.isCustomCampaign) {
    const attributes = JSON.parse(product.attributes || '{}')
    return attributes.flat_thumb_url || product.thumb_url
  }
  return product.thumb_url
}
</script>

<template>
  <div
    v-if="isDropdownType"
    class="product-list"
  >
    <common-dropdown
      dropdown-id="productDropdownSelect"
      :btn-class="`btn-border w-full py-2 capitalize text-overflow-hidden ${additionalProductSelectorBtnClass}`"
      dropdown-class="w-full"
    >
      <span>{{ currentProduct?.name }}</span>
      <template #content>
        <div
          v-for="(product, index) in products"
          :key="index"
          sp-action="change_product"
          data-test-id="change-product"
          class="block px-3 py-1 btn-text capitalize text-overflow-hidden"
          :class="{
            'bg-primary !text-contrast': product.id === currentProduct?.id,
            [additionalProductSelectorClass]: true
          }"
          @click="product.id !== currentProduct?.id ? $emit('updateProduct', stringHelperToSlug(product.name)) : ''; showListingProduct = true"
        >
          {{ product.name }}
        </div>
      </template>
    </common-dropdown>
  </div>
  <div v-else class="product-list grid grid-cols-4 sm:grid-cols-6 md:grid-cols-4 lg:grid-cols-5 overflow-x-auto gap-2.5">
    <template v-if="isModal">
      <div
        v-for="(product, index) in products"
        :key="index"
        sp-action="change_product"
        data-test-id="change-product"
        class="h-[100px] w-[80px] min-w-[80px] m-1 cursor-pointer"
        :class="{
          'border border-primary': product.id === currentProduct?.id,
          'md:hidden': (index > 3) && !showListingProduct
        }"
        @click.prevent.stop="product.id !== currentProduct?.id ? $emit('updateProduct', stringHelperToSlug(product.name)) : ''; showListingProduct = true"
      >
        <common-image
          :image="{ path: product.thumb_url }"
          :alt="product.name"
        />
      </div>
    </template>
    <template v-else>
      <nuxt-link
        v-for="(product, index) in products"
        :key="index"
        :to="getProductUrl(product.name)"
        sp-action="change_product"
        data-test-id="change-product"
        class="product-item cursor-pointer overflow-hidden aspect-square border hover:opacity-100 transition-opacity duration-200"
        :class="{
          'border border-primary': product.id === currentProduct?.id,
          'opacity-50': product.id !== currentProduct?.id,
          'hidden': (index > 3) && !showListingProduct
        }"
        @click.prevent.stop="product.id !== currentProduct?.id ? $emit('updateProduct', stringHelperToSlug(product.name)) : ''; showListingProduct = true"
      >
        <common-image
          :image="{ path: getThumbUrl(product) }"
          :alt="product.name"
          img-class="h-full object-cover mx-auto"
        />
      </nuxt-link>
      <div
        v-if="products?.length && products?.length > 4"
        class="product-item cursor-pointer overflow-hidden aspect-square flex justify-center items-center text-3xl border cursor-pointer opacity-50 hover:opacity-100 transition-opacity duration-200"
        @click="showListingProduct = !showListingProduct"
      >
        <i :class="showListingProduct ? 'icon-sen-minus' : 'icon-sen-plus'" />
      </div>
    </template>
  </div>
</template>
