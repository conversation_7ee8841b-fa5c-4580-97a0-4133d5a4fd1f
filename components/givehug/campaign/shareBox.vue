<script lang="ts" setup>
const encodedUrl = computed(() => {
  let baseUrl = useRuntimeConfig().public.baseUrl

  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1)
  }

  return encodeURIComponent(baseUrl + useRoute().fullPath)
})
</script>

<template>
  <div id="share-box" class="gap-3 flex">
    <!-- Telegram -->
    <a :href="`https://t.me/share/url?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
      <svg class="text-social hover:text-[#55ACEE] trans__time" width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 0.453125C6.26613 0.453125 0 6.74023 0 14.5C0 22.2598 6.26613 28.5469 14 28.5469C21.7339 28.5469 28 22.2598 28 14.5C28 6.74023 21.7339 0.453125 14 0.453125ZM20.4919 10.0084C20.2831 12.2287 19.3685 17.6209 18.9056 20.1074C18.7081 21.1609 18.3242 21.5121 17.9516 21.5461C17.1387 21.6197 16.5234 21.008 15.7331 20.4869C14.5024 19.677 13.8024 19.1729 12.6113 18.3799C11.2282 17.468 12.1258 16.9639 12.9105 16.1426C13.1194 15.9273 16.6984 12.6592 16.7661 12.3646C16.7774 12.325 16.7831 12.1891 16.6984 12.1154C16.6137 12.0418 16.4952 12.0701 16.4105 12.0871C16.2863 12.1135 14.318 13.4182 10.5056 16.001C9.94866 16.3861 9.44247 16.573 8.9871 16.5617C8.48468 16.5504 7.525 16.2785 6.80806 16.0463C5.93306 15.7631 5.23306 15.6102 5.29516 15.123C5.32527 14.8701 5.67339 14.6114 6.33952 14.3471C10.4191 12.5648 13.1401 11.3885 14.5024 10.8184C18.3919 9.19844 19.1992 8.91523 19.7242 8.90391C19.8427 8.90391 20.0968 8.93223 20.2661 9.06816C20.3767 9.16566 20.4471 9.30093 20.4637 9.44766C20.4942 9.6329 20.5037 9.82101 20.4919 10.0084Z" fill="currentColor" />
      </svg>
    </a>

    <!-- Pinterest -->
    <a :href="`https://pinterest.com/pin/create/button/?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
      <svg class="text-social hover:text-[#E60023] trans__time" width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M28 14.5C28 22.2598 21.7339 28.5469 14 28.5469C12.5548 28.5469 11.1661 28.326 9.85645 27.9182C10.4266 26.9836 11.279 25.4543 11.5952 24.2365C11.7645 23.5795 12.4645 20.8947 12.4645 20.8947C12.9218 21.767 14.254 22.509 15.671 22.509C19.8935 22.509 22.9363 18.6121 22.9363 13.7693C22.9363 9.13047 19.1597 5.6584 14.3048 5.6584C8.26452 5.6584 5.05242 9.72519 5.05242 14.1602C5.05242 16.2219 6.14758 18.7877 7.89194 19.6033C8.15726 19.7279 8.29839 19.6713 8.36048 19.4164C8.40565 19.2238 8.64274 18.2666 8.75 17.8248C8.78387 17.6832 8.76694 17.5586 8.65403 17.4227C8.08387 16.7146 7.62097 15.4232 7.62097 14.2168C7.62097 11.1186 9.95806 8.12227 13.9435 8.12227C17.3815 8.12227 19.7919 10.4729 19.7919 13.8373C19.7919 17.6379 17.8782 20.2717 15.3887 20.2717C14.0169 20.2717 12.9839 19.1332 13.3169 17.7342C13.7121 16.0633 14.4742 14.2621 14.4742 13.0557C14.4742 11.9795 13.8984 11.0789 12.7016 11.0789C11.296 11.0789 10.1669 12.5346 10.1669 14.4887C10.1669 15.7348 10.5847 16.573 10.5847 16.573C10.5847 16.573 9.20161 22.4523 8.94758 23.5512C8.66532 24.7633 8.77823 26.4738 8.89677 27.584C3.69194 25.5393 0 20.4529 0 14.5C0 6.74023 6.26613 0.453125 14 0.453125C21.7339 0.453125 28 6.74023 28 14.5Z" fill="currentColor" />
      </svg>
    </a>

    <!-- X -->
    <a :href="`https://x.com/share?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
      <svg class="text-social hover:text-[#000000] trans__time" width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect y="0.5" width="28" height="28" rx="14" fill="currentColor" />
        <path d="M19.1747 6.5H21.9361L15.9048 13.2769L23 22.5H17.4459L13.0926 16.9077L8.11734 22.5H5.35202L11.8018 15.25L5 6.5H10.6949L14.6258 11.6115L19.1747 6.5ZM18.2047 20.8769H19.734L9.8618 8.03846H8.21904L18.2047 20.8769Z" fill="white" />
      </svg>
    </a>

    <!-- Facebook -->
    <a :href="`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
      <svg class="text-social hover:text-[#1877F2] trans__time" width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M28 14.5521C28 6.79053 21.7328 0.5 14 0.5C6.26719 0.5 0 6.79053 0 14.5521C0 21.1391 4.52266 26.6721 10.6203 28.1926V18.8446H7.73281V14.5521H10.6203V12.7023C10.6203 7.92129 12.775 5.70369 17.4563 5.70369C18.3422 5.70369 19.8734 5.87934 20.5023 6.05499V9.94129C20.1742 9.90835 19.6 9.8864 18.8836 9.8864C16.5867 9.8864 15.7008 10.7592 15.7008 13.0262V14.5521H20.2727L19.4852 18.8446H15.6953V28.5C22.6297 27.6602 28 21.7374 28 14.5521Z" fill="currentColor" />
      </svg>
    </a>
  </div>
</template>
