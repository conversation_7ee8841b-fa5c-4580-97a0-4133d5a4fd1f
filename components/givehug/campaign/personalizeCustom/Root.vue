<script lang="ts" setup>
import CustomImage from './CustomImage.vue'
import CustomText from './CustomText.vue'

const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  isAddToCart: {
    default: false,
    type: <PERSON><PERSON><PERSON>
  },
  isModal: {
    default: false,
    type: <PERSON><PERSON>an
  }
})
const $viewport = useViewport()
const currentCustomItem = shallowRef<CustomItem>()
const isOnSelectCustomDesign = ref(false)

resetCustomData()

watch(() => props.product && props.product.customItemList, resetCustomData)

function resetCustomData() {
  if (props.product?.customItemList?.length) {
    currentCustomItem.value = props.product?.customItemList[0]
  }
  else if (props.product?.customDesignType) {
    isOnSelectCustomDesign.value = true
  }
}

const editCustomImage = shallowRef<CustomImageItem>()
const isShowDesign = ref(false)

defineExpose({ currentCustomItem, isOnSelectCustomDesign, editCustomImage })
</script>

<template>
  <div class="flex items-end gap-2">
    <div class="w-full space-y-6">
      <CustomText
        v-if="product?.customTextList?.length"
        :text-list="product.customTextList"
        :is-modal="isModal"
        :is-add-to-cart="isAddToCart"
        @update-personalize-custom="(customText: CustomTextItem, value: string) => {
          $emit('updatePersonalizeCustom', customText, value)
        }"
        @show-design="$emit('showDesign', $event)"
        @update-design-query="$emit('updateDesignQuery', $event)"
      />
      <div v-if="product?.customDesignType">
        <div class="<md:hidden flex items-center">
          <span class="font-medium uppercase mr-1">{{ $t('Select design') }}</span>
          <i class="icon-sen-image-multiple-outline" />
        </div>
        <common-dropdown
          v-show="isOnSelectCustomDesign || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
          btn-class="w-full border text-left h-12 px-3 capitalize"
          dropdown-class="w-full"
        >
          <span>{{ product?.selectedCustomDesign }}</span>
          <template #content>
            <button
              v-for="item in product.customDesignList"
              :key="item"
              class="btn-text w-full capitalize text-left px-3 py-1"
              :class="{ 'bg-primary !text-contrast': product?.selectedCustomDesign === item }"
              @click="$emit('selectCustomDesign', product, item)"
            >
              {{ item }}
            </button>
          </template>
        </common-dropdown>
      </div>
      <CustomImage
        v-if="product?.customImageList?.length"
        :is-add-to-cart="isAddToCart"
        :is-modal="isModal"
        :custom-image-list="product.customImageList"
        @update-personalize-custom="(customImage: CustomImageItem, value: string) => {
          $emit('updatePersonalizeCustom', customImage, value)
        }"
        @edit-custom-image="editCustomImage = $event"
      />
    </div>
    <lazy-common-modal
      id="editCustomImage"
      v-model="editCustomImage"
      modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 pb-15"
      @close-modal=" editCustomImage?.personalize?.designCanvas?._onupdate()"
      @shown="isShowDesign = true"
      @hidden="isShowDesign = false"
    >
      <div class="max-h-[70vh] overflow-y-auto pr-2 -mr-2">
        <lazy-common-design-canvas
          v-if="isShowDesign"
          :custom-image="editCustomImage"
        />
      </div>

      <common-control-custom-image
        v-if="editCustomImage"
        class="my-5"
        :custom-image-item="editCustomImage"
      />

      <div class="p-1 bottom-fixed w-full bg-white z-1">
        <button
          id="saveEditDesign"
          class="uppercase font-bold btn-fill text-xl w-full h-12"
          @click="editCustomImage = undefined"
        >
          <span>{{ $t('Save') }}</span>
        </button>
      </div>
    </lazy-common-modal>
  </div>
</template>
