<script setup lang="ts">
defineProps<{
  isAddToCart: boolean
  isModal: boolean
  customImageList: CustomImageItem[]
}>()
</script>

<template>
  <div>
    <div v-if="isAddToCart && customImageList.find(item => !item.currentFileUploadUrl)" class="<md:hidden text-red-500">
      {{ $t('Add images to customize the product') }}
    </div>
    <div
      v-for="(customImage, index) in customImageList"
      :key="index"
    >
      <div class="flex items-center justify-between">
        <div class="<md:hidden flex items-center font-medium uppercase">
          {{ $t('Custom image') }}
        </div>
        <div
          class="flex items-center gap-1 cursor-pointer hover:text-primary trans__time"
          :title="$t('Adjust Print Space')"
          @click="$emit('editCustomImage', customImage)"
        >
          <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M4 1.5C4 0.946875 3.55312 0.5 3 0.5C2.44688 0.5 2 0.946875 2 1.5V2.5H1C0.446875 2.5 0 2.94688 0 3.5C0 4.05312 0.446875 4.5 1 4.5H2V12.5C2 13.6031 2.89687 14.5 4 14.5H11V12.5H4V1.5ZM12 15.5C12 16.0531 12.4469 16.5 13 16.5C13.5531 16.5 14 16.0531 14 15.5V14.5H15C15.5531 14.5 16 14.0531 16 13.5C16 12.9469 15.5531 12.5 15 12.5H14V4.5C14 3.39687 13.1031 2.5 12 2.5H5V4.5H12V15.5Z" fill="currentColor" />
          </svg>
          <div>Adjust Print Space</div>
        </div>
      </div>
      <givehug-shared-file-selector-personalize
        :id="`customImage_${isModal ? 'modal' : 'campaign'}_${index}`"
        :state="isAddToCart ? !!customImage.currentFileUploadUrl : undefined"
        :file-upload="customImage.currentFileUploadUrl"
        browse-text="Upload"
        :auto-upload="false"
        class="md:mt-2 w-full"
        @on-change="$emit('updatePersonalizeCustom', customImage, $event[0])"
      />
    </div>
  </div>
</template>
