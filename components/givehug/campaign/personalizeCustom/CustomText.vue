<script lang="ts" setup>
interface Props {
  textList: CustomTextItem[]
  isModal: boolean
  isAddToCart: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  (e: 'updatePersonalizeCustom', customText: CustomTextItem, value: string): void
  (e: 'showDesign', personalizeKey: string): void
  (e: 'updateDesignQuery', customText: CustomTextItem): void
}>()
</script>

<template>
  <div>
    <div v-if="isAddToCart && textList.find(item => !item.data.text)" class="<md:hidden text-red-500 mb-1">
      {{ $t('Write something to customize the product') }}
    </div>
    <div class="space-y-6">
      <givehug-common-input
        v-for="(customText, index) in textList"
        :id="`customText_${isModal ? 'modal' : 'campaign'}_${customText.data.name}`"
        :key="index"
        class="!space-y-3"
        input-class="!text-[13px] !leading-5 !text-[#595959]"
        :max-length="Number(customText.data.maxLength) || 16"
        :model-value="customText.data.text"
        :label="`${$t(customText.data.name?.replaceAll('_', ' ') || '')}`"
        :state="isAddToCart ? !!customText.data.text : undefined"
        :countable="{
          count: customText.data.text?.length || 0,
          maxCount: Number(customText.data.maxLength) || 16
        }"
        @update:model-value="$emit('updatePersonalizeCustom', customText, $event)"
        @focus="$emit('showDesign', customText.personalize.personalizeKey || '')"
        @blur="isModal ? '' : updateDesignQuery(customText)"
      />
    </div>
  </div>
</template>
