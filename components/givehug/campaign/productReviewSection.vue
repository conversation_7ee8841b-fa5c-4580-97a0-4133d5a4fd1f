<script lang="ts" setup>
import ProductRating from '~/components/givehug/campaign/ProductRating.vue'
import ProductReviewFilter from '~/components/givehug/campaign/productReviewFilter.vue'
import ReviewItem from '~/components/givehug/campaign/ReviewItem.vue'

const props = defineProps({
  currentProduct: {
    type: Object as PropType<Product>,
    default: undefined
  }
})

const currentProduct = toRef(() => props.currentProduct)

const {
  isLoadingReviews,
  productReviews,

  changePage,
  changeFilter
} = useCampaignReview(currentProduct)

function viewImage(imageList: { src: string, type: any }[], index: number) {
  uiManager().viewImage(imageList.map(item => item?.src), index)
}

const totalReviewPages = computed(() => {
  if (productReviews.value.reviews.last_page > 4) {
    const diff = productReviews.value.reviews.last_page - productReviews.value.reviews.current_page

    return productReviews.value.reviews.current_page + (diff > 4 ? 4 : diff)
  }
  return productReviews.value.reviews.last_page
})
</script>

<template>
  <div v-if="productReviews?.reviewSummary.summary?.review_count > 0 && productReviews?.reviews?.data?.length" id="productReviewBox">
    <div class="lg:flex justify-between items-center review-header">
      <div class="text-lg md:text-xl lg:text-2xl font-medium">
        {{ $t('Based on {count} customer reviews', { count: productReviews.reviewSummary.summary.review_count || 0 }) }}
      </div>
      <ProductRating
        class="<lg:mt-2"
        :rating="parseFloat(productReviews.reviewSummary.summary.average_rating as string)"
        :show-text="true"
      />
    </div>
    <ProductReviewFilter
      class="w-48 ml-auto mb-1 mt-8 <lg:hidden"
      :current="productReviews.filter"
      @update:current="changeFilter"
    />
    <div class="mt-4 relative">
      <div v-if="isLoadingReviews" class="absolute w-full h-full">
        <div class="flex w-full h-full justify-center items-center bg-[rgba(221,221,221,0.6)]">
          <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
        </div>
      </div>
      <template v-if="productReviews.reviews?.data?.length">
        <ReviewItem
          v-for="(review, index) in productReviews.reviews.data"
          :key="index"
          :review="review"
          @view-image="viewImage"
        />
      </template>
      <div v-else class="mt-6 <md:mt-2" align="center">
        {{ $t('There are no reviews yet') }}
      </div>
    </div>
    <GivehugSharedPagination
      v-if="productReviews.reviews.last_page > 1"
      class="mt-4 md:mt-10"
      :current-page="productReviews.reviews.current_page"
      :total-pages="totalReviewPages"
      :short-mode="true"
      @update:current-page="(pageNumber) => { changePage(pageNumber) }"
    />
    <GivehugCampaignPhotoFromReview
      v-if="productReviews.reviewSummary.files.length"
      class="mt-10"
      :images="productReviews.reviewSummary.files"
      @view-image="viewImage"
    />
  </div>
</template>
