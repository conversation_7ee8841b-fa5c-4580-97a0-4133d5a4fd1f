<script lang="ts" setup>
const props = defineProps({
  sizes: {
    type: Array as PropType<string[]>,
    required: true
  },
  selectedSize: {
    type: String,
    default: ''
  },
  isModal: {
    type: Boolean,
    default: false
  },
  hasError: {
    type: Boolean,
    default: false
  },
  campaignSlug: {
    type: String,
    default: ''
  },
  currentProduct: {
    type: Object as PropType<Product>,
    default: () => ({})
  }
})

const emit = defineEmits(['updateOption'])

const getOptionUrl = computed(() => {
  return (value: string) => {
    const newQuery = { ...useRoute().query }
    newQuery.size = value
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName: props.currentProduct?.name,
      query: newQuery
    })
  }
})

function handleSizeSelect(value: string) {
  if (value === props.selectedSize) {
    return ''
  }
  else {
    emit('updateOption', { key: 'size', value })
  }
}
</script>

<template>
  <div>
    <div
      class="w-full overflow-auto gap-2.5 grid grid-cols-[repeat(auto-fill,minmax(62px,1fr))]"
      :class="{
        'animate__animated animate__headShake': hasError
      }"
    >
      <template v-if="isModal">
        <button
          v-for="(size, index) in sizes"
          :key="`size-${index}`"
          @click="handleSizeSelect(size)"
        >
          <givehug-campaign-size-item :size="size" :active="size === selectedSize" />
        </button>
      </template>
      <template v-else>
        <nuxt-link
          v-for="(size, index) in sizes"
          :key="`size-${index}`"
          :to="getOptionUrl(size)"
          @click="handleSizeSelect(size)"
        >
          <givehug-campaign-size-item :size="size" :active="size === selectedSize" />
        </nuxt-link>
      </template>
    </div>
  </div>
</template>
