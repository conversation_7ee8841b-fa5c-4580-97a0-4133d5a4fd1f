<script lang="ts" setup>
defineProps({
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  campaignData: {
    default: undefined,
    type: Object as PropType<Campaign>
  },
  totalCustomOptionFee: {
    default: 0,
    type: Number
  },
  productStats: {
    default: undefined,
    type: Object as PropType<ProductStats>
  }
})

const { $formatPrice } = useNuxtApp()
</script>

<template>
  <div v-if="userCampaignOption && campaignData">
    <!-- <lazy-givehug-campaign-countdown
      v-if="storeInfo().store_type !== 'google_ads' && campaignData.show_countdown && (campaignData.end_time || campaignData.show_countdown > 1)"
      :show-countdown="campaignData.show_countdown"
      :end-time="campaignData.end_time"
    /> -->
    <div class="space-y-2">
      <div v-if="productStats?.add_to_cart || productStats?.visit" class="flex items-center text-[#D00739] text-[17px] font-medium">
        <div>
          <span v-if="productStats?.add_to_cart"> {{ $t('There are value people have this in cart right now', { value: productStats.add_to_cart }) }}.</span>
          <span v-else-if="productStats?.visit"> {{ $t('There are value people viewing this', { value: productStats.visit }) }}.</span>
        </div>
      </div>
      <div class="flex items-center">
        <span class="text-3xl font-semibold text-secondary" data-test-id="price">{{ $formatPrice((userCampaignOption?.currentPrice || 0) + totalCustomOptionFee, userCampaignOption?.currentProduct?.currency_code) }}</span>
        <del v-if="storeInfo().store_type !== 'google_ads' && !storeInfo().disable_pre_discount && userCampaignOption.currentOldPrice" class="ml-3 text-[#595959] text-[13px]">{{ $formatPrice((userCampaignOption.currentOldPrice || 0) + totalCustomOptionFee, userCampaignOption.currentProduct?.currency_code) }}</del>
        <div
          v-if="storeInfo().store_type !== 'google_ads' && !storeInfo().disable_pre_discount && userCampaignOption.currentOldPrice"
          class="font-semibold rounded-full px-2.5 py-[3px] ml-3 text-sm"
          style="background-color: rgba(210, 252, 190, 1); text-wrap: nowrap;"
        >
          {{ $formatPrice((userCampaignOption.currentOldPrice || 0) - (userCampaignOption?.currentPrice || 0), userCampaignOption.currentProduct?.currency_code) }} {{ $t('Off') }}
        </div>
      </div>
    </div>
    <div class="space-y-[7px] mt-6">
      <h6 class="font-medium text-sm">
        {{ campaignData.name }}
      </h6>
    </div>
  </div>
</template>
