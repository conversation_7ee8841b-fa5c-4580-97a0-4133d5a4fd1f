<script lang="ts" setup>
const props = defineProps({
  isModal: {
    default: false,
    type: Boolean
  },
  containerClass: {
    default: '',
    type: String
  }
})

const id = computed(() => {
  return props.isModal ? 'artwork_form_selector_modal' : 'artwork_form_selector'
})
</script>

<template>
  <div
    class="relative w-full transition-all"
    :class="{
      [containerClass]: true,
      'custom-opt-vari-position bg-white': !isModal
    }"
    @click="$emit('showDesign')"
  >
    <common-collapse
      :when="true"
      class="transition-default"
    >
      <div>
        <div :id="id" />
      </div>
    </common-collapse>
  </div>
</template>
