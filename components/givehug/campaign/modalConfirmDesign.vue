<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

const isShowModal = ref<Array<string>>()
defineExpose({
  isShowModal
})
const confirm = ref(true)
</script>

<template>
  <common-modal
    :model-value="isShowModal?.length"
    modal-class="w-[90%] md:max-w-[498px]  p-5 md:p-9 px-"
    :close-icon="false"
    @close-modal="isShowModal = []"
  >
    <Splide
      v-if="isShowModal?.length && isShowModal?.length >= 2"
      :options="splideSetting"
    >
      <SplideSlide v-for="(imgUrl, index) in isShowModal" :key="index">
        <div class="p-1">
          <img :src="imgUrl" alt="confirmDesign">
        </div>
      </SplideSlide>
    </Splide>
    <div v-else-if="isShowModal?.length" class="w-full center-flex">
      <common-image
        img-class="w-full"
        :image="{ path: isShowModal[0] }"
        alt="confirmDesign"
      />
    </div>
    <GivehugCommonCheckbox1
      id="confirmDesignCheckBox"
      v-model:value="confirm"
      class="my-6"
      :label="$t('I have reviewed my design and confirm that I have entered the text correctly')"
      label-classes="font-medium"
      checkbox-classes="mt-1"
    />

    <div class="grid grid-cols-2 gap-3">
      <button class="h-[45px] font-medium text-sm border border-black hover:bg-givehug-gray rounded-[10px] center-flex" @click="isShowModal = []">
        {{ $t('Cancel') }}
      </button>

      <button
        class="h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-[10px]"
        :disabled="!confirm"
        @click="$emit('confirmDesign'); isShowModal = []"
      >
        {{ $t('Add to cart') }}
      </button>
    </div>
  </common-modal>
</template>
