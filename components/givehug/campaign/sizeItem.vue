<script setup lang="ts">
defineProps({
  size: {
    type: String,
    required: true
  },
  active: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div
    class="text-[13px] h-[45px] flex justify-center items-center uppercase bg-[#F3F3F3] rounded-[10px] text-center" :class="{
      '!bg-primary text-white': active,
      'btn-text trans__time': !active
    }"
  >
    {{ size }}
  </div>
</template>
