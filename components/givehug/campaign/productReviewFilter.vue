<script setup lang="ts">
import CommonDropdown from '~/components/givehug/common/dropdown.vue'
import IconChecked from '~/components/givehug/shared/IconChecked.vue'
import IconStar from '~/components/givehug/shared/IconStar.vue'

interface Props {
  csKey?: string
}

withDefaults(defineProps<Props>(), {
  placeholder: 'Select an option',
  csKey: 'option'
})

const current = defineModel<ProductReviewFilter>('current', { required: true })
const { t } = useI18n()

const optionsList = [
  {
    value: 'helpful',
    text: t('Helpful')
  },
  {
    value: 'newest',
    text: t('Newest')
  },
  {
    value: 'five_star',
    text: t('5'),
    icon: IconStar
  },
  {
    value: 'four_star',
    text: t('4'),
    icon: IconStar
  },
  {
    value: 'three_star',
    text: t('3'),
    icon: IconStar
  },
  {
    value: 'two_star',
    text: t('2'),
    icon: IconStar
  },
  {
    value: 'one_star',
    text: t('1'),
    icon: IconStar
  }
]

function onSelect(option: ProductReviewFilter) {
  current.value = option
}

const currentOption = computed(() => {
  return optionsList.find(option => option.value === current.value)
})
</script>

<template>
  <div>
    <CommonDropdown
      btn-class="capitalize text-overflow-hidden text-sm pl-2 py-1 w-full font-semibold !gap-5"
      data-test-id="cart-item-option"
      :data-test-prop="csKey"
    >
      <span v-if="currentOption" class="text-left py-1 pr-3 pl-1 w-full flex items-center">{{ $t('Filter by') }}: {{ currentOption.text }} <component :is="currentOption.icon" v-if="currentOption.icon" class="w-2.5 h-2.5 ml-2 mb-0.5" /></span>
      <template #content>
        <div
          v-for="optionItem in optionsList"
          :key="optionItem.value"
          :sp-action="`change_${csKey}`"
          class="capitalize py-1 px-8 text-overflow-hidden hover:bg-gray-200 flex items-center relative"
          :data-test-id="`cart-item-option-change-${csKey}`"
          @click="onSelect(optionItem.value as ProductReviewFilter)"
        >
          <IconChecked v-if="current === optionItem.value" class="w-2.5 h-2.5 mr-2 absolute left-4" /><span v-if="current === optionItem.value" class="mr-1">{{ $t('Filter by') }}:</span>{{ $t(optionItem.text) }}
          <component :is="optionItem.icon" v-if="optionItem.icon" class="w-2.5 h-2.5 ml-2" />
        </div>
      </template>
    </CommonDropdown>
  </div>
</template>
