<script lang="ts" setup>
defineProps({
  collections: {
    type: Array as PropType<Collection[]>,
    required: true
  }
})

const localePath = useLocalePath()
</script>

<template>
  <div class="mt-4 flex gap-3 <md:overflow-x-auto no-scrollbar md:flex-wrap items-center">
    <div class="hidden md:block whitespace-nowrap">
      {{ $t('Related Collections') }}
    </div>
    <nuxt-link
      v-for="(collection, index) in collections"
      :key="index"
      :to="localePath({ path: `/collection/${collection.slug}` })"
      :title="collection.name"
      class="px-5 py-[5px] font-semibold rounded-[10px] bg-[#F3F3F3] hover:text-primary trans__time"
    >
      <small class="whitespace-nowrap">{{ collection.name }}</small>
    </nuxt-link>
  </div>
</template>
