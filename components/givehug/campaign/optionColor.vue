<script lang="ts" setup>
import CommonColorItem from '~/components/givehug/common/colorItem.vue'

const props = defineProps({
  colors: {
    type: Array as PropType<string[]>,
    required: true
  },
  selectedColor: {
    type: String,
    default: ''
  },
  isModal: {
    type: Boolean,
    default: false
  },
  hasError: {
    type: Boolean,
    default: false
  },
  campaignSlug: {
    type: String,
    default: ''
  },
  currentProduct: {
    type: Object as PropType<Product>,
    default: () => ({})
  }
})

const emit = defineEmits(['updateOption'])

const getOptionUrl = computed(() => {
  return (value: string) => {
    const newQuery = { ...useRoute().query }
    newQuery.color = value
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName: props.currentProduct?.name,
      query: newQuery
    })
  }
})

function handleColorSelect(value: string) {
  if (value === props.selectedColor) {
    return ''
  }
  else {
    emit('updateOption', { key: 'color', value })
  }
}
</script>

<template>
  <div class="space-y-3">
    <div v-if="!isModal" class="flex gap-2.5">
      <div class="font-semibold uppercase text-sm">
        Color
      </div>
      <div class="text-sm text-option uppercase">
        {{ selectedColor }}
      </div>
    </div>
    <div
      class="w-full overflow-auto flex flex-wrap gap-2.5"
      :class="{
        'animate__animated animate__headShake': hasError
      }"
    >
      <template v-if="isModal">
        <button
          v-for="(color, index) in colors"
          :key="`color-${index}`"
          @click="handleColorSelect(color)"
        >
          <CommonColorItem
            :color="color"
            :title="color"
            size="givehug"
            :active="color === selectedColor"
            data-test-id="product-color"
            sp-action="change_color"
          />
        </button>
      </template>
      <template v-else>
        <nuxt-link
          v-for="(color, index) in colors"
          :key="`color-${index}`"
          :to="getOptionUrl(color)"
          @click="handleColorSelect(color)"
        >
          <CommonColorItem
            :color="color"
            :title="color"
            size="givehug"
            :active="color === selectedColor"
            data-test-id="product-color"
            sp-action="change_color"
          />
        </nuxt-link>
      </template>
    </div>
  </div>
</template>
