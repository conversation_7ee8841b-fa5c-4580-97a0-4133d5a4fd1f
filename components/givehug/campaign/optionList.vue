<script lang="ts" setup>
import OptionColor from './optionColor.vue'
import OptionGeneric from './optionGeneric.vue'
import OptionSize from './optionSize.vue'

const props = defineProps({
  optionList: {
    default: undefined,
    type: Object as PropType<OptionsList>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  optionError: {
    default: '',
    type: [String, Boolean]
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const hasSizeGuide = computed(() => {
  return generalSettings().hasSizeGuide(props.currentProduct?.template_id)
})

function keyToLabel(key: string) {
  // Convert 'size' to 'Size', super_size to 'Super Size'
  return key.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase())
}

// Sort option keys with color always last
const sortedOptionKeys = computed(() => {
  if (!props.optionList)
    return []

  return Object.keys(props.optionList).sort((a, b) => {
    // If a is 'color', it should come last
    if (a === 'color')
      return 1
    // If b is 'color', it should come last
    if (b === 'color')
      return -1
    // Otherwise maintain alphabetical order
    return a.localeCompare(b)
  })
})
</script>

<template>
  <client-only>
    <div v-if="props.optionList && props.currentOptions" class="space-y-6">
      <div v-for="(key, index) in sortedOptionKeys" :key="index">
        <!-- Color option handling -->
        <OptionColor
          v-if="key === 'color'"
          :colors="props.optionList.color"
          :selected-color="props.currentOptions.color"
          :is-modal="props.isModal"
          :has-error="props.optionError === 'color'"
          :campaign-slug="props.campaignSlug"
          :current-product="props.currentProduct"
          @update-option="$emit('updateOption', $event)"
        />

        <div v-else-if="key === 'size'" class="space-y-3">
          <div class="flex items-center justify-between">
            <div class="flex gap-2.5">
              <div class="uppercase font-semibold text-sm">
                {{ $t(keyToLabel(key)) }}
              </div>
              <div class="text-sm text-option uppercase">
                {{ props.currentOptions[key] }}
              </div>
            </div>
            <div
              v-if="hasSizeGuide"
              class="cursor-pointer inline-block font-medium text-primary btn-text"
              @click="uiManager().updateSizeGuideData(props.currentProduct, true)"
            >
              <span class="font-medium text-sm">
                {{ $t('Size chart') }}
              </span>
            </div>
          </div>
          <OptionSize
            :sizes="props.optionList.size"
            :selected-size="props.currentOptions.size"
            :is-modal="props.isModal"
            :has-error="props.optionError === 'size'"
            :campaign-slug="props.campaignSlug"
            :current-product="props.currentProduct"
            @update-option="$emit('updateOption', $event)"
          />
        </div>

        <!-- Other options handling -->
        <div v-else>
          <div class="flex items-center justify-between">
            <div class="uppercase font-semibold text-sm">
              {{ $t(keyToLabel(key)) }}
            </div>
            <div class="text-sm text-option uppercase">
              {{ props.currentOptions[key] }}
            </div>
          </div>
          <OptionGeneric
            class="mt-3"
            :options="props.optionList[key]"
            :option-key="key"
            :selected-option="props.currentOptions[key]"
            :is-modal="props.isModal"
            :has-error="props.optionError === key"
            :campaign-slug="props.campaignSlug"
            :current-product="props.currentProduct"
            @update-option="$emit('updateOption', $event)"
          />
        </div>
      </div>
    </div>
  </client-only>
</template>
