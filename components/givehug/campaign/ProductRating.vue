<script lang="ts" setup>
import IconStar from '~/components/givehug/shared/IconStar.vue'

interface Props {
  rating: number
  showText?: boolean
  variant?: string
  size?: string
}

const props = withDefaults(defineProps<Props>(), {
  showText: true,
  variant: 'default',
  size: 'normal'
})

const roundedRating = computed(() => {
  return Math.round(Number(props.rating) * 2) / 2
})

const sizeClass = computed(() => {
  return props.size === 'xs' ? 'w-[14px] h-[14px]' : 'w-[21px] h-[21px]'
})
</script>

<template>
  <div class="flex items-center gap-3.5">
    <div class="flex gap-1.25">
      <div v-for="i in 5" :key="i" :class="variant === 'default' ? 'text-[#222222]' : 'text-primary'">
        <IconStar
          v-if="i <= roundedRating"
          :class="sizeClass"
        />
        <svg v-else-if="i === roundedRating + 0.5" :class="sizeClass" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14.8771 17.614L13.7309 12.6022L17.5864 9.26096L12.5152 8.80851L10.5007 4.07511V14.9341L14.8771 17.614ZM4.00549 20.5L5.72828 13.1034L0 8.13191L7.54555 7.4748L10.5007 0.5L13.4558 7.4748L21 8.13191L15.2717 13.102L16.9959 20.5L10.5007 16.5727L4.00549 20.5Z" fill="currentColor" />
        </svg>
        <svg v-else :class="sizeClass" viewBox="0 0 21 21" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
          <path d="M4.00549 20.5L5.72828 13.1034L0 8.13191L7.54555 7.4748L10.5007 0.5L13.4558 7.4748L21 8.13191L15.2717 13.102L16.9959 20.5L10.5007 16.5727L4.00549 20.5Z" />
        </svg>
      </div>
    </div>
    <div v-if="showText" class="<md:text-sm">
      {{ Number(rating).toFixed(1) }} out of 5
    </div>
  </div>
</template>
