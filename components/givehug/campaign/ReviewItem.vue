<script lang="ts" setup>
import ProductRating from '~/components/givehug/campaign/ProductRating.vue'

defineProps<{
  review: ProductReview
}>()

const emit = defineEmits(['view-image'])

function viewImage(images: { src: string, type: any }[], index: number) {
  emit('view-image', images, index)
}
</script>

<template>
  <div class="grid grid-cols-3 gap-4 py-10 border-b border-[#F4F4F4]">
    <div class="col-span-3 lg:col-span-1 <md:flex justify-between">
      <div class="flex items-center gap-1 md:gap-2.5">
        <div>
          <img
            v-if="review.avatar_url"
            :src="review.avatar_url"
            onerror="this.src = `${cdnURL}images/default-user-icon.webp`"
            loading="lazy"
            :alt="review.customer_name"
            class="max-w-[43px] max-h-[43px] md:max-w-[37px] md:max-h-[37px] rounded-full md:rounded-lg"
          >
          <div v-else class="bg-[#FFF2EC] w-[43px] h-[43px] md:w-[37px] md:h-[37px] rounded-full md:rounded-lg flex items-center justify-center">
            <svg width="19" height="21" viewBox="0 0 19 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9.5 10.736C14.0393 10.736 18.6972 13.7934 18.9989 19.9356C19.0105 20.1724 18.9309 20.4042 18.7776 20.5803C18.6243 20.7563 18.4098 20.8621 18.1813 20.8744C15.7861 21.0009 4.69355 21.0784 0.819839 20.8744C0.591097 20.8624 0.376336 20.7567 0.222797 20.5807C0.0692574 20.4046 -0.0104856 20.1726 0.00110829 19.9356C0.302807 13.7946 4.96071 10.736 9.5 10.736ZM9.5 0C8.35474 0 7.25639 0.471298 6.44657 1.31021C5.63675 2.14913 5.1818 3.28694 5.1818 4.47335C5.1818 5.65975 5.63675 6.79756 6.44657 7.63648C7.25639 8.4754 8.35474 8.94669 9.5 8.94669C10.6453 8.94669 11.7436 8.4754 12.5534 7.63648C13.3632 6.79756 13.8182 5.65975 13.8182 4.47335C13.8182 3.28694 13.3632 2.14913 12.5534 1.31021C11.7436 0.471298 10.6453 0 9.5 0Z" fill="#F1641E" />
            </svg>
          </div>
        </div>
        <div class="space-y-0.5">
          <h5 class="font-medium text-sm">
            {{ review.customer_name }}
          </h5>
          <div class="text-secondary text-[12px] <md:hidden">
            {{ $t('Purchased in') }} {{ review.customer_location }}
          </div>
          <div class="text-sm text-gray-500 md:hidden">
            {{ review.created_at }}
          </div>
        </div>
      </div>
      <ProductRating
        :rating="review.average_rating"
        :show-text="false"
        variant="primary"
        size="xs"
        class="md:hidden"
      />
    </div>
    <div class="col-span-3 lg:col-span-2">
      <div class="flex items-center gap-2.5 <md:hidden">
        <ProductRating
          :rating="review.average_rating"
          :show-text="false"
          variant="primary"
          size="xs"
        />
        <div class="text-[11px] text-gray-500 text-[#595959] leading-4">
          {{ review.created_at }}
        </div>
      </div>
      <p class="mt-3">
        {{ review.comment }}
      </p>
      <div v-if="review.files?.length" class="flex gap-2 mt-3">
        <common-image
          v-for="(file, fileIndex) in review.files"
          :key="fileIndex"
          class="border-givehug"
          img-class="h-25 w-25 !object-cover cursor-zoom-in rounded-lg"
          :image="{ path: file.src, type: (file.type === 'video') ? 'product-review-video' : 'product-review-image' }"
          @click="viewImage(review.files, fileIndex)"
        />
      </div>
    </div>
  </div>
</template>
