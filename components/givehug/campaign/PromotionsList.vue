<script lang="ts" setup>
defineProps({
  promotionsList: {
    default: undefined,
    type: Object as PropType<Array<Promotion>>,
  },
  defaultNumberItem: {
    default: 2,
    type: Number,
  },
})

const isShowModal = ref(false)
const isListAllItem = ref(false)
const currentPromotion = ref<Promotion>()

const {
  rules,
  type,
  condition,
  countries,
} = usePromotion(currentPromotion)
</script>

<template>
  <div class="overflow-y-auto max-h-[80vh] bg-[#F3F3F3] rounded-[10px] px-6 py-4">
    <div class="flex items-center gap-1">
      <div>
        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9.5925 15.75L2.25 8.40747V9.90747C2.25 10.305 2.4075 10.6875 2.6925 10.965L8.535 16.8075C9.12 17.3925 10.0725 17.3925 10.6575 16.8075L15.315 12.15C15.9 11.565 15.9 10.6125 15.315 10.0275L9.5925 15.75Z" fill="black" />
          <path d="M8.535 13.0575C8.8275 13.35 9.21 13.5 9.5925 13.5C9.975 13.5 10.3575 13.35 10.65 13.0575L15.3075 8.4C15.8925 7.815 15.8925 6.8625 15.3075 6.2775L9.465 0.435C9.1875 0.1575 8.805 0 8.4075 0H3.75C2.925 0 2.25 0.675 2.25 1.5V6.1575C2.25 6.555 2.4075 6.9375 2.6925 7.215L8.535 13.0575ZM3.75 1.5H8.4075L14.25 7.3425L9.5925 12L3.75 6.1575V1.5Z" fill="black" />
          <path d="M5.4375 4.125C5.95527 4.125 6.375 3.70527 6.375 3.1875C6.375 2.66973 5.95527 2.25 5.4375 2.25C4.91973 2.25 4.5 2.66973 4.5 3.1875C4.5 3.70527 4.91973 4.125 5.4375 4.125Z" fill="black" />
        </svg>
      </div>
      <div class="text-sm font-semibold">
        Buy more save more
      </div>
    </div>
    <div class="flex flex-wrap gap-2 mt-2.5">
      <givehug-common-promotion-item
        v-for="(promotion, index) in promotionsList"
        :key="index"
        :class="{ hidden: index > (defaultNumberItem || 2) && !isListAllItem }"
        :promotion="promotion"
        @click="currentPromotion = promotion ; isShowModal = true"
        @action="value => { $emit('updateDiscountApply', value) }"
      />
    </div>
    <div
      v-if="(promotionsList?.length || 0) > ((defaultNumberItem + 1) || 3)"
      class="cursor-pointer text-center font-weight-500 py-3"
      @click="isListAllItem = !isListAllItem"
    >
      <span>
        {{ isListAllItem ? $t('Show less') : $t('Show more') }}
      </span>
      <span><i :class="isListAllItem ? 'icon-sen-chevron-up' : 'icon-sen-chevron-down'" /></span>
    </div>
    <common-modal
      v-model="isShowModal"
      modal-class="w-[90%] md:max-w-[498px] p-5"
      modal-container-class="z-99999"
      :modal-id="`modal-promotion-${currentPromotion?.discount_code}`"
      :title="`${$t('Discount code')}: ${currentPromotion?.discount_code}`"
    >
      <ul class="mt-4 ml-8 list-disc">
        <li v-if="type">
          <span>{{ $t('Type') }}: {{ type }}</span>
        </li>
        <li v-if="condition">
          <span>{{ $t('Condition') }}: {{ condition }}</span>
        </li>
        <li v-if="rules.countries">
          <span>{{ $t('Location') }}: {{ countries }}</span>
        </li>
        <li v-if="currentPromotion?.end_time">
          <span>{{ $t('Expired time') }}: {{ currentPromotion?.end_time.slice(0, -3) }}</span>
        </li>
        <template v-if="rules.tiers && rules.tiers.length">
          <li v-for="(tier, index) in rules.tiers" :key="index">
            <span>{{ $t('Buy') }} {{ tier.qty }} {{ $t('Get') }} {{ tier.discount }}% {{ $t('Off') }}</span>
          </li>
        </template>
      </ul>
      <button
        v-if="isCheckoutPage"
        class="btn-fill py-1 px-2 mt-4"
        @click.stop="
          $emit('updateDiscountApply', currentPromotion?.discount_code);
          isShowModal = false"
      >
        <span>
          {{ $t('Apply Code') }}
        </span>
      </button>
    </common-modal>
  </div>
</template>
