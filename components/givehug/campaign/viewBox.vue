<script lang="ts" setup>
import { Splide, SplideSlide, SplideTrack } from '@splidejs/vue-splide'
import CsVideoPlayer from '~/components/default/campaign/cs-video-player.vue'
import VideoPlayTrigger from '~/components/default/campaign/video-play-trigger.vue'
import useCampaignImage from '~/composables/campaignImage'
import useCampaignVideo from '~/composables/campaignVideo'

const props = defineProps({
  imagesList: {
    type: Object as PropType<Array<ImageData>>,
    required: true
  },
  campaignData: {
    type: Object as PropType<Campaign>,
    required: true
  },
  currentProduct: {
    type: Object as PropType<Product>,
    required: true
  },
  color: {
    default: undefined,
    type: String
  },
  isModal: {
    default: false,
    type: Boolean
  },
  personalize: {
    default: undefined,
    type: Object as PropType<Personalize>
  },
  isShowDesign: {
    default: false,
    type: Boolean
  }
})
const $emit = defineEmits(['changeImage'])

const currentImage = ref(0)
const viewZone = ref<HTMLElement>()
const imageCarousel = ref<any>(null)

const imagesList = computed(() => { return props.imagesList })
const currentProduct = computed(() => { return props.currentProduct })
const color = computed(() => { return props.color })
const preloadIndex = imagesList.value.findIndex(image => image?.file_url === currentProduct.value.thumb_url)

const { videos, isVideoTab, modifiedIndex } = useCampaignVideo(currentProduct, currentImage)
const { isInViewPort, viewImagesList, enlargeViewboxImage, slideTo } = useCampaignImage(imagesList, currentProduct, currentImage, imageCarousel, viewZone, color, modifiedIndex, $emit, computed(() => props.campaignData))
</script>

<template>
  <div
    ref="viewZone"
    class="flex relative w-full pt-[140%] lg:pt-[90%] xl:pt-[115%] viewzone"
  >
    <div
      v-if="(imagesList?.length + modifiedIndex) > 1 && $viewport.isGreaterOrEquals(VIEWPORT.desktop) && !isModal"
      class="overflow-y-auto w-[100px] px-1 absolute top-0 left-0 h-full space-y-3"
    >
      <client-only>
        <VideoPlayTrigger
          v-for="(video, index) in videos"
          :key="video.url"
          :is-active="currentImage === index"
          :thumb-url="video.thumb"
          class="hover:(shadow-custom2)"
          @clicked="slideTo(index)"
        />
        <div
          v-for="(image, index) in imagesList"
          :key="index"
          class="product-item h-[76px] w-[76px] min-w-[76px] cursor-pointer overflow-hidden border"
          :class="{
            'border border-primary': currentImage === index + modifiedIndex
          }"
          @click="slideTo(index + modifiedIndex)"
        >
          <common-image
            :image="{
              path: image?.file_url,
              color
            }"
            :alt="campaignData?.name || currentProduct?.name"
            img-class="h-full object-cover mx-auto"
          />
        </div>
      </client-only>
    </div>
    <div class="w-full lg:w-[calc(100%-100px)] h-full absolute top-0 right-0 rounded-[10px] overflow-hidden">
      <button
        v-if="!isVideoTab"
        class="absolute center-flex top-1 right-1 z-1 h-7 w-7 rounded-full btn bg-gray-300 hover:bg-gray-400"
        @click="enlargeViewboxImage(isShowDesign)"
      >
        <i class="icon-sen-expand" />
      </button>
      <div v-if="isShowDesign">
        <template
          v-for="personalizeItem in currentProduct?.personalizeList"
        >
          <common-mockup-canvas
            v-if="personalizeItem.personalizeKey === personalize?.personalizeKey"
            :key="personalizeItem.personalizeKey"
            :is-show-design="true"
            :is-in-view-port="isInViewPort"
            :personalize="personalizeItem"
            :color="color"
            :custom-image="personalizeItem?.customImageList && personalizeItem?.customImageList[0]"
          />
        </template>
      </div>
      <client-only v-else-if="(imagesList?.length + modifiedIndex) > 1">
        <Splide
          ref="imageCarousel"
          class="relative h-full"
          :class="{ hidden: isShowDesign }"
          :options="{
            ...splideSetting,
            start: currentImage
          }"
          :extensions="splideExtensions"
          :has-track="false"
          @splide:move="currentImage = $event.index"
        >
          <div class="flex items-center justify-between <md:hidden">
            <div class="splide__arrows flex gap-2">
              <button class="splide__arrow splide__arrow--prev w-10 h-10 flex items-center justify-center rounded-full hover:opacity-90 absolute left-5 z-10 top-1/2 -translate-y-1/2" style="background-color: rgba(14, 14, 14, 0.1);">
                <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.49566 16C7.62707 16.0008 7.75735 15.972 7.87901 15.9152C8.00067 15.8583 8.11132 15.7745 8.20463 15.6686C8.29822 15.5624 8.37251 15.4361 8.4232 15.2968C8.4739 15.1576 8.5 15.0083 8.5 14.8575C8.5 14.7066 8.4739 14.5573 8.4232 14.4181C8.37251 14.2788 8.29822 14.1525 8.20463 14.0463L2.9123 8.00233L8.20463 1.95839C8.39266 1.74325 8.4983 1.45145 8.4983 1.1472C8.4983 0.996545 8.47236 0.847368 8.42197 0.708184C8.37159 0.568999 8.29773 0.442533 8.20463 0.336006C8.11153 0.229479 8.001 0.144977 7.87935 0.0873254C7.75771 0.0296734 7.62733 -7.62916e-08 7.49566 -8.78024e-08C7.22974 -1.1105e-07 6.97472 0.120865 6.78669 0.336006L0.795369 7.19114C0.701777 7.29735 0.62749 7.42371 0.576795 7.56294C0.5261 7.70217 0.499999 7.8515 0.499999 8.00233C0.499999 8.15315 0.5261 8.30249 0.576795 8.44171C0.62749 8.58094 0.701777 8.70731 0.795369 8.81352L6.78668 15.6686C6.87999 15.7745 6.99064 15.8583 7.11231 15.9152C7.23397 15.972 7.36424 16.0008 7.49566 16Z" fill="#222222" />
                </svg>
              </button>
              <button class="splide__arrow splide__arrow--next w-10 h-10 flex items-center justify-center rounded-full hover:opacity-90 absolute right-5 z-10 top-1/2 -translate-y-1/2" style="background-color: rgba(14, 14, 14, 0.1);">
                <svg width="9" height="16" viewBox="0 0 9 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1.50434 2.00272e-05C1.37293 -0.00084877 1.24265 0.0279703 1.12099 0.0848274C0.999331 0.141685 0.888675 0.225462 0.79537 0.331352C0.701777 0.437564 0.627491 0.563928 0.576796 0.703155C0.526101 0.842381 0.5 0.991715 0.5 1.14254C0.5 1.29337 0.526101 1.4427 0.576796 1.58193C0.627491 1.72116 0.701777 1.84752 0.79537 1.95373L6.0877 7.99767L0.79537 14.0416C0.607339 14.2568 0.501704 14.5485 0.501704 14.8528C0.501704 15.0035 0.527638 15.1526 0.578025 15.2918C0.628413 15.431 0.702267 15.5575 0.79537 15.664C0.888474 15.7705 0.999003 15.855 1.12065 15.9127C1.24229 15.9703 1.37267 16 1.50434 16C1.77026 16 2.02528 15.8791 2.21331 15.664L8.20463 8.80886C8.29822 8.70265 8.37251 8.57629 8.4232 8.43706C8.4739 8.29783 8.5 8.1485 8.5 7.99767C8.5 7.84685 8.4739 7.69751 8.4232 7.55828C8.37251 7.41906 8.29822 7.29269 8.20463 7.18648L2.21331 0.331352C2.12001 0.225462 2.00935 0.141685 1.88769 0.0848274C1.76603 0.0279703 1.63576 -0.00084877 1.50434 2.00272e-05Z" fill="#222222" />
                </svg>
              </button>
            </div>
          </div>
          <SplideTrack>
            <SplideSlide
              v-for="(video, index) in videos"
              :key="video.url"
              class="center-flex"
            >
              <CsVideoPlayer :video-url="video.url" :thumbnail-url="video.thumb" :autoplay="currentImage === index" />
            </SplideSlide>
            <SplideSlide
              v-for="(image, index) in imagesList"
              :key="index"
              class="center-flex"
            >
              <div class="aspect-square viewbox-image">
                <common-zoom-on-hover
                  :image="{
                    path: image?.file_url,
                    type: 'full_hd',
                    color
                  }"
                  :alt="campaignData?.name || currentProduct?.name"
                  :scale="1.6"
                  :index="index"
                  :preload-index="preloadIndex"
                  @click="isShowDesign ? '' : uiManager().viewImage(viewImagesList, currentImage)"
                />
              </div>
            </SplideSlide>
          </SplideTrack>
        </Splide>
      </client-only>
      <common-zoom-on-hover
        v-else
        :image="{
          path: imagesList && imagesList.length > 0 ? imagesList[0]?.file_url || currentProduct?.thumb_url : currentProduct?.thumb_url,
          type: 'full_hd',
          color
        }"
        :alt="campaignData?.name || currentProduct?.name"
        :scale="1.6"
        :index="0"
        @click="isShowDesign ? '' : uiManager().viewImage(viewImagesList, currentImage)"
      />
    </div>
    <div v-if="currentProduct.full_printed === 4" class="flex justify-end mb-2 w-full" style="color: rgb(255, 145, 0);">
      <small>* {{ $t('Images are for illustrative purposes only, the actual embroidered product may differ from the illustrated image.') }}</small>
    </div>
  </div>
</template>

<style>
.\!\<md\:fixed .control-design {
  display: none;
}
</style>
