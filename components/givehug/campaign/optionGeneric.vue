<script lang="ts" setup>
import DropdownComponent from '~/components/givehug/shared/dropdown.vue'

const props = defineProps({
  options: {
    type: Array as PropType<string[]>,
    required: true,
  },
  optionKey: {
    type: String,
    required: true,
  },
  selectedOption: {
    type: String,
    default: '',
  },
  isModal: {
    type: Boolean,
    default: false,
  },
  hasError: {
    type: Boolean,
    default: false,
  },
  campaignSlug: {
    type: String,
    default: '',
  },
  currentProduct: {
    type: Object as PropType<Product>,
    default: () => ({}),
  },
})

const emit = defineEmits(['updateOption'])
const router = useRouter()
// URL construction logic for non-modal mode
const getOptionUrl = computed(() => {
  return (value: string) => {
    const newQuery = { ...useRoute().query }
    newQuery[props.optionKey] = value
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName: props.currentProduct?.name,
      query: newQuery,
    })
  }
})

// Convert string options to dropdown format
const dropdownOptions = computed<DropdownItemType[]>(() => {
  return props.options.map(option => ({
    label: option,
    value: option,
  }))
})

// Current selected option in dropdown format
const currentDropdownOption = computed<DropdownItemType | undefined>(() => {
  if (!props.selectedOption)
    return undefined
  return {
    label: props.selectedOption,
    value: props.selectedOption,
  }
})

// Handle dropdown selection change
function handleDropdownChange(option: DropdownItemType | undefined) {
  if (!option || option.value === props.selectedOption)
    return

  // Emit update event
  emit('updateOption', { key: props.optionKey, value: option.value })

  // Update URL if not in modal mode
  if (!props.isModal) {
    const url = getOptionUrl.value(option.value)
    router.push(url)
  }
}
</script>

<template>
  <div
    :class="{ 'animate__animated animate__headShake': hasError }"
  >
    <DropdownComponent
      :list="dropdownOptions"
      :cs-key="optionKey"
      :placeholder="`Select ${optionKey}`"
      :current="currentDropdownOption"
      @update:current="handleDropdownChange"
    />
  </div>
</template>
