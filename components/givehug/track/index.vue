<script lang="ts" setup>
const {
  email,
  orderNumber,
  isShowValidate,

  resendConfirmation,
  onSubmit
} = useOrderTrack()

const { lastOrderUrl } = useLastOrder()
</script>

<template>
  <main class="container py-15 min-h-[70vh]">
    <h1 class="text-3xl leading-normal font-semibold text-center mb-12">
      {{ $t('Track Your Order') }}
    </h1>
    <form action="#" method="POST" class="mx-auto border rounded-[10px] p-9 max-w-[428px]" @submit.prevent="onSubmit">
      <div class="relative">
        <givehug-common-input
          id="email"
          v-model:model-value="email"
          :label="$t('Email')"
          required
          autofocus
          type="email"
          :state="isShowValidate ? !!email : undefined"
          :message="$t('Please enter a valid email')"
          :placeholder="$t('Enter email')"
          pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
        />
        <button
          v-if="false"
          type="button"
          class="btn-text absolute top-0.5 right-0 text-xs font-medium flex gap-1 items-center"
          @click.prevent="resendConfirmation"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13">
            <path d="M11.6746 0.631312C11.9114 0.795341 12.0356 1.07888 11.9911 1.36241L10.491 11.1104C10.4559 11.3377 10.3176 11.5369 10.116 11.6493C9.91446 11.7618 9.67305 11.7759 9.45976 11.6868L6.6566 10.5222L5.0511 12.2586C4.84251 12.4859 4.51438 12.5609 4.22609 12.4484C3.9378 12.3359 3.7503 12.0571 3.7503 11.7478V9.78879C3.7503 9.69506 3.78546 9.60601 3.84874 9.53572L7.77693 5.25223C7.91287 5.1046 7.90818 4.87731 7.76755 4.73671C7.62692 4.59612 7.39958 4.58674 7.25192 4.72031L2.48466 8.95459L0.415094 7.91886C0.166653 7.79467 0.00727548 7.54628 0.000244118 7.26978C-0.00678724 6.99327 0.138528 6.73552 0.377594 6.59726L10.8778 0.598507C11.1285 0.455568 11.4379 0.469627 11.6746 0.631312Z" fill="currentColor" />
          </svg>
          <span class="<sm:hidden">
            {{ $t('Resend Confirmation Email') }}
          </span>
          <span class="sm:hidden">
            {{ $t('Resend Confirmation') }}
          </span>
        </button>
      </div>

      <givehug-common-input
        id="order_number"
        v-model:model-value="orderNumber"
        class="my-6"
        :label="$t('Order Number')"
        :placeholder="$t('Enter order number')"
        required
      />
      <nuxt-link
        v-if="lastOrderUrl"
        type="button"
        class="w-full h-[45px] font-medium text-sm border border-black hover:bg-givehug-gray mb-3 rounded-[10px] center-flex"
        :to="`${lastOrderUrl}?utm_source=page&utm_medium=track-order`"
      >
        {{ $t('View Last Order') }}
      </nuxt-link>

      <button
        type="submit"
        class="w-full h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-[10px]"
      >
        {{ $t('Track Order') }}
      </button>
    </form>
  </main>
</template>
