<script lang="ts" setup>
interface GiveHugBanner extends Banner {
  color: string
  component: Component
}

const { banner } = defineProps({
  banner: {
    default: (): GiveHugBanner => {
      return {
        color: '',
        banner_url: '',
        banner_link: '',
        banner_text: '',
        component: () => {}
      }
    },
    type: Object
  },
  imageClass: {
    required: true,
    type: String
  }
})
const localePath = useLocalePath()
const $router = useRouter()
function goToPage() {
  $router.push(localePath(banner.banner_link || '/'))
}
</script>

<template>
  <div v-click-not-drag="goToPage" class="select-none" :style="`--collection-primary-color-rgb: ${banner.color}`">
    <nuxt-link :to="localePath(banner.banner_link || '/')">
      <div class="flex justify-center items-center aspect-square givehug-banner rounded-full max-w-[163px] max-h-[163px] mx-auto">
        <component :is="banner.component" :class="imageClass" />
      </div>

      <div class="text-center font-medium capitalize mt-1.5">
        {{ $t(banner.banner_text) }}
      </div>
    </nuxt-link>
  </div>
</template>

<style>
.givehug-banner {
  background-color: rgba(var(--collection-primary-color-rgb), 0.1);
  color: rgba(var(--collection-primary-color-rgb), 1);
  transition: all 0.2s ease-in-out;
}

.givehug-banner:hover {
  background-color: rgba(var(--collection-primary-color-rgb), 0.2);
}

.givehug-banner svg {
  transition: all 0.2s ease-in-out;
}

.givehug-banner:hover svg {
  transform: scale(1.05);
}
</style>
