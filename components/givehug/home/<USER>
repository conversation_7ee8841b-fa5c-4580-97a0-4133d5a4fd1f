<script lang="ts" setup>
defineProps({
  banner: {
    type: Object,
    required: true
  }
})
</script>

<template>
  <NuxtLink
    :to="banner.buttonLink"
    class="relative overflow-hidden flex-1 min-h-[300px] sm2:h-[400px] flex flex-col sm2:flex-row mx-auto w-full hover:opacity-90 trans__time"
  >
    <div class="relative sm2:absolute right-0 top-0 bottom-0 w-full sm2:w-[54%] h-[400px] sm2:h-full">
      <img
        :src="banner.image.src"
        :alt="banner.image.alt"
        class="object-cover h-full w-full rounded-none"
      >
    </div>
    <div
      :style="{ backgroundColor: banner.bgColor }"
      class="w-full sm2:w-[46%] p-10 flex items-center transition"
    >
      <div class="w-full text-center">
        <h2 class="text-xl sm2:text-2xl lg:text-3xl text-gray-800 mb-2 font-medium">
          {{ banner.title }}
        </h2>
        <p class="text-[13px] sm2:text-sm text-stone-600 mb-3 sm2:mb-4 leading-relaxed">
          {{ banner.description }}
        </p>
        <button
          class="bg-black text-white rounded-full py-2 sm2:py-2.5 px-4 sm2:px-6 text-sm w-fit"
        >
          {{ banner.buttonText }}
        </button>
      </div>
    </div>
  </NuxtLink>
</template>
