<script lang="ts" setup>
import CarouselRoot from '~/components/givehug/shared/CarouselRoot.vue'
import Banner from './banner.vue'
// import HappyCustomers from './HappyCustomers.vue'
import ShopByProduct from './ShopByProduct.vue'
import WhyYouLoveUs from './WhyYouLoveUs.vue'

const LazyCollectionBannerCarousel = defineAsyncComponent(() =>
  import('./collectionBannerCarousel.vue')
)

const { givehugListProductData } = await useHomePage()

const todayBigDeal = computed(() => {
  return givehugListProductData.value?.filter(item => item.name.toLowerCase().includes('big deal'))[0]?.products || []
})
</script>

<template>
  <main id="homePage">
    <Banner class="xl:mt-10" />
    <div class="cs-container mt-28 space-y-12">
      <div>
        <div class="font-medium text-[40px] leading-[52px] max-w-md text-center mx-auto">
          Bringing you the joy of personalized gifting
        </div>
        <div class="text-muted text-center max-w-3xl mx-auto mt-5">
          At GiveHug, we’re here to help you create one-of-a-kind gifts that speak from the heart. Thoughtfully designed, proudly made for you and your loved ones.
        </div>
      </div>
      <LazyCollectionBannerCarousel />
    </div>
    <!-- <HappyCustomers class="mt-36" /> -->
    <CarouselRoot
      v-if="todayBigDeal?.length"
      class="mt-36 cs-container"
      :products="todayBigDeal"
      :title="$t(`Today's big deals`)"
      :max-items="6"
    />
    <WhyYouLoveUs class="my-28 md:my-36" />
    <ShopByProduct class="cs-container mx-auto mb-28" />
  </main>
</template>
