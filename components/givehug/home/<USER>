<script setup lang="ts">
const {
  subscribeEmail,
  emailNewsletter
} = useFooter()

const {
  name: storeName
} = storeInfo()
</script>

<template>
  <div class="bg-[#FFF2EC] py-12">
    <div class="cs-container mx-auto">
      <div class="w-full mx-auto px-4 py-8 text-center space-y-[15px]">
        <h5 class="text-sm md:text-[15px] font-medium mb-6">
          {{ $t('Yes! Send me exclusive offers, unique gift ideas, and personalized tips for shopping and selling on') }} <span class="capitalize">{{ storeName }}</span>.
        </h5>
        <form class="relative max-w-xl mx-auto text-sm" @submit.prevent="subscribeEmail">
          <div class="relative flex items-center gap-2 p-1 bg-white rounded-full border border-black shadow-sm bg-transparent">
            <input
              v-model="emailNewsletter"
              autocomplete="email"
              type="email"
              :placeholder="`${$t('Enter your email')}...`"
              class="flex-1 py-[11.5px] pl-5 bg-transparent outline-none text-sm"
            >
            <button
              type="submit"
              class="text-sm absolute right-[5px] top-1 bottom-1 font-medium px-4 py-2.5 text-white bg-primary rounded-full transition-colors disabled:opacity-50 hover:opacity-90 transition-all flex items-center gap-2"
            >
              <span>{{ $t('Subscribe') }}</span>
              <svg width="7" height="15" viewBox="0 0 7 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.8788 0.500017C0.763811 0.499256 0.649821 0.524474 0.543368 0.574224C0.436914 0.623974 0.340091 0.697279 0.258449 0.789933C0.176555 0.882869 0.111555 0.993436 0.0671964 1.11526C0.0228381 1.23708 0 1.36775 0 1.49972C0 1.6317 0.0228381 1.76236 0.0671964 1.88419C0.111555 2.00601 0.176555 2.11658 0.258449 2.20951L4.88924 7.49796L0.258449 12.7864C0.0939216 12.9747 0.00149111 13.23 0.00149111 13.4962C0.00149111 13.628 0.0241832 13.7586 0.0682721 13.8803C0.112361 14.0021 0.176983 14.1128 0.258449 14.206C0.339915 14.2992 0.436628 14.3731 0.543068 14.4236C0.649508 14.474 0.76359 14.5 0.8788 14.5C1.11148 14.5 1.33462 14.3942 1.49915 14.206L6.74155 8.20775C6.82344 8.11482 6.88845 8.00425 6.9328 7.88243C6.97716 7.7606 7 7.62994 7 7.49796C7 7.36599 6.97716 7.23532 6.9328 7.1135C6.88845 6.99168 6.82344 6.88111 6.74155 6.78817L1.49915 0.789933C1.41751 0.697279 1.32069 0.623974 1.21423 0.574224C1.10778 0.524474 0.993789 0.499256 0.8788 0.500017Z" fill="white" />
              </svg>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
