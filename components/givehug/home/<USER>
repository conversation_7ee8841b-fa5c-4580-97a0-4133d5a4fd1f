<script setup>
defineProps({
  iconComponent: {
    type: Object,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  }
})
</script>

<template>
  <div class="feature-item">
    <div class="mb-6">
      <component :is="iconComponent" />
    </div>
    <h5>
      {{ title }}
    </h5>
    <p>
      {{ description }}
    </p>
  </div>
</template>
