<script setup lang="ts">
import BannerItem from './BannerItem.vue'
import BannerItemMobile from './BannerItemMobile.vue'

interface Banner {
  id: number
  title: string
  description: string
  buttonText: string
  buttonLink: string
  bgColor: string
  image: {
    src: string
    alt: string
    objectPosition?: string
  }
}

const banners: Banner[] = [
  {
    id: 1,
    title: 'Back To School',
    description: 'Gear Up for Success - Make This School Year Unforgettable',
    buttonText: 'Grab Yours Now',
    buttonLink: '/collection/back-to-school',
    bgColor: '#C3DEFF',
    image: {
      src: '/images/givehug/back_to_school.webp',
      alt: 'Back To School'
    }
  },
  {
    id: 2,
    title: 'Labor Day',
    description: 'Celebrate Hard Work - Treat Yourself or Someone Special',
    buttonText: 'Shop Now',
    buttonLink: '/collection/labor-day',
    bgColor: '#FFDCB4',
    image: {
      src: '/images/givehug/laborday.webp',
      alt: 'Labor Day'
    }
  }
]
</script>

<template>
  <div>
    <div class="hidden md:block cs-container mx-auto">
      <div class="grid grid-cols-1 xl:grid-cols-2 xl:gap-5 <xl:-mx-5">
        <BannerItem v-for="banner in banners" :key="banner.id" :banner="banner" />
      </div>
    </div>
    <div class="md:hidden">
      <BannerItemMobile v-for="banner in banners" :key="banner.id" :banner="banner" />
    </div>
  </div>
</template>
