<script lang="ts" setup>
import type { Options } from '@splidejs/vue-splide'
import Collection1 from '@/components/givehug/shared/collection/1.vue'
import Collection2 from '@/components/givehug/shared/collection/2.vue'
import Collection3 from '@/components/givehug/shared/collection/3.vue'
import Collection4 from '@/components/givehug/shared/collection/4.vue'
import Collection5 from '@/components/givehug/shared/collection/5.vue'
import Collection6 from '@/components/givehug/shared/collection/6.vue'
import { Splide, SplideSlide } from '@splidejs/vue-splide'

const { collection_banners: collectionBanners } = storeInfo()
const FIXED_COLLECTION_DATA = $getFixedCollectionData()

const components = [
  Collection1,
  Collection2,
  Collection3,
  Collection4,
  Collection5,
  Collection6
]
const transformedCollectionBanners = computed(() => {
  return collectionBanners.map((banner, index) => {
    return {
      ...banner,
      color: FIXED_COLLECTION_DATA[index].color,
      component: components[index]
    }
  })
})

const $viewport = useViewport()
const itemsToShow = ref<number>(getItemsToShow())

watch($viewport.breakpoint, () => {
  itemsToShow.value = getItemsToShow()
})

function getItemsToShow() {
  if ($viewport.isGreaterThan(VIEWPORT.desktop)) {
    return 7
  }
  if ($viewport.isGreaterThan(VIEWPORT.tablet)) {
    return 4
  }
  return 2
}

const splideSettings = computed(() => {
  return {
    ...collectionBannerSplideSetting,
    breakpoints: {
      500: {
        perPage: 2
      },
      760: {
        perPage: 3
      },
      1023: {
        perPage: 4
      }
    },
    type: 'loop',
    classes: {
      prev: 'hidden',
      next: 'hidden'
    },
    autoplay: true,
    interval: 5000,
    infinite: true,
    loop: true
  }
}) as ComputedRef<Options>

const imageClassMap = [
  'w-[63px]',
  'w-[80px]',
  'w-[72px]',
  'w-[69px] mr-2',
  'w-[44px]',
  'w-[52px]'
]
</script>

<template>
  <client-only>
    <div
      v-if="collectionBanners.length <= itemsToShow"
      :key="itemsToShow"
      class="flex justify-center group"
    >
      <givehug-home-collection-banner-item
        v-for="(banner, index) in transformedCollectionBanners"
        :key="index"
        :banner="banner"
        :index="index"
        :image-class="imageClassMap[index]"
        class="w-1/2 md:w-1/3 lg:w-1/7 min-w-1/2 md:min-w-1/3 lg:min-w-1/7 max-w-70 px-[15px] banner"
      />
    </div>
    <Splide
      v-else
      :options="splideSettings"
      :extensions="splideExtensions"
      title="Drag to scroll"
    >
      <SplideSlide v-for="(banner, index) in transformedCollectionBanners" :key="index" class="px-[15px] banner">
        <givehug-home-collection-banner-item
          :banner="banner"
          :index="index"
          :image-class="imageClassMap[index]"
        />
      </SplideSlide>
    </Splide>
    <template #fallback>
      <div class="flex justify-center overflow-hidden p-2 md:p-4 mt-5">
        <givehug-home-collection-banner-item
          v-for="(banner, index) in transformedCollectionBanners"
          :key="index"
          :banner="banner"
          class="w-1/2 md:w-1/3 lg:w-1/7 min-w-1/2 md:min-w-1/3 lg:min-w-1/7 max-w-28 px-[15px] banner"
          :index="index"
          :image-class="imageClassMap[index]"
        />
      </div>
    </template>
  </client-only>
</template>
