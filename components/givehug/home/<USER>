<script setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'
import FeatureItem from './FeatureItem.vue'
import DeliveryIcon from './icons/DeliveryIcon.vue'
import ShieldCheckIcon from './icons/ShieldCheckIcon.vue'
import ShieldIcon from './icons/ShieldIcon.vue'

const $viewport = useViewport()

// Custom reactive window width for this component
const windowWidth = ref(0)

const useSlider = computed(() => {
  return windowWidth.value <= 1200
})

// Update window width on resize
onMounted(() => {
  const updateWidth = () => {
    windowWidth.value = window.innerWidth
  }
  updateWidth()
  window.addEventListener('resize', updateWidth)

  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth)
  })
})

// Splide options for screens up to 1300px
const splideOptions = {
  type: 'loop', // Changed to loop type for better pagination calculation
  perPage: 2,
  perMove: 1,
  gap: 0, // Remove all gaps
  padding: 0, // Remove all padding
  arrows: false,
  pagination: true,
  autoplay: true,
  interval: 50000,
  focus: 'left',
  pauseOnHover: true,
  pauseOnFocus: true,
  breakpoints: {
    768: {
      perPage: 1,
      perMove: 1,
      gap: 0, // Remove gap on mobile
      padding: 0 // Remove padding on mobile
    }
  }
}

// Feature items data with component references
const featureItems = [
  {
    iconComponent: ShieldCheckIcon,
    title: 'Personalize, Just for you',
    description: 'Let every gift resonate with the uniqueness of your bond.'
  },
  {
    iconComponent: ShieldIcon,
    title: 'Protection you can feel',
    description: 'Let every gift resonate with the uniqueness of your bond.'
  },
  {
    iconComponent: DeliveryIcon,
    title: 'Here for you, always',
    description: 'From helping hands to heartwarming chats, we’re here for you.'
  }
]
</script>

<template>
  <section class="why__love__us">
    <div class="cs-container">
      <div class="content">
        <h2>Why You'll Love Us</h2>

        <!-- Static grid for desktop (3 items, no scrolling) -->
        <div v-if="!useSlider" class="features-grid">
          <FeatureItem
            v-for="item in featureItems"
            :key="item.title"
            :icon-component="item.iconComponent"
            :title="item.title"
            :description="item.description"
          />
        </div>
      </div>
    </div>

    <Splide
      v-if="useSlider"
      :options="splideOptions"
      class="why-love-us-splide"
    >
      <SplideSlide v-for="item in featureItems" :key="item.title">
        <FeatureItem
          :icon-component="item.iconComponent"
          :title="item.title"
          :description="item.description"
        />
      </SplideSlide>
    </Splide>
  </section>
</template>
