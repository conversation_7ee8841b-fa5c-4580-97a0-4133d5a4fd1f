<script lang="ts" setup>
defineProps({
  banner: {
    type: Object,
    required: true
  }
})
</script>

<template>
  <NuxtLink
    :to="banner.buttonLink"
    class="relative overflow-hidden xl:rounded-[10px] flex-1 h-[400px] flex mx-auto w-full hover:opacity-90 trans__time"
  >
    <div
      :style="{ backgroundColor: banner.bgColor }"
      class="flex-1 p-4 md:p-6 xl:p-8 md:p-12 h-full flex items-center transition givehug-banner-content"
    >
      <div class="max-w-sm">
        <h2 class="text-2xl md:text-3xl text-gray-800 mb-2 font-medium">
          {{ banner.title }}
        </h2>
        <p class="text-[13px] text-stone-600 mb-4 leading-relaxed">
          {{ banner.description }}
        </p>
        <button
          class="bg-black text-white rounded-full py-2.5 px-6 text-sm w-fit"
        >
          {{ banner.buttonText }}
        </button>
      </div>
    </div>
    <div class="givehug-banner-image">
      <img
        :src="banner.image.src"
        :alt="banner.image.alt"
        class="object-cover h-full w-full rounded-none"
      >
    </div>
  </NuxtLink>
</template>

<style>
.givehug-banner-image {
  width: 54%;
  @media (min-width: 1024px) and (max-width: 1279px) {
    width: 40%;
  }
}

.givehug-banner-content {
  @media (min-width: 1024px) and (max-width: 1279px) {
    padding-left: 7rem;
    padding-right: 7rem;
  }
}
</style>
