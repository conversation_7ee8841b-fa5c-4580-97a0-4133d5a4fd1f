<script setup>
const localePath = useLocalePath()
const images = [{
  url: '/images/givehug/shop/poster.jpg',
  color: '#285699',
  text: 'Wall Decor',
  link: '/collection/wall-decor'
}, {
  url: '/images/givehug/shop/mug.jpg',
  color: '#f1641e',
  text: 'Mug',
  link: '/collection/mug'
}, {
  url: '/images/givehug/shop/ornament.jpg',
  color: '#e4a23f',
  text: 'Ornament',
  link: '/collection/ornament'
}, {
  url: '/images/givehug/shop/totebag.jpg',
  color: '#f7db6c',
  text: 'Tote Bag',
  link: '/collection/bag'
}, {
  url: '/images/givehug/shop/pillow.jpg',
  color: '#59b261',
  text: 'Pillow Cover',
  link: '/collection/pillow'
}]
</script>

<template>
  <div>
    <div class="text-center text-2xl md:text-3xl font-medium">
      Shop By Product
    </div>
    <div class="mt-6 md:mt-10">
      <div class="flex flex-wrap -mx-3 -my-3 justify-center">
        <div
          v-for="(image, index) in images"
          :key="index"
          class="xl:w-[20%] md:w-[33.33%] w-1/2 px-3 py-3"
          :class="{
            '<md:hidden': index === images.length - 1
          }"
        >
          <router-link
            :key="image"
            :to="localePath(image.link)"
            class="block relative rounded-[10px] overflow-hidden hover:opacity-80 trans__time"
          >
            <svg
              id="Layer_1" class="relative h-full w-full top-0 left-0 z-1" :style="{
                fill: image.color
              }" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 281 407"
            >
              <!-- Generator: Adobe Illustrator 29.5.1, SVG Export Plug-In . SVG Version: 2.1.0 Build 141)  -->
              <path d="M271,0H10C4.5,0,0,4.5,0,10v387c0,5.5,4.5,10,10,10h260.9c5.5,0,10-4.5,10-10V10c0-5.5-4.5-10-10-10ZM265.9,386c0,3.3-2.7,6-6,6H21.1c-3.3,0-6-2.7-6-6V126.5c0-1.9.9-3.7,2.5-4.8,42.2-28.5,145.9-65.7,245.7,0,1.7,1.1,2.7,3,2.7,5v259.5Z" />
            </svg>
            <div
              class="absolute top-[5%] left-[5%] text-xl z-2 font-medium"
              :style="{
                color: isLightColor(image.color) ? '#000000' : '#ffffff'
              }"
            >
              {{ image.text }}
            </div>
            <img :src="image.url" alt="image" class="w-full absolute bottom-0 left-0 p-0.25 rounded-2xl">
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>
