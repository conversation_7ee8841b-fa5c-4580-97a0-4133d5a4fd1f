<script setup>
const allImages = ref([
  '/images/givehug/customer/1.jpeg',
  '/images/givehug/customer/8.jpeg',
  '/images/givehug/customer/3.jpeg',
  '/images/givehug/customer/6.jpeg',
  '/images/givehug/customer/4.jpeg',
  '/images/givehug/customer/5.jpeg',
  '/images/givehug/customer/2.jpeg',
  '/images/givehug/customer/7.jpeg'
])

const localePath = useLocalePath()
</script>

<template>
  <div class="cs-container">
    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 items-center gap-x-[17px] gap-y-6">
      <div class="col-span-2 p-4">
        <p class="md:text-xs lg:text-[13px] mb-2 text-stone-600 leading-relaxed">
          {{ $t('{brand} Family of Delighted Shoppers', { brand: 'GiveHug' }) }}!
        </p>
        <h2 class="text-2xl lg:text-3xl font-medium mb-6">
          {{ $t('Happy Customers') }}
        </h2>
        <!-- <NuxtLink
          :to="localePath('/collection')"
          class="rounded-full px-5 py-2.5 shadow-sm trans__time text-sm !hover:bg-primary hover:text-white"
          style="background-color: rgba(14, 14, 14, 0.1);"
        >
          {{ $t('Show more') }}
        </NuxtLink> -->
      </div>
      <div
        v-for="image in allImages"
        :key="image"
        class="aspect-square rounded-[10px] overflow-hidden col-span-1"
      >
        <img :src="image" class="w-full h-full object-cover" alt="Customer image">
      </div>
    </div>
  </div>
</template>
