<script lang="ts" setup>
import CartItemMobile from '~/components/givehug/cart/cartItemMobile.vue'
import Quantity from '~/components/givehug/shared/quantity.vue'

const props = defineProps({
  index: {
    default: 0,
    type: Number
  },
  cartItem: {
    default: undefined,
    type: Object as PropType<CartItem>
  }
})
const localePath = useLocalePath()
const {
  isHasCampaignData,
  // campaignData,
  // isEditCart,
  isShowModalDeleteCartItem,
  currentVariant,
  // updateCartItem,
  // duplicateCartItem,
  removeCartItem,
  getProductUrl,
  updateQuantity,
  totalPrice,
  totalPriceBundleWithCustomOptionFee,
  priceWithCustomOptionFee
  // totalPriceWithCustomOptionFee,
} = useCartItem(props.cartItem as CartItem)

// const suffix = computed(() => {
//   if (!props.cartItem?.options) {
//     return ''
//   }
//   const suffix = [] as string[]
//   const options = props.cartItem?.optionList
//   if (!options) {
//     return ''
//   }
//   Object.keys(options).forEach((key) => {
//     if (options[key].length > 1) {
//       suffix.push((props.cartItem as CartItem).options[key])
//     }
//   })

//   if (suffix.length === 0) {
//     return ''
//   }

//   return suffix.join(' / ')
// })
</script>

<template>
  <div>
    <div v-if="isHasCampaignData" class="<md:hidden flex cart-item grid grid-cols-12" data-test-id="cart-item">
      <!-- Item Info -->
      <div class="col-span-7 flex gap-4">
        <div class="h-25 w-25">
          <!-- Use base64 image if available for AI mockup campaigns -->
          <img
            v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
            :src="cartItem.design_image_base64"
            :alt="cartItem?.campaign_title"
            :title="$t('Click to view image')"
            class="!w-25 h-25"
            @click="uiManager().viewImage(cartItem.design_image_base64)"
          >
          <common-image
            v-else
            img-class="!w-25 h-25"
            :image="{
              path: cartItem?.thumb_url,
              color: cartItem?.options?.color
            }"
            :alt="cartItem?.campaign_title"
            :title="$t('Click to view image')"
            @click="uiManager().viewImage(cartItem?.thumb_url)"
          />
        </div>
        <div class="flex items-center flex-1">
          <div>
            <GivehugSharedItemTitle
              :product-url="getProductUrl(cartItem?.product_url) as string"
              :campaign-title="cartItem?.campaign_title || ''"
              :product-name="cartItem?.product_name || ''"
              :option-list="cartItem?.optionList || {}"
              :current-options="cartItem?.options || {}"
            />
            <GivehugCartCustomOptions v-if="cartItem" :cart-item="cartItem" />
            <button
              class="btn-text text-xs opacity-50 hover:opacity-100"
              @click="isShowModalDeleteCartItem = true"
            >
              Remove
            </button>
          </div>
        </div>
      </div>
      <!-- Item Price -->
      <div class="col-span-5 grid grid-cols-3 items-center">
        <div class="font-semibold text-black pt-1.5 text-left" data-test-id="cart-item-price">
          <div v-if="totalPriceBundleWithCustomOptionFee" class="center-flex flex-wrap">
            <span>
              {{ $formatPrice(priceWithCustomOptionFee * (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100, cartItem?.currency_code) }}
            </span>
            <del class="text-gray-500 text-xs font-normal">
              {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
            </del>
          </div>
          <span v-else>
            {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
          </span>
        </div>
        <!-- Item Quantity -->
        <div class="font-semibold text-black" data-test-id="cart-item-price">
          <Quantity
            :quantity="cartItem?.quantity || 0"
            variant="xs"
            class="mx-auto"
            @update-quantity="updateQuantity"
          />
        </div>
        <!-- Item Total Price -->
        <div class="font-semibold text-black pt-1.5 text-right" data-test-id="cart-item-price">
          <span v-if="currentVariant?.out_of_stock" class="text-danger" data-test-id="cart-item-oos">
            {{ $t('Out of stock') }}
          </span>
          <div v-else-if="totalPriceBundleWithCustomOptionFee" class="flex flex-wrap gap-x-2">
            <span>
              {{ $formatPrice(totalPriceBundleWithCustomOptionFee, cartItem?.currency_code) }}
            </span>
            <del class="text-gray-500">
              {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
            </del>
          </div>
          <span v-else-if="totalPrice" data-test-id="cart-item-total-price">
            {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
          </span>
        </div>
      </div>
    </div>
    <default-cart-fallback-item v-else :cart-item="cartItem" />
    <givehug-cart-confirm-delete
      :state="isShowModalDeleteCartItem"
      @confirm="removeCartItem"
      @close="isShowModalDeleteCartItem = false"
    />
    <CartItemMobile
      v-if="isHasCampaignData"
      class="md:hidden"
      :cart-item="cartItem"
      :total-price="totalPrice"
      :total-price-bundle-with-custom-option-fee="totalPriceBundleWithCustomOptionFee"
      :price-with-custom-option-fee="priceWithCustomOptionFee"
      :product-url="getProductUrl(cartItem?.product_url) as string"
      :is-out-of-stock="Boolean(currentVariant?.out_of_stock)"
      @remove-cart-item="isShowModalDeleteCartItem = true"
      @update-quantity="updateQuantity"
    />
  </div>
</template>
