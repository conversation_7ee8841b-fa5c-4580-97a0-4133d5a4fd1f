<script setup lang="ts">
defineProps<{
  cartItem: CartItem
}>()
</script>

<template>
  <div>
    <div v-if="cartItem?.custom_options">
      <ul>
        <li v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
          <div v-if="key === 'customImage'">
            {{ $t('Your image') }}:
            <a
              :href="$imgUrl({ path: value, type: 'full' })"
              target="_blank"
              class="btn-text text-blue-900"
              @click.prevent="uiManager().viewImage(value)"
            >{{ $t('View image') }}</a>
          </div>
          <div v-else class="text-overflow-hidden">
            {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
          </div>
        </li>
      </ul>
    </div>
    <div v-if="cartItem?.customer_custom_options?.length">
      <ul v-for="(groupOptions, groupNumber) in cartItem.customer_custom_options" :key="groupNumber" class="pl-5 list-disc mt-3">
        <li v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
          <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
            {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
              :href="$imgUrl({ path: customOption.value as string, type: 'full' })"
              target="_blank"
              class="btn-text text-blue-900"
              @click.prevent="uiManager().viewImage(customOption.value as string)"
            >{{ $t('View image') }}</a>
          </div>
          <div v-else class="text-overflow-hidden">
            {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
