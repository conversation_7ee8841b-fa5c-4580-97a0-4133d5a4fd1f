<script lang="ts" setup>
const props = defineProps<{
  cartItem?: CartItem
}>()

const {
  removeCartItem
} = useCartItem(props.cartItem as CartItem)
</script>

<template>
  <div class="p-4 flex border-b gap-4" data-test-id="cart-item">
    <!-- Use base64 image if available for AI mockup campaigns -->
    <img
      v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
      :src="cartItem.design_image_base64"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      @click="uiManager().viewImage(cartItem.design_image_base64)"
    >
    <common-image
      v-else
      img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: cartItem?.thumb_url,
        color: cartItem?.options?.color
      }"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(cartItem?.thumb_url)"
    />
    <div class="w-full max-w-[calc(100%-90px)]" data-test-id="cart-item-not-exist">
      <h3 class="font-semibold w-full">
        {{ $t('The campaign does not exist or has been deleted. Kindly remove the product.') }}
      </h3>
      <button class="btn-border p-2 py-1 text-xl mt-3" data-test-id="cart-item-remove" @click="removeCartItem">
        {{ $t('Remove') }}
      </button>
    </div>
  </div>
</template>
