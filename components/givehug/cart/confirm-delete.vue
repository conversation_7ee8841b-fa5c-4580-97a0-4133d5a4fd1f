<script setup lang="ts">
defineProps<{
  state: boolean
}>()
defineEmits(['confirm', 'close'])

const yesButtonRef = ref<HTMLElement>()

function focusButton() {
  nextTick(() => {
    yesButtonRef.value?.focus()
  })
}
</script>

<template>
  <common-modal
    :model-value="state"
    :close-icon="false"
    @close-modal="$emit('close')"
    @shown="focusButton"
  >
    <div
      class="max-w-[90vw] text-center p-9"
      data-test-id="modal-confirm-remove-item"
    >
      <h4 class="mb-6 font-medium">
        {{ $t('Are you sure you want to remove this item?') }}
      </h4>
      <div class="grid grid-cols-2 gap-3">
        <button class="h-[45px] font-medium text-sm border border-black hover:bg-givehug-gray rounded-[10px] center-flex" @click="$emit('confirm')">
          {{ $t('Yes') }}
        </button>

        <button
          class="h-[45px] font-medium text-sm bg-black hover:bg-gray-800 border border-black text-white !shadow-none rounded-[10px]"
          @click="$emit('close')"
        >
          {{ $t('Cancel') }}
        </button>
      </div>
    </div>
  </common-modal>
</template>
