<script lang="ts" setup>
const props = defineProps({
  cartItem: {
    required: true,
    type: Object as PropType<CartItem>
  },
  convertCurrencyRate: {
    default: 1,
    type: Number
  },
  convertCurrencyCode: {
    default: undefined,
    type: String as PropType<CurrencyCode>
  },
})

const totalPrice = computed(() => (props.cartItem?.quantity || 0) * (props.cartItem?.variantPrice || props.cartItem?.price || 0))

</script>

<template>
  <div class="p-4 flex border-b">
    <common-image
      img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: cartItem?.thumb_url,
        type: 'list',
        color: cartItem?.options?.color
      }"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(cartItem?.thumb_url)"
    />
    <div class="w-full max-w-[calc(100%-90px)] px-1">
      <h6 class="text-overflow-hidden font-light" :title="cartItem?.campaign_title">
        {{ cartItem?.product_name }}
      </h6>
      <div v-if="cartItem?.options" class="font-semibold">
        <span
          v-for="(item, optionIndex) in Object.keys(cartItem?.options).filter(key=>(!cartItem?.options[key].startsWith('__')))"
          :key="optionIndex"
          class="uppercase"
        >
          <span v-if="optionIndex>0">&nbsp;/&nbsp;</span>
          {{ cartItem?.options && cartItem?.options[item] }}
        </span>
      </div>
      <div class="px-1 mb-1 font-weight-500 flex flex-wrap">
        <div class="mt-2 min-w-40">
          <div class="flex justify-center">
            <button
              class="btn-border h-[30px] min-w-[30px] center-flex"
              @click="cartItem?.quantity && cartItem?.quantity >= 2 ? $emit('updateQuantity', (cartItem?.quantity || 2) - 1) : ''"
            >
              <i class="icon-sen-minus" />
            </button>
            <common-dropdown
              btn-class="text-sm btn-border py-1 w-full"
              class="mx-2 w-full"
            >
              <span>
                {{ cartItem?.quantity }}
              </span>
              <template #content>
                <div
                  v-for="item in 50"
                  :key="item"
                  class="min-w-[50vw] md:min-w-16 btn-text px-4"
                  :value="item"
                  :class="{'bg-primary text-contrast': cartItem?.quantity == item}"
                  @click="$emit('updateQuantity', item)"
                >
                  {{ item }}
                </div>
              </template>
            </common-dropdown>
            <button
              class="btn-border h-[30px] min-w-[30px] center-flex"
              @click="$emit('updateQuantity', (cartItem?.quantity || 0) + 1)"
            >
              <i class="icon-sen-plus" />
            </button>
          </div>
        </div>
        <div class="min-w-40 font-medium center-flex mt-2">
          {{ $formatPrice(totalPrice, cartItem?.currency_code, convertCurrencyCode) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
</style>
