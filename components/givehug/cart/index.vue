<script lang="ts" setup>
import { useCartStore } from '~~/store/cart'
import SubtotalBox from '~/components/givehug/cart/subtotalBox.vue'
import CarouselRoot from '~/components/givehug/shared/CarouselRoot.vue'
import { useCampaignStore } from '~/store/campaign'
import { useComboCartStore } from '~/store/comboCart'
import { useListingStore } from '~/store/listing'

const localePath = useLocalePath()

const listCartItem = computed(() => useCartStore().listCartItem)
const comboCartItem = computed(() => useComboCartStore().enoughCombo)
const totalPrice = computed(() => {
  return useCartStore().getTotalPrice
})

const spriteUrl = computed(() => {
  return `--sprite-url: url("${cdnURL.value}images/logo_cart_sprite.webp")`
})

const promotionsList = ref<Promotion[]>()
const relatedProducts = ref<Product[]>()

const campaignIds = computed(() => {
  return listCartItem.value.map(item => item.campaign_id)
})

onMounted(async () => {
  // Related Products
  const listRelatedProducts: RelatedProductPostData = {
    type: 'post_sale',
    filter: listCartItem.value.map((item) => {
      return {
        product_id: item.product_id,
        campaign_id: item.campaign_id,
        template_id: item.template_id
      }
    })
  }

  if (listRelatedProducts.filter.length > 0) {
    relatedProducts.value = await useListingStore().postRelatedProduct(listRelatedProducts)
  }

  promotionsList.value = await useCampaignStore().getPromotion(campaignIds.value)
})

const listCartItemFiltered = computed(() => {
  const cartItemInCombo = comboCartItem.value.map(combo => combo.items.map(item => item.id)).flat()
  return listCartItem.value.filter(item => !cartItemInCombo.includes(item.id))
})
</script>

<template>
  <main id="cartPage" :style="spriteUrl" class="cs-container min-h-95 overflow-x-hidden">
    <GivehugSharedProgressBar class="mt-7 md:mt-15" :is-fade="listCartItem.length === 0" />
    <h1 v-if="listCartItem.length" class="mt-7 md:mt-15 font-semibold text-3xl capitalize text-center md:text-left capitalize">
      {{ $t('Your cart') }}
    </h1>
    <client-only>
      <div v-if="listCartItem.length" class="mt-6 md:mt-12">
        <div>
          <!-- Header -->
          <div class="grid grid-cols-12 font-semibold text-black pb-3 border-b border-[#F4F4F4]">
            <div class="col-span-7">
              {{ $t('Product') }}
            </div>
            <div class="<md:hidden col-span-5 grid grid-cols-3 items-center">
              <div class="text-left">
                {{ $t('Price') }}
              </div>
              <div class="text-center">
                {{ $t('Quantity') }}
              </div>
              <div class="text-right">
                {{ $t('Total') }}
              </div>
            </div>
          </div>
          <givehug-cart-item
            v-for="(cartItem, index) in listCartItemFiltered"
            :key="cartItem.id"
            class="py-4 border-b border-[#F4F4F4]"
            :index="index"
            :cart-item="cartItem"
          />
          <default-cart-combo
            v-for="(comboItem, index) in comboCartItem"
            :key="comboItem.id"
            :index="index"
            :combo="comboItem"
          />
        </div>
        <div>
          <SubtotalBox
            class="w-full md:w-5/12 ml-auto mt-12"
            :total-price="totalPrice"
            @create-order="createOrder"
          />
        </div>
      </div>
      <GivehugCartEmptySection v-else class="mt-7 md:mt-15" />
      <CarouselRoot
        v-if="relatedProducts?.length && !storeInfo().disable_related_product"
        class="mt-16"
        :products="relatedProducts"
        :title="$t(`Frequently bought together`)"
        :max-items="6"
      />
    </client-only>
  </main>
</template>
