<script setup lang="ts">
const localePath = useLocalePath()
</script>

<template>
  <section class="empty__cart">
    <svg width="205" height="203" viewBox="0 0 205 203" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_1561_2337)">
        <path d="M102.908 197.956C151.249 197.956 190.438 158.766 190.438 110.426C190.438 62.0849 151.249 22.8955 102.908 22.8955C54.5673 22.8955 15.373 62.0849 15.373 110.426C15.373 158.766 54.5624 197.956 102.903 197.956H102.908Z" fill="url(#paint0_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M101.167 152.326C101.167 148.708 104.097 145.778 107.715 145.778H145.7C150.892 145.778 155.851 147.314 159.668 150.122C163.486 152.93 166.518 157.343 166.518 162.823C166.518 168.303 163.491 172.715 159.673 175.523C155.855 178.332 150.892 179.872 145.7 179.872H55.4443C51.8267 179.872 48.8965 176.942 48.8965 173.324C48.8965 169.707 51.8267 166.777 55.4443 166.777H145.7C148.391 166.777 150.566 165.967 151.911 164.973C153.262 163.983 153.423 163.144 153.423 162.818C153.423 162.491 153.262 161.662 151.911 160.668C150.566 159.678 148.391 158.869 145.695 158.869L107.33 157.581C103.712 157.581 101.167 155.938 101.167 152.321V152.326Z" fill="url(#paint1_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M160.751 74.142C161.614 70.9241 164.529 68.6814 167.864 68.6814H197.644C201.71 68.6814 205.006 71.9772 205.006 76.0434C205.006 80.1096 201.71 83.4055 197.644 83.4055H173.52L164.929 115.511C163.876 119.44 159.844 121.771 155.914 120.718C151.984 119.665 149.654 115.633 150.707 111.703L160.756 74.1371L160.751 74.142Z" fill="url(#paint2_linear_1561_2337)" />
        <path d="M154.388 46.5906H65.0686L58.0234 61.9923H147.338L154.393 46.5906H154.388Z" fill="url(#paint3_linear_1561_2337)" />
        <path d="M109.392 44.26H50.9688L58.0236 61.9874H116.437L109.392 44.26Z" fill="url(#paint4_linear_1561_2337)" />
        <path d="M155.583 44.26H123.492L116.438 61.9874H148.528L155.583 44.26Z" fill="url(#paint5_linear_1561_2337)" />
        <path d="M148.533 61.9873H90.1094V120.411H148.533V61.9873Z" fill="url(#paint6_linear_1561_2337)" />
        <path d="M116.442 61.9873H58.0186V120.411H116.442V61.9873Z" fill="url(#paint7_linear_1561_2337)" />
        <path d="M77.2486 202.504C86.0586 202.504 93.1964 195.362 93.1964 186.557C93.1964 177.752 86.0538 170.609 77.2486 170.609C68.4434 170.609 61.3008 177.752 61.3008 186.557C61.3008 195.362 68.4434 202.504 77.2486 202.504Z" fill="currentColor" />
        <path d="M156.402 88.4224H46.4886C38.249 88.4224 32.1643 95.409 34.0268 102.722L42.9831 145.724C44.3287 151.004 49.4968 154.749 55.4449 154.749L152.355 161.019L168.888 102.742C172.959 90.8114 164.661 88.4224 156.402 88.4224Z" fill="currentColor" />
        <path d="M76.8044 194.596C81.2314 194.596 84.8149 191.008 84.8149 186.586C84.8149 182.164 81.2265 178.575 76.8044 178.575C72.3823 178.575 68.7939 182.164 68.7939 186.586C68.7939 191.008 72.3823 194.596 76.8044 194.596Z" fill="url(#paint8_linear_1561_2337)" />
        <path d="M114.487 121.483C117.017 121.503 119.041 123.57 119.011 126.1L118.602 159.449C118.573 161.979 116.496 164.012 113.965 163.988C111.435 163.969 109.411 161.901 109.441 159.371L109.85 126.022C109.88 123.492 111.957 121.459 114.487 121.483Z" fill="url(#paint9_linear_1561_2337)" />
        <path d="M189.624 94.7995L189.634 96.3255C189.648 98.4317 187.952 100.143 185.845 100.153L32.2375 100.548C30.1313 100.553 28.4102 98.851 28.3956 96.7399L28.3858 95.2139C28.3712 93.1077 30.0679 91.3964 32.1741 91.3866L185.782 90.9917C187.888 90.9868 189.609 92.6884 189.624 94.7995Z" fill="currentColor" />
        <path d="M140.366 121.595C142.896 121.615 144.92 123.682 144.89 126.213L144.481 159.561C144.451 162.091 142.375 164.125 139.844 164.1C137.314 164.081 135.29 162.013 135.32 159.483L135.729 126.135C135.758 123.604 137.835 121.571 140.366 121.595Z" fill="url(#paint10_linear_1561_2337)" />
        <path d="M88.608 121.376C91.1384 121.395 93.1617 123.463 93.1325 125.993L92.7229 159.342C92.6937 161.872 90.6167 163.905 88.0863 163.881C85.5559 163.861 83.5326 161.794 83.5618 159.264L83.9714 125.915C84.0006 123.385 86.0776 121.352 88.608 121.376Z" fill="currentColor" />
        <path d="M62.4459 121.376C64.9763 121.395 66.9996 123.463 66.9704 125.993L66.5608 159.342C66.5316 161.872 64.4546 163.905 61.9242 163.881C59.3938 163.861 57.3705 161.794 57.3997 159.264L57.8093 125.915C57.8385 123.385 59.9155 121.352 62.4459 121.376Z" fill="url(#paint11_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M134.691 2.35481C133.575 0.399732 131.508 -0.516864 130.069 0.302222C128.631 1.12131 128.368 3.3738 129.484 5.32888C129.519 5.38251 129.548 5.44101 129.582 5.49464C128.851 5.64578 128.163 6.35761 127.895 7.34246C127.549 8.6101 128.027 9.8436 128.958 10.102C129.894 10.3555 130.932 9.53644 131.279 8.26881C131.371 7.92265 131.405 7.57649 131.386 7.2547C132.307 7.75688 133.307 7.84464 134.111 7.38634C134.818 6.98167 135.242 6.23572 135.355 5.3435C135.55 5.44589 135.759 5.52877 135.983 5.58728C137.251 5.93344 138.485 5.45564 138.743 4.52442C138.997 3.58832 138.177 2.54983 136.91 2.20367C136.096 1.9794 135.291 2.09641 134.755 2.46207C134.735 2.42795 134.716 2.39382 134.696 2.35969L134.691 2.35481Z" fill="url(#paint12_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M68.6964 10.0096C68.0431 10.6775 67.2191 11.0237 66.3854 10.8969C65.4201 10.7458 64.6644 9.99008 64.2695 8.93697C64.0939 9.17587 63.8697 9.40014 63.6064 9.59516C62.5435 10.3704 61.2223 10.3655 60.6518 9.58541C60.0814 8.80533 60.4763 7.54257 61.5343 6.76737C62.3778 6.15305 63.3821 6.02629 64.0501 6.39195C64.4011 4.17847 66.0003 2.59393 67.6336 2.84258C69.2425 3.09123 70.281 5.03656 69.9884 7.21591C70.6174 7.28417 71.2951 7.68884 71.7826 8.35191C72.5578 9.40989 72.553 10.736 71.7729 11.3065C70.9928 11.8769 69.73 11.482 68.9499 10.424C68.8524 10.2875 68.7647 10.151 68.6915 10.0096H68.6964Z" fill="url(#paint13_linear_1561_2337)" />
        <path d="M9.24397 96.506C9.63888 94.8678 11.9645 94.8678 12.3594 96.506L13.6904 102.01C13.8318 102.595 14.2901 103.049 14.8703 103.19L20.3748 104.521C22.0129 104.916 22.0129 107.242 20.3748 107.637L14.8703 108.968C14.2852 109.109 13.8269 109.567 13.6904 110.148L12.3594 115.652C11.9645 117.29 9.63888 117.29 9.24397 115.652L7.91295 110.148C7.77156 109.563 7.31326 109.109 6.7282 108.968L1.22375 107.637C-0.414426 107.242 -0.414426 104.916 1.22375 104.521L6.7282 103.19C7.31326 103.049 7.77156 102.591 7.91295 102.01L9.24397 96.506Z" fill="url(#paint14_linear_1561_2337)" />
        <path d="M178.659 151.448C179.054 149.81 181.38 149.81 181.774 151.448L183.105 156.953C183.247 157.538 183.705 157.991 184.285 158.132L189.79 159.463C191.428 159.858 191.428 162.184 189.79 162.579L184.285 163.91C183.7 164.051 183.247 164.51 183.105 165.09L181.774 170.594C181.38 172.232 179.054 172.232 178.659 170.594L177.328 165.09C177.187 164.505 176.728 164.051 176.148 163.91L170.644 162.579C169.005 162.184 169.005 159.858 170.644 159.463L176.148 158.132C176.733 157.991 177.187 157.533 177.328 156.953L178.659 151.448Z" fill="url(#paint15_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M32.6417 57.2239C34.4797 57.2239 35.9668 55.7319 35.9668 53.8988C35.9668 52.0656 34.4749 50.5737 32.6417 50.5737C30.8085 50.5737 29.3166 52.0656 29.3166 53.8988C29.3166 55.7319 30.8036 57.2239 32.6417 57.2239ZM32.6417 59.7201C35.8546 59.7201 38.463 57.1117 38.463 53.8988C38.463 50.6858 35.8546 48.0774 32.6417 48.0774C29.4287 48.0774 26.8203 50.6858 26.8203 53.8988C26.8203 57.1117 29.4287 59.7201 32.6417 59.7201Z" fill="url(#paint16_linear_1561_2337)" />
        <path d="M19.1998 142.745C18.9462 141.751 19.8677 140.849 20.8574 141.127L27.5418 143.004C28.5315 143.282 28.8484 144.53 28.1122 145.247L23.1441 150.098C22.4078 150.814 21.1695 150.468 20.9159 149.474L19.1998 142.745Z" fill="url(#paint17_linear_1561_2337)" />
        <path d="M192.203 119.825C193.197 119.572 194.094 120.493 193.816 121.483L191.93 128.167C191.652 129.157 190.404 129.474 189.687 128.738L184.845 123.765C184.129 123.029 184.48 121.79 185.474 121.537L192.203 119.83V119.825Z" fill="url(#paint18_linear_1561_2337)" />
        <path fill-rule="evenodd" clip-rule="evenodd" d="M170.385 50.0862C171.862 50.0862 173.056 48.8917 173.056 47.4144C173.056 45.9371 171.862 44.7426 170.385 44.7426C168.907 44.7426 167.713 45.9371 167.713 47.4144C167.713 48.8917 168.907 50.0862 170.385 50.0862ZM170.385 52.09C172.969 52.09 175.06 49.9984 175.06 47.4144C175.06 44.8304 172.969 42.7388 170.385 42.7388C167.801 42.7388 165.709 44.8304 165.709 47.4144C165.709 49.9984 167.801 52.09 170.385 52.09Z" fill="url(#paint19_linear_1561_2337)" />
        <path d="M34.0707 86.9939H168.381C171.121 86.9939 173.34 89.2171 173.34 91.9523C173.34 94.6923 171.117 96.9107 168.381 96.9107H34.0707C31.3307 96.9107 29.1123 94.6875 29.1123 91.9523C29.1123 89.2123 31.3355 86.9939 34.0707 86.9939Z" fill="currentColor" />
        <path d="M65.8346 12.2913C64.5182 17.5471 68.7745 28.5852 75.3418 29.4628C87.4965 31.0815 85.2635 16.8694 78.0136 19.5411C69.384 22.72 78.0185 36.3178 89.8416 39.3845" stroke="currentColor" stroke-width="0.765456" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="3.05 3.05" />
        <path d="M134.906 10.9651C142.127 25.0212 122.995 40.028 117.695 42.8119" stroke="currentColor" stroke-width="0.765456" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="3.05 3.05" />
        <path d="M127.987 202.504C136.797 202.504 143.935 195.362 143.935 186.557C143.935 177.752 136.792 170.609 127.987 170.609C119.182 170.609 112.039 177.752 112.039 186.557C112.039 195.362 119.182 202.504 127.987 202.504Z" fill="currentColor" />
        <path d="M127.934 194.596C132.361 194.596 135.945 191.008 135.945 186.586C135.945 182.164 132.356 178.575 127.934 178.575C123.512 178.575 119.924 182.164 119.924 186.586C119.924 191.008 123.512 194.596 127.934 194.596Z" fill="url(#paint20_linear_1561_2337)" />
      </g>
      <defs>
        <linearGradient id="paint0_linear_1561_2337" x1="102.23" y1="-5.61162" x2="104.025" y2="301.785" gradientUnits="userSpaceOnUse">
          <stop stop-color="white" stop-opacity="0" />
          <stop offset="1" stop-color="#8EE2F4" />
        </linearGradient>
        <linearGradient id="paint1_linear_1561_2337" x1="53.1674" y1="112.132" x2="88.7636" y2="147.514" gradientUnits="userSpaceOnUse">
          <stop stop-color="white" />
          <stop offset="1" stop-color="#8EE2F4" />
        </linearGradient>
        <linearGradient id="paint2_linear_1561_2337" x1="232.547" y1="93.527" x2="152.799" y2="95.014" gradientUnits="userSpaceOnUse">
          <stop stop-color="white" />
          <stop offset="1" stop-color="#8EE2F4" />
        </linearGradient>
        <linearGradient id="paint3_linear_1561_2337" x1="57.8235" y1="52.3242" x2="154.666" y2="56.2587" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#41ACC4" />
        </linearGradient>
        <linearGradient id="paint4_linear_1561_2337" x1="83.7029" y1="44.26" x2="83.7029" y2="61.9874" gradientUnits="userSpaceOnUse">
          <stop stop-color="#CCF6FF" />
          <stop offset="1" stop-color="#B9ECF8" />
        </linearGradient>
        <linearGradient id="paint5_linear_1561_2337" x1="136.008" y1="44.26" x2="136.008" y2="61.9874" gradientUnits="userSpaceOnUse">
          <stop stop-color="#92DDEC" />
          <stop offset="1" stop-color="#6CCFE3" />
        </linearGradient>
        <linearGradient id="paint6_linear_1561_2337" x1="88.598" y1="90.7138" x2="173.003" y2="92.0448" gradientUnits="userSpaceOnUse">
          <stop offset="0.39" stop-color="#77D3E6" />
          <stop offset="1" stop-color="#E9FCFF" />
        </linearGradient>
        <linearGradient id="paint7_linear_1561_2337" x1="75.7118" y1="88.1981" x2="107.417" y2="96.4572" gradientUnits="userSpaceOnUse">
          <stop stop-color="#E9FBFF" />
          <stop offset="1" stop-color="#BCF3FF" />
        </linearGradient>
        <linearGradient id="paint8_linear_1561_2337" x1="68.3795" y1="186.449" x2="91.5333" y2="186.815" gradientUnits="userSpaceOnUse">
          <stop stop-color="white" />
          <stop offset="1" stop-color="#BCF3FF" />
        </linearGradient>
        <linearGradient id="paint9_linear_1561_2337" x1="108.044" y1="142.667" x2="121.276" y2="142.82" gradientUnits="userSpaceOnUse">
          <stop stop-color="#62C9E0" />
          <stop offset="1" stop-color="#62C9E0" />
        </linearGradient>
        <linearGradient id="paint10_linear_1561_2337" x1="133.923" y1="142.778" x2="147.154" y2="142.93" gradientUnits="userSpaceOnUse">
          <stop stop-color="#62C9E0" />
          <stop offset="1" stop-color="#62C9E0" />
        </linearGradient>
        <linearGradient id="paint11_linear_1561_2337" x1="56.0078" y1="142.557" x2="69.2391" y2="142.714" gradientUnits="userSpaceOnUse">
          <stop stop-color="#62C9E0" />
          <stop offset="1" stop-color="#62C9E0" />
        </linearGradient>
        <linearGradient id="paint12_linear_1561_2337" x1="127.749" y1="5.05097" x2="138.821" y2="5.12898" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint13_linear_1561_2337" x1="63.3431" y1="4.99268" x2="70.4419" y2="12.6326" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint14_linear_1561_2337" x1="-5.31919" y1="105.974" x2="26.9567" y2="106.184" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint15_linear_1561_2337" x1="164.091" y1="160.921" x2="196.372" y2="161.131" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint16_linear_1561_2337" x1="26.7862" y1="53.8598" x2="38.5069" y2="53.9329" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint17_linear_1561_2337" x1="17.7712" y1="149.23" x2="27.454" y2="139.898" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint18_linear_1561_2337" x1="185.718" y1="118.382" x2="195.035" y2="128.08" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint19_linear_1561_2337" x1="165.685" y1="47.3803" x2="175.099" y2="47.4388" gradientUnits="userSpaceOnUse">
          <stop stop-color="#90E3F5" />
          <stop offset="1" stop-color="#65C8DE" />
        </linearGradient>
        <linearGradient id="paint20_linear_1561_2337" x1="119.509" y1="186.449" x2="142.658" y2="186.815" gradientUnits="userSpaceOnUse">
          <stop stop-color="white" />
          <stop offset="1" stop-color="#BCF3FF" />
        </linearGradient>
        <clipPath id="clip0_1561_2337">
          <rect width="205.001" height="202.504" fill="white" />
        </clipPath>
      </defs>
    </svg>

    <div>
      <h6 data-test-id="cart-is-empty">
        {{ $t('Your cart is Empty') }}
      </h6>
      <p>
        {{ $t('Looks like you haven’t added anything to your cart yet') }}
      </p>
      <nuxt-link :to="localePath('/')">
        {{ $t('Go home') }}
      </nuxt-link>
    </div>
  </section>
</template>

<style scoped>
.empty__cart svg {
  color: var(--color-primary);
  margin: 0 auto;
}

.empty__cart div {
  margin: 34px auto 0;
  width: fit-content;
  text-align: center;
  max-width: 260px;
}

.empty__cart h6 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 3.3rem;
}

.empty__cart p {
  margin: 0.75rem 0;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.empty__cart a {
  display: inline-block;
  text-transform: capitalize;
  color: #ffffff;
  background-color: var(--color-primary);
  border-radius: 9999px;
  padding: 0.5rem 1.25rem;
  transition: all 0.2s ease-in;
}

.empty__cart a:hover {
  opacity: 0.85;
}
</style>
