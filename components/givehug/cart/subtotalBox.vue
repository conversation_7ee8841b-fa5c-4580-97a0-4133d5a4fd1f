<script setup lang="ts">
import PaymentGatewayAccept from '~/components/givehug/common/paymentGatewayAccept.vue'

defineProps<{
  totalPrice: number
}>()

defineEmits(['createOrder'])

const localePath = useLocalePath()
</script>

<template>
  <div class="md:h-max sticky top-[100px] z-1">
    <div>
      <div class="space-y-4 pb-4 border-b border-[#F4F4F4]">
        <div class="text-sm text-black">
          Taxes and <NuxtLink :to="localePath('/page/shipping-policy')" class="underline">
            shipping
          </NuxtLink> calculated at checkout
        </div>
        <div class="flex justify-between text-sm text-black">
          <div>
            Total quantity
          </div>
          <div>
            {{ totalQuantity().value }} {{ $t('items') }}
          </div>
        </div>
      </div>
      <div class="flex justify-between text-xl font-semibold mt-4">
        <div class="text-black">
          Total
        </div>
        <div class="text-primary">
          {{ $formatPrice(totalPrice) }}
        </div>
      </div>
    </div>
    <form class="mt-9" @submit.prevent="$emit('createOrder')">
      <div class="<md:(bottom-fixed p-4 z-3) w-full bg-transparent">
        <button
          type="submit"
          class="w-full py-4 font-semibold text-lg z-1 bg-black text-white rounded-full hover:opacity-80 trans__time"
          dusk="proceed-to-checkout-button"
          data-test-id="cart-proceed-to-checkout-button"
          :disabled="!!isLoading"
        >
          <common-loading-dot v-if="!!isLoading" />
          <span v-else class="flex gap-1 items-center justify-center">
            {{ $t('Proceed to checkout') }}
          </span>
        </button>
      </div>
    </form>

    <PaymentGatewayAccept class="mt-6" />
  </div>
</template>
