<script lang="ts" setup>
import Quantity from '~/components/givehug/shared/quantity.vue'

defineProps({
  cartItem: {
    default: undefined,
    type: Object as PropType<CartItem>
  },
  totalPrice: {
    default: 0,
    type: Number
  },
  totalPriceBundleWithCustomOptionFee: {
    default: 0,
    type: Number
  },
  priceWithCustomOptionFee: {
    default: 0,
    type: Number
  },
  productUrl: {
    default: '',
    type: String
  },
  isOutOfStock: {
    default: false,
    type: Boolean
  }
})

defineEmits([
  'remove-cart-item',
  'update-quantity'
])
</script>

<template>
  <div class="flex cart-item overflow-hidden" data-test-id="cart-item">
    <div class="flex gap-4">
      <div class="h-25 w-25 flex-shrink-0">
        <!-- Use base64 image if available for AI mockup campaigns -->
        <img
          v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
          :src="cartItem.design_image_base64"
          :alt="cartItem?.campaign_title"
          :title="$t('Click to view image')"
          class="!w-25 h-25 btn"
          @click="uiManager().viewImage(cartItem.design_image_base64)"
        >
        <common-image
          v-else
          img-class="!w-25 h-25 btn"
          :image="{
            path: cartItem?.thumb_url,
            color: cartItem?.options?.color
          }"
          :alt="cartItem?.campaign_title"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(cartItem?.thumb_url)"
        />
      </div>
      <div class="my-auto overflow-hidden flex-1 overflow-hidden">
        <div class="flex items-center">
          <GivehugSharedItemTitle
            :product-url="productUrl"
            :campaign-title="cartItem?.campaign_title || ''"
            :product-name="cartItem?.product_name || ''"
            :option-list="cartItem?.optionList || {}"
            :current-options="cartItem?.options || {}"
          />
        </div>
        <GivehugCartCustomOptions v-if="cartItem" :cart-item="cartItem" />
        <div class="flex mt-4 items-center gap-8">
          <div class="font-semibold text-black" data-test-id="cart-item-price">
            <div v-if="totalPriceBundleWithCustomOptionFee" class="center-flex flex-wrap">
              <span>
                {{ $formatPrice(priceWithCustomOptionFee * (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100, cartItem?.currency_code) }}
              </span>
              <del class="text-gray-500 text-xs font-normal">
                {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
              </del>
            </div>
            <div v-else>
              {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
            </div>
          </div>
          <!-- Item Quantity -->
          <div class="font-semibold text-black" data-test-id="cart-item-quantity">
            <Quantity
              :quantity="cartItem?.quantity || 0"
              variant="xs"
              @update-quantity="$emit('update-quantity', $event)"
            />
          </div>
        </div>
      </div>
    </div>
    <button
      class="btn-text underline text-xs relative top-0 h-fit flex ml-auto my-auto"
      @click="$emit('remove-cart-item')"
    >
      <GivehugSharedIconX class="w-2.5 h-2.5" />
    </button>
  </div>
</template>
