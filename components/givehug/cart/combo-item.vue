<script setup lang="ts">

const props = defineProps<{
  item: CartItem
  isEdit: boolean
}>()

const { updateCartItem } = useCartItem(props.item)
const isShowEditOptions = computed(() => props.isEdit || useViewport().isGreaterOrEquals(VIEWPORT.tablet))
</script>

<template>
  <div class="flex gap-2">
    <common-image
      img-class="max-w-14 mr-2 btn"
      :image="{
        path: item?.thumb_url,
        color: item?.options?.color
      }"
      :alt="item?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(item?.thumb_url)"
    />
    <div class="text-xs text-nowrap mt-2" style="width: 200px;">
      1 x {{ item.product_name }}
      <span v-if="item.options && !isShowEditOptions">
        <span
          v-for="(key, optionIndex) in Object.keys(item.options)"
          :key="optionIndex"
          class="uppercase"
        >
          <template v-if="item.optionList[key] && item.optionList[key].length > 1">
            <span>&nbsp;/&nbsp;</span>
            {{ $t(item.options[key]) }}
          </template>
        </span>
      </span>
    </div>
    <div v-if="isShowEditOptions" class="w-full max-w-[calc(100%-90px)] grid grid-cols-12 h-min gap-2">
      <div class="col-span-12 xl:col-span-7 grid grid-cols-4 gap-2">
        <template v-if="item?.optionList && item.options">
          <template v-for="(key, optionListIndex) in Object.keys(item?.optionList)">
            <span
              v-if="item?.optionList[key].length > 1 && (item?.personalized === 1 || item?.personalized === 2)"
              :key="`text-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1 capitalize"
              data-test-id="cart-item-option"
              data-test-prop="false"
            >
              {{ item?.options[key] }}
            </span>
            <common-dropdown
              v-else-if="item?.optionList[key].length > 1"
              :key="`dropdown-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-2"
              btn-class="capitalize text-overflow-hidden btn-border w-full text-sm pl-2 py-1"
              dropdown-class="md:min-w-full"
              data-test-id="cart-item-option"
              :data-test-prop="key"
            >
              <span>{{ item.options[key] }}</span>
              <template #content>
                <div
                    v-for="(optionItem, optionIndex) in item?.optionList[key]"
                    :key="optionIndex"
                    :sp-action="`change_${key}`"
                    class="capitalize py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center"
                    :class="{'bg-primary text-contrast': optionItem === item.options[key]}"
                    :data-test-id="`cart-item-option-change-${key}`"
                    @click="optionItem === (item?.options && item.options[key]) ? '' : updateCartItem('option', {optionName: key, optionItem})"
                >
                  <lazy-common-color-item v-if="key==='color'" :color="optionItem" size="sm" class="inline-block mr-2" />
                  {{ optionItem }}
                </div>
              </template>
            </common-dropdown>
          </template>
        </template>
        <div v-if="item?.custom_options" class="col-span-4">
          <ul class="pl-5 list-disc">
            <li v-for="(value, key) in item.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
              <div v-if="key === 'customImage'">
                {{ $t('Your image') }}:
                <a
                    :href="$imgUrl({path: value, type: 'full'})"
                    target="_blank"
                    class="btn-text text-blue-900"
                    @click.prevent="uiManager().viewImage(value)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
              </div>
            </li>
          </ul>
        </div>
        <div v-if="item?.customer_custom_options?.length" class="col-span-4 <md:text-center">
          <ul v-for="(groupOptions, groupNumber) in item.customer_custom_options" :key="groupNumber" class="pl-5 list-disc mt-3">
            <li v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
              <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                  :href="$imgUrl({path: customOption.value as string, type: 'full'})"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(customOption.value as string)"
              >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
