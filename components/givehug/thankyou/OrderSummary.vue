<script setup lang="ts">
interface OrderSummaryProps {
  totalProductAmount: number
  totalShippingAmount: number
  totalDiscount: number
  totalAmount: number
  currencyRate: number
  currencyCode: CurrencyCode
}

defineProps<OrderSummaryProps>()
</script>

<template>
  <div class="bg-white rounded-[10px] border border-[#E0E0E0] overflow-hidden h-full flex flex-col">
    <div class="min-h-[300px] flex-1">
      <div class="font-semibold text-black text-xl mt-9 px-8">
        {{ $t('Order summary') }}
      </div>
      <div class="px-8 mt-9 space-y-6">
        <div class="flex justify-between">
          <div>
            {{ $t('Subtotal') }}
          </div>
          <div class="font-semibold">
            {{ $formatPriceByCurrency(totalProductAmount, currencyRate, currencyCode) }}
          </div>
        </div>
        <div class="flex justify-between">
          <div>{{ $t('Shipping') }}</div>
          <div class="font-semibold">
            {{ $formatPriceByCurrency(totalShippingAmount, currencyRate, currencyCode) }}
          </div>
        </div>
        <div v-if="totalDiscount" class="flex justify-between text-secondary">
          <div>{{ $t('Discount') }}</div>
          <div class="font-semibold">
            -{{ $formatPriceByCurrency(totalDiscount, currencyRate, currencyCode) }}
          </div>
        </div>
      </div>
    </div>

    <div class="bg-[#222222] text-white text-xl font-semibold px-9 py-5 flex justify-between">
      <div>{{ $t('Total') }}</div>
      <div>{{ $formatPriceByCurrency(totalAmount, currencyRate, currencyCode) }}</div>
    </div>
  </div>
</template>
