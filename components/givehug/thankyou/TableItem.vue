<script setup lang="ts">
import type { TableItemProps } from '~/types/givehug'

defineProps<TableItemProps>()

const localePath = useLocalePath()
</script>

<template>
  <div
    class="hidden md:flex border-b border-[#F4F4F4] py-6 gap-0 xl:gap-16"
  >
    <div class="w-7/12 lg:w-8/12 xl:w-7/12 cart-item flex gap-4">
      <div class="h-25 w-25">
        <common-image
          img-class="!w-25 h-25 btn"
          :image="{
            path: thumbUrl,
            color
          }"
          alt="Product Image"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(thumbUrl)"
        />
      </div>

      <div class="flex-1 pr-4 flex items-center">
        <div>
          <nuxt-link
            :to="localePath($getProductUrl(productUrl))"
            class="w-full"
          >
            <h5 class="text-sm" :title="title">
              {{ title }}
            </h5>
          </nuxt-link>
          <GivehugSharedItemSubTitle
            :product-name="productName"
            :option-list="optionList"
            :current-options="currentOptions"
          />
        </div>

        <div class="font-semibold mt-3 md:hidden">
          {{ $formatPriceByCurrency(price, currencyRate, currencyCode) }} x {{ quantity }}
        </div>
      </div>
    </div>
    <div class="items-center w-3/12 md:w-5/12 lg:w-4/12 xl:w-5/12 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 font-semibold">
      <div class="text-left hidden md:block">
        {{ $formatPriceByCurrency(price, currencyRate, currencyCode) }}<span class="pl-1 inline-block md:hidden lg:inline-block xl:hidden">x{{ quantity }}</span>
      </div>
      <div class="text-center hidden md:block lg:hidden xl:block">
        {{ quantity }}
      </div>
      <div class="text-right">
        {{ $formatPriceByCurrency(totalAmount, currencyRate, currencyCode) }}
      </div>
    </div>
  </div>
</template>
