<script lang="ts" setup>
import TableItem from './TableItem.vue'
import TableItemMobile from './TableItemMobile.vue'

const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
const ONE_DAY = 60 * 60 * 24 * 1000

const {
  relatedProducts,
  currentShippingMethod,
  order,
  isShowModalConfirmAddress,
  isShowModalEditInfo
} = await useThankYouPage()

const products = computed(() => {
  return order.products.map((product) => {
    return { ...product, options: JSON.parse(product.options) }
  })
})

function getOptionsText(options: Record<string, string>) {
  return Object.entries(options).map(([_, value]) => `${value}`).join(' - ')
}

const estimatedDeliveryDate = computed(() => {
  if (!currentShippingMethod.value) {
    return ''
  }

  const { shipping_time, printing_time } = currentShippingMethod.value
  const { date: dateLocale } = $i18n.localeProperties
  const date2 = new Date(Date.now() + ONE_DAY * (shipping_time[1] + printing_time))
  return date2.toLocaleDateString(dateLocale, { month: 'short', day: 'numeric', year: 'numeric' })
})
</script>

<template>
  <main class="cs-container" dusk="thank-you-page">
    <GivehugSharedProgressBar :current-step="2" class="mt-7 md:mt-15" />

    <GivehugThankyouTitleWrapper>
      {{ $t('Your Order Confirmed') }}
    </GivehugThankyouTitleWrapper>

    <div
      class="mt-8 md:mt-15"
    >
      <GivehugThankyouOrderInfo
        :order="order"
        :current-shipping-method="currentShippingMethod"
      />
    </div>

    <div class="flex flex-wrap justify-between mt-12 md:mt-15 gap-y-12">
      <!-- Table Product -->
      <div class="w-full lg:w-[54%]">
        <!-- Header -->
        <div class="flex border-b border-[#F4F4F4] font-semibold text-black pb-3 gap-0 xl:gap-16">
          <div class="w-8/12 md:w-7/12 lg:w-8/12 xl:w-7/12">
            {{ $t('Product') }}
          </div>
          <div class="hidden md:grid md:w-5/12 lg:w-4/12 xl:w-5/12 w-4/12 grid-cols-1 md:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3">
            <div class="text-left hidden md:block xl:block">
              {{ $t('Price') }}
            </div>
            <div class="text-center hidden md:block lg:hidden xl:block">
              {{ $t('Quantity') }}
            </div>
            <div class="text-right">
              {{ $t('Total') }}
            </div>
          </div>
        </div>

        <!-- Body -->
        <TableItem
          v-for="(product, index) in products"
          :key="product.id"
          :thumb-url="product.thumb_url"
          :product-url="product.product_url"
          :color="product.options?.color"
          :quantity="product.quantity"
          :title="product.campaign_title"
          :product-name="product.product_name"
          :currency-rate="order.currency_rate"
          :option-list="product.options"
          :current-options="product.options"
          :price="product.price"
          :total-amount="product.total_amount"
          :currency-code="order.currency_code"
          :class="{ 'border-b border-[#F4F4F4]': index !== products.length - 1 }"
        />

        <TableItemMobile
          v-for="(product, index) in products"
          :key="product.id"
          :thumb-url="product.thumb_url"
          :product-url="product.product_url"
          :color="product.options?.color"
          :title="product.campaign_title"
          :product-name="product.product_name"
          :option-list="product.options"
          :current-options="product.options"
          :quantity="product.quantity"
          :price="product.price"
          :total-amount="product.total_amount"
          :currency-rate="order.currency_rate"
          :currency-code="order.currency_code"
          :class="{ 'border-b border-[#F4F4F4]': index !== products.length - 1 }"
        />
      </div>

      <div class="w-full lg:w-[calc(40%-8px)]">
        <GivehugThankyouOrderSummary
          :total-product-amount="order.total_product_amount"
          :total-shipping-amount="order.total_shipping_amount"
          :total-discount="order.total_discount"
          :total-amount="order.total_amount"
          :currency-rate="order.currency_rate"
          :currency-code="order.currency_code"
        />
      </div>
    </div>

    <div class="flex flex-wrap justify-between mt-12 sm:mt-15 md:mt-20 gap-y-12 sm:gap-y-15">
      <!-- Thank you for your order -->
      <div class="md:w-full lg:w-[54%] <md:-mx-5 <md:px-1">
        <LazyGivehugThankyouPromotion />
      </div>

      <div class="w-full lg:w-[calc(40%-8px)]">
        <div class="space-y-2 md:space-y-3">
          <div class="text-xl font-semibold">
            {{ $t('Shop with confidence') }}
          </div>
          <div class="text-sm">
            {{ $t("Shopping on Givehug are safe and secure. Guaranteed! You'll pay nothing if unauthorized charges are made to your credit card as a result of shopping at Givehug.") }}
          </div>
        </div>

        <div class="space-y-2 md:space-y-3 mt-9">
          <div class="text-xl font-semibold">
            {{ $t('Generic reprint & refund policies') }}
          </div>
          <div class="text-sm">
            {{ $t('If, for any encounter product quality issues or defects. We can offer reproductions or refunds for your orders if there are order mistakes.') }}
            <NuxtLink :to="localePath('/page/return-policy')" class="text-primary hover:opacity-80 trans__time">
              {{ $t('Read our shipping and return policies.') }}
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <LazyGivehugSharedCarouselRoot
      v-if="relatedProducts?.length && !storeInfo().disable_related_product"
      class="mt-20"
      :products="relatedProducts"
      :title="$t(`Frequently bought together`)"
      :max-items="6"
    />

    <div class="mb-12 md:mb-40" />

    <lazy-default-order-modal-edit-info
      :order="order"
      :is-show="isShowModalEditInfo"
      @refresh-order="useRouter().go(0)"
      @is-show-modal="(val) => { isShowModalEditInfo = val }"
    />

    <lazy-default-order-modal-confirm-address
      :order="order"
      :is-show="isShowModalConfirmAddress"
      @is-show-modal="(val) => { isShowModalConfirmAddress = val; }"
      @show-edit-address="() => { isShowModalEditInfo = true; }"
    />
  </main>
</template>
