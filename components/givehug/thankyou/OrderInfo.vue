<script setup lang="ts">
import CardInfo from './CardInfo.vue'

const props = defineProps<{
  order: Order
  currentShippingMethod: ShippingMethod | null
}>()

const { $i18n } = useNuxtApp()
const ONE_DAY = 60 * 60 * 24 * 1000

const estimatedDeliveryDate = computed(() => {
  if (!props.currentShippingMethod) {
    return ''
  }

  const { shipping_time, printing_time } = props.currentShippingMethod
  const { date: dateLocale } = $i18n.localeProperties
  const date2 = new Date(Date.now() + ONE_DAY * (shipping_time[1] + printing_time))
  return date2.toLocaleDateString(dateLocale, { month: 'short', day: 'numeric', year: 'numeric' })
})
</script>

<template>
  <div class="flex flex-wrap justify-center lg:grid grid-cols-30 gap-4">
    <div class="w-full lg:col-span-10 xl:col-span-6">
      <CardInfo :title="$t('Order')" class="h-full min-h-[124px]">
        <div class="text-2xl md:text-3xl font-semibold text-black order-1">
          {{ order.order_number }}
        </div>
      </CardInfo>
    </div>
    <div class="w-full lg:col-span-10 xl:col-span-6 order-2">
      <CardInfo :title="$t('Estimated delivery date')" class="h-full min-h-[124px]">
        <div class="text-2xl md:text-3xl font-semibold text-black">
          {{ estimatedDeliveryDate }}
        </div>
      </CardInfo>
    </div>
    <div class="w-full lg:col-span-15 xl:col-span-6 order-3 md:order-4 xl:order-3">
      <CardInfo :title="$t('Shipping address')" class="h-full min-h-[124px]">
        <div class="font-semibold text-black">
          {{ order.address }} {{ order.address_2 ? `- ${order.address_2}` : '' }} - {{ order.city }} {{ order.state }} - {{ order.postcode }}
        </div>
      </CardInfo>
    </div>
    <div class="w-full lg:col-span-15 xl:col-span-6 order-4 md:order-5 xl:order-4">
      <CardInfo :title="$t('Order note')" class="h-full min-h-[124px]">
        <div class="text-sm">
          {{ order.order_note }}
        </div>
      </CardInfo>
    </div>
    <div class="w-full order-4 lg:col-span-10 xl:col-span-6 order-5 md:order-3 xl:order-5">
      <CardInfo :title="$t('Payment method')" class="h-full">
        <div>
          <img class="h-7 mt-5" :src="$paymentMethodToImageLink(order.payment_method)" :alt="order.payment_method">
        </div>
      </CardInfo>
    </div>
  </div>
</template>
