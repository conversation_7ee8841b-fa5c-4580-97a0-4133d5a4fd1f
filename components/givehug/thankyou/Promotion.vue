<script setup lang="ts">
import { useStoreInfo } from '~~/store/storeInfo'

const localePath = useLocalePath()

const discountCode = 'THANKYOU5'
const copyText = ref('Copy')

const storeInfo = useStoreInfo()

function copyDiscountCode() {
  navigator.clipboard.writeText(discountCode)
  copyText.value = 'Copied'

  setTimeout(() => {
    copyText.value = 'Copy'
  }, 2000)
}
</script>

<template>
  <div class="space-y-3 bg-[#F7FFF3] p-4 md:p-9 <md:rounded-none rounded-[10px]">
    <div class="text-[#222222] text-xl font-semibold">
      {{ $t('Thank you for your order') }}
    </div>
    <div class="text-sm">
      {{ storeInfo.promotion_title }}
    </div>
    <div
      class="flex gap-4"
    >
      <div
        class="rounded-[10px] bg-[#D2FCBE] flex py-2.5 px-4 flex-1 justify-between hover:opacity-80 trans__time cursor-pointer"
        @click="copyDiscountCode"
      >
        <div class="text-[#222222] text-sm font-semibold">
          {{ storeInfo.discount_code }}
        </div>
        <div class="flex gap-0.5 md:gap-2 items-center text-muted">
          <div>
            <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.95801 6.84744C4.95801 6.34641 5.15704 5.8659 5.51132 5.51162C5.8656 5.15735 6.34611 4.95831 6.84713 4.95831H12.9856C13.2336 4.95831 13.4793 5.00718 13.7085 5.10211C13.9377 5.19705 14.1459 5.3362 14.3214 5.51162C14.4968 5.68705 14.6359 5.8953 14.7309 6.1245C14.8258 6.3537 14.8747 6.59935 14.8747 6.84744V12.9859C14.8747 13.2339 14.8258 13.4796 14.7309 13.7088C14.6359 13.938 14.4968 14.1462 14.3214 14.3217C14.1459 14.4971 13.9377 14.6362 13.7085 14.7312C13.4793 14.8261 13.2336 14.875 12.9856 14.875H6.84713C6.59905 14.875 6.3534 14.8261 6.1242 14.7312C5.895 14.6362 5.68674 14.4971 5.51132 14.3217C5.3359 14.1462 5.19675 13.938 5.10181 13.7088C5.00687 13.4796 4.95801 13.2339 4.95801 12.9859V6.84744Z" stroke="#222222" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" />
              <path d="M2.84183 11.8554C2.62437 11.7318 2.4435 11.5529 2.3176 11.3368C2.19169 11.1207 2.12524 10.8751 2.125 10.625V3.54167C2.125 2.7625 2.7625 2.125 3.54167 2.125H10.625C11.1562 2.125 11.4452 2.39771 11.6875 2.83333" stroke="#222222" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
          </div>
          <div
            class="text-[13px] ml-auto"
          >
            {{ copyText }}
          </div>
        </div>
      </div>
      <div class="bg-black cursor-pointer text-white text-sm font-medium px-4 md:px-8 py-2.5 rounded-[10px] hover:bg-black/80 trans__time">
        <NuxtLink :to="localePath('/collection')">
          {{ $t('Shop now') }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>
