<script setup lang="ts">
import type { TableItemProps } from '~/types/givehug'

defineProps<TableItemProps>()

const localePath = useLocalePath()
</script>

<template>
  <div
    class="border-b border-[#F4F4F4] py-5 gap-16 md:hidden"
  >
    <div class="cart-item flex gap-4">
      <div class="h-25 w-25">
        <common-image
          img-class="!w-25 h-25 btn"
          :image="{
            path: thumbUrl,
            color
          }"
          :alt="title"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(thumbUrl)"
        />
      </div>

      <div class="flex-1 space-y-3 flex items-center">
        <div>
          <nuxt-link
            :to="localePath($getProductUrl(productUrl))"
            class="w-full"
          >
            <h5 class="text-sm" :title="title">
              {{ title }}
            </h5>
          </nuxt-link>
          <GivehugSharedItemSubTitle
            :product-name="productName"
            :option-list="optionList"
            :current-options="currentOptions"
          />
        </div>

        <div class="font-semibold flex justify-between items-center">
          <div>
            {{ $formatPriceByCurrency(price, currencyRate, currencyCode) }}
          </div>
          <div>
            x{{ quantity }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
