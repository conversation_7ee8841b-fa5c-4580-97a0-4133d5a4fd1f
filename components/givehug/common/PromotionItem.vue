<script lang="ts" setup>
const props = defineProps({
  promotion: {
    default: undefined,
    type: Object as PropType<Promotion>
  }
})
const {
  stringRule
} = usePromotion(toRef(props.promotion))
</script>

<template>
  <div
    :id="`promotion-item-${promotion?.discount_code}}`"
    class="flex cursor-pointer items-center bg-transparent text-[13px] leading-5"
  >
    <div class="px-2.5 py-0.5 bg-[#D2FCBE] rounded-full hover:opacity-80 trans__time">
      <div>{{ stringRule }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
