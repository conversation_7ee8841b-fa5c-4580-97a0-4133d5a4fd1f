<script lang="ts" setup>
defineProps({
  value: {
    type: String,
    default: ''
  },
  active: {
    type: Boolean,
    default: false
  },
  slug: {
    type: String,
    default: ''
  }
})

const localePath = useLocalePath()
</script>

<template>
  <nuxt-link
    :to="localePath({ path: slug })"
    :title="value"
    class="px-2.5 py-[3px] text-sm rounded-full bg-[#F3F3F3] trans__time"
    :class="{
      '!bg-primary text-white': active,
      'hover:bg-primary hover:text-white': !active
    }"
  >
    <small class="whitespace-nowrap">{{ value }}</small>
  </nuxt-link>
</template>
