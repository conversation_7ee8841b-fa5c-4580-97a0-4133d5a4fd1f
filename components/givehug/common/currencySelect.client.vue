<script lang="ts" setup>
import IconChecked from '~/components/givehug/shared/IconChecked.vue'
import { useUserSession } from '~/store/userSession'
import CommonDropdown from './dropdown.vue'

defineProps({
  btnClass: {
    default: 'border',
    type: String
  },
  showOnHover: {
    default: true,
    type: Boolean
  },
  showDropdownIcon: {
    default: true,
    type: Boolean
  },
  showFlagInButton: {
    default: true,
    type: Boolean
  }
})

const { currencies: currencyList } = generalSettings()
const userSession = useUserSession()
const currentCurrency = computed(() => userSession.currentCurrency)
</script>

<template>
  <CommonDropdown
    dropdown-id="currencySelect"
    :btn-class="btnClass"
    :show-on-hover="showOnHover"
    :show-dropdown-icon="showDropdownIcon"
    class="!bg-transparent"
  >
    <div class="flex items-center p-1 min-w-20 font-[13px] font-medium hover:opacity-80 trans__time" data-test-id="currency-select-btn">
      <span>{{ $getCurrencySymbol(currentCurrency?.locale === 'en' ? undefined : currentCurrency?.locale, currentCurrency?.code) }}</span>
      <span class="ml-2">{{ currentCurrency ? `(${$t(currentCurrency?.code)})` : $t('Currency') }}</span>
    </div>
    <template #content>
      <div
        v-for="(currencyItem, index) in currencyList"
        :key="index"
        class="flex items-center px-8 w-full hover:bg-gray-200 py-1 relative"
        :class="{
          [currencyItem.code]: true
        }"
        data-test-id="currency-select-currency"
        @click="userSession.update('userBehavior', { currencyCode: currencyItem.code });userSession.update('visitInfo', { currency_code: currencyItem.code })"
      >
        <IconChecked v-if="currencyItem.code === currentCurrency?.code" class="absolute left-4" />
        <span :class="`vti__flag ${currencyItem?.locale === 'en' ? 'us' : currencyItem?.locale.slice(-2).toLowerCase()}`" />
        <span class="ml-2">{{ $getCurrencySymbol(currencyItem?.locale === 'en' ? undefined : currencyItem?.locale, currencyItem?.code) }}</span>
        <span class="ml-2 w-max">{{ $t(currencyItem?.name) }}</span>
      </div>
    </template>
  </CommonDropdown>
</template>
