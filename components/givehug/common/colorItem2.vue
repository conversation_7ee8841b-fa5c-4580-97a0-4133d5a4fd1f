<script lang="ts" setup>
const props = defineProps({
  color: {
    required: true,
    type: String
  },
  size: {
    default: 'md',
    type: String
  },
  active: {
    default: false,
    type: Boolean
  }
})
const hexColor = computed(() => {
  return colorVal(props.color)
})
</script>

<template>
  <div
    class="rounded-full hover:opacity-80"
  >
    <div
      :style="`background: ${hexColor}`"
      class="rounded-full relative border border-transparent"
      :class="{
        'h-5 w-5': size === 'sm',
        'h-6 w-6': size === 'md',
        'h-7 w-7': size === 'lg',
        'h-8 w-8': size === 'xl',
        'h-full w-full': size === 'full',
        '!border-gray-100': hexColor.toLowerCase() === '#ffffff'
      }"
    >
      <i v-if="active" class="z-1 absolute position-center icon-sen-check" :class="isLightColor(hexColor) ? 'text-black' : 'text-white'" />
    </div>
  </div>
</template>
