<script lang="ts" setup>
const props = defineProps({
  color: {
    required: true,
    type: String
  },
  size: {
    default: 'md',
    type: String
  },
  active: {
    default: false,
    type: Boolean
  }
})
const hexColor = computed(() => {
  return colorVal(props.color)
})
</script>

<template>
  <div
    class="rounded-[10px] border hover:border-primary overflow-hidden trans__time"
    :class="active ? 'border-primary' : hexColor.toLocaleLowerCase() === '#ffffff' ? 'border-gray-100' : 'border-transparent'"
  >
    <div
      :style="`background: ${hexColor}`"
      class="relative"
      :class="{
        'h-5 w-5': size === 'sm',
        'h-6 w-6': size === 'md',
        'h-7 w-7': size === 'lg',
        'h-8 w-8': size === 'xl',
        'h-[45px] w-[45px]': size === 'givehug',
        'w-full h-full aspect-square': size === 'full'
      }"
    >
      <i v-if="active" class="z-1 absolute position-center icon-sen-check" :class="isLightColor(hexColor) ? 'text-black' : 'text-white'" />
    </div>
  </div>
</template>
