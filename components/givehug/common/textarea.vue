<script lang="ts" setup>
import type { Countable } from '~/types/givehug'

defineProps({
  inputLabelStyle: {
    default: 'text-sm',
    type: String
  },
  id: {
    default: undefined,
    type: String
  },
  type: {
    default: undefined,
    type: String
  },
  label: {
    default: undefined,
    type: String
  },
  placeholder: {
    default: undefined,
    type: String
  },
  pattern: {
    default: undefined,
    type: String
  },
  maxLength: {
    default: undefined,
    type: Number
  },
  modelValue: {
    default: undefined,
    type: String
  },
  state: {
    default: undefined,
    type: [String, Boolean]
  },
  required: {
    default: false,
    type: Boolean
  },
  message: {
    default: undefined,
    type: String
  },
  inputClass: {
    default: undefined,
    type: String
  },
  autocomplete: {
    default: undefined,
    type: String
  },
  autofocus: {
    default: false,
    type: Boolean
  },
  enableTextCounter: {
    default: false,
    type: Boolean
  },
  countable: {
    default: undefined,
    type: Object as PropType<Countable>
  },
  rows: {
    default: 3,
    type: Number
  }
})

defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'click:feedback'])

const input = ref()

defineExpose({
  focus
})
function focus() {
  input.value.focus()
}
</script>

<template>
  <div class="relative space-y-1">
    <label
      v-if="label"
      :for="id"
      class="text-sm font-semibold block"
    >
      <span>{{ label }}</span>
      <span v-if="required" class="text-error">*</span>
      <span v-if="countable" class="text-sm ml-1">({{ `${countable.count}/${countable.maxCount}` }})</span>
    </label>
    <textarea
      :id="id"
      ref="input"
      :name="id"
      required
      :autocomplete="autocomplete || id"
      :type="type"
      :rows="rows"
      :pattern="pattern"
      :value="modelValue"
      :maxlength="maxLength"
      :autofocus="autofocus"
      class="border border-givehug-gray bg-givehug-gray-with-hover p-5 w-full resize-none text-sm overflow-y-scroll focus:border-primary focus-visible:outline-none trans__time"
      :class="[
        inputClass,
        state === true ? '!border-green-500 input-success' : '',
        state === false ? '!border-red-500 input-error' : '']"
      :placeholder="placeholder"
      @input="$emit('update:modelValue', $event.target?.value); $emit('input', $event.target?.value)"
      @focus="$emit('focus', $event)"
      @blur="$emit('blur', $event)"
    />
    <span
      v-if="state !== undefined && message"
      class="text-sm"
      :class="{
        '!text-orange-500': state === 'warning',
        '!text-green-500': state === true,
        '!text-red-500': state === false,
        'cursor-pointer select-none': (message.toLowerCase().includes('click'))
      }"
      @click="$emit('click:feedback')"
    >{{ $t(message) }}</span>
    <slot />
  </div>
</template>
