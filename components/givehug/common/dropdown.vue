<script lang="ts" setup>
const props = defineProps({
  inputType: {
    default: 1,
    type: Number
  },
  btnClass: {
    default: 'btn-text hover:(border-primary-hover)',
    type: String
  },
  showDataClass: {
    default: '',
    type: String
  },
  dropdownClass: {
    default: undefined,
    type: String
  },
  closeOnClick: {
    default: true,
    type: Boolean
  },
  dropdownId: {
    default: undefined,
    type: String
  },
  showDropdownIcon: {
    type: Boolean,
    default: true
  },
  showOnHover: {
    type: Boolean,
    default: false
  },
  dropdownIcon: {
    type: String,
    default: 'icon-sen-menu-down text-2xl'
  },
  dropdownIconClass: {
    type: String,
    default: 'w-3.5 h-3.5'
  }
})

const $emit = defineEmits(['shown', 'hidden'])
defineExpose({
  hideData,
  toggleData
})
const $viewport = useViewport()

const isShowData = ref<boolean>(false)
const element = ref<HTMLElement>()
const showPositionX = ref<'left' | 'right'>('left')
const showPositionY = ref<'top' | 'bottom'>('bottom')

function hideData(): void {
  if (isShowData.value) {
    isShowData.value = false
  }
  nextTick(() => {
    $emit('hidden')
  })
}

function toggleData(): void {
  if (!isShowData.value) {
    const { x, y, height, width } = element.value?.getBoundingClientRect() as DOMRect
    const { innerHeight, innerWidth } = window
    showPositionX.value = (x + width / 2 >= innerWidth / 2) ? 'right' : 'left'
    showPositionY.value = (y + height / 2 >= innerHeight / 2) ? 'top' : 'bottom'
  }
  isShowData.value = !isShowData.value
  nextTick(() => {
    if (props.dropdownId) {
      useTracking().newCustomTracking({
        event: 'uiManager',
        actionName: isShowData.value ? 'dropdown_shown' : 'dropdown_hidden',
        elementName: props.dropdownId
      })
    }
    $emit(isShowData.value ? 'shown' : 'hidden')
  })
}

function hoverHandler() {
  if (!props.showOnHover || $viewport.isLessThan(VIEWPORT.desktop)) { return }

  toggleData()
}
</script>

<template>
  <div
    :id="dropdownId"
    ref="element"
    v-click-outside="hideData"
    class="dropdown relative py-1 pl-2 pr-1 bg-[#f3f3f3] text-muted rounded-lg text-sm"
    @mouseenter="hoverHandler"
    @mouseleave="hoverHandler"
  >
    <button
      type="button"
      class="relative flex items-center text-black gap-2"
      :class="[
        btnClass,
        isShowData ? showDataClass : ''
      ]"
      @click="toggleData"
    >
      <slot />
      <svg v-if="props.showDropdownIcon" style="transform: rotate(90deg);" :class="dropdownIconClass" viewBox="0 0 7 15" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.8788 0.500017C0.763811 0.499256 0.649821 0.524474 0.543368 0.574224C0.436914 0.623974 0.340091 0.697279 0.258449 0.789933C0.176555 0.882869 0.111555 0.993436 0.0671964 1.11526C0.0228381 1.23708 0 1.36775 0 1.49972C0 1.6317 0.0228381 1.76236 0.0671964 1.88419C0.111555 2.00601 0.176555 2.11658 0.258449 2.20951L4.88924 7.49796L0.258449 12.7864C0.0939216 12.9747 0.00149111 13.23 0.00149111 13.4962C0.00149111 13.628 0.0241832 13.7586 0.0682721 13.8803C0.112361 14.0021 0.176983 14.1128 0.258449 14.206C0.339915 14.2992 0.436628 14.3731 0.543068 14.4236C0.649508 14.474 0.76359 14.5 0.8788 14.5C1.11148 14.5 1.33462 14.3942 1.49915 14.206L6.74155 8.20775C6.82344 8.11482 6.88845 8.00425 6.9328 7.88243C6.97716 7.7606 7 7.62994 7 7.49796C7 7.36599 6.97716 7.23532 6.9328 7.1135C6.88845 6.99168 6.82344 6.88111 6.74155 6.78817L1.49915 0.789933C1.41751 0.697279 1.32069 0.623974 1.21423 0.574224C1.10778 0.524474 0.993789 0.499256 0.8788 0.500017Z" fill="currentColor" />
      </svg>
    </button>
    <client-only>
      <template v-if="$viewport.isGreaterOrEquals(VIEWPORT.tablet)">
        <div
          v-if="isShowData"
          class="absolute z-3 w-full bg-transparent"
          style="min-width: min-content;"
          :class="[showPositionX === 'right' ? 'right-0' : 'left-0', showPositionY === 'top' ? 'transform -translate-y-[100%] top-0' : 'pt-3']"
        >
          <div
            class="bg-[#f3f3f3] cursor-pointer rounded-[10px] popover"
            :class="dropdownClass"
            @click="closeOnClick ? hideData() : ''"
          >
            <ul
              class="overflow-auto max-h-50 py-2"
            >
              <slot name="content" />
            </ul>
          </div>
        </div>
      </template>
      <Teleport v-else to="body">
        <div v-if="isShowData" class="relative z-3" @click="closeOnClick ? hideData() : ''">
          <div class="fixed position-center bg-gray-900 opacity-50 w-full h-full" />
          <div
            class="fixed position-center bg-[#f3f3f3] rounded-[10px]"
            :class="dropdownClass"
            @click="closeOnClick ? hideData() : ''"
          >
            <ul class="overflow-auto max-h-[80vh] min-w-[50vw] my-2">
              <slot name="content" />
            </ul>
          </div>
        </div>
      </Teleport>
    </client-only>
  </div>
</template>
