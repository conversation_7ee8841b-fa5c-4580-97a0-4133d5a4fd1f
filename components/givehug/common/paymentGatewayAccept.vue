<script setup lang="ts">
const paymentGatewayAccept = [
  {
    name: 'Paypal',
    image: '/images/givehug/campaign/2.svg'
  },
  {
    name: 'Visa',
    image: '/images/givehug/campaign/4.svg'
  },
  {
    name: 'Mastercard',
    image: '/images/givehug/campaign/5.svg'
  },
  {
    name: 'American Express',
    image: '/images/givehug/campaign/6.svg'
  },
  {
    name: 'Stripe',
    image: '/images/givehug/campaign/stripe.svg',
    class: 'p-2'
  }
]
</script>

<template>
  <div class="flex items-center gap-2.5 justify-center">
    <div
      v-for="item in paymentGatewayAccept"
      :key="item.name"
      class="bg-white rounded-[6px] flex items-center justify-center h-8 w-14 overflow-hidden border border-[#EEEEEE]"
      :class="item.class"
    >
      <img :src="item.image" :alt="item.name">
    </div>
  </div>
</template>
