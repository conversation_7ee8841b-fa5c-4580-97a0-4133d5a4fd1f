<script lang="ts" setup>
import type { FileSelectorEmits, FileSelectorProps } from '~/components-logic/FileSelector/type'
import { getDefaultsPropsValue, useFileSelector } from '~/components-logic/FileSelector'

const props = withDefaults(defineProps<FileSelectorProps>(), getDefaultsPropsValue())
const emit = defineEmits<FileSelectorEmits>()

const { fileInput, resetFilesInput, filesName, multiple, internalDisable, onInputChange, isDroping, onDragging, onDrop } = useFileSelector(props, emit)

defineExpose({ resetFilesInput })
</script>

<template>
  <div>
    <div class="flex relative w-full select-none" :class="{ disabled: internalDisable, [selectorClass]: true }">
      <input
        :id="id"
        ref="fileInput"
        class="-z-5 relative w-full hidden"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="internalDisable"
        @change="onInputChange"
      >

      <label
        :for="id"
        class="w-full cursor-pointer bg-givehug-gray-with-hover border border-givehug-gray p-7 focus:border-primary h-[108px]"
        :class="[
          inputClass,
          state === 'warning' ? '!border-orange-500' : '',
          state === true ? '!border-green-500' : '',
          state === false ? '!border-red-500' : ''
        ]"
        @dragover.prevent="onDragging"
        @dragenter.prevent="onDragging"
        @drop.prevent="onDrop"
      >
        <div v-if="isLoading" class="center-flex h-full">
          <common-loading-dot variant="bg-gray-600" />
        </div>
        <div v-else-if="fileUpload || (isDroping && $t(dropMsg)) || filesName" class="text-sm">
          {{ fileUpload || (isDroping && $t(dropMsg)) || filesName }}
        </div>
        <template v-else>
          <div class="center-flex">
            <svg xmlns="http://www.w3.org/2000/svg" width="27" height="21" viewBox="0 0 27 21" fill="none">
              <path d="M14.0625 20.3125V14.6875H17.8125L13.125 9.0625L8.4375 14.6875H12.1875V20.3125H7.5V20.2656C7.3425 20.275 7.1925 20.3125 7.03125 20.3125C5.16645 20.3125 3.37802 19.5717 2.05941 18.2531C0.74079 16.9345 0 15.1461 0 13.2812C0 9.67375 2.72813 6.73375 6.22875 6.33063C6.53568 4.72618 7.3921 3.27883 8.65071 2.23751C9.90933 1.1962 11.4915 0.626001 13.125 0.625C14.7588 0.625903 16.3412 1.19602 17.6001 2.23732C18.859 3.27861 19.7158 4.72602 20.0231 6.33063C23.5238 6.73375 26.2481 9.67375 26.2481 13.2812C26.2481 15.1461 25.5073 16.9345 24.1887 18.2531C22.8701 19.5717 21.0817 20.3125 19.2169 20.3125C19.0594 20.3125 18.9075 20.275 18.7481 20.2656V20.3125H14.0625Z" fill="#595959" />
            </svg>
          </div>
          <div class="text-center text-[13px] mt-2">
            <span>Drag & drop or click to </span>
            <span class="underline font-semibold">choose file</span>
          </div>
        </template>
      </label>

      <span
        v-if="state !== undefined && message"
        class="mt-1 text-sm"
        :class="[
          state === null ? '' : state === true ? '!text-green-500' : '!text-red-500']"
      >{{ $t(message) }}</span>
      <slot />
    </div>
  </div>
</template>
