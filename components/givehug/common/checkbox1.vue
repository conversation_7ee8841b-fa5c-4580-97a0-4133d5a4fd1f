<script setup lang="ts">
defineProps({
  value: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  checkboxClasses: {
    type: String,
    default: 'mt-0.5'
  },
  labelClasses: {
    type: String,
    default: 'text-[13px] leading-normal'
  }
})
</script>

<template>
  <div class="flex items-start cursor-pointer gap-3" @click="$emit('update:value', !value)">
    <div
      class="w-4 h-4 min-w-4 rounded-[3px] border flex items-center justify-center"
      :class="{
        [checkboxClasses]: true,
        'cursor-not-allowed !bg-gray-200 !border-gray-200': disabled,
        'bg-secondary border-secondary': value,
        'border-black': !value
      }"
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="11" height="8" viewBox="0 0 11 8" fill="none">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M10.1133 0.225912C10.254 0.370443 10.333 0.566442 10.333 0.770809C10.333 0.975175 10.254 1.17117 10.1133 1.3157L4.48874 7.09248C4.41441 7.16884 4.32616 7.22941 4.22903 7.27074C4.13191 7.31206 4.02781 7.33333 3.92268 7.33333C3.81755 7.33333 3.71345 7.31206 3.61632 7.27074C3.5192 7.22941 3.43095 7.16884 3.35662 7.09248L0.562099 4.22285C0.490427 4.15175 0.433259 4.06671 0.393931 3.97268C0.354603 3.87864 0.333902 3.77751 0.333036 3.67518C0.33217 3.57284 0.351157 3.47135 0.388888 3.37664C0.426619 3.28192 0.48234 3.19587 0.552797 3.1235C0.623255 3.05114 0.70704 2.99391 0.799262 2.95516C0.891485 2.9164 0.990298 2.8969 1.08994 2.89779C1.18958 2.89868 1.28804 2.91994 1.3796 2.96034C1.47115 3.00073 1.55395 3.05944 1.62318 3.13305L3.92243 5.49453L9.05172 0.225912C9.12141 0.154292 9.20415 0.0974775 9.29523 0.0587145C9.3863 0.0199515 9.48392 0 9.58251 0C9.6811 0 9.77872 0.0199515 9.86979 0.0587145C9.96087 0.0974775 10.0436 0.154292 10.1133 0.225912Z" fill="white" />
      </svg>
    </div>
    <slot name="label">
      <span
        :class="{
          [labelClasses]: true,
          'text-gray-400': disabled
        }"
      >{{ label }}</span>
    </slot>
  </div>
</template>
