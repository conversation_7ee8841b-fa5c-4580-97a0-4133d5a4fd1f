<script lang="ts" setup>
const {
  currentTemplate,
  sizeGuideProperties,
  currentSizeGuideUnit,
  sizeGuideList,
  converseData
} = useSizeGuide()
</script>

<template>
  <common-modal
    modal-id="modalSizeGuide"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 px-4"
    :model-value="currentTemplate?.isShowModal"
    :title="$t(currentTemplate?.name || '')"
    @close-modal="uiManager().toggleModalSizeGuide(false)"
  >
    <div class="grid grid-cols-4 mt-3 max-h-[75vh] overflow-y-scroll medium-scroll">
      <div class="col-span-4 lg:col-span-2 lg:pr-2">
        <common-image
          :image="{
            path: currentTemplate?.size_chart || `${cdnURL}images/size_guide_sleeve.png`,
            type: 'list'
          }"
          alt="Size guide template"
        />
      </div>
      <div v-if="sizeGuideList?.length" class="col-span-4 lg:col-span-2 lg:pl-2">
        <div>
          <button
            class="btn-text border-b-2 min-w-1/4 px-2"
            :class="{ 'border-primary text-primary': currentSizeGuideUnit === 'in' }"
            @click="currentSizeGuideUnit = 'in'"
          >
            {{ $t('Inches') }}
          </button>
          <button
            class="btn-text border-b-2 min-w-1/4 px-2"
            :class="{ 'border-primary text-primary': currentSizeGuideUnit === 'cm' }"
            @click="currentSizeGuideUnit = 'cm'"
          >
            {{ $t('Centimeters') }}
          </button>
        </div>
        <table class="w-full table-auto text-center border-collapse border">
          <thead>
            <tr>
              <th class="border p-2 font-medium text-xs">
                {{ $t('Size') }}
              </th>
              <template v-for="sizeGuideProperty in sizeGuideProperties">
                <th
                  v-if="sizeGuideList.some(item => item[sizeGuideProperty.key])"
                  :key="sizeGuideProperty.key"
                  class="border p-2 font-medium text-xs"
                >
                  {{ $t(sizeGuideProperty.title) }}
                </th>
              </template>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sizeGuideItem, index) in sizeGuideList" :key="index">
              <td class="border p-2">
                {{ sizeGuideItem.size }}
              </td>
              <template v-for="sizeGuideProperty in sizeGuideProperties">
                <td v-if="sizeGuideList.some(item => item[sizeGuideProperty.key])" :key="sizeGuideProperty.key" class="border p-2">
                  {{ converseData(sizeGuideProperty.key, sizeGuideItem[sizeGuideProperty.key], sizeGuideItem.unit) }}
                </td>
              </template>
            </tr>
          </tbody>
        </table>
        <div class="mt-3">
          <span class="font-medium">{{ $t('Note') }}: </span>
          <span v-if="currentSizeGuideUnit === 'in'">{{ $t('Allow for a tolerance level of 2 inch') }}</span>
          <span v-else>{{ $t('Allow for a tolerance level of 5 cm') }}</span>
        </div>
      </div>
      <div class="col-span-3 mt-6">
        <div class="font-medium">
          {{ $t('Note: The size guide for most clothing products is as follows:') }}
        </div>
        <ul class="mt-4 ml-8 list-disc">
          <li>{{ $t('Inches are used for customers from North America (US and CA), Australia, and New Zealand.') }}</li>
          <li>{{ $t('Centimeters are used for customers from all other regions.') }}</li>
        </ul>
        <div class="mt-4">
          {{ $t('In cases of limited stock where products are shipped from Europe to North America or vice versa, we will send an email to inform customers and advise them to double-check the applicable size chart.') }}
        </div>
      </div>
    </div>
  </common-modal>
</template>
