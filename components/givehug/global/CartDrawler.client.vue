<script setup lang="ts">
import { useCartStore } from '~~/store/cart'
import CartItemMobile from '~/components/givehug/cart/cartItemMobile.vue'

const { $i18n } = useNuxtApp()
const localePath = useLocalePath()

const cartStore = useCartStore()

const cartItems = computed(() => cartStore.listCartItem)

// const recommendedItems = ref<CartItem[]>([
//   {
//     id: '3',
//     name: 'Mother And Daughter Forever Linked Together - Personalized',
//     price: 29.0,
//     image: '/placeholder.svg?height=80&width=80'
//   }
// ])

function removeItem(id: number) {
  cartStore.removeCartItem(id)
}

const totalPrice = computed(() => {
  return cartStore.getTotalPrice
})

const freeShippingThreshold = 150
const amountToFreeShipping = computed(() =>
  Math.max(freeShippingThreshold - totalPrice.value, 0)
)
const freeShippingProgress = computed(() =>
  Math.min((totalPrice.value / freeShippingThreshold) * 100, 100)
)

function updateQuantity(cartItem: CartItem, number: number) {
  cartStore.updateCartItem(cartItem, { quantity: number })
}

function getPriceWithCustomOptionFee(cartItem: CartItem) {
  const realPrice = cartItem?.variantPrice || cartItem?.price || 0
  return realPrice + cartItem.extra_custom_fee
}

function isBundle(cartItem: CartItem) {
  const campaignBundleId = Array.isArray(cartItem.campaignBundleId) ? cartItem.campaignBundleId?.at(-1) : cartItem.campaignBundleId
  return !!cartStore.listCartItem?.find(item => item.campaign_id === campaignBundleId)
}

function getTotalPriceBundleWithCustomOptionFee(cartItem: CartItem) {
  if (!isBundle(cartItem)) {
    return 0
  }

  const discountRate = (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100
  const priceWithCustomOptionFee = getPriceWithCustomOptionFee(cartItem)
  return priceWithCustomOptionFee * cartItem.quantity * discountRate
}

async function proceedToCheckout() {
  await createOrder()
  uiManager().toggleCartDrawler(false)
}

function viewCart() {
  uiManager().toggleCartDrawler(false)
  useRouter().push(localePath('/cart'))
}
</script>

<template>
  <!-- Overlay mờ -->
  <transition name="fade">
    <div
      v-if="uiManager().isShowCartDrawler"
      class="fixed inset-0 z-40 bg-black/40"
      @click.self="uiManager().toggleCartDrawler(false)"
    />
  </transition>

  <transition name="slide">
    <aside
      v-if="uiManager().isShowCartDrawler"
      class="fixed right-0 pt-6 top-0 z-50 flex h-full w-full md:min-w-xl flex-col bg-white sm:max-w-md"
    >
      <!-- Header -->
      <div class="px-4 md:px-9 flex-1 flex flex-col overflow-hidden">
        <header>
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold">
              Your Cart ({{ cartItems.length }})
            </h2>
            <button @click="uiManager().toggleCartDrawler(false)">
              <GivehugSharedIconX class="w-4 h-4" />
            </button>
          </div>
          <div class="mt-6 space-y-3">
            <p v-if="amountToFreeShipping > 0" class="text-sm">
              Spend <span class="font-semibold text-sm">${{ amountToFreeShipping.toFixed(2) }}</span> more to reach
              <span class="text-secondary font-medium">free shipping</span>
            </p>
            <p v-else class="text-sm text-secondary">
              You are eligible for free shipping
            </p>
            <div class="h-1 w-full rounded bg-gray-200">
              <div
                class="h-full rounded-full bg-secondary"
                :style="{ width: `${freeShippingProgress}%` }"
              />
            </div>
          </div>
        </header>
        <div class="flex-1 overflow-y-auto pr-4 md:pr-9 -mr-4 md:-mr-9">
          <template v-if="cartItems.length">
            <div class="pt-9 space-y-6">
              <CartItemMobile
                v-for="cartItem in cartItems"
                :key="cartItem.id"
                :cart-item="cartItem"
                :total-price="totalPrice"
                :total-price-bundle-with-custom-option-fee="getTotalPriceBundleWithCustomOptionFee(cartItem)"
                :price-with-custom-option-fee="getPriceWithCustomOptionFee(cartItem)"
                :product-url="$getProductUrl(cartItem?.product_url) as string"
                :is-out-of-stock="false"
                @remove-cart-item="removeItem(Number(cartItem.id))"
                @update-quantity="updateQuantity(cartItem, $event)"
              />
            </div>
            <!-- Gợi ý mua thêm -->
            <div v-if="recommendedItems?.length" class="p-4">
              <h3 class="mb-3 font-medium">
                You may also like
              </h3>
              <div class="relative">
                <div
                  class="no-scrollbar flex gap-3 overflow-x-auto px-1 pb-2 -mx-1"
                >
                  <div
                    v-for="item in recommendedItems"
                    :key="item.id"
                    class="flex w-[calc(100%-2rem)] flex-shrink-0 items-center gap-3 rounded-md border p-3 sm:w-64"
                  >
                    <img
                      :src="item.image || '/placeholder.svg'"
                      :alt="item.name"
                      width="60"
                      height="60"
                      class="rounded-md object-cover"
                    >
                    <div class="min-w-0 flex-1">
                      <p class="line-clamp-2 text-sm font-medium">
                        {{ item.name }}
                      </p>
                      <div class="mt-1 flex items-center gap-2">
                        <p class="text-sm font-semibold">
                          ${{ item.price.toFixed(2) }}
                        </p>
                        <p class="text-xs text-gray-500 line-through">
                          US$12.71+
                        </p>
                      </div>
                    </div>
                    <button class="h-7 w-7 flex-shrink-0 rounded border">
                      +
                    </button>
                  </div>
                </div>
                <!-- Nút slide thủ công (tuỳ bạn hiện/ẩn bằng JS) -->
                <button
                  class="absolute left-0 top-1/2 -translate-y-1/2 rounded-full bg-white p-1 shadow"
                  aria-label="Previous"
                >
                  ‹
                </button>
                <button
                  class="absolute right-0 top-1/2 -translate-y-1/2 rounded-full bg-white p-1 shadow"
                  aria-label="Next"
                >
                  ›
                </button>
              </div>
            </div>
          </template>
          <!-- Không có sản phẩm -->
          <div v-else class="p-8 text-center">
            <p class="text-gray-500">
              Your cart is empty
            </p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div v-if="cartItems.length" class="mt-auto">
        <p class="text-sm px-6 md:px-9 border-b pb-4 pt-4 md:pt-9">
          Taxes and <span class="underline">shipping</span> calculated at checkout
        </p>
        <div class="flex items-center justify-between px-6 md:px-9 py-6">
          <h3 class="font-semibold text-xl">
            Estimated total
          </h3>
          <p class="text-xl font-semibold text-primary">
            ${{ totalPrice.toFixed(2) }}
          </p>
        </div>
        <div class="space-y-3 px-4 md:px-9 pb-4 md:pb-11">
          <button
            class="block text-center w-full rounded-full border border-black py-4 text-xl font-medium hover:text-primary trans__time"
            @click="viewCart()"
          >
            View cart
          </button>
          <button
            class="w-full rounded-full bg-black py-4 text-xl font-semibold text-white hover:bg-black/90 trans__time"
            @click="proceedToCheckout()"
          >
            Check out
          </button>
        </div>
      </div>
    </aside>
  </transition>
</template>

<style scoped>
/* Hiệu ứng fade & slide */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}
.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}

/* Ẩn scrollbar ngang (Chrome, Safari) */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
/* Ẩn scrollbar ngang (Firefox) */
.no-scrollbar {
  scrollbar-width: none;
}
</style>
