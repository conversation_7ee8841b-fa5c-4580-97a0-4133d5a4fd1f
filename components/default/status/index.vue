<script lang="ts" setup>
import WarningShipLate from '~/components/Common/warningShipLate.vue'
import WarningShipLateChina from '~/components/Common/WarningShipLateChina.vue'

const localePath = useLocalePath()

const {
  relatedProducts,

  getOrderStatus,
  getTimeTracking,
  compareTrackingTime,

  showConfirmDeleteRequestCancelOrder,
  showPendingCustomerAction,
  resumeOrder,
  cancelOrder,
  getTotalTrackingItemText,

  order,
  tracking,
  timeframe,

  hasMailboxNumber,
  hasHouseNumber,
  currentCountry,
  orderStatuses,
  cancelOrderMsgs,
  isShowModal,
  canEditAddressInfo
} = await useOrderStatusPage()

onMounted(() => {
  const { query } = useRoute()
  if (query?.open_edit === 'true' && canEditAddressInfo.value) {
    isShowModal.value.editInfo = true
  }
})

async function confirmed() {
  const router = useRouter()
  await router.replace({ query: {} })
  router.go(0)
}

function groupProductInCombo(products?: OrderProduct[]) {
  if (!products)
    return []

  const comboProducts = products.filter(product => product.combo_id)
  const comboMap = new Map<string, OrderProduct[]>()
  comboProducts.forEach((product) => {
    const comboId = product.combo_id as string
    if (comboMap.has(comboId)) {
      comboMap.get(comboId)?.push(product)
    }
    else {
      comboMap.set(comboId, [product])
    }
  })

  return Array.from(comboMap.values())
}
</script>

<template>
  <main class="container grid grid-cols-12 gap-6">
    <div v-if="tracking" class="col-span-12 mt-10 mb-15 flex pl-6 md:pl-0">
      <div class="w-full flex flex-wrap flex-col order-process md:justify-center md:flex-row">
        <div class="milestone active">
          <i class="icon-sen-hand-coin text-2xl md:text-4xl" />
          <span class="font-medium leading-none">{{ $t('Received') }}</span>
          <span class="text-gray-600 font-medium whitespace-nowrap text-secondary"> ( {{ getTimeTracking(timeframe?.received) }} )</span>
        </div>

        <div class="step w-30" :class="{ active: compareTrackingTime(timeframe?.validate) }" />
        <div class="milestone" :class="{ active: compareTrackingTime(timeframe?.validate) }">
          <i class="icon-sen-account-check text-2xl md:text-4xl" />
          <span class="font-medium leading-none">{{ $t('Validated') }}</span>
          <span class="text-gray-600 font-medium whitespace-nowrap text-secondary"> ( {{ getTimeTracking(timeframe?.validate) }} )</span>
        </div>

        <div class="step w-30" :class="{ active: compareTrackingTime(timeframe?.print) }" />
        <div class="milestone" :class="{ active: compareTrackingTime(timeframe?.print) }">
          <i class="icon-sen-printer-check text-2xl md:text-4xl" />
          <span class="font-medium leading-none">{{ $t('Printed') }}</span>
          <span class="text-gray-600 font-medium whitespace-nowrap text-secondary"> ( {{ getTimeTracking(timeframe?.print) }} )</span>
        </div>

        <div class="step w-30" :class="{ active: compareTrackingTime(timeframe?.package) }" />
        <div class="milestone" :class="{ active: compareTrackingTime(timeframe?.package) }">
          <i class="icon-sen-package-variant text-2xl md:text-4xl" />
          <span class="font-medium leading-none">{{ $t('Packaged') }}</span>
          <span class="text-gray-600 font-medium whitespace-nowrap text-secondary"> ( {{ getTimeTracking(timeframe?.package) }} )</span>
        </div>

        <div class="step w-30" :class="{ active: (['completed', 'cancelled', 'refunded'].includes(order.status) || compareTrackingTime(timeframe?.ship)) }" />
        <div
          class="milestone"
          :class="{
            active: (['completed', 'cancelled', 'refunded'].includes(order.status) || compareTrackingTime(timeframe?.ship))
          }"
        >
          <i v-if="order.status === 'refunded'" class="text-2xl md:text-4xl icon-sen-cash-refund" />
          <i v-else-if="order.status === 'cancelled'" class="text-2xl md:text-4xl icon-sen-cart-remove" />
          <i v-else class="text-2xl md:text-4xl icon-sen-truck-delivery-outline" />
          <span v-if="order.status === 'refunded'" class="font-medium leading-none">{{ $t('Refunded') }}</span>
          <span v-else-if="order.status === 'cancelled'" class="font-medium leading-none">{{ $t('Cancelled') }}</span>
          <span v-else-if="order.fulfill_status === 'fulfilled'" class="font-medium leading-none">{{ $t('Delivered') }}</span>
          <span v-else class="font-medium leading-none">{{ $t('On Delivery') }}</span>
          <span v-if="order.status === 'completed'" class="text-gray-600 font-medium whitespace-nowrap text-secondary"> ( {{ getTimeTracking(timeframe?.delivered && (compareTrackingTime(timeframe?.delivered, timeframe?.ship)) ? timeframe?.delivered : timeframe?.ship) }} )</span>
        </div>
      </div>
    </div>
    <div class="col-span-12 md:col-span-7 mb-16 md:mb-5 px-2">
      <h4 class="font-medium text-2xl">
        <span>{{ $t('Details for order') }}</span> <strong>#{{ order.order_number }}</strong>
      </h4>
      <WarningShipLate />
      <WarningShipLateChina :order="order" />

      <div class="flex my-4 justify-between flex-wrap">
        <p>
          <span>{{ $t('Need support?') }}&nbsp;</span>
          <a :href="localePath('/page/contact-us')" target="_blank" rel="noopener" class="submit-ticket font-medium">{{ $t('Submit a ticket') }}</a>
        </p>

        <template v-if="order.shipping_method === 'standard' && !['completed', 'cancelled', 'refunded'].includes(order.status)">
          <span
            v-if="!order.request_cancel && (Date.parse(order.paid_at || '') + 43200000 > Date.now())"
            class="text-[#17a2b8] hover:text-[#138799] font-weight-500 cursor-pointer"
            @click="isShowModal.requestCancelOrder = true"
          >{{ $t('Request to cancel order') }}</span>
          <span
            v-else-if="order.request_cancel && order.request_cancel.status === 'pending' && parseInt(order.request_cancel.sent_email) === 0"
            class="text-[#17a2b8] hover:text-[#138799] font-weight-500 cursor-pointer"
            @click="showConfirmDeleteRequestCancelOrder"
          >{{ $t('Cancel requested') }}</span>
          <span
            v-else-if="order.request_cancel && order.request_cancel.status === 'pending' && parseInt(order.request_cancel.sent_email) === 1"
            class="text-[#17a2b8] hover:text-[#138799] font-weight-500 cursor-pointer"
            @click="showPendingCustomerAction"
          >{{ $t('Pending Customer Action') }}</span>
          <span
            v-else-if="order.request_cancel && order.request_cancel.status === 'confirmed'"
            class="text-red-600 hover:text-red-700 font-weight-500 cursor-pointer"
            @click="showConfirmDeleteRequestCancelOrder"
          >{{ $t('Cancellation confirmed') }}</span>
          <span
            v-else-if="order.request_cancel && order.request_cancel.status === 'processing'"
            class="text-[#17a2b8] font-weight-500"
          >{{ $t('Processing refund') }}</span>
        </template>
      </div>

      <div class="mb-4">
        <div v-if="cancelOrderMsgs.submitted" class="p-4 bg-[#d9f6eb] text-[#226d52]">
          {{ $t('Your cancellation request has been submitted. We will send you a confirmation email shortly. Please check your inbox in a few minutes. You have 12 hours to confirm the cancellation of your order via email, otherwise it will automatically be resumed.') }}
        </div>
        <div v-if="cancelOrderMsgs.cannotCancel" class="p-4 bg-[#fff3cd] text-[#856404]">
          {{ $t('You cannot cancel your order cancellation request. Your order is in processing status.') }}
        </div>
        <div v-if="cancelOrderMsgs.expired" class="p-4 bg-[#fff3cd] text-[#856404]">
          {{ $t('Your request to cancel the order is expired.') }}
        </div>
      </div>

      <div>
        <div v-for="(status, index) in orderStatuses" :key="index" class="mb-5">
          <div class="p-3 border border-gray-300 !rounded-b-none">
            <p class="font-medium">
              <span :class="getOrderStatus(status).className">{{ $t(getOrderStatus(status).text) }}</span>&nbsp;({{ getTotalTrackingItemText(tracking?.[status] as OrderProduct[]) }})
            </p>
            <div v-if="status !== 'unfulfilled' && (status.includes('on_delivery') || status.includes('fulfilled'))" class="flex">
              <span> {{ $t('Tracking') }}: </span>
              <span class="flex flex-col ml-2">
                <template v-for="(trackingCode, idx) in tracking[status][0].tracking_info.tracking_code">
                  <a
                    v-if="tracking[status][0].tracking_info?.tracking_url?.[idx]"
                    :key="status + idx"
                    :href="tracking[status][0].tracking_info?.tracking_url?.[idx]"
                    target="_blank"
                    rel="noopener noreferrer nofollow"
                    class="capitalize underline-gray-600 tracking-url cursor-pointer"
                  >
                    {{ tracking[status][0].tracking_info.shipping_carrier?.[idx] }} ({{ trackingCode }})
                  </a>
                  <span
                    v-else
                    :key="status + idx"
                    class="text-capitalize"
                  >{{ tracking[status][0].tracking_info.shipping_carrier?.[idx] }} ({{ trackingCode }})</span>
                </template>
              </span>
            </div>
          </div>
          <div class="border border-gray-300 border-t-0 p-3 !rounded-t-none">
            <div class="list-items">
              <default-order-product-item
                v-for="(product, index2) in tracking?.[status].filter((p) => !p.combo_id)"
                :key="index2"
                :product="product"
                :convert-currency-code="order.currency_code"
                :convert-currency-rate="order.currency_rate"
                page="order-status"
                :pos="index2"
                :customer-name="order.customer_name"
                :order-status="order.status"
              />
              <div v-for="(combo, index3) in groupProductInCombo(tracking?.[status])" :key="index3" class="flex justify-between w-full gap-4">
                <div class="relative w-32 flex items-start h-fit">
                  <common-image
                    :image="{
                      path: combo[0].campaign?.thumb_url,
                      type: 'list'
                    }"
                    :alt="combo[0].campaign?.name"
                  />
                </div>
                <div class="w-full relative flex-1">
                  <div class="text-lg font-medium mb-2 mt-1">
                    {{ combo[0].campaign?.name }}
                  </div>
                  <div class="space-y-2 md:space-y-0">
                    <default-order-product-item
                      v-for="(product, index4) in combo"
                      :key="index4"
                      :product="product"
                      :convert-currency-code="order.currency_code"
                      :convert-currency-rate="order.currency_rate"
                      page="order-status"
                      :pos="index4"
                      :customer-name="order.customer_name"
                      :order-status="order.status"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-span-12 md:col-span-5 px-2">
      <div>
        <h4 class="font-medium text-2xl">
          {{ $t('Delivery to') }}
        </h4>
        <p class="capitalize">
          <span class="font-medium">{{ $t(`${order.shipping_method} delivery`) }}</span>
          <span v-if="order.shipping_method === SHIPPING_METHOD.express" class="font-medium text-red-600">({{ $t('No refund allowed') }})</span>
        </p>
        <div>
          <a v-if="canEditAddressInfo" href="#" class="edit-address float-right" @click.prevent="isShowModal.editInfo = true">{{ $t('Edit') }}</a>
          <p v-if="order.customer_name" class="mb-2">
            <i><strong>{{ order.customer_name }}</strong></i>
          </p>
          <p v-if="order.address" class="mb-2">
            <i>{{ order.address }} {{ order.address_2 || '' }}</i>
          </p>
          <p v-if="order.city" class="mb-2">
            <i>{{ order.city }} {{ order.state || '' }} {{ order.postcode || '' }}</i>
          </p>
          <p v-if="hasMailboxNumber" class="mb-2">
            <i>{{ $t('Mailbox number') }}: {{ order.mailbox_number || '' }}</i>
          </p>
          <p v-if="hasHouseNumber" class="mb-2">
            <i>{{ $t('House number') }}: {{ order.house_number || '' }}</i>
          </p>
          <p v-if="currentCountry && currentCountry.name" class="mb-2">
            <i>{{ currentCountry.name }}</i>
          </p>
          <p v-if="order.customer_phone" class="mb-2">
            <i>{{ order.customer_phone }}</i>
          </p>
        </div>

        <h4 class="font-medium text-2xl">
          {{ $t('Order Summary') }}
        </h4>
        <div class="list-items">
          <default-order-product-item
            v-for="(product, index) in order.products"
            :key="index"
            :product="product"
            :convert-currency-code="order.currency_code"
            :convert-currency-rate="order.currency_rate"
            page="order-status"
          />
        </div>
        <default-order-calculate-price :order="order" />
      </div>
      <div>
        <lazy-common-thankyou-promotion class="mt-10 border-primary" btn-class="border-gray-300 text-contrast bg-primary" />
      </div>
    </div>

    <lazy-common-product-carousel
      v-if="relatedProducts?.length"
      class="container mt-10 col-span-12"
      title-class="text-center uppercase text-3xl"
      title="You may be interested in these products"
      :products="relatedProducts"
      static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
    >
      <template #default="{ product, index }">
        <default-common-product-item :product="product" :index="index" class="inline-block" />
      </template>
    </lazy-common-product-carousel>

    <!-- Modals -->
    <lazy-default-order-modal-edit-info
      v-if="canEditAddressInfo"
      :order="order"
      :is-show="isShowModal.editInfo"
      @refresh-order="confirmed"
      @is-show-modal="(val) => { isShowModal.editInfo = val }"
    />
    <lazy-common-modal
      modal-id="modalRequestCancelOrder"
      :model-value="isShowModal.requestCancelOrder"
      :close-icon="false"
    >
      <div class="w-200 p-4 max-w-[90vw]">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-medium">
            {{ $t('Do you want to cancel this order?') }}
          </h2>
        </div>
        <div class="flex gap-2">
          <button class="btn w-full border border-[#6c757d] hover:border-[#545b62] py-2 text-white bg-[#6c757d] hover:bg-[#5a6268]" @click="cancelOrder">
            {{ $t('Proceed') }}
          </button>
          <button class="btn w-full border border-[#007bff] hover:border-[#0062cc] bg-[#007bff] hover:bg-[#0069d9] text-white" @click="isShowModal.requestCancelOrder = false">
            {{ $t('Resume order') }}
          </button>
        </div>
      </div>
    </lazy-common-modal>
    <lazy-common-modal
      modal-id="modalShowPendingCustomerAction"
      :model-value="isShowModal.pendingCustomerAction"
      :close-icon="false"
    >
      <div class="w-200 p-4 max-w-[90vw]">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-medium">
            {{ $t('We have sent an email to your email, please check your inbox to confirm the cancellation of this order.') }}
          </h2>
        </div>
        <div class="flex gap-2">
          <button class="btn w-full border border-[#6c757d] hover:border-[#545b62] py-2 text-white bg-[#6c757d] hover:bg-[#5a6268]" @click="isShowModal.pendingCustomerAction = false">
            {{ $t('Close') }}
          </button>
          <button class="btn w-full border border-[#007bff] hover:border-[#0062cc] bg-[#007bff] hover:bg-[#0069d9] text-white" @click="resumeOrder">
            {{ $t('Resume order') }}
          </button>
        </div>
      </div>
    </lazy-common-modal>
    <lazy-common-modal
      modal-id="modalConfirmDeleteRequestCancelOrder"
      :model-value="isShowModal.confirmDeleteRequest"
      :close-icon="false"
    >
      <div class="w-200 p-4 max-w-[90vw]">
        <div class="text-center mb-8">
          <h2 class="text-2xl font-medium">
            {{ $t('Are you sure you want to resume this order?') }}
          </h2>
        </div>
        <div class="flex gap-2">
          <button class="btn w-full border border-[#6c757d] hover:border-[#545b62] py-2 text-white bg-[#6c757d] hover:bg-[#5a6268]" @click="isShowModal.confirmDeleteRequest = false">
            {{ $t('Close') }}
          </button>
          <button class="btn w-full border border-[#007bff] hover:border-[#0062cc] bg-[#007bff] hover:bg-[#0069d9] text-white" @click="resumeOrder">
            {{ $t('Resume order') }}
          </button>
        </div>
      </div>
    </lazy-common-modal>
  </main>
</template>

<style scoped>
.submit-ticket,
.tracking-url,
.edit-address,
.order-process .milestone.active {
  color: var(--color-primary);
}

.submit-ticket:hover,
.tracking-url:hover,
.edit-address:hover {
  color: var(--color-primary-hover);
}

.order-process .step {
  background: gray;
}
.order-process .step.active {
  background: var(--color-primary);
}

.order-process .milestone {
  color: #999;
  padding: 0 20px;
}

.order-process .milestone i {
  font-size: 24px;
  position: absolute;
  transform: translateX(-125%);
}

@media (min-width: 768px) {
  .order-process .step {
    height: 3px;
    transform: translateY(-12px);
  }
  .order-process .milestone i {
    font-size: 32px;
    transform: translate(-50%, -100%);
  }
  .order-process .milestone span {
    position: absolute;
    transform: translateX(-50%);
    width: max-content;
  }
  .order-process .milestone .text-secondary {
    color: #999;
    transform: translate(-50%, 75%);
  }
}
</style>
