<script lang="ts" setup>
const { banner, index } = defineProps({
  banner: {
    default: (): Banner => {
      return {}
    },
    type: Object
  },
  index: {
    default: null,
    type: Number || null
  }
})
const localePath = useLocalePath()
const $router = useRouter()
function goToPage() {
  $router.push(localePath(banner.banner_link || '/'))
}
</script>

<template>
  <div v-click-not-drag="goToPage" class="select-none flex justify-center">
    <nuxt-link :to="localePath((banner.banner_link !== '#') ? banner.banner_link : '/')">
      <common-image
        :alt="$t(banner.banner_text) || storeInfo().name"
        :image="{
          path: banner.banner_url,
          type: 'full'
        }"
        :fetchpriority="(index === 0) ? 'high' : ''"
        mobile-type="banner_mobile"
      />
    </nuxt-link>
  </div>
</template>
