<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

defineProps({
  splideSettings: {
    default: splideSetting,
    type: Object
  }
})

const { banners } = storeInfo()
</script>

<template>
  <div v-if="banners.length === 1">
    <default-home-banner-item :banner="banners[0]" :index="0" />
  </div>
  <Splide
    v-else
    :options="splideSettings"
  >
    <SplideSlide v-for="(banner, index) in banners" :key="index">
      <default-home-banner-item :banner="banner" :index="index" />
    </SplideSlide>
  </Splide>
</template>
