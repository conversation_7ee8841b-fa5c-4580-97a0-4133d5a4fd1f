<script lang="ts" setup>
const { banner } = defineProps({
  banner: {
    default: (): Banner => {
      return {}
    },
    type: Object
  }
})
const localePath = useLocalePath()
const $router = useRouter()
function goToPage() {
  $router.push(localePath(banner.banner_link || '/'))
}
</script>

<template>
  <div v-click-not-drag="goToPage" class="select-none transform transition-transform duration-200 hover:scale-105">
    <nuxt-link :to="localePath(banner.banner_link || '/')">
      <common-image
        :alt="$t(banner.banner_text) || storeInfo().name"
        :image="{
          path: banner.banner_url,
          type: 'collection_banner'
        }"
        img-class="w-full h-full"
        :title="$t(banner.banner_text)"
        class="banner-item-img"
      />

      <button class="absolute btn-text uppercase font-semibold position-center-x bottom-[15%] px-3 py-1 text-lg bg-light-800 bg-opacity-80 hover:shadow-custom2">
        {{ $t(banner.banner_text) }}
      </button>
    </nuxt-link>
  </div>
</template>
