<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

defineProps({
  splideSettings: {
    default: collectionBannerSplideSetting,
    type: Object
  }
})

const { collection_banners: collectionBanners } = storeInfo()
const $viewport = useViewport()
const itemsToShow = ref<number>($viewport.isGreaterThan(VIEWPORT.desktop) ? 5 : $viewport.isGreaterThan(VIEWPORT.mobile) ? 3 : 2)

watch($viewport.breakpoint, () => {
  itemsToShow.value = $viewport.isGreaterThan(VIEWPORT.desktop) ? 5 : $viewport.isGreaterThan(VIEWPORT.tablet) ? 3 : 2
})
</script>

<template>
  <client-only>
    <div
      v-if="collectionBanners.length <= itemsToShow"
      :key="itemsToShow"
      class="flex justify-center p-2 md:p-4"
    >
      <default-home-collection-banner-item
        v-for="(banner, index) in collectionBanners"
        :key="index"
        :banner="banner"
        :index="index"
        class="w-1/2 md:w-1/3 lg:w-1/5 min-w-1/2 md:min-w-1/3 lg:min-w-1/5 max-w-70 p-2 md:p-4"
      />
    </div>
    <Splide
      v-else
      :options="splideSettings"
      :extensions="splideExtensions"
      class="py-2 md:py-4"
    >
      <SplideSlide v-for="(banner, index) in collectionBanners" :key="index" class="p-2 md:p-4">
        <default-home-collection-banner-item :banner="banner" :index="index" />
      </SplideSlide>
    </Splide>
    <template #fallback>
      <div class="flex justify-center overflow-hidden p-2 md:p-4">
        <default-home-collection-banner-item
          v-for="(banner, index) in collectionBanners"
          :key="index"
          :banner="banner"
          class="w-1/2 md:w-1/3 lg:w-1/5 min-w-1/2 md:min-w-1/3 lg:min-w-1/5 max-w-70 p-2 md:p-4"
          :index="index"
        />
      </div>
    </template>
  </client-only>
</template>
