<script lang="ts" setup>
const { listProductData } = await useHomePage()
</script>

<template>
  <main id="homePage">
    <default-home-banner-carousel v-if="storeInfo().banners.length" />
    <lazy-default-home-collection-banner-carousel v-if="storeInfo().collection_banners.length" />
    <template v-for="(productsData, index) in listProductData">
      <common-product-carousel
        v-if="productsData?.products?.length"
        :key="index"
        class="container mt-10"
        title-class="text-center uppercase text-2xl font-semibold my-10"
        :title="productsData.name"
        :products="productsData.products"
        static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
      >
        <template #default="{ product, index: itemIndex }">
          <default-common-product-item :product="product" :index="itemIndex" />
        </template>
      </common-product-carousel>
    </template>
  </main>
</template>
