<script lang="ts" setup>

const { faq } = defineProps({
  faq: {
    type: Object as PropType<FAQ>,
    default: (): FAQ | object => {
      return {}
    }
  }
})

const visible = ref(false)
const replaced = computed(() => faq.answer.replace(/\\/g, '').replace(/(?<=href=")(?!http|\/)/g, '/'))
</script>
<template>
  <div class="mt-4">
    <div class="cursor-pointer flex justify-between" @click="$event => visible = !visible">
      <div class="self-center">
        {{ faq.question }}
      </div>
      <button class="rounded-full border-1 border-gray-300 text-3xl flex self-center my-2 mx-1">
        <i :class="`${(visible) ? 'icon-sen-minus' : 'icon-sen-plus'}`" />
      </button>
    </div>
    <common-collapse :when="visible" class="transition-default ease-in-out duration-500 mt-2">
      <div class="answer" v-html="replaced" />
    </common-collapse>
  </div>
</template>
<style>
.answer a {
  color: var(--color-primary)
}

.answer a:hover {
  color: var(--color-primary-hover)
}
</style>
