<script lang="ts" setup>
const localePath = useLocalePath()
const { usefulLinkList, faqData } = await useFAQPage()

onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
})
</script>
<template>
  <main class="container py-6 grid grid-cols-3 gap-1">
    <div class="p-4 col-span-12 md:col-span-2">
      <h1 class="text-4xl mb-10 font-medium">
        {{ $t('Frequently Asked Questions') }}
      </h1>
      <div v-for="category in faqData" :key="category.id" class="mb-5">
        <h5 class="text-xl font-medium pb-3 border-b-[1px] border-slate-300">
          {{ category.title }}
        </h5>
        <default-faq-item v-for="faq in category.faqList" :key="faq.id" :faq="faq" />
      </div>
    </div>
    <div class="p-4 col-span-12 md:col-span-1 sticky h-max top-[100px]">
      <h1 class="text-2xl mb-2 font-medium">
        Useful Links
      </h1>
      <div v-for="(usefulLink, index) in usefulLinkList" :key="index" class="mb-3">
        <nuxt-link :to="localePath(usefulLink.url)" class="btn-text">
          {{ $t(usefulLink.title) }}
        </nuxt-link>
      </div>
    </div>
  </main>
</template>
