<script lang="ts" setup>
const localePath = useLocalePath()

const {
  socialsLink,
  storeContact,
  logo_url: logoUrl,
  name: storeName
} = storeInfo()

const {
  emailNewsletter,
  contactPhoneList,
  currentContactPhoneIndex,
  enableContactForm,
  canHasExtraInfo,
  subscribeEmail,
  getSocialLink,
  encode,
  decode,
  copyrightText,
  getFooters
} = useFooter()
</script>

<template>
  <footer id="footer" class="lg:min-h-[450px] md:min-h-[650px] <md:min-h-[900px]">
    <common-html-head-tag for-position="footer_start" />

    <div class="container flex flex-wrap text-center lg:text-left mb-10">
      <div class="w-full lg:w-[40%] lg:order-2 flex flex-wrap text-center lg:text-left mt-10">
        <div class="pt-3 hidden">
          <nuxt-link exact :to="localePath('/bot')">
            {{ $t('Bot') }}
          </nuxt-link>
        </div>
        <div v-for="menuList in getFooters()" :key="menuList.title" class="w-full md:w-1/2 <md:my-5">
          <p class="uppercase font-semibold">
            {{ $t(menuList.title) }}
          </p>
          <div
            v-for="(item, index) in menuList.menuData"
            :key="index"
            class="pt-3 capitalize"
          >
            <nuxt-link exact :to="localePath(item.url)" class="btn-text">
              {{ $t(item.name) }}
            </nuxt-link>
          </div>
        </div>
      </div>
      <div class="md:hidden w-full flex justify-center mt-5 flex-wrap">
        <common-language-select class="px-1 mt-5" />
        <common-currency-select class="px-1 mt-5" />
      </div>
      <div class="w-full md:w-1/2 lg:w-[30%] lg:order-1 text-sm mt-10">
        <div class="flex <lg:justify-center my-5">
          <nuxt-link :to="localePath('/')">
            <common-image
              v-if="logoUrl"
              :image="{
                path: logoUrl,
                type: 'logo'
              }"
              img-id="footerLogo"
              :alt="storeName"
              img-class="h-10 -z-1"
            />
            <div v-else>
              <span class="text-2xl md:text-3xl font-bold">{{ storeName }}</span>
            </div>
          </nuxt-link>
        </div>
        <p v-if="canHasExtraInfo && storeContact.phone" class="font-medium mt-3">
          {{ storeName }}
        </p>
        <p v-if="canHasExtraInfo && storeContact.email_info">
          <span>{{ $t('Email') }}: </span>
          <span class="ml-2">{{ storeContact.email_info }}</span>
        </p>
        <p
          v-if="storeContact.address"
          :class="{
            'mt-2': !(canHasExtraInfo && (storeContact.phone || storeContact.email_info))
          }"
        >
          <span>{{ $t('Address') }}: </span>
          <span class="ml-2">{{ storeContact.address }}</span>
        </p>
        <div class="flex <lg:justify-center items-center">
          <span>{{ $t('Phone number') }}: </span>
          <span v-if="canHasExtraInfo && storeContact.phone" class="ml-2">
            {{ decode(encode(storeContact.phone)) }}
          </span>
          <common-dropdown
            v-else
            class="ml-0.5"
            dropdown-id="contactPhoneDropdown"
            btn-class="px-3 py-1"
          >
            <div class="flex items-center">
              <img
                v-for="(flag, flagIndex) in contactPhoneList[currentContactPhoneIndex].flags"
                :key="flagIndex"
                :src="`${cdnURL}${flag}`"
                alt="flag"
                class="mr-1 h-4"
                loading="lazy"
              >
              <span>{{ decode(encode(contactPhoneList[currentContactPhoneIndex].phone)) }}</span>
            </div>
            <template #content>
              <li
                v-for="(contactPhone, index) in contactPhoneList"
                :key="index"
                class="btn-text flex items-center px-4 min-w-full w-max py-1"
                :class="{ 'bg-primary !text-contrast': index === currentContactPhoneIndex }"
                @click="currentContactPhoneIndex = index"
              >
                <img
                  v-for="(flag, flagIndex) in contactPhone.flags"
                  :key="flagIndex"
                  :src="`${cdnURL}${flag}`"
                  alt="flag"
                  class="mr-1 h-4"
                  loading="lazy"
                >
                <span>{{ decode(encode(contactPhone.phone)) }}</span>
              </li>
            </template>
          </common-dropdown>
        </div>
        <div v-if="enableContactForm">
          <span>
            {{ $t('Need support?') }}
            <nuxt-link class="text-primary hover:text-primary-hover" :to="localePath('/page/contact-us')">{{ $t('Submit a ticket') }}</nuxt-link>
          </span>
        </div>
      </div>
      <div class="w-full md:w-1/2 lg:w-[30%] lg:order-3 mt-10">
        <template v-if="!storeInfo().disable_promotion">
          <p class="font-semibold uppercase mb-5">
            {{ $t('Subscribe to our newsletter') }}
          </p>
          <p class="mb-5">
            {{ $t('For sales, exclusive content, and more!') }}
          </p>
          <form class="flex <lg:px-10" @submit.prevent="subscribeEmail">
            <input
              id="newsletter_email"
              v-model="emailNewsletter"
              autocomplete="email"
              type="email"
              class="border rounded-l-[var(--sp-border-radius-2)] px-2 py-1 w-full"
              name="newsletter_email"
              :placeholder="$t('Enter your email')"
            >
            <button
              type="submit"
              class="px-2 py-1 bg-gray-600 text-white border border-l-0 rounded-r-[var(--sp-border-radius-2)] capitalize whitespace-nowrap"
            >
              {{ $t('Subscribe') }}
            </button>
          </form>
        </template>
        <common-payment-gateway-accept />

        <!-- social -->
        <template v-if="socialsLink">
          <p
            v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.google || socialsLink.youtube"
            class="font-semibold mt-5"
          >
            {{ $t('Follow us') }}
          </p>
          <div v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.google || socialsLink.youtube" class="contact-icon my-3 text-4xl">
            <a
              v-if="socialsLink.facebook"
              :href="getSocialLink(socialsLink.facebook)"
              target="_blank"
              class="mr-2"
              aria-label="facebook"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-facebook" /></span>
            </a>
            <a
              v-if="socialsLink.instagram"
              :href="getSocialLink(socialsLink.instagram)"
              target="_blank"
              class="mr-2"
              aria-label="instagram"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-instagram" /></span>
            </a>
            <a
              v-if="socialsLink.skype"
              :href="getSocialLink(socialsLink.skype)"
              target="_blank"
              class="mr-2"
              aria-label="skype"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-skype-business" /></span>
            </a>
            <a
              v-if="socialsLink.pinterest"
              :href="getSocialLink(socialsLink.pinterest)"
              target="_blank"
              class="mr-2"
              aria-label="pinterest"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-pinterest" /></span>
            </a>
            <a
              v-if="socialsLink.twitter"
              :href="getSocialLink(socialsLink.twitter)"
              target="_blank"
              class="mr-2"
              aria-label="twitter"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-twitter" /></span>
            </a>
            <a
              v-if="socialsLink.google"
              :href="getSocialLink(socialsLink.google)"
              target="_blank"
              class="mr-2"
              aria-label="google"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-google" /></span>
            </a>
            <a
              v-if="socialsLink.youtube"
              :href="getSocialLink(socialsLink.youtube)"
              target="_blank"
              class="mr-2"
              aria-label="youtube"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-youtube" /></span>
            </a>
          </div>
        </template>
      </div>
    </div>

    <common-html-head-tag for-position="footer_middle" />

    <div id="copyright" class="<md:(hidden mb-20) border-t py-2">
      <div class="container px-3 flex flex-wrap md:justify-between items-center justify-center">
        <small class="<md:(order-1 w-full) font-weight-500 text-center" v-html="copyrightText" />
        <div class="my-3 flex flex-wrap items-center gap-2 justify-center <md:( w-full order-0)">
          <span class="text-sm">{{ `${$t('Language')}/${$t('Currency')}` }}: </span>
          <common-language-select />
          <common-currency-select />
        </div>
      </div>
    </div>
    <common-html-head-tag for-position="footer_end" />
  </footer>
</template>
