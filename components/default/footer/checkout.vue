<script lang="ts" setup>
const localePath = useLocalePath()

const {
  logo_url: logoUrl,
  name: storeName,
} = storeInfo()
</script>
<template>
  <footer id="footer">
    <div class="my-6 container flex justify-between items-center <md:(justify-center mb-20)">
      <div class="<md:hidden">
        <nuxt-link :to="localePath('/')">
          <common-image
            v-if="logoUrl"
            :image="{
              path: logoUrl,
              type: 'logo',
            }"
            img-id="footerLogo"
            :alt="storeName"
            img-class="h-10 -z-1 rounded-none"
          />
          <div v-else>
            <span class="text-2xl md:text-3xl font-bold">{{ storeName }}</span>
          </div>
        </nuxt-link>
      </div>
      <div class="flex items-center gap-2">
        <span class="<md:hidden text-sm">{{ $t('Language') }}:</span>
        <common-language-select />
      </div>
    </div>
  </footer>
</template>
