<script lang="ts" setup>
import WarningShipLate from '~/components/Common/warningShipLate.vue'

const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  },
  shippingMethods: {
    type: Object,
    default: () => ({})
  },
  currentShippingMethod: {
    type: String,
    default: ''
  }
})
const { shippingMethods, currentShippingMethod } = toRefs(props)
const $emit = defineEmits(['updateShippingMethod'])
const { $i18n } = useNuxtApp()

const ONE_DAY = 60 * 60 * 24 * 1000
const dateFormat: Intl.DateTimeFormatOptions = { month: 'short', day: 'numeric' }

const userTrigger = ref(false)
const computedShippingMethods = computed(() => {
  return shippingMethods.value.map((shippingMethod: any) => {
    const shippingTime = shippingMethod.shipping_time ?? [3, 10]
    const printingTime = shippingMethod.printing_time ?? 3

    let description = ''
    const { date: dateLocale } = $i18n.localeProperties
    if (shippingMethod.name === SHIPPING_METHOD.standard) {
      const date = new Date(Date.now() + ONE_DAY * printingTime)
      description = $i18n.t('Printed before _day', { day: date.toLocaleDateString(dateLocale, dateFormat) })
      // description += "<br><span class='note'>* Due to the peak season, we do not guarantee delivery before Christmas and New Year.</span>"
    }
    if (shippingMethod.name === SHIPPING_METHOD.express) {
      description = $i18n.t('We print your order immediately (no cancellation allowed)')
    }
    const date1 = new Date(Date.now() + ONE_DAY * (shippingTime[0] + printingTime))
    const date2 = new Date(Date.now() + ONE_DAY * (shippingTime[1] + printingTime))
    description += '. ' + $i18n.t('Estimated delivery from _day1 to _day2.', { day1: date1.toLocaleDateString(dateLocale, dateFormat), day2: date2.toLocaleDateString(dateLocale, dateFormat) })
    shippingMethod.description = description

    return shippingMethod
  })
})

function updateShippingMethod (method: string) {
  if (isLoading.value) {
    return
  }

  userTrigger.value = true
  $emit('updateShippingMethod', method || SHIPPING_METHOD.standard)
}
</script>
<template>
  <template v-if="!userTrigger && currentShippingMethod !== SHIPPING_METHOD.express">
    <div class="my-3">
      {{ computedShippingMethods[0].description }}<br>
      <warning-ship-late />
      <a v-if="computedShippingMethods.find((item) => item.name === SHIPPING_METHOD.express)" href="#" class="btn-text text-primary" @click.prevent="updateShippingMethod(SHIPPING_METHOD.express)">
        <small v-text="$t('Need it fast? Upgrade to express shipping by clicking here')" />
      </a>
    </div>
  </template>
  <template v-else>
    <h6 class="my-3 font-medium">
      {{ $t('Delivery options') }}
    </h6>
    <div
      v-for="(shipping, index) in computedShippingMethods"
      :key="index"
      class="p-3 border cursor-pointer hover:shadow-custom border-radius-override-p2 relative"
      :class="{
        'border-t-0 !rounded-t-none': (index < computedShippingMethods.length && index > 0),
        '!rounded-b-none': (index === 0)
      }"
      @click="updateShippingMethod(shipping?.name)"
    >
      <div class="flex gap-3">
        <common-radio class="mt-2" :active="currentShippingMethod === shipping?.name" />
        <div>
          <div class="capitalize font-medium">
            <span> {{ shipping?.name }}</span>
            <span v-if="shipping?.shipping_cost"> - {{ $formatPrice(shipping?.shipping_cost, USD_CODE, order.currency_code) }}</span>
            <common-loading-dot v-if="isLoading === `shipping_fee_${shipping?.name}`" variant="bg-black" class="ml-2" />
          </div>
          <div
            v-if="storeInfo().show_checkout_shipping_info !== 0"
            class="text-sm"
            v-html="shipping?.description"
          />
        </div>
      </div>
      <div v-if="isLoading && isLoading !== `shipping_fee_${shipping?.name}`" class="absolute top-0 left-0 h-full w-full bg-[rgba(233,233,233,0.7)]" />
    </div>
  </template>
</template>
