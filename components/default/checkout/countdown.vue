<script lang="ts" setup>
const {
  timer,
  parsedTimer,
  runTimer
} = useCheckoutCountTimer()

onMounted(() => {
  runTimer()
})
</script>

<template>
  <div v-if="!storeInfo().disable_promotion" class="flex items-center justify-between bg-[#fff3cd] h-15 px-4 mb-4 mt-2">
    <div v-if="timer" class="text-[#856404]">
      {{ $t('Your order is reserved for') }}
      <span v-if="parsedTimer.minutes" class="font-weight-500">
        {{ parsedTimer.minutes }} {{ $t('minute', { count: parsedTimer.minutes }) }}&nbsp;
      </span>
      <span v-if="parsedTimer.seconds" class="font-weight-500">
        {{ parsedTimer.seconds }} {{ $t('second', { count: parsedTimer.seconds }) }}
      </span>
    </div>
    <button
      v-else
      class="btn-text text-blue-900"
      @click="timer = 600"
    >
      {{ $t('Reset order') }}
    </button>
  </div>
</template>
