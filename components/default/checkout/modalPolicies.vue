<script lang="ts" setup>
const isShow = ref(false)
const pageData = ref<PageData>()

async function toggleShow (state: boolean, policyType: string) {
  if (state) {
    const { pageData: data } = await useCommonFooterPage(policyType)
    pageData.value = data.value
  }
  isShow.value = state
}

defineExpose({ toggleShow })
</script>
<template>
  <common-modal
    modal-id="checkoutPolicy"
    :model-value="isShow"
    @close-modal="isShow = false"
  >
    <div class="w-300 p-4 pt-3 max-w-[90vw]">
      <div class="flex justify-between items-center">
        <h3 class="font-medium text-xl">
          {{ $t(pageData?.title || '') }}
        </h3>
      </div>
      <hr class="mt-3">
      <div class="overflow-auto max-h-[70vh] mt-3 pr-4" v-html="pageData?.content" />
    </div>
  </common-modal>
</template>
<style lang="scss">
#checkoutPolicy {
  z-index: 999;

  h2, h3, h4, h5 {
    font-weight: 500;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.8rem;
  }

  h4 {
    font-size: 1.5rem;
  }

  h5 {
    font-size: 1.25rem;
  }
}
</style>
