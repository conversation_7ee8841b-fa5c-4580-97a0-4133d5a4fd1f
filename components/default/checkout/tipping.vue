<script lang="ts" setup>
import { USD_CODE } from '~/utils/constant'

const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  }
})

const emit = defineEmits(['updateTip'])

const {
  currentTip,
  tipList,
  currentTipIndex,
  userTriggerTipping,
} = useCheckoutTip()

const backCurrentTipToOriginalTimeout = ref<number | null>(null)
const isCustomTipFocus = ref(false)

const { $formatPriceByCurrency, $convertPrice } = useNuxtApp()

const currentTipText = computed({
  get () {
    if (isCustomTipFocus.value) {
      return currentTip.value
    }
    return $formatPriceByCurrency(currentTip.value, 1, props.order.currency_code)
  },
  set (value: number | string) {
    currentTip.value = Number(value)
  }
})

onMounted(() => {
  resetTip(props.order)
})

watch(props.order, () => {
  resetTip(props.order)
})

function resetTip (order:Order) {
  const index = tipList.findIndex(item => order?.tip_amount?.toFixed(2) === ((order?.total_product_amount || 0) * item).toFixed(2))
  currentTipIndex.value = (index < 0 ? false : index)
  currentTip.value = Number(order?.tip_amount || 0)
  backCurrentTipToOriginal()
}

function backCurrentTipToOriginal () {
  currentTip.value = $convertPrice(props.order.tip_amount, USD_CODE, props.order.currency_code).value
}

function onCustomTipFocusOut () {
  isCustomTipFocus.value = false
  backCurrentTipToOriginalTimeout.value = setTimeout(resetTip, 200)
}

function onUpdateCustomTip () {
  if (backCurrentTipToOriginalTimeout.value) {
    clearTimeout(backCurrentTipToOriginalTimeout.value)
  }
  emit('updateTip', {
    key: 'custom-tip',
    value: {
      tip_amount: $convertPrice(currentTip.value || 0, props.order.currency_code, USD_CODE).value.toFixed(2)
    }
  })
}
</script>

<template>
  <div class="clearfix mt-2" />
  <a
    href="#"
    class="btn-text text-primary mt-2"
    data-test-id="tipping-toggle"
    @click.prevent="userTriggerTipping = !userTriggerTipping"
  >{{ $t('Want to tip our designer?') }}</a>
  <div v-show="order.tip_amount || userTriggerTipping">
    <p class="font-small text-gray2 mt-2">
      {{ $t('Please consider tipping to appreciate our designer\'s work.') }}
    </p>
    <div class="grid grid-cols-4 mt-4">
      <button
        v-for="(tip, index) in tipList"
        :key="index"
        class="col-span-1 border border-primary text-primary py-3 hover:(bg-primary-hover text-contrast shadow-custom2) active:(bg-primary text-contrast shadow-custom3)"
        :class="{
          'border-l-0': index > 0,
          'bg-primary !text-contrast': currentTipIndex === index,
          'cursor-not-allowed': !!isLoading,
          '!rounded-r-none': index === 0,
          '!rounded-l-none': index === (tipList.length - 1),
          '!rounded-none': (index < (tipList.length - 1) && index > 0),
        }"
        @click="
          $emit('updateTip',{
            key: 'tip',
            value: {
              tip_amount: ((order?.total_product_amount || 0) * tip).toFixed(2)
            }
          });
          currentTipIndex = index"
      >
        <common-loading-dot v-if="isLoading === 'tip' && currentTipIndex === index" />
        <span v-else-if="tip===0">
          {{ $t('No tip') }}
        </span>
        <template v-else>
          <div>{{ tip * 100 }} %</div>
        </template>
      </button>
    </div>
    <form
      action="#"
      class="flex mt-4"
      @submit.prevent.stop="onUpdateCustomTip"
    >
      <default-common-input
        v-model="currentTipText"
        class="w-full"
        :input-class="'!rounded-r-none'"
        :label="$t('Custom tip')"
        data-test-id="tipping-input-amount"
        @blur="onCustomTipFocusOut"
        @focus="isCustomTipFocus = true"
      />
      <button
        :disabled="!!isLoading"
        type="submit"
        :class="{'cursor-not-allowed bg-gray-400': isLoading}"
        class="min-w-max w-30 btn-fill-black px-3 !rounded-l-none"
        data-test-id="tipping-update-tip-btn"
      >
        <common-loading-dot v-if="isLoading === 'custom-tip'" />
        <span v-else>
          {{ $t('Update tip') }}
        </span>
      </button>
    </form>
  </div>
</template>

<style>
</style>
