<script lang="ts" setup>
import { useCheckoutDynamicForm } from '~/composables/checkout'
import { useComboCartStore } from '~/store/comboCart'
import { useUserSession } from '~/store/userSession'
import { ORDER_SUMMARY_POSITION } from '~/utils/constant'

interface ModalPolicies extends HTMLElement {
  toggleShow: Function
}

const token = useRoute().params.token as string
const route = useRoute()
const localePath = useLocalePath()
const $viewport = useViewport()
const modalPolicies = ref<ModalPolicies>()
const { $formatPriceNoUnit, $fetchDefault } = useNuxtApp()
const {
  order,
  shippingMethods,
  userInfo,
  currentShippingMethod,
  isNotOrderService,
  paymentGateways,
  isShowOrderSummary,
  countryDisabledCheckout,
  updateCheckoutDiscount,
  resetData,
  reloadOrder,
  updateOrder,
  removeProduct
} = await useCheckout(token as string)
const {
  userInfoForm,

  paymentDisplayType,
  isShowModalConfirmEmail,

  gateways,
  currentGateway,

  initPaymentGateways,
  submitCheckout,
  useCreditCardDiscount,

  paypalIframeFullScreen,
  paypalButtonContainer,
  paypalIsShowModalCreditCardDiscount,
  paypalModalConfirmed,

  stripeIframeContainer,
  stripeIframeEWalletContainer,
  stripeData,
  stripeProcessedBanks
} = useCheckoutPayment(token, userInfo, order, paymentGateways, updateOrder)

const {
  currentCountry
} = useCheckoutForm(userInfo)

onMounted(() => {
  initPaymentGateways()
  if (route.query.cart_key) {
    useUserSession().setOrderKey(order.access_token as string, route.query.cart_key as string)
    $fetchDefault(`/public/order/callback/abandoned/track?cartKey=${route.query.cart_key}`)
  }

  document.addEventListener('visibilitychange', reloadOrder)
  resetData()
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', reloadOrder)
})

watch(() => order.total_amount, (oldPrice, newPrice) => {
  if ($formatPriceNoUnit(oldPrice, order.currency_code, 'USD') === $formatPriceNoUnit(newPrice, order.currency_code, 'USD')) {
    return
  }
  initPaymentGateways()
})

let updateTimeout: any
const { isHasField } = useCheckoutDynamicForm(userInfo, false)
watch(userInfo, () => {
  if (!userInfoForm.value?.isFormValid) {
    return
  }
  if (!isHasField('state')) {
    userInfo.state = ''
  }
  if (!isHasField('address_2')) {
    userInfo.address_2 = ''
  }
  if (!isHasField('houseNumber', false)) {
    userInfo.house_number = ''
  }
  if (!isHasField('mailboxNumber', false)) {
    userInfo.mailbox_number = ''
  }
  if (!isHasField('deliveryNote', false)) {
    userInfo.note = ''
  }
  clearTimeout(updateTimeout)
  updateTimeout = setTimeout(() => {
    const isRequiredInfoFilled = (userInfo.email && userInfo.name) // Require at least email & name to make sure the request is succeed
    updateOrder('user-info', {
      order_token: token,
      ...(isRequiredInfoFilled) ? ({ user_info: { ...userInfo }, order_note: userInfo.note }) : ({}),
      email: userInfo.email,
      country: userInfo.country
    }, userInfo)
  }, 1000)
})

const { combos, productIdsInCombo } = campaignComboUI(toRef(order, 'products'), computed(() => order))

const products = computed(() => order.products.filter(product => !productIdsInCombo.value.includes(product.id)))

function getProductDB(product: CartItem) {
  return order.products.find(p => p.product_id === product.product_id) as OrderProduct
}

function isComboNoShip(combo: ComboItem) {
  return combo.items.some(item => getProductDB(item).fulfill_status === 'no_ship')
}
</script>

<template>
  <main id="checkoutPage" :style="`--sprite-url: url('${cdnURL}images/logo_checkout_sprite.webp')`" class="container mt-6">
    <nuxt-link v-if="isNotOrderService" class="text-sm text-blue-900 btn-text" :to="localePath('/cart')">
      <i class="icon-sen-arrow-left mr-1" />
      {{ $t('Return to cart') }}
    </nuxt-link>
    <default-checkout-countdown v-if="isNotOrderService" />
    <div class="grid grid-cols-12 flex">
      <!-- Order summary on Mobile top and Tablet, PC -->
      <div v-if="($viewport.isLessThan(VIEWPORT.tablet) && storeInfo().order_summary_position === ORDER_SUMMARY_POSITION.TOP) || $viewport.isGreaterOrEquals(VIEWPORT.tablet)" class="col-span-12 md:(col-span-5 pl-3 order-last sticky h-max top-20) <md:(bg-[#F5F5F5] px-2) mb-4">
        <div
          class="<md:(text-primary flex justify-between items-center cursor-pointer py-3)"
          data-test-id="order-summary-btn"
          @click="isShowOrderSummary = !isShowOrderSummary"
        >
          <h3 class="text-xl md:text-[1.75rem] font-medium">
            {{ $t('Order Summary') }}
            <span
              class="md:hidden"
              :class="isShowOrderSummary ? 'icon-sen-chevron-down' : 'icon-sen-chevron-up'"
            />
          </h3>
          <span class="md:hidden text-2xl">{{ $formatPriceByCurrency(order.total_amount, order.currency_rate, order.currency_code) }}</span>
        </div>
        <client-only>
          <common-collapse :when="$viewport.isGreaterOrEquals(VIEWPORT.tablet) || isShowOrderSummary" class="transition-default">
            <lazy-default-campaign-combo-item
              v-for="combo in combos"
              :key="combo.id"
              :is-no-ship="isComboNoShip(combo)"
              :combo="combo"
              :title="combo.title"
              :thumb_url="combo.thumb_url"
              :price="combo.combo_price"
              :quantity="combo.quantity"
              :items="combo.items"
            />
            <default-order-product-item
              v-for="(product, index) in products"
              :key="index"
              :product="product"
              :convert-currency-code="order.currency_code"
              :convert-currency-rate="order.currency_rate"
              :show-img="isNotOrderService"
              page="checkout"
              @remove-product="removeProduct(product)"
            />

            <default-order-calculate-price :order="order" />
          </common-collapse>
        </client-only>
      </div>

      <div class="col-span-12 md:(col-span-7 pr-3 order-first)">
        <h1 class="text-[1.75rem] font-medium mb-4">
          {{ $t('Checkout') }}
        </h1>
        <ClientOnly>
          <default-checkout-form
            key="client"
            ref="userInfoForm"
            :user-info="userInfo"
            :is-phone-number-required="order.shipping_method === SHIPPING_METHOD.express"
          />
          <template #fallback>
            <default-checkout-form
              ref="userInfoForm"
              :user-info="userInfo"
              :is-phone-number-required="order.shipping_method === SHIPPING_METHOD.express"
            />
          </template>
        </ClientOnly>
        <div v-if="order.fulfill_status === 'no_ship'" class="bg-[#fff6e6] text-center my-4 py-2">
          <div class="font-medium">
            {{ countryDisabledCheckout.includes(userInfo.country) ? $t(`no_ship_text1`, { country: currentCountry?.name }) : $t('We are sorry, but there are some product variants in your cart that are not available.') }}
          </div>
          <div>{{ countryDisabledCheckout.includes(userInfo.country) ? $t('no_ship_text2') : $t('Please remove it or edit to continue.') }}</div>
        </div>

        <default-checkout-shipping-methods
          v-else-if="isNotOrderService && shippingMethods && shippingMethods.length > 0"
          :shipping-methods="shippingMethods"
          :current-shipping-method="currentShippingMethod"
          @update-shipping-method="(value) => { currentShippingMethod = value; }"
        />

        <div
          v-if="isNotOrderService && order.insurance_fee_2 && storeInfo().enable_insurance_fee"
          id="ShippingInsuranceCheckbox"
          class="text-gray-500 text-sm cursor-pointer flex items-center mt-4 btn-text"
          :class="isLoading ? '!text-gray-300' : ''"
          data-test-id="shipping-insurance-checkbox"
          @click="isLoading ? '' : updateOrder('insurance_fee', { delivery_insurance: !order.insurance_fee })"
        >
          <i class="mr-1" :class="order.insurance_fee ? 'icon-sen-checkbox-marked-outline' : 'icon-sen-checkbox-blank-outline'" />
          <span>{{ $t('Add Delivery Insurance and SMS tracking for only') }} {{ $formatPriceByCurrency(order.insurance_fee_2, order.currency_rate, order.currency_code) }}</span>
        </div>

        <default-checkout-discount
          v-if="isNotOrderService"
          class="mt-4"
          :order="order"
          @update-checkout-discount="updateCheckoutDiscount"
        />

        <default-checkout-tipping
          v-if="isNotOrderService && storeInfo().show_tipping"
          :order="order"
          @update-tip="updateOrder($event.key, $event.value)"
        />

        <div v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.NULL" class="text-xl mt-6">
          <strong class="text-red-600">{{ $t('Error') }}:</strong> {{ $t('Please set up a payment gateway for processing transactions!') }}
        </div>

        <div
          v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT"
          id="paypal-button-container"
          ref="paypalButtonContainer"
          data-test-id="checkout-payment-type"
          data-test-prop="paypal-smartcheckout"
          class="mt-4"
        />
        <div
          v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT_IFRAME"
          id="paypal-button-container"
          ref="paypalButtonContainer"
          data-test-id="checkout-payment-type"
          data-test-prop="paypal-smartcheckout"
          :class="(paypalIframeFullScreen) ? 'fixed h-[100vh] w-[100vw] z-99999 top-0 left-0' : 'mt-4 lg:h-[11rem] w-full'"
        />

        <h6
          v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.LIST"
          id="paymentInfo"
          class="font-medium mt-4 mb-4 flex flex-wrap gap-2 justify-between items-center"
        >
          {{ $t('Payment info') }}
          <span v-if="storeInfo().enable_payment_ssl_norton" class="md:flex flex-wrap gap-2 hidden">
            <i class="icons-sprite" style="--icon-height: 73;--icon-target-height: 30;" />
            <i class="icons-sprite" style="--icon-height: 80;--icon-target-height: 30;--icon-y-pos: 28;" />
          </span>
        </h6>

        <div v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.LIST">
          <!-- stripe-card -->
          <div
            v-if="gateways.stripeGateway"
            class="p-3 border cursor-pointer gateway"
            data-test-id="checkout-payment-type"
            data-test-prop="stripe"
            @click="currentGateway = PAYMENT_METHOD.stripeCard"
          >
            <div class="flex">
              <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeCard" />
              <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                <span class="capitalize">
                  {{ $t('Card') }}
                </span>
                <div class="flex flex-wrap gap-1">
                  <div
                    class="icon-card"
                    :class="stripeData.brandCard === 'visa' ? 'opacity-100' : 'opacity-60'"
                  >
                    <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 42;" />
                  </div>
                  <div
                    class="icon-card"
                    :class="stripeData.brandCard === 'amex' ? 'opacity-100' : 'opacity-60'"
                  >
                    <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 67;" />
                  </div>
                  <div
                    class="icon-card"
                    :class="stripeData.brandCard === 'mastercard' ? 'opacity-100' : 'opacity-60'"
                  >
                    <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 92;" />
                  </div>
                  <div
                    class="icon-card"
                    :class="stripeData.brandCard === 'discover' ? 'opacity-100' : 'opacity-60'"
                  >
                    <i class="icons-sprite" style="--icon-height: 94;--icon-y-pos: 117;" />
                  </div>
                </div>
              </div>
            </div>
            <common-collapse
              :when="currentGateway === PAYMENT_METHOD.stripeCard"
              :class="{ 'mt-4': currentGateway === PAYMENT_METHOD.stripeCard }"
              class="transition-default grid grid-cols-2 gap-3"
            >
              <div v-if="stripeData.stripeGatewayError" class="mt-2 w-full text-sm text-red-500">
                {{ stripeData.stripeGatewayError }}
              </div>
              <div
                v-if="gateways.stripeGateway.checkout_domain"
                id="stripe-iframe-container"
                ref="stripeIframeContainer"
                class="col-span-2 w-full"
              />
              <template v-else>
                <div
                  class="col-span-2 p-3 border relative"
                  :class="{ 'input-error': stripeData.stripeGatewayError }"
                >
                  <div id="card-payment-number" />
                  <i v-if="!stripeData.stripeGatewayError" class="absolute icon-sen-lock right-3 position-center-y z-1" />
                </div>
                <div
                  class="col-span-2 md:col-span-1 p-3 border relative"
                  :class="{ 'input-error': stripeData.stripeGatewayError }"
                >
                  <div id="card-payment-expiry" />
                </div>
                <div
                  class="col-span-2 md:col-span-1 p-3 border relative border-radius-override-p2"
                  :class="{ 'input-error': stripeData.stripeGatewayError }"
                >
                  <div id="card-payment-cvc" />
                  <div v-if="!stripeData.stripeGatewayError" class="phone-tooltip z-1 absolute -right-1 h-full position-center-y">
                    <i class="icon-sen-info-outline absolute right-4 position-center-y rounded-full center-flex" />
                    <div class="tooltip-content">
                      <p class="mb-1 text-white">
                        {{ $t('3-digit security code usually found on the back of your card. American Express cards have a 4-digit code located on the front.') }}
                      </p>
                    </div>
                  </div>
                </div>
              </template>
            </common-collapse>
          </div>
          <!-- stripe-ewallet -->
          <div
            v-if="gateways.stripeGateway"
            class="p-3 border cursor-pointer gateway"
            data-test-id="checkout-payment-type"
            data-test-prop="stripe-ewallet"
            @click="currentGateway = PAYMENT_METHOD.stripeEwallet"
          >
            <div class="flex">
              <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeEwallet" />
              <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                <span>Apple/Google Pay</span>
                <div
                  class="flex flex-wrap gap-2"
                  :class="currentGateway === PAYMENT_METHOD.stripeEwallet ? 'opacity-100' : 'opacity-60'"
                >
                  <i class="icon-card icons-sprite" style="--icon-height: 100;--icon-y-pos: 134; height: 16px;" />
                  <i class="icon-card icons-sprite" style="--icon-height: 90;--icon-y-pos: 167; height: 16px;" />
                </div>
              </div>
            </div>
            <common-collapse
              :when="currentGateway === PAYMENT_METHOD.stripeEwallet"
              class="transition-default"
            >
              <div v-if="stripeData.stripeGatewayError" class="mt-2 w-full text-sm text-red-500">
                {{ stripeData.stripeGatewayError }}
              </div>
              <div
                v-if="gateways.stripeGateway.checkout_domain"
                id="stripe-iframe-ewallet-container"
                ref="stripeIframeEWalletContainer"
                class="col-span-2 w-full"
              />
              <div v-else id="payment-element" />
            </common-collapse>
          </div>
          <!-- paypal -->
          <div
            v-if="gateways.paypalGateway?.clientId"
            class="p-3 border cursor-pointer gateway"
            data-test-id="checkout-payment-type"
            data-test-prop="paypal"
            @click="currentGateway = PAYMENT_METHOD.paypal"
          >
            <div class="flex">
              <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.paypal" />
              <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                <span>PayPal</span>
                <i :class="currentGateway === PAYMENT_METHOD.paypal ? 'opacity-100' : 'opacity-60'" class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371;" />
              </div>
            </div>
          </div>
          <!-- banks -->
          <div
            v-if="gateways.stripeGateway && currentCountry?.code !== 'US' && gateways.stripeGateway?.options?.other_payments"
            class="p-3 border cursor-pointer gateway"
            data-test-id="checkout-payment-type"
            data-test-prop="stripe-bank"
            @click="currentGateway = PAYMENT_METHOD.stripeBank"
          >
            <div class="flex">
              <common-radio class="self-center" :active="currentGateway === PAYMENT_METHOD.stripeBank" />
              <div class="pl-3 flex flex-wrap gap-2 w-full items-center justify-between">
                <span class="capitalize">
                  {{ $t('Other') }}
                </span>
                <div
                  class="flex flex-wrap gap-1"
                >
                  <i
                    v-for="(bank, index) in stripeProcessedBanks"
                    :key="index"
                    class="icon-card icons-sprite" :class="[
                      `icons-sprite-stripe-${bank.name}`,
                      (currentGateway === PAYMENT_METHOD.stripeBank && stripeData.currentBank?.name === bank.name) ? 'opacity-100' : 'opacity-60'
                    ]"
                    @click="currentGateway = PAYMENT_METHOD.stripeBank; stripeData.currentBank = bank"
                  />
                </div>
              </div>
            </div>
            <common-collapse
              :when="currentGateway === PAYMENT_METHOD.stripeBank"
              class="transition-default"
            >
              <div v-if="stripeData.stripeGatewayError" class="mt-2 w-full text-sm text-red-500">
                {{ stripeData.stripeGatewayError }}
              </div>
              <common-dropdown
                class="mt-3"
                btn-class="border w-full p-2"
                dropdown-class="w-full"
              >
                <span>{{ stripeData.currentBank.name }}</span>
                <template #content>
                  <button
                    v-for="(bank, index) in stripeProcessedBanks"
                    :key="index"
                    :style="`--sprite-url: url('${cdnURL}images/logo_checkout_sprite.webp')`"
                    class="btn-text block px-3 py-1 w-full flex justify-between items-center hover:(bg-gray-300 text-white)"
                    :class="{ '!bg-primary !text-contrast': stripeData.currentBank?.name === bank.name }"
                    @click="currentGateway = PAYMENT_METHOD.stripeBank; stripeData.currentBank = bank"
                  >
                    <span>{{ bank.name }}</span>
                    <i
                      class="ml-1 icons-sprite icons-sprite-list-banks"
                      :class="`icons-sprite-${bank.name}`"
                    />
                  </button>
                </template>
              </common-dropdown>
              <template v-if="stripeData.currentBank?.required">
                <common-dropdown
                  v-if="stripeData.currentBank.required.includes('country')"
                  class="mt-3"
                  btn-class="w-full border p-2"
                  dropdown-class="max-w-[80vw] w-full text-center"
                >
                  <template v-if="stripeData.billingDetails.country">
                    <span class="inline-block vti__flag" :class="stripeData.billingDetails.country.toLowerCase()" />
                    <span class="ml-2">
                      {{ countryByCode(stripeData.billingDetails.country)?.name }}
                    </span>
                  </template>
                  <span v-else>
                    {{ $t('Country') }}
                  </span>
                  <template #content>
                    <div
                      v-for="(countryCode, index) in stripeData.currentBank.countries[(gateways.stripeGateway.options.klarna_currency || stripeData.currentBank.currency)]"
                      :key="index"
                      class="btn-text py-1 px-3"
                      :class="{
                        'bg-gray-100 cursor-not-allowed hover:text-black': countryDisabledCheckout.includes(countryCode),
                        'bg-primary text-contrast': countryCode === userInfo?.country
                      }"
                      @click="stripeData.billingDetails.country = countryCode"
                    >
                      <span class="vti__flag inline-block" :class="countryCode.toLowerCase()" />
                      <span class="ml-2">
                        {{ countryByCode(countryCode)?.name }}
                      </span>
                    </div>
                  </template>
                </common-dropdown>
              </template>

              <div class="w-full" :class="stripeData.currentBank.name_component ? 'block mt-3 border' : 'hidden'">
                <div :id="`stripe-${stripeData.currentBank.name}`" />
              </div>
            </common-collapse>
          </div>
        </div>

        <div
          v-if="paymentDisplayType === PAYMENT_DISPLAY_TYPE.LIST"
          class="<md:(p-1 border bottom-fixed w-full bg-white z-1) mt-4"
          @click="
            useTracking().customTracking({
              event: 'checkout_place_order_button_click'
            })
          "
        >
          <button
            :disabled="
              !!isLoading
                || !currentGateway
                || order.fulfill_status === 'no_ship'
                || countryDisabledCheckout.includes(userInfo.country)
            "
            class="w-full btn-fill py-3 text-xl font-medium relative center-flex capitalize"
            :class="{
              'cursor-not-allowed': !!isLoading || !currentGateway,
              'hover:brightness-95 filter': currentGateway === PAYMENT_METHOD.paypal
            }"
            :style="currentGateway === PAYMENT_METHOD.paypal ? '--color-primary: #ffc439; --color-primary-hover: #ffc439; --color-primary-active: #ffc439;' : ''"
            data-test-id="checkout-submit-form"
            @click="submitCheckout"
          >
            <common-loading-spinner v-if="isLoading === 'place_order'" class="right-3" />
            <span v-if="countryDisabledCheckout.includes(userInfo.country || '')">{{ $t('Cannot ship to', { country: currentCountry?.name }) }}</span>
            <span v-else-if="[PAYMENT_METHOD.stripeCard, PAYMENT_METHOD.stripeEwallet].includes(currentGateway)" class="center-flex gap-1">
              <i class="icon-sen-lock" />
              {{ $t('Place your order') }}
            </span>
            <span v-else-if="currentGateway === PAYMENT_METHOD.paypal" class="flex gap-1 normal-case text-gray-900">
              {{ $t('Checkout with') }}
              <div class="overflow-hidden" style="width: 94px;">
                <i class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; margin-left: -11px; scale: 1.5;" />
              </div>
            </span>
            <span v-else-if="currentGateway === PAYMENT_METHOD.stripeBank">
              {{ $t('Continue With {gate}', { gate: stripeData.currentBank.name }) }}
            </span>
            <span v-else>{{ $t('Select a method') }}</span>
          </button>
        </div>
      </div>
    </div>
    <!-- Mobile order summary bottom -->
    <div v-if="($viewport.isLessThan(VIEWPORT.tablet) && storeInfo().order_summary_position === ORDER_SUMMARY_POSITION.BOTTOM)" class="col-span-12 md:(col-span-5 pl-3 order-last sticky h-max top-20) <md:(bg-[#F5F5F5] px-2) mb-4">
      <div
        class="<md:(text-primary flex justify-between items-center cursor-pointer py-3)"
        data-test-id="order-summary-btn"
        @click="isShowOrderSummary = !isShowOrderSummary"
      >
        <h3 class="text-xl md:text-[1.75rem] font-medium">
          {{ $t('Order Summary') }}
          <span
            class="md:hidden"
            :class="isShowOrderSummary ? 'icon-sen-chevron-down' : 'icon-sen-chevron-up'"
          />
        </h3>
        <span class="md:hidden text-2xl">{{ $formatPriceByCurrency(order.total_amount, order.currency_rate, order.currency_code) }}</span>
      </div>
      <client-only>
        <common-collapse :when="isShowOrderSummary" class="transition-default">
          <lazy-default-order-combo-item-group
            v-if="useComboCartStore().enoughCombo.length > 0"
            :order="order"
            page="checkout"
          />
          <default-order-product-item
            v-for="(product, index) in products"
            :key="index"
            :product="product"
            :convert-currency-code="order.currency_code"
            :convert-currency-rate="order.currency_rate"
            :show-img="isNotOrderService"
            page="checkout"
            @remove-product="removeProduct(product)"
          />

          <default-order-calculate-price :order="order" />
        </common-collapse>
      </client-only>
    </div>
    <div v-if="isNotOrderService" class="mt-3 index-0 pb-5 md:pb-0">
      <p v-if="false">
        {{ `${$t('checkout_text_1')} ${storeInfo().name || 'store'} ${$t('checkout_text_2')} ${storeInfo().name || 'store'} ${$t('checkout_text_3')}` }}
      </p>
      <p class="mb-3">
        <span>{{ $t('checkout_text_4') }}&nbsp;</span><a
          class="text-blue-600 hover:text-blue-800"
          :href="localePath('/page/terms-of-service')"
          @click.prevent="modalPolicies?.toggleShow(true, 'terms-of-service')"
          v-text="$t('terms of service')"
        />.
      </p>
      <span v-if="storeInfo().enable_payment_ssl_norton" class="flex flex-wrap justify-center gap-8 md:hidden my-2">
        <i class="icons-sprite" style="--icon-height: 73;--icon-target-height: 45;" />
        <i class="icons-sprite" style="--icon-height: 80;--icon-target-height: 45;--icon-y-pos: 45;" />
      </span>
    </div>

    <hr class="mt-4">
    <div class="mt-4 flex flex-wrap gap-4">
      <a
        class="text-xs text-blue-600 hover:text-blue-800"
        :href="localePath('/page/return-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'return-policy')"
        v-text="$t('Return policy')"
      />
      <a
        class="text-xs text-blue-600 hover:text-blue-800"
        :href="localePath('/page/shipping-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'shipping-policy')"
        v-text="$t('Shipping policy')"
      />
      <a
        class="text-xs text-blue-600 hover:text-blue-800"
        :href="localePath('/page/privacy-policy')"
        @click.prevent="modalPolicies?.toggleShow(true, 'privacy')"
        v-text="$t('Privacy policy')"
      />
    </div>
  </main>

  <default-checkout-modal-policies ref="modalPolicies" />

  <common-modal
    modal-id="checkoutConfirmEmail"
    :model-value="isShowModalConfirmEmail"
    :close-icon="false"
  >
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="my-4 text-center">
        <h4>{{ $t('Payment confirmation with email address', { email: userInfo.email }) }}</h4>
      </div>
      <div class="flex justify-center gap-4">
        <button
          class="btn text-center border border-[#6c757d] hover:border-[#545b62] py-2 w-full text-white bg-[#6c757d] hover:bg-[#5a6268]"
          @click="() => { userInfoForm?.emailRef.focus(); isShowModalConfirmEmail = false; }"
        >
          {{ $t('Edit email') }}
        </button>
        <button
          class="btn text-center border border-[#007bff] hover:border-[#0062cc] py-2 w-full text-white bg-[#007bff] hover:bg-[#0069d9]"
          @click="() => { userInfoForm.advancedEmailChecking.serverValidate = true; isShowModalConfirmEmail = false; submitCheckout(); }"
        >
          {{ $t('Continue checkout') }}
        </button>
      </div>
    </div>
  </common-modal>
  <common-modal
    modal-id="checkoutCreditCardDiscountModal"
    :model-value="paypalIsShowModalCreditCardDiscount"
    :close-icon="false"
  >
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="my-4 text-center">
        <h4>{{ $t('Do you want to checkout with credit card for 2% discount?') }}</h4>
      </div>
      <div class="flex justify-center gap-4">
        <button
          class="btn text-center border border-[#6c757d] hover:border-[#545b62] py-2 w-full text-white bg-[#6c757d] hover:bg-[#5a6268]"
          @click="() => { paypalModalConfirmed = true; paypalIsShowModalCreditCardDiscount = false; submitCheckout(); }"
        >
          {{ $t('Checkout with') }}
          <div class="overflow-hidden" style="width: 94px;">
            <i class="icon-card icons-sprite" style="--icon-height: 45;--icon-y-pos: 371; margin-left: -11px; scale: 1.5;" />
          </div>
        </button>
        <button
          class="btn text-center border border-[#007bff] hover:border-[#0062cc] py-2 w-full text-white bg-[#007bff] hover:bg-[#0069d9]"
          @click="() => { paypalIsShowModalCreditCardDiscount = false; useCreditCardDiscount(); }"
        >
          {{ $t('Use credit card') }}
        </button>
      </div>
    </div>
  </common-modal>

  <default-checkout-modal-confirm-product
    :order="order"
    @submit="reloadOrder"
  />
</template>

<style>
.icons-sprite {
  --icon-target-height: 25;
  --scale-fac: calc(var(--icon-height) / var(--icon-target-height));

  height: calc(var(--icon-target-height) * 1px);
  width: calc(150px / var(--scale-fac));
  background: var(--sprite-url) no-repeat;
  background-size: 100%;
  background-position-y: calc(var(--icon-y-pos) * -1px);
  display: block;
}

.icons-sprite-list-banks {
  --icon-target-height: 42;
}

.icons-sprite-stripe-iDEAL {
  --icon-height: 120;
  --icon-y-pos: 150;
}

.icons-sprite-stripe-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 193.5;
}

.icons-sprite-stripe-Sofort {
  --icon-height: 55;
  --icon-y-pos: 437;
}

.icons-sprite-stripe-Klarna {
  --icon-height: 148;
  --icon-y-pos: 172;
}

.icons-sprite-list-banks {
  --icon-target-height: 42;
}

.icons-sprite-iDEAL {
  --icon-height: 145;
  --icon-y-pos: 205;
}

.icons-sprite-Bancontact {
  --icon-height: 110;
  --icon-y-pos: 325;
}

.icons-sprite-Sofort {
  --icon-height: 50;
  --icon-y-pos: 810;
}

.icons-sprite-Klarna {
  --icon-height: 145;
  --icon-y-pos: 295;
}

.tooltip-content {
  display: none;
  position: absolute;
  background-color: rgb(66, 66, 66);
  width: -moz-max-content;
  width: max-content;
  border-radius: 10px;
  padding: 0.5rem;
  color: white;
  transform: translate(-60%, -100%);
  max-width: 250px;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  bottom: -9px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgb(66, 66, 66);
  left: 50%;
  transform: translateX(-50%);
}

.phone-tooltip:hover .tooltip-content {
  display: block;
}

@media (max-width: 767.9px) {
  .tooltip-content {
    transform: translate(-100%, -100%);
  }
  .tooltip-content::after {
    left: 86%;
    transform: translateX(-0%);
  }
}

#checkoutCreditCardDiscountModal {
  z-index: 999;
}
</style>

<style scoped>
.gateway:not(:first-child) {
  border-top: 0px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.gateway:not(:last-child) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
</style>
