<script lang="ts" setup>
import WarningShipLateChina from '~/components/Common/WarningShipLateChina.vue'

const localePath = useLocalePath()

const {
  relatedProducts,
  currentCountry,
  currentShippingMethod,
  order,

  canEditAddressInfo,
  hasHouseNumber,
  hasMailboxNumber,
  hasNote,

  isShowModalConfirmAddress,
  isShowModalEditInfo,
} = await useThankYouPage()

const { combos, productIdsInCombo } = campaignComboUI(toRef(order, 'products'), computed(() => order))
</script>

<template>
  <main class="container mt-5" dusk="thank-you-page">
    <div class="grid grid-cols-12 gap-6 mb-10">
      <div class="col-span-12 md:col-span-7">
        <div id="combo" />
        <h3 class="font-medium text-3xl mb-5" dusk="h3-receive-your-order">
          {{ $t('We’ve receive your order') }}
        </h3>
        <h5 class="font-medium text-xl mb-4">
          <span>
            {{ $t('Your order number is') }}
          </span>
          <span :id="`order-id-${order.order_number}`" class="text-primary">&nbsp;{{ order.order_number }}</span>
        </h5>
        <a
          :href="localePath(`/order/status/${order.access_token}`)"
          target="_blank"
          rel="noopener"
          class="text-contrast bg-primary p-3 uppercase border hover:opacity-70"
        >{{ $t('Track Your Order') }}</a>
        <br>
        <br>
        <i>
          {{ $t('Your order confirmation is sent to') }}
          <span id="email-purchase">{{ order.customer_email }}</span>
          <br>
          <small>
            {{ $t("Please check your spam folder if you haven't received the email.") }}
            <br>
            <a :href="localePath('/page/contact-us')" target="_blank" rel="noopener" class="text-primary">{{ $t("Or contact us to edit your email address") }}</a>
            {{ $t('if it is incorrect.') }}
          </small>
        </i>

        <div v-if="currentShippingMethod" class="mt-4">
          <h5 class="font-medium text-xl">
            {{ $t('Shipping method') }}
          </h5>
          <span class="capitalize">{{ $t(currentShippingMethod.description) }}</span>
          <span v-if="currentShippingMethod.name === SHIPPING_METHOD.express" class="text-red-600 font-medium"> ({{ $t('No cancellation allowed') }})</span>
          <WarningShipLateChina :order="order" />
        </div>

        <div class="mt-4">
          <h5 class="font-medium text-xl">
            {{ $t('Shipping to') }}
          </h5>
          <div>
            <a v-if="canEditAddressInfo" href="#" class="btn-text edit-address float-right" @click.prevent="isShowModalEditInfo = true">{{ $t('Edit') }}</a>
            <p v-if="order.customer_name" class="mb-2">
              <i><strong id="full-name-purchase">{{ order.customer_name }}</strong></i>
            </p>
            <p v-if="order.address" class="mb-2">
              <i id="address-purchase">{{ order.address }} {{ order.address_2 || '' }}</i>
            </p>
            <p v-if="order.city" class="mb-2">
              <i><span id="city-purchase">{{ order.city }}</span>&nbsp;<span id="region-purchase">{{ order.state || '' }}</span>&nbsp;<span id="postal-code-purchase">{{ order.postcode || '' }}</span></i>
            </p>
            <p v-if="hasHouseNumber" class="mb-2">
              <i id="house-number-purchase">
                <span id="house-number-purchase">
                  {{ $t('House number') }}: {{ order.house_number }}
                </span>
              </i>
            </p>
            <p v-if="hasMailboxNumber" class="mb-2">
              <i id="mailbox-number-purchase">
                <span id="mailbox-number-purchase">
                  {{ $t('Mailbox number') }}: {{ order.mailbox_number }}
                </span>
              </i>
            </p>
            <p v-if="currentCountry && currentCountry.name" class="mb-2">
              <i id="country-purchase">{{ currentCountry.name }}</i>
            </p>
            <p v-if="order.customer_phone" class="mb-2">
              <i id="phone-purchase">{{ order.customer_phone }}</i>
            </p>
          </div>
        </div>
        <!-- ReprintsRefundPolicy -->
        <div class="mt-4 grid gap-2">
          <div v-if="hasNote" class="bg-[#f6f7f9] p-4 grid gap-2" style="line-height: 1.3;">
            <h6 class="font-medium uppercase">
              {{ $t('Order note') }}
            </h6>
            <p class="text-[0.875em]">
              {{ order.order_note }}
            </p>
          </div>
          <div class="bg-[#f6f7f9] p-4 grid gap-2">
            <h6 class="font-medium uppercase">
              {{ $t('Shop with confidence') }}
            </h6>
            <p class="font-bold text-[0.875em]">
              {{ $t('Shopping on {domain} are safe and secure. Guaranteed!', { domain: $getHost() }) }}
            </p>
            <p class="text-[0.875em]">
              {{ $t("You'll pay nothing if unauthorized charges are made to your credit card as a result of shopping at {domain}", { domain: $getHost() }) }}
            </p>
          </div>
          <div class="bg-[#f6f7f9] p-4 grid gap-2">
            <h6 class="font-medium uppercase">
              {{ $t('Generic reprints & refund policies') }}
            </h6>
            <p class="text-[0.875em]">
              {{ $t('If, for any encounter product quality issues or defects. We can offer reproductions or refunds for your orders if there are order mistakes.') }}
            </p>
            <a :href="localePath('/page/return-policy')" target="_blank" class="btn-text text-[#17a2b8]">
              <small>{{ $t('Read our shipping and return policies') }}</small>
            </a>
          </div>
        </div>

        <lazy-common-thankyou-promotion class="mt-10 border-primary" btn-class="border-gray-300 text-contrast bg-primary" />
      </div>
      <div class="col-span-12 md:col-span-5">
        <h3 class="font-medium text-3xl mb-5">
          {{ $t('Order Summary') }}
        </h3>
        <div class="list-items">
          <default-order-product-item
            v-for="(product, index) in order.products.filter((p) => !productIdsInCombo.includes(p.id))"
            :key="index"
            :product="product"
            :convert-currency-code="order.currency_code"
            :convert-currency-rate="order.currency_rate"
            page="thank-you"
          />
          <lazy-default-campaign-combo-item
            v-for="combo in combos"
            :key="combo.id"
            :is-no-ship="false"
            :combo="combo"
            :title="combo.title"
            :thumb_url="combo.thumb_url"
            :price="combo.combo_price"
            :quantity="combo.quantity"
            :items="combo.items"
          />
        </div>
        <default-order-calculate-price :order="order" />
      </div>
    </div>

    <lazy-common-product-carousel
      v-if="relatedProducts?.length"
      class="container mt-10"
      title-class="text-center uppercase text-3xl"
      title="You may be interested in these products"
      :products="relatedProducts"
      static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
    >
      <template #default="{ product, index }">
        <default-common-product-item :product="product" :index="index" class="inline-block" />
      </template>
    </lazy-common-product-carousel>

    <lazy-default-order-modal-edit-info
      :order="order"
      :is-show="isShowModalEditInfo"
      @refresh-order="useRouter().go(0)"
      @is-show-modal="(val) => { isShowModalEditInfo = val }"
    />
    <lazy-default-order-modal-confirm-address
      :order="order"
      :is-show="isShowModalConfirmAddress"
      @is-show-modal="(val) => { isShowModalConfirmAddress = val; }"
      @show-edit-address="() => { isShowModalEditInfo = true; }"
    />
  </main>
</template>

<style scoped>
.order-id {
  color: rgba(var(--color-primary), 0.8);
}
</style>
