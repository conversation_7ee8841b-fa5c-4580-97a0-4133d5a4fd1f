<script lang="ts" setup>
const route = useRoute()
const { $fetchDefault, $fetchWrite } = useNuxtApp()
const localePath = useLocalePath()
const token = route.params.token

const isLoadingSubmit = ref(true)
const isSubmitSuccess = ref(false)
const decryptedEmail = ref('')

try {
  if (!token) {
    throw new Error('No Token')
  }
  const { data, success } = await $fetchDefault<ResponseData<{ email: string }>>(`${$api.API_UNSUBSCRIBE_EMAIL}/${token}`)

  if (!success) {
    throw new Error('Email not found')
  }

  decryptedEmail.value = data.email
  isLoadingSubmit.value = false
}
catch (err) {
  showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
}

async function unsubscribe() {
  isLoadingSubmit.value = true
  try {
    const response = await $fetchWrite(`${$api.API_UNSUBSCRIBE_EMAIL}/${token}`, { method: $method.post })
    if (response?.success) {
      isSubmitSuccess.value = true
    }
    isLoadingSubmit.value = false
  }
  catch (_err) {
    isLoadingSubmit.value = false
  }
}
</script>

<template>
  <main class="container py-6 text-center">
    <div v-if="isLoadingSubmit">
      <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
    </div>
    <div v-else-if="isSubmitSuccess">
      <h4 class="text-medium text-3xl">
        {{ $t('Unsubscribe Success') }}
      </h4>
      <h5 class="text-medium text-xl mt-2">
        {{ decryptedEmail }}
      </h5>
      <div class="mt-4" align="center">
        <img src="/images/unsubscribe.webp" alt="unsubscribe" loading="lazy" height="400">
      </div>
      <div class="mt-4">
        <nuxt-link :to="localePath('/')" class="text-primary">
          {{ $t('Back to shopping') }}
        </nuxt-link>
      </div>
    </div>
    <div v-else>
      <h4 class="text-medium text-3xl">
        {{ $t('Unsubscribe') }}
      </h4>
      <h5 class="text-medium text-xl mt-2">
        {{ decryptedEmail }}
      </h5>
      <h6>
        {{ $t('We are sorry to see you go!') }}
      </h6>
      <div align="center">
        <img src="/images/unsubscribe.webp" alt="unsubscribe" loading="lazy" height="400">
      </div>
      <p>{{ $t('Are you sure you wish to unsubscribe from all emails') }}</p>
      <div class="mt-4">
        <nuxt-link class="btn-fill-black px-12 py-2 uppercase" :to="localePath('/')">
          {{ $t('No') }}
        </nuxt-link>
        <nuxt-link class="btn-fill px-12 py-2 ml-5" @click="unsubscribe">
          {{ $t('Yes') }}
        </nuxt-link>
      </div>
    </div>
  </main>
</template>

<style scoped>

</style>
