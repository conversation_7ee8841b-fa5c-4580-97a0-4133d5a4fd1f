<script lang="ts" setup>
const {
  email,
  orderNumber,

  resendConfirmation,
  onSubmit
} = useOrderTrack()

const { lastOrderUrl } = useLastOrder()
</script>

<template>
  <main class="container py-6 min-h-[70vh]">
    <h1 class="text-4xl font-medium">
      {{ $t('Track Your Order') }}
    </h1>
    <form action="#" method="POST" class="mt-8" @submit.prevent="onSubmit">
      <div>
        <label for="email">
          {{ $t('Email') }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-1 md:flex">
          <input
            id="email"
            v-model="email"
            class="border border-gray-300 px-3 py-1 w-full focus:border-blue-500/50 md:border-r-0 rounded-[var(--sp-border-radius-2)] !md:rounded-r-none"
            type="email"
            :placeholder="$t('Email')"
            required
            autofocus
          >
          <button
            type="button"
            class="border border-gray-300 bg-gray-500 text-white whitespace-nowrap px-3 py-1 mt-4 md:mt-0 rounded-[var(--sp-border-radius-2)] !md:rounded-l-none"
            @click="resendConfirmation"
          >
            {{ $t('Resend Confirmation Email') }}
          </button>
        </div>
      </div>
      <div class="mt-4">
        <label for="order_number">
          {{ $t('Order Number') }} <span class="text-red-500">*</span>
        </label>
        <div class="mt-1">
          <input
            id="order_number"
            v-model="orderNumber"
            class="border border-gray-300 px-3 py-1 w-full focus:border-blue-500/50"
            type="text"
            :placeholder="$t('Order Number')"
            required
          >
        </div>
      </div>

      <div class="mt-6">
        <button
          type="submit"
          class="border border-gray-300 bg-red-500 text-white px-5 py-1"
        >
          {{ $t('Track Order') }}
        </button>
        <nuxt-link
          v-if="lastOrderUrl"
          type="button"
          class="border border-gray-300 bg-blue-500 text-white px-5 py-1 ml-4"
          :to="`${lastOrderUrl}?utm_source=page&utm_medium=track-order`"
        >
          {{ $t('View Last Order') }}
        </nuxt-link>
      </div>
    </form>
  </main>
</template>
