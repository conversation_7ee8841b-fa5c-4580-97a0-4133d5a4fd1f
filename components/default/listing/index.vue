<script lang="ts" setup>
const localePath = useLocalePath()
const {
  filterValue,
  listingProduct,
  filterProduct,
  currentSortType,
  resetData,
  updatePrice
} = useListing()

await resetData()
</script>

<template>
  <main id="listingPage" class="container flex flex-wrap">
    <div :class="{ 'md:w-[66%] lg:w-[75%]': storeInfo().isShowFilterBox1 }" class="md:order-2 w-full lg:pl-2">
      <div class="flex flex-wrap justify-between md:p-3 items-center">
        <div class="flex items-center max-w-full md:max-w-1/2 <md:flex-wrap">
          <h1 class="text-2xl font-bold text-overflow-hidden">
            {{ filterValue.title }}:
          </h1>
          <div v-if="storeInfo().enable_search" class="ml-2 min-w-max">
            {{ $t('result', { count: listingProduct.total || 0 }) }}
          </div>
        </div>
        <div v-if="storeInfo().enable_search">
          <span class="pr-2">{{ $t('Sort by') }}:</span>
          <common-dropdown class="inline">
            <div class="font-medium">
              {{ $t(currentSortType?.text) }}
            </div>
            <template #content>
              <nuxt-link
                v-for="sortItem in filterValue.sortTypeList"
                :key="sortItem.value"
                sp-action="sort-listing"
                :to="getFilterUrl('sort', sortItem.value)"
                class="w-full min-w-max px-2 btn-text block"
                :class="{ 'bg-primary text-contrast': filterValue.sort === sortItem.value }"
              >
                {{ $t(sortItem.text) }}
              </nuxt-link>
            </template>
          </common-dropdown>
        </div>
      </div>
      <div
        v-if="filterValue.pageType !== 'artist' && filterProduct.collection_group.length > 0 && !storeInfo().disable_related_collection"
        class="md:px-3 w-full flex gap-2 md:ml-1 pb-1 small-scroll"
      >
        <span class="min-w-max hidden md:block">{{ $t('Related collections') }}: </span>
        <div class="w-full overflow-auto">
          <common-collection-item
            v-for="(collection, index) in filterProduct.collection_group"
            :key="index"
            class="mx-1"
            :value="collection.name"
            :slug="useRoute().params.collection === collection.slug ? '/collection' : `/collection/${collection.slug}`"
            :active="useRoute().params.collection === collection.slug"
          />
        </div>
      </div>
      <div
        v-if="listingProduct.bannerUrl"
        class="bg-cover bg-center bg-no-repeat w-full mt-2.5"
        :style="`background-image: url(${$imgUrl({
          path: listingProduct.bannerUrl,
          type: 'full'
        })}); aspect-ratio: 13 / 2;`"
      />
      <div
        :class="{
          'lg:grid-cols-4 md:grid-cols-3': !storeInfo().isShowFilterBox1,
          'lg:grid-cols-3': storeInfo().isShowFilterBox1
        }"
        class="grid grid-cols-2"
      >
        <div
          v-for="(product, index) in listingProduct.products"
          :key="index"
          class="p-1 md:p-3"
        >
          <default-common-product-item
            :product="product"
            :color="filterValue.color?.name"
          />
        </div>
      </div>
      <CommonPagination v-if="listingProduct" class="flex justify-end md:p-3" :last-page="listingProduct.lastPage" :current-page="listingProduct.currentPage" @input="(pageNumber) => { $router.push(getFilterUrl('page', pageNumber)) }" />
    </div>
    <div v-if="storeInfo().isShowFilterBox1" class="h-max sticky top-[100px] md:order-1 text-[#C4C4C4] w-full md:w-[33%] lg:w-[25%] lg:pr-2">
      <div>
        <nuxt-link
          :to="localePath($route.path)"
          class="btn-fill px-2 rounded-full text-xs"
        >
          {{ $t('Clear filters') }}
        </nuxt-link>
      </div>

      <div v-if="filterValue.filterCategories && filterValue.filterCategories.length">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Explore') }}
        </p>
        <div class="mb-8">
          <common-category-filter-item
            v-for="(category, index) in filterValue.filterCategories"
            :key="index"
            :category="category"
            :current-category="filterValue.category"
          />
        </div>
      </div>

      <div v-if="filterValue.filterTemplates && filterValue.filterTemplates.length">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Product') }}
        </p>
        <div class="mb-8">
          <common-template-filter-item
            v-for="(template, index) in filterValue.filterTemplates"
            :key="index"
            :template="template"
            :current-template="filterValue.template"
          />
        </div>
      </div>

      <div>
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Price') }}
        </p>
        <p class="text-center  text-555 mb-3">
          {{ $formatPrice(filterValue.priceFilter[0]) }} - {{ $formatPrice(filterValue.priceFilter[1]) }}
        </p>
        <common-listing-price-slider
          v-model="filterValue.priceFilter"
          :min="filterProduct.min_price"
          :max="filterProduct.max_price"
          @change="updatePrice"
        />
      </div>

      <div v-if="filterProduct && filterProduct.color_group && filterProduct.color_group.length > 0" class="filter-color mt-3">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Color') }}
        </p>
        <div class="flex flex-wrap">
          <nuxt-link
            v-for="(color, index) in filterProduct.color_group"
            :key="index"
            :to="getFilterUrl('color', filterValue.color?.name === color ? '' : color)"
            sp-action="change_color"
          >
            <common-color-item
              :color="color"
              size="xl"
              class="hover:shadow-color-active"
              :active="filterValue.color?.name === color"
            />
          </nuxt-link>
        </div>
      </div>
    </div>
  </main>
</template>
