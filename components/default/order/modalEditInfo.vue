<script lang="ts" setup>
import type { PropType, ref } from 'vue'

const { order, isShow } = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  },
  isShow: {
    type: Boolean,
    default: false
  }
})

const userInfoForm = ref()

const $emit = defineEmits(['isShowModal', 'refreshOrder'])

const {
  currentCountry,
  userInfo,
  isConfirmAddr,
  isValidOrderNumber,

  updateCustomerData,
} = useModalEditInfo(order, $emit, userInfoForm)
</script>
<template>
  <common-modal modal-id="modalEditAddress" :model-value="isShow" :close-icon="false" @close-modal="$emit('isShowModal', false)">
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="flex justify-between items-center">
        <h3 class="font-medium text-xl">
          {{ $t('Edit address') }}
        </h3>
      </div>
      <div class="mt-2 grid px-3" style="max-height: 70vh; overflow-x: hidden;">
        <default-checkout-form
          ref="userInfoForm"
          :user-info="userInfo"
          :is-phone-number-required="order.shipping_method === SHIPPING_METHOD.express"
          :is-country-select-hidden="true"
        >
          <template #pre_shipping_info>
            <default-common-input
              id="ordernumber"
              :label="$t('Order Number (Check in email)')"
              :model-value="userInfo.order_number"
              :state="(userInfo.order_number.length === 0) ? false : (isValidOrderNumber || undefined)"
              class="mt-3"
              @update:model-value="(val) => { userInfo.order_number = val }"
            />
          </template>
        </default-checkout-form>
        <div class="border p-2 font-medium text-xl mt-3">
          <span class="inline-block" :class="`vti__flag ${currentCountry.code && currentCountry.code.toLowerCase()}`" /> {{ currentCountry.name }}
        </div>
      </div>
      <div class="mt-2 mx-4 grid">
        <div class="cursor-pointer email-checkbox flex items-center mt-3" @click="isConfirmAddr = !isConfirmAddr">
          <i class="mr-1" :class="isConfirmAddr ? 'icon-sen-checkbox-marked-outline':'icon-sen-checkbox-blank-outline'" />
          <small>
            {{ $t('I confirm this is a valid address to receive the order') }}
          </small>
        </div>
        <button class="bg-primary mt-4 btn border border-gray-300 py-2 text-white" :disabled="!isConfirmAddr" @click="updateCustomerData">
          {{ $t('Save changes') }}
        </button>
      </div>
    </div>
  </common-modal>
</template>
<style scoped>
.close {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  cursor: pointer;
}
.close:hover {
  opacity: 1;
}

button:disabled {
  opacity: 0.5;
}
</style>
