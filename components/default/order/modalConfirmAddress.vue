<script lang="ts" setup>

const { order, isShow } = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  },
  isShow: {
    type: Boolean,
    default: false
  }
})

const $emit = defineEmits(['isShowModal', 'showEditAddress'])

const {
  currentCountry,
  confirmAddress,
  showModalEditAddress,
} = useModalConfirmAddress(order, $emit)
</script>
<template>
  <common-modal modal-id="modalConfirmAddress" :model-value="isShow" :close-icon="false" @close-modal="$emit('isShowModal', false)">
    <div class="w-200 p-4 max-w-[90vw]">
      <div class="flex justify-between items-center">
        <h3 class="font-medium text-xl">
          {{ $t('Confirm Address') }}
        </h3>
      </div>
      <div class="mt-8 text-center">
        <h5 class="font-medium text-lg">
          <div class="text-red-600" v-text="$t('Our system has detected that your address may be invalid. Please confirm the shipping address within 12 hours; otherwise, you will be liable in case of non-delivery.')" />
          <em>
            <br>
            "{{ order.address }} {{ order.address_2 || '' }}
            <br>
            {{ order.city }} {{ order.state || '' }} {{ order.postcode || '' }}
            <br>
            <span class="inline-block" :class="`vti__flag ${currentCountry.code && currentCountry.code.toLowerCase()}`" /> {{ currentCountry.name }}"
            <br>
          </em>
        </h5>
        <h5 class="font-medium text-xl mt-4 mb-2" v-text="$t('Do you want to edit address?')" />
      </div>
      <div class="flex justify-center gap-4">
        <button
          class="btn text-center border border-[#6c757d] hover:border-[#545b62] py-2 w-full text-white bg-[#6c757d] hover:bg-[#5a6268]"
          @click="confirmAddress"
        >
          {{ $t("It's correct") }}
        </button>
        <button
          class="btn text-center border border-[#007bff] hover:border-[#0062cc] py-2 w-full text-white bg-[#007bff] hover:bg-[#0069d9]"
          @click="showModalEditAddress"
        >
          {{ $t('Edit Address') }}
        </button>
      </div>
    </div>
  </common-modal>
</template>
<style scoped>
.btn {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
</style>
