<script lang="ts" setup>
const props = defineProps({
  currentProduct: {
    type: Object as PropType<Product>,
    default: undefined
  }
})

const currentProduct = toRef(() => props.currentProduct)

const {
  starCounts,
  filtersList,
  isLoadingReviews,
  productReviews,

  changePage,
  changeFilter,
} = useCampaignReview(currentProduct)

function viewImage (imageList:{src: string, type: any}[], index:number) {
  uiManager().viewImage(imageList.map(item => item?.src), index)
}
</script>
<template>
  <div v-if="productReviews?.reviewSummary.summary?.review_count > 0" id="productReviewBox" class="mb-14">
    <div class="grid grid-cols-12 review-header">
      <div class="col-span-8">
        <h4 class="uppercase font-medium text-2xl">
          {{ $t('Product reviews') }}
        </h4>
      </div>
      <div class="col-span-4">
        <lazy-default-campaign-modal-check-review-order />
      </div>
    </div>
    <div class="grid grid-cols-3 gap-4 mt-4">
      <!-- Reviews Summary -->
      <div class="col-span-3 lg:col-span-1">
        <div class="flex gap-8">
          <h2 class="font-bold text-4xl">
            {{ productReviews.reviewSummary.summary.average_rating }}
          </h2>
          <div>
            <default-product-review-stars
              :model-value="parseInt(productReviews.reviewSummary.summary.average_rating as string)"
              :container-class="'flex justify-around'"
              :common-class="'text-[#ffc107] px-1 text-sm'"
              disabled
            />
            <span class="text-capitalize">{{ $t('review', { count: productReviews.reviewSummary.summary.review_count }) }}</span>
          </div>
        </div>

        <!-- Reviews counts block -->
        <div class="grid mt-2 gap-1">
          <div v-for="(starCount, starIndex) in starCounts" :key="starCount" class="flex gap-2 items-center md:max-w-[60vw]">
            <default-product-review-stars
              :model-value="5 - starIndex"
              :container-class="'flex justify-around'"
              :common-class="'text-[#ffc107] px-[0.1rem] text-xs'"
              disabled
            />
            <div class="bg-[#E9E9E9] rounded-[63px] h-[13px] w-full">
              <div class="bg-[#C7C7C7] rounded-[63px] h-[13px]" :style="{ width: `${parseFloat(productReviews.reviewSummary.summary[starCount]) / productReviews.reviewSummary.summary.review_count * 100}%` }" />
            </div>
            <div class="min-w-[4rem]">
              <span>
                {{ productReviews.reviewSummary.summary[starCount] }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-3 lg:col-span-2">
        <div class="mb-8">
          <h3 class="font-medium text-2xl">
            {{ $t('All Images') }} ({{ productReviews.reviewSummary.files.length }})
          </h3>
          <div v-if="productReviews.reviewSummary.files?.length" class="flex flex-wrap gap-2">
            <common-image
              v-for="(file, index) in productReviews.reviewSummary.files"
              :key="index"
              img-class="object-cover cursor-zoom-in h-25 w-25"
              :image="{ path: file.src, type: (file.type === 'video') ? 'product-review-video' : 'product-review-image'}"
              @click="viewImage(productReviews.reviewSummary.files, index)"
            />
          </div>
        </div>
        <div class="grid lg:flex gap-3">
          <h3 class="font-medium text-2xl">
            {{ $t('Filter by') }}:
          </h3>
          <div class="flex flex-wrap gap-4">
            <span
              v-for="filter in filtersList"
              :key="filter.value"
              class="cursor-pointer bg-[#E9E9E9] text-[#838383] py-[2px] px-4 rounded-[50rem] text-medium border-[2px] border-solid"
              :class="[
                (productReviews.filter === filter.value) ? 'border-[#FFC107]' : 'border-[#e0e0e0]'
              ]"
              @click="changeFilter(filter.value)"
            >
              {{ $t(filter.text) }}
              <span v-if="filter.icon" :class="filter.icon" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-4 relative">
      <div v-if="isLoadingReviews" class="absolute w-full h-full">
        <div class="flex w-full h-full justify-center items-center bg-[rgba(221,221,221,0.6)]">
          <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
        </div>
      </div>
      <template v-if="productReviews.reviews?.data?.length">
        <div v-for="(review, index) in productReviews.reviews.data" :key="index" class="grid grid-cols-3 gap-4 mt-6 pt-6 border-t border-gray-300">
          <div class="col-span-3 lg:col-span-1">
            <div class="flex items-center gap-4">
              <img
                :src="review.avatar_url || `${cdnURL}images/default-user-icon.webp`"
                onerror="this.src = `${cdnURL}images/default-user-icon.webp`"
                loading="lazy"
                :alt="review.customer_name"
                class="max-w-[65px] max-h-[65px]"
              >
              <div>
                <h5 class="text-lg">
                  {{ review.customer_name }}
                </h5>
                <span class="text-[#dc3545] font-semibold text-sm">{{ $t('Purchased in') }} {{ review.customer_location }}</span>
              </div>
            </div>
          </div>
          <div class="col-span-3 lg:col-span-2 flex flex-col gap-2">
            <div class="flex">
              <default-product-review-stars
                :model-value="parseFloat(review.average_rating)"
                :container-class="'flex justify-around'"
                :common-class="'text-[#ffc107] px-1 text-xl'"
                disabled
              />
            </div>
            <div class="flex gap-2 flex-wrap">
              <nuxt-link
                v-if="review.product_url"
                :to="review.product_url"
                class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold hover:bg-[rgba(75,178,50,0.2)] transition"
              >
                {{ review.product_name }}
              </nuxt-link>
              <span
                v-else
                class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
              >{{ review.product_name }}</span>
              <span
                v-if="review.product_size"
                class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
              >{{ $t('Size') }} <span class="uppercase">{{ review.product_size }}</span></span>
              <span
                v-if="review.product_color"
                class="flex items-center gap-2 text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
              ><span>{{ $t('Color') }}:</span><span class="rounded-1 w-[0.8em] h-[0.8em] border-gray-300 border-[0.1px]" :style="`background: ${colorVal(review.product_color)}`" /></span>
            </div>
            <p>
              {{ review.comment }}
            </p>
            <div v-if="review.files?.length" class="flex">
              <common-image
                v-for="(file, fileIndex) in review.files"
                :key="fileIndex"
                img-class="h-25 w-25 mx-1 !object-cover cursor-zoom-in"
                :image="{ path: file.src, type: (file.type === 'video') ? 'product-review-video' : 'product-review-image'}"
                @click="viewImage(review.files, fileIndex)"
              />
            </div>
            <p class="text-[#808080]">
              {{ $t('Reviewed on') }} {{ review.created_at }}
            </p>
          </div>
        </div>
      </template>
      <div v-else class="mt-6" align="center">
        {{ $t('There are no reviews yet') }}
      </div>
    </div>
    <CommonPagination
      class="<md:justify-center justify-end"
      :current-page="productReviews.reviews.current_page"
      :last-page="productReviews.reviews.last_page"
      @input="(pageNumber) => { changePage(pageNumber) }"
    />
  </div>
</template>
