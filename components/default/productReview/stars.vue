<script lang="ts" setup>
const props = defineProps({
  iconEmpty: {
    type: String,
    default: 'icon-sen-star-empty',
  },
  iconHalf: {
    type: String,
    default: 'icon-sen-star-half',
  },
  iconFull: {
    type: String,
    default: 'icon-sen-star-fill',
  },

  containerClass: {
    type: String,
    default: 'flex justify-around my-2'
  },
  commonClass: {
    type: String,
    default: 'text-[#ffc107] px-1',
  },
  disabled: {
    type: <PERSON>olean,
    default: null
  },

  modelValue: {
    type: Number,
    default: 0,
  },
  count: {
    type: Number,
    default: 5,
  },
})
const {
  iconEmpty,
  iconHalf,
  iconFull,
  commonClass,
  disabled,

  count,
} = props
const modelValue = toRef(props, 'modelValue')

const starsList = computed((): string[] => {
  const result: string[] = []

  for (let index = 1; index <= count; index++) {
    const roundedP5 = Math.round(modelValue.value * 2) / 2
    const remaining = index - roundedP5

    if (index <= modelValue.value) {
      result.push(iconFull)
    } else if (remaining >= 0.5 && remaining < 1) {
      result.push(iconHalf)
    } else {
      result.push(iconEmpty)
    }
  }

  return result
})
</script>
<template>
  <div :class="containerClass" :disabled="disabled">
    <i
      v-for="(starClass, index) of starsList"
      :key="index"
      class="star"
      :class="{
        [commonClass]: true,
        [starClass]: true,
      }"
      @click="() => {
        if (!disabled) {
          $emit('update:modelValue', index + 1)
        }
      }"
    />
  </div>
</template>
<style scoped>
div:not([disabled]) > .star {
  cursor: pointer;
}

div:not([disabled]) > .star:hover {
  transform: scale(1.8);
  transition: transform 0.15s;
}
</style>
