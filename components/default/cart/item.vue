<script lang="ts" setup>
const props = defineProps({
  index: {
    default: 0,
    type: Number
  },
  cartItem: {
    default: undefined,
    type: Object as PropType<CartItem>
  }
})
const localePath = useLocalePath()
const {
  isHasCampaignData,
  campaignData,
  isEditCart,
  isShowModalDeleteCartItem,
  currentVariant,
  updateCartItem,
  duplicateCartItem,
  removeCartItem,
  getProductUrl,
  updateQuantity,
  totalPrice,
  totalPriceBundleWithCustomOptionFee,
  priceWithCustomOptionFee,
  totalPriceWithCustomOptionFee
} = useCartItem(props.cartItem as CartItem)

const suffix = computed(() => {
  if (!props.cartItem?.options) {
    return ''
  }
  const suffix = [] as string[]
  const options = props.cartItem?.optionList
  if (!options) {
    return ''
  }
  Object.keys(options).forEach((key) => {
    if (options[key].length > 1) {
      suffix.push((props.cartItem as CartItem).options[key])
    }
  })

  if (suffix.length === 0) {
    return ''
  }

  return suffix.join(' / ')
})
</script>

<template>
  <div v-if="isHasCampaignData" class="p-4 flex gap-2 border-b" data-test-id="cart-item">
    <!-- Use base64 image if available for AI mockup campaigns -->
    <img
      v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
      :src="cartItem.design_image_base64"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      @click="uiManager().viewImage(cartItem.design_image_base64)"
    >
    <common-image
      v-else
      img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: cartItem?.thumb_url,
        color: cartItem?.options?.color
      }"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(cartItem?.thumb_url)"
    />
    <div
      :class="{ '<md:hidden': !isEditCart }"
      class="w-full max-w-[calc(100%-90px)] grid grid-cols-12 h-min gap-2"
    >
      <div class="col-span-12 xl:col-span-7 grid grid-cols-4 gap-2">
        <div class="col-span-4 flex">
          <nuxt-link
            :to="localePath(getProductUrl(cartItem?.product_url))"
            class="w-full"
          >
            <h5 class="text-overflow-hidden font-bold" :title="cartItem?.campaign_title">
              {{ cartItem?.campaign_title }}
            </h5>
          </nuxt-link>
          <span class="xl:hidden btn-text" data-test-id="cart-item-remove" @click="isShowModalDeleteCartItem = true"><i class="icon-sen-delete" /></span>
        </div>
        <template v-if="campaignData.products && campaignData.products.length > 1">
          <span
            v-if="cartItem?.personalized === 1 || cartItem?.personalized === 2"
            class="col-span-4 md:col-span-2 capitalize"
            data-test-id="cart-item-product-name"
            data-test-prop="false"
          >
            {{ cartItem?.product_name }}
          </span>
          <common-dropdown
            v-else
            class="col-span-4 md:col-span-2"
            btn-class="text-overflow-hidden btn-border w-full text-sm pl-2 py-1 z-1"
            dropdown-class="md:min-w-full"
            data-test-id="cart-item-product-name"
            data-test-prop="true"
          >
            <span>{{ cartItem?.product_name }}</span>
            <template #content>
              <div
                v-for="(product, productIndex) in campaignData.products"
                :key="productIndex"
                sp-action="change_product"
                data-test-id="change-product"
                class="py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast)"
                :class="{ 'bg-primary text-contrast': product.id === cartItem?.product_id }"
                @click="product.id === cartItem?.product_id ? '' : updateCartItem('product', product)"
              >
                {{ product.name }}
              </div>
            </template>
          </common-dropdown>
        </template>
        <template
          v-if="cartItem?.optionList && cartItem.options"
        >
          <template
            v-for="(key, optionListIndex) in Object.keys(cartItem?.optionList)"
          >
            <span
              v-if="cartItem?.optionList[key].length > 1 && (cartItem?.personalized === 1 || cartItem?.personalized === 2)"
              :key="`text-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1 capitalize"
              data-test-id="cart-item-option"
              data-test-prop="false"
            >
              {{ cartItem?.options[key] }}
            </span>
            <common-dropdown
              v-else-if="cartItem?.optionList[key].length > 1"
              :key="`dropdown-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1"
              btn-class="capitalize text-overflow-hidden btn-border w-full text-sm pl-2 py-1"
              dropdown-class="md:min-w-full"
              data-test-id="cart-item-option"
              :data-test-prop="key"
            >
              <span class="flex">
                <common-color-item
                  v-if="key === 'color' && cartItem?.options[key]"
                  :color="cartItem?.options[key]"
                  :title="cartItem?.options[key]"
                  size="xs"
                  data-test-id="product-color"
                  parent-class="mr-2"
                />
                <span>{{ cartItem.options[key] }}</span>
              </span>
              <template #content>
                <div
                  v-for="(optionItem, optionIndex) in cartItem?.optionList[key]"
                  :key="optionIndex"
                  :sp-action="`change_${key}`"
                  class="capitalize py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center"
                  :class="{ 'bg-primary text-contrast': optionItem === cartItem.options[key] }"
                  :data-test-id="`cart-item-option-change-${key}`"
                  @click="optionItem === (cartItem?.options && cartItem.options[key]) ? '' : updateCartItem('option', { optionName: key, optionItem })"
                >
                  <lazy-common-color-item v-if="key === 'color'" :color="optionItem" size="sm" class="inline-block mr-2" />
                  {{ optionItem }}
                </div>
              </template>
            </common-dropdown>
          </template>
        </template>
        <div v-if="cartItem?.custom_options" class="col-span-4">
          <ul class="pl-5 list-disc">
            <li v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
              <div v-if="key === 'customImage'">
                {{ $t('Your image') }}:
                <a
                  :href="$imgUrl({ path: value, type: 'full' })"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(value)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
              </div>
            </li>
          </ul>
        </div>
        <div v-if="cartItem?.customer_custom_options?.length" class="col-span-4 <md:text-center">
          <ul v-for="(groupOptions, groupNumber) in cartItem.customer_custom_options" :key="groupNumber" class="pl-5 list-disc mt-3">
            <li v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
              <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                  :href="$imgUrl({ path: customOption.value as string, type: 'full' })"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(customOption.value as string)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="col-span-12 xl:col-span-5 flex">
        <div class="grid xl:grid-cols-10 grid-cols-6 w-full text-center gap-2">
          <div class="<xl:hidden col-span-3 font-medium" data-test-id="cart-item-price">
            <div v-if="totalPriceBundleWithCustomOptionFee" class="center-flex flex-wrap gap-x-2">
              <span>
                {{ $formatPrice(priceWithCustomOptionFee * (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100, cartItem?.currency_code) }}
              </span>
              <del class="text-gray-500">
                {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
              </del>
            </div>
            <span v-else>
              {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
            </span>
          </div>
          <default-cart-quantity
            class="col-span-6 sm:col-span-3 md:col-span-2 xl:col-span-4"
            :quantity="cartItem?.quantity || 0"
            @duplicate-cart-item="duplicateCartItem"
            @update-quantity="updateQuantity"
          />
          <div class="col-span-6 sm:col-span-3 md:col-span-2 xl:col-span-3 font-medium <xl:center-flex xl:(flex flex-col gap-3)" data-test-id="cart-item-total-price">
            <div v-if="totalPriceBundleWithCustomOptionFee" class="center-flex flex-wrap gap-x-2">
              <span>
                {{ $formatPrice(totalPriceBundleWithCustomOptionFee, cartItem?.currency_code) }}
              </span>
              <del class="text-gray-500">
                {{ $formatPrice(totalPriceWithCustomOptionFee, cartItem?.currency_code) }}
              </del>
            </div>
            <span v-else>
              {{ $formatPrice(totalPriceWithCustomOptionFee, cartItem?.currency_code) }}
            </span>
            <span class="<xl:hidden ml-2 btn-text" data-test-id="cart-item-remove" @click="isShowModalDeleteCartItem = true"><i class="icon-sen-delete" /></span>
          </div>
          <button
            v-if="isEditCart"
            class="md:hidden col-span-6 md:col-span-2 btn-border-fill-red center-flex text-sm py-1 px-2 w-full text-overflow-hidden"
            :title="$t('done')"
            data-test-id="cart-item-edit-done"
            @click="isEditCart = false"
          >
            {{ $t('Done') }}
          </button>
          <button
            class="<md:hidden xl:hidden col-span-6 md:col-span-2 btn-border-fill-red center-flex text-sm py-1 px-2 w-full text-overflow-hidden"
            :title="$t('Add more item')"
            data-test-id="cart-item-add-more"
            @click="duplicateCartItem"
          >
            {{ $t('Add more item') }}
          </button>
        </div>
      </div>
    </div>
    <div
      :class="{ '<md:hidden': isEditCart }"
      class="md:hidden w-full max-w-[calc(100%-90px)]"
    >
      <div class="flex">
        <nuxt-link :to="localePath(getProductUrl(cartItem?.product_url))" class="w-full">
          <h5 class="text-overflow-hidden" :title="cartItem?.campaign_title" data-test-id="cart-item-campaign-title">
            {{ cartItem?.product_name }}
          </h5>
        </nuxt-link>
        <span
          class="xl:hidden btn-text"
          data-test-id="cart-item-remove"
          @click="isShowModalDeleteCartItem = true"
        ><i class="icon-sen-delete" /></span>
      </div>
      <div class="font-medium uppercase">
        {{ suffix }}
      </div>
      <div v-if="cartItem?.custom_options">
        <div v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
          <div v-if="key === 'customImage'">
            {{ $t('Your image') }}:
            <a
              :href="$imgUrl({ path: value, type: 'full' })"
              target="_blank"
              class="btn-text text-blue-900"
              @click.prevent="uiManager().viewImage(value)"
            >{{ $t('View image') }}</a>
          </div>
          <div v-else class="text-overflow-hidden">
            {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
          </div>
        </div>
      </div>
      <div v-if="cartItem?.customer_custom_options.length">
        <div v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
          <div v-if="key === 'customImage'">
            {{ $t('Your image') }}:
            <a
              :href="$imgUrl({ path: value, type: 'full' })"
              target="_blank"
              class="btn-text text-blue-900"
              @click.prevent="uiManager().viewImage(value)"
            >{{ $t('View image') }}</a>
          </div>
          <div v-else class="text-overflow-hidden">
            {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
          </div>
        </div>
      </div>
      <div class="mb-1 font-weight-500">
        <span data-test-id="cart-item-qty">
          x{{ cartItem?.quantity }}
        </span>
        <span class="ml-3 font-weight-500">
          <span v-if="currentVariant?.out_of_stock" class="text-danger" data-test-id="cart-item-oos">
            {{ $t('Out of stock') }}
          </span>
          <div v-else-if="totalPriceBundleWithCustomOptionFee" class="flex flex-wrap gap-x-2">
            <span>
              {{ $formatPrice(totalPriceBundleWithCustomOptionFee, cartItem?.currency_code) }}
            </span>
            <del class="text-gray-500">
              {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
            </del>
          </div>
          <span v-else-if="totalPrice" data-test-id="cart-item-total-price">
            {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
          </span>
        </span>
      </div>
      <div class="flex">
        <button class="btn-border-fill-red px-2 py-1 text-sm w-full" data-test-id="cart-item-add-more" @click="duplicateCartItem">
          {{ $t('Add more item') }}
        </button>
        <button class="btn-text-red w-fit px-2 ml-3" data-test-id="cart-item-edit" @click="isEditCart = true">
          <u>{{ $t('Edit') }}</u>
        </button>
      </div>
    </div>
    <default-cart-confirm-delete
      :state="isShowModalDeleteCartItem"
      @confirm="removeCartItem"
      @close="isShowModalDeleteCartItem = false"
    />
  </div>
  <default-cart-fallback-item v-else :cart-item="cartItem" />
</template>

<style lang="scss">
</style>
