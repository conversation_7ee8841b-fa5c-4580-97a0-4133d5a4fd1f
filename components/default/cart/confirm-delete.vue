<script setup lang="ts">
defineProps<{
  state: boolean
}>()
defineEmits(['confirm', 'close'])

const yesButtonRef = ref<HTMLElement>()

function focusButton () {
  nextTick(() => {
    yesButtonRef.value?.focus()
  })
}
</script>

<template>
  <common-modal
    :model-value="state"
    @close-modal="$emit('close')"
    @shown="focusButton"
  >
    <div
        class="w-150 max-w-[90vw] text-center p-8"
        data-test-id="modal-confirm-remove-item"
    >
      <h4 class="mb-4 font-medium text-xl md:text-2xl">
        {{ $t('Are you sure you want to remove this item?') }}
      </h4>
      <button
          ref="noButtonRef"
          class="text-white bg-gray-500 hover:bg-gray-700 focus:bg-gray-800 active:bg-gray-900 w-20 md:w-40 px-4 py-1 mr-4 uppercase"
          data-test-id="decline-remove-item"
          @click="$emit('close')"
      >
        {{ $t('No') }}
      </button>
      <button
          ref="yesButtonRef"
          class="text-white bg-red-500 hover:bg-red-700 focus:bg-red-800 active:bg-red-900 w-20 md:w-40 px-4 py-1 ml-4 uppercase"
          data-test-id="accept-remove-item"
          @click="$emit('confirm')"
      >
        {{ $t('Yes') }}
      </button>
    </div>
  </common-modal>
</template>
