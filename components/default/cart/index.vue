<script lang="ts" setup>
import { useCampaignStore } from '~/store/campaign'
import { useListingStore } from '~/store/listing'
import { useCartStore } from '~~/store/cart'
import { useComboCartStore } from "~/store/comboCart"

const localePath = useLocalePath()

const listCartItem = computed(() => useCartStore().listCartItem)
const comboCartItem = computed(() => useComboCartStore().enoughCombo)
const totalPrice = computed(() => {
  return useCartStore().getTotalPrice
})

const spriteUrl = computed(() => {
  return `--sprite-url: url("${cdnURL.value}images/logo_cart_sprite.webp")`
})

const promotionsList = ref<Promotion[]>()
const relatedProducts = ref<Product[]>()

const campaignIds = computed(() => {
  return listCartItem.value.map((item) => item.campaign_id)
})

onMounted(async () => {
  // Related Products
  const listRelatedProducts: RelatedProductPostData = {
    type: 'post_sale',
    filter: listCartItem.value.map((item) => {
      return {
        product_id: item.product_id,
        campaign_id: item.campaign_id,
        template_id: item.template_id,
      }
    })
  }

  if (listRelatedProducts.filter.length > 0) {
    relatedProducts.value = await useListingStore().postRelatedProduct(listRelatedProducts)
  }

  promotionsList.value = await useCampaignStore().getPromotion(campaignIds.value)
})

const email = ref('')

const listCartItemFiltered = computed(() => {
  const cartItemInCombo = comboCartItem.value.map(combo => combo.items.map(item => item.id)).flat()
  return listCartItem.value.filter(item => !cartItemInCombo.includes(item.id))
})
</script>

<template>
  <main id="cartPage" :style="spriteUrl" class="container min-h-95">
    <h1 class="font-medium text-2xl capitalize my-4 text-center md:text-left">
      {{ $t('Your shopping bag') }}
    </h1>
    <client-only>
      <div v-if="listCartItem.length" class="grid grid-cols-12">
        <div class="col-span-12 md:col-span-8 xl:col-span-9 md:pr-2">
          <div class="flex">
            <div class="!w-22" />
            <div class="w-full <xl:hidden grid grid-cols-12 font-medium uppercase text-center">
              <div class="col-span-7">
                {{ $t('Product') }}
              </div>
              <div class="w-full col-span-5 grid grid-cols-12">
                <div class="col-span-3">
                  {{ $t('Price') }}
                </div>
                <div class="col-span-5">
                  {{ $t('Quantity') }}
                </div>
                <div class="col-span-3">
                  {{ $t('Total') }}
                </div>
                <div class="col-1" />
              </div>
            </div>
          </div>
          <default-cart-item
            v-for="(cartItem, index) in listCartItemFiltered"
            :key="cartItem.id"
            :index="index"
            :cart-item="cartItem"
          />
          <default-cart-combo
            v-for="(comboItem, index) in comboCartItem"
            :key="comboItem.id"
            :index="index"
            :combo="comboItem"
          />
          <lazy-default-cart-bundle-box
            v-if="!storeInfo().disable_promotion"
            class="mt-3"
            :campaign-ids="campaignIds"
          />
          <lazy-default-common-promotions-list v-if="promotionsList?.length && !storeInfo().disable_promotion" :promotions-list="promotionsList" class="mt-4 mb-8" />
        </div>
        <div class="col-span-12 md:col-span-4 xl:col-span-3 md:pl-2 md:h-max sticky top-[100px] z-1">
          <p class="uppercase font-medium border-b pb-4 mb-4">
            {{ $t('Subtotal') }} <span class="text-primary float-right" data-test-id="cart-subtotal">{{ $formatPrice(totalPrice) }}</span>
          </p>
          <p class="font-semibold mb-4 text-sm">
            <span>{{ $t('Total quantity') }}</span> <span class="float-right" data-test-id="cart-total-qty">{{ $t('item', { count: totalQuantity().value }) }}</span>
          </p>
          <p class="mb-4 text-sm">
            <span class="font-semibold">{{ $t('Shipping & fees') }}</span> <span class="float-right italic">{{ $t('Calculated at checkout') }}</span>
          </p>
          <form @submit.prevent="createOrder({email})">
            <input
              id="email"
              v-model="email"
              type="email"
              pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
              class="italic text-xs border w-full p-2"
              :placeholder="$t('Enter e-mail for express checkout')"
            >
            <div class="<md:(bottom-fixed border p-1 z-3) w-full bg-white">
              <button
                type="submit"
                class="btn-fill w-full py-2 md:mt-4 font-medium text-xl md:text-base lg:text-xl z-1"
                dusk="proceed-to-checkout-button"
                data-test-id="cart-proceed-to-checkout-button"
                :disabled="!!isLoading"
              >
                <common-loading-dot v-if="!!isLoading" />
                <span v-else class="flex gap-1 items-center justify-center">
                  <i class="icon-sen-lock" />
                  {{ $t('Proceed to checkout') }}
                </span>
              </button>
            </div>
          </form>

          <common-payment-gateway-accept />
        </div>
      </div>
      <div v-else class="text-center">
        <h6 class="py-3" data-test-id="cart-is-empty">
          {{ $t('Your cart is empty') }} :(
        </h6>
        <nuxt-link class="btn-border-fill px-3 py-2" :to="localePath('/')">
          {{ $t('Continue Shopping') }}
        </nuxt-link>
      </div>
      <lazy-common-product-carousel
        v-if="relatedProducts?.length"
        class="container mt-10"
        title-class="text-center uppercase md:text-3xl text-2xl"
        title="Frequently bought together"
        :products="relatedProducts"
        :static-grid="'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4'"
      >
        <template #default="{ product, index }">
          <default-common-product-item :product="product" :index="index" class="inline-block" />
        </template>
      </lazy-common-product-carousel>
    </client-only>
  </main>
</template>

<style lang="scss" scoped></style>
