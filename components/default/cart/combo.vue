<script lang="ts" setup>
import { useComboCartStore } from "~/store/comboCart"
import { useUserSession } from "~/store/userSession";
import { $comboPrice } from "~/utils/price"

const props = defineProps<{
  combo: ComboItem,
}>()

const localePath = useLocalePath()
const isShowModalDelete = ref(false)
const comboCartStore = useComboCartStore()
const user = useUserSession()
const { $getDynamicBaseCostIndex } = useNuxtApp()

const totalPrice = computed(() => {
  let total = 0
  props.combo.items.forEach((item) => {
    total += item.variantPrice || item.price
  })
  return total
})

const productsDB = computed(() => {
  return props.combo.products
})

const totalDynamicBaseCost = computed(() => {
  if (user.countryCode !== props.combo.country) return 0

  let total = 0
  props.combo.items.forEach((item) => {
    const product = productsDB.value.find((product) => product.id === item.product_id)
    if (!product) return
    total += $getDynamicBaseCostIndex(product) * item.quantity
  })
  return total
})

const sellPrice = computed(() => {
  return $comboPrice(props.combo) + totalDynamicBaseCost.value
})

function removeCombo () {
  isShowModalDelete.value = false
  comboCartStore.removeComboItem(props.combo)
}

function duplicateCombo () {
  comboCartStore.duplicateComboItem(props.combo, props.combo.items[0].campaign_id)
}

function updateQuantity (quantity: number) {
  if (quantity < 1) {
    isShowModalDelete.value = true
    return
  }
  comboCartStore.updateQuantity(props.combo, quantity)
}

const isEditCart = ref(false)
</script>

<template>
  <div class="p-4 flex gap-2 border-b relative">
    <div
      class="xl:hidden btn-text absolute right-4 top-1"
       data-test-id="cart-item-remove"
       @click="isShowModalDelete = true"
    >
      <i class="icon-sen-delete" />
    </div>
    <common-image
      img-class="w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: combo?.thumb_url,
      }"
      :alt="combo?.title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(combo?.thumb_url)"
    />
    <div class="w-full max-w-[calc(100%-90px)] grid grid-cols-12 h-min gap-2">
      <div class="col-span-12 md:col-span-8 xl:col-span-7 gap-2 pt-2">
        <div>
          <nuxt-link
              :to="localePath(`/${combo.items[0].campaign_slug}`)"
              class="w-full"
          >
            <h5 class="text-overflow-hidden font-bold" :title="combo.title">
              {{ combo.title }}
            </h5>
          </nuxt-link>
        </div>
        <default-cart-combo-item
          v-for="item in combo.items"
          :key="item.product_id"
          :item="item"
          :is-edit="isEditCart"
        />
      </div>
      <div class="col-span-12 md:col-span-4 xl:col-span-5 flex">
        <div class="grid xl:grid-cols-10 grid-cols-6 w-full text-center gap-2 h-fit my-auto">
          <div class="<xl:hidden col-span-3 font-medium my-auto" data-test-id="cart-item-price">
            <div>
              {{ $formatPrice(sellPrice) }}
            </div>
            <del v-if="sellPrice !== totalPrice && totalPrice > 0" class="text-gray-500 block">
              {{ $formatPrice(totalPrice) }}
            </del>
          </div>
          <button
            v-if="isEditCart"
            class="md:hidden col-span-6 md:col-span-2 btn-border-fill-red center-flex text-sm py-1 px-2 w-full text-overflow-hidden"
            :title="$t('done')"
            data-test-id="cart-item-edit-done"
            @click="isEditCart = false"
          >
            {{ $t('Done') }}
          </button>
          <div v-if="!isEditCart" class="col-span-6 xl:hidden flex">
            <button
                class="btn-border-fill-red center-flex text-sm py-1 px-2 mt-2 w-full truncate"
                data-test-id="cart-item-add-more"
                @click="duplicateCombo"
            >
              {{ $t('Add more item') }}
            </button>
            <button class="btn-text-red w-fit px-2 ml-3 md:hidden" data-test-id="cart-item-edit" @click="isEditCart = true">
              <u>{{ $t('Edit') }}</u>
            </button>
          </div>
          <default-cart-quantity
            class="col-span-6 sm:col-span-3 md:col-span-6 xl:col-span-4 my-auto"
            :quantity="combo.quantity"
            @duplicate-cart-item="duplicateCombo"
            @update-quantity="updateQuantity"
          />
          <div class="my-auto col-span-6 sm:col-span-3 md:col-span-6 xl:col-span-3 font-medium <xl:center-flex xl:(flex flex-col gap-3)" data-test-id="cart-item-total-price">
            <div>
              {{ $formatPrice(sellPrice * combo.quantity) }}
            </div>
            <span class="<xl:hidden ml-2 btn-text" data-test-id="cart-item-remove" @click="isShowModalDelete = true"><i class="icon-sen-delete" /></span>
          </div>
        </div>
      </div>
      <default-cart-confirm-delete
        :state="isShowModalDelete"
        @confirm="removeCombo"
        @close="isShowModalDelete = false"
      />
    </div>
  </div>
</template>
