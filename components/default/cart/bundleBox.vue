<script lang="ts" setup>
import type DefaultCampaignModalConfirmDesign from '../campaign/modalConfirmDesign.vue'
import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/utils/constant'

const props = defineProps({
  campaignIds: {
    default: undefined,
    type: Object as PropType<number[]>
  }
})

const { $toLocalePrice } = useNuxtApp()

const modalConfirmDesign = ref<InstanceType<typeof DefaultCampaignModalConfirmDesign>>()

const campaignIds = computed(() => props.campaignIds)

const {
  isAddToCart,
  isShowBundleProduct,
  bundleProduct,
  currentBundleProduct,
  totalBundleDiscount,
  saveBundleDiscount,
  isFetched,
  getDataBundle,
  refillBundleProducts,
  getMoreBundleProducts,
  submitBundleProduct
} = useCampaignBundle(campaignIds, true, modalConfirmDesign, BUNDLE_DISCOUNT_VIEW_PLACE.CART)

watch(campaignIds, () => {
  refillBundleProducts()
})

onMounted(() => {
  getDataBundle()
})
</script>

<template>
  <div v-if="isShowBundleProduct && bundleProduct?.length && isFetched && getMoreBundleProducts()">
    <div class="center-flex w-full">
      <div class="w-full">
        <div class="font-medium text-blue-400 text-center md:text-2xl cursor-pointer" @click="submitBundleProduct()">
          <span>
            + {{ $t('Add a matching') }}
          </span>
          <span class="capitalize ml-2">
            {{ bundleProduct[0]?.campaign_name || bundleProduct[0]?.slug?.replaceAll('-', ' ') }}
          </span>
        </div>
        <div class="center-flex mt-5 flex-wrap">
          <span v-if="bundleProduct[0]?.currentVariant?.out_of_stock" class="text-red-500 font-medium">
            {{ $t('Out of stock') }}
          </span>
          <span v-else class="text-center">
            <del v-if="storeInfo().store_type !== 'google_ads'" class="mx-1 text-gray-400">
              {{ $toLocalePrice(totalBundleDiscount) }}
            </del>
            <span class="mx-1 font-medium">{{ $toLocalePrice(totalBundleDiscount - saveBundleDiscount) }}</span>
          </span>
          <button
            class="btn-fill uppercase font-semibold px-2 py-1 mx-1"
            :disabled="!!isLoading"
            @click="submitBundleProduct(false, true)"
          >
            <common-loading-dot v-if="isLoading" />
            <span v-else>{{ $t('Add to cart') }}</span>
          </button>
        </div>
      </div>
      <div
        :id="`image-bundleProduct-${bundleProduct[0]?.id}`"
        class="relative w-28 min-w-28 m-1 hover:shadow-custom cursor-pointer"
        :title="$t('Click to update options')"
        @click="currentBundleProduct = shallowReactive(bundleProduct[0])"
      >
        <common-image
          :image="{
            path: bundleProduct[0]?.thumb_url,
            color: bundleProduct[0]?.currentOptions?.color
          }"
          :alt="bundleProduct[0]?.name"
        />

        <default-common-personalize-tag v-if="bundleProduct[0]?.personalized" size="xs" />
      </div>
    </div>

    <lazy-default-campaign-modal-bundle-product
      v-if="currentBundleProduct"
      :current-product="currentBundleProduct"
      :is-add-to-cart="isAddToCart"
      @close-modal="currentBundleProduct = undefined; isAddToCart = false"
      @submit="isAddToCart ? submitBundleProduct() : currentBundleProduct = undefined"
    />

    <lazy-default-campaign-modal-confirm-design
      ref="modalConfirmDesign"
      @confirm-design="submitBundleProduct(true)"
    />
  </div>
</template>
