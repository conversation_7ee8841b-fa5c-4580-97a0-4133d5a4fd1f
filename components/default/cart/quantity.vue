<script setup lang="ts">
defineProps<{
  quantity: number
}>()
defineEmits(['updateQuantity', 'duplicateCartItem'])
</script>

<template>
  <div>
    <div class="flex justify-center">
      <button
          class="btn-border h-[30px] min-w-[30px] center-flex"
          data-test-id="cart-item-decrease"
          @click="$emit('updateQuantity', quantity - 1)"
      >
        <i class="icon-sen-minus" />
      </button>
      <common-dropdown
          btn-class="text-sm btn-border py-1 w-full"
          class="mx-2 w-full"
          data-test-id="cart-item-qty-button"
      >
          <span data-test-id="cart-item-qty">
            {{ quantity }}
          </span>
        <template #content>
          <div
              v-for="item in 50"
              :key="item"
              class="min-w-[50vw] md:min-w-16 btn-text px-4"
              :value="item"
              :class="{'bg-primary text-contrast': quantity == item}"
              @click="$emit('updateQuantity', item)"
          >
            {{ item }}
          </div>
        </template>
      </common-dropdown>
      <button
          class="btn-border h-[30px] min-w-[30px] center-flex"
          data-test-id="cart-item-increase"
          @click="$emit('updateQuantity', quantity + 1)"
      >
        <i class="icon-sen-plus" />
      </button>
    </div>
    <button
        class="<xl:hidden btn-border-fill-red center-flex text-sm py-1 px-2 mt-2 w-full"
        data-test-id="cart-item-add-more"
        @click="$emit('duplicateCartItem')"
    >
      {{ $t('Add more item') }}
    </button>
  </div>
</template>
