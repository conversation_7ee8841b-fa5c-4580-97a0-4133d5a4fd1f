<script lang="ts" setup>
const { showCountdown, endTime } = defineProps({
  showCountdown: {
    default: undefined,
    type: Number
  },
  endTime: {
    default: undefined,
    type: Number
  }
})

const {
  times,
  changeTime,
} = useCampaignCountdown(showCountdown, endTime)
</script>
<template>
  <div
    v-if="changeTime > 0"
    class="relative mt-4 pt-6 grid grid-cols-4 px-3 gap-3 md:gap-5 border-2 border-gray-300 rounded-md text-center font-medium"
    :class="{'!grid-cols-3': times[0].time <= 0 }"
  >
    <div
      v-for="(time, index) in times"
      :key="index"
      :class="{hidden: time.time <= 0 && index === 0}"
    >
      <div class="py-2 bg-gray-700 text-white text-lg">
        {{ time.time }}
      </div>
      <div class="py-2 text-sm text-gray-500 capitalize">
        {{ $t(time.text, { count: time.time }) }}
      </div>
    </div>
    <div class="absolute px-2 bg-white w-max top-0 position-center-x -translate-y-1/2 z-0">
      <span class="capitalize font-bold">{{ $t('Last minute') }}</span>
      <span> - {{ $t('Sale ends in') }}</span>
    </div>
  </div>
</template>
