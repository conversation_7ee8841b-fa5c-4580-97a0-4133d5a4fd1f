<script lang="ts" setup>
defineProps({
  optionList: {
    default: undefined,
    type: Object as PropType<OptionsList>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  }
})

const isShowModal = ref(false)

defineExpose({
  isShowModal
})

const {
  sizeGuideList
} = useSizeGuide()
</script>

<template>
  <common-modal
    v-model="isShowModal"
    :title="$t('Please choose a size')"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 px-4 text-center "
    @shown="uiManager().updateSizeGuideData(currentProduct)"
  >
    <div class="my-2 w-full lg:w-1/2 mx-auto lg:max-h-[50vh] max-h-[80vh] overflow-y-auto">
      <template v-if="sizeGuideList && sizeGuideList.length">
        <button
          v-for="(sizeGuide, productIndex) in sizeGuideList.filter(item => optionList?.size.includes(item.size.toLowerCase()))"
          :key="productIndex"
          class="btn-border-fill border-none w-full p-2 capitalize"
          @click="$emit('selectSize', sizeGuide.size.toLowerCase()); isShowModal = false;"
        >
          {{ sizeGuide.size }} - {{ $t('Chest') }} {{ sizeGuide.width }} in
        </button>
      </template>
      <template v-else-if="optionList?.size.length">
        <button
          v-for="(size, productIndex) in optionList?.size"
          :key="productIndex"
          class="btn-border-fill border-none w-full p-2 capitalize"
          @click="$emit('selectSize', size); isShowModal = false;"
        >
          {{ size }}
        </button>
      </template>
    </div>
    <div v-if="sizeGuideList && sizeGuideList.length" class="w-full lg:w-1/2 flex justify-center mx-auto">
      <div class="btn-text cursor-pointer mt-2 text-blue-400" @click="$emit('openSizeGuide')">
        <span class="font-medium">
          {{ $t('Size guide') }}
        </span>
        <span>
          <i class="icon-sen-ruler ml-1" />
        </span>
      </div>
    </div>
  </common-modal>
</template>
