<script lang="ts" setup>
const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  isAddToCart: {
    default: false,
    type: Boolean
  },
  isModal: {
    default: false,
    type: Boolean
  },
  containerClass: {
    default: 'p-2 border bg-[#f5f5f5]',
    type: String
  }
})
const $viewport = useViewport()
const currentCustomItem = shallowRef<CustomItem>()
const isOnSelectCustomDesign = ref(false)

resetCustomData()

watch(() => props.product && props.product.customItemList, resetCustomData)

function resetCustomData() {
  if (props.product?.customItemList?.length) {
    currentCustomItem.value = props.product?.customItemList[0]
  }
  else if (props.product?.customDesignType) {
    isOnSelectCustomDesign.value = true
  }
}

const editCustomImage = shallowRef<CustomImageItem>()
const isShowDesign = ref(false)

const isShowNextCustomItemButton = computed(() => props.product?.customDesignType ? (props.product?.customItemList?.length || 0) >= 1 : (props.product?.customItemList?.length || 0) >= 2)

function nextCustomItem() {
  if (isOnSelectCustomDesign.value && props.product?.customItemList?.length) {
    currentCustomItem.value = props.product.customItemList[0]
    isOnSelectCustomDesign.value = false
    return
  }

  if (props.product?.customItemList?.length) {
    const index = props.product.customItemList.findIndex(item => item === currentCustomItem.value)
    if (props.product.customItemList[index + 1]) {
      currentCustomItem.value = props.product.customItemList[index + 1]
    }
    else if (props.product.customDesignType) {
      isOnSelectCustomDesign.value = true
    }
    else {
      currentCustomItem.value = props.product.customItemList[0]
    }
  }
}

defineExpose({ currentCustomItem, isOnSelectCustomDesign, editCustomImage })
</script>

<template>
  <div
    class="z-3 w-full max-h-100 flex personalize"
    :class="{
      [containerClass]: (product?.customTextList?.length || product?.customImageList?.length || product?.customDesignType),
      '<md:(fixed bottom-0 left-0 z-3) custom-opt-vari-position': !isModal
    }"
  >
    <div class="w-full">
      <div v-if="product?.customTextList?.length" class="md:my-3">
        <div class="<md:hidden flex items-center">
          <span class="font-medium uppercase mr-1">{{ $t('Customize text') }}</span>
          <i class="icon-sen-text-box-multiple-outline" />
        </div>
        <div v-if="isAddToCart && product.customTextList.find(item => !item.data.text)" class="<md:hidden text-red-500">
          {{ $t('Write something to customize the product') }}
        </div>
        <default-common-input
          v-for="(customText, index) in product.customTextList"
          v-show="(customText === currentCustomItem && !isOnSelectCustomDesign) || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
          :id="`customText_${isModal ? 'modal' : 'campaign'}_${customText.data.name}`"
          :key="index"
          class="md:mt-2"
          :max-length="Number(customText.data.maxLength) || 16"
          :model-value="customText.data.text"
          :label="`${$t(customText.data.name?.replaceAll('_', ' ') || '')}`"
          :state="isAddToCart ? !!customText.data.text : undefined"
          @update:model-value="$emit('updatePersonalizeCustom', customText, $event)"
          @focus="$emit('showDesign', customText.personalize.personalizeKey)"
          @blur="isModal ? '' : updateDesignQuery(customText)"
        >
          <span class="absolute right-2 position-center-y text-sm text-red-500">{{ `${customText.data.text?.length} / ${customText.data.maxLength || 16}` }}</span>
        </default-common-input>
      </div>
      <div v-if="product?.customDesignType" class="md:my-3">
        <div class="<md:hidden flex items-center">
          <span class="font-medium uppercase mr-1">{{ $t('Select design') }}</span>
          <i class="icon-sen-image-multiple-outline" />
        </div>
        <common-dropdown
          v-show="isOnSelectCustomDesign || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
          btn-class="w-full border text-left h-12 px-3 capitalize"
          dropdown-class="w-full"
        >
          <span>{{ product?.selectedCustomDesign }}</span>
          <template #content>
            <button
              v-for="item in product.customDesignList"
              :key="item"
              class="btn-text w-full capitalize text-left px-3 py-1"
              :class="{ 'bg-primary !text-contrast': product?.selectedCustomDesign === item }"
              @click="$emit('selectCustomDesign', product, item)"
            >
              {{ item }}
            </button>
          </template>
        </common-dropdown>
      </div>
      <div v-if="product?.customImageList?.length" class="md:my-3">
        <div class="<md:hidden flex items-center">
          <span class="font-medium uppercase mr-1">{{ $t('Customize image') }}</span>
          <i class="icon-sen-image-multiple-outline" />
        </div>
        <div v-if="isAddToCart && product.customImageList.find(item => !item.currentFileUploadUrl)" class="<md:hidden text-red-500">
          {{ $t('Add images to customize the product') }}
        </div>
        <template
          v-for="(customImage, index) in product.customImageList"
          :key="index"
        >
          <lazy-common-file-selector
            v-show="(customImage === currentCustomItem && !isOnSelectCustomDesign) || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
            :id="`customImage_${isModal ? 'modal' : 'campaign'}_${index}`"
            :state="isAddToCart ? !!customImage.currentFileUploadUrl : undefined"
            :file-upload="customImage.currentFileUploadUrl"
            browse-text="Upload"
            :auto-upload="false"
            class="md:mt-2 w-full"
            @on-change="$emit('updatePersonalizeCustom', customImage, $event[0])"
          >
            <template #before>
              <button
                class="border btn-text h-12 min-w-12 mr-2 text-2xl"
                :title="$t('Edit Position')"
                @click="editCustomImage = customImage"
              >
                <i class="icon-sen-image-edit" />
              </button>
            </template>
          </lazy-common-file-selector>
        </template>
      </div>
    </div>
    <button v-if="isShowNextCustomItemButton" class="md:hidden h-12 min-w-12 border flex-center text-2xl ml-2" @click="nextCustomItem">
      <i class="icon-sen-skip-next " />
    </button>
    <lazy-common-modal
      id="editCustomImage"
      v-model="editCustomImage"
      modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 pb-15"
      @close-modal=" editCustomImage?.personalize?.designCanvas?._onupdate()"
      @shown="isShowDesign = true"
      @hidden="isShowDesign = false"
    >
      <lazy-common-design-canvas
        v-if="isShowDesign"
        class="mt-5"
        :custom-image="editCustomImage"
      />

      <common-control-custom-image
        v-if="editCustomImage"
        class="my-5"
        :custom-image-item="editCustomImage"
      />

      <div class="p-1 border bottom-fixed w-full bg-white z-1">
        <button
          id="saveEditDesign"
          class="uppercase font-bold btn-fill text-xl w-full h-12"
          @click="editCustomImage = undefined"
        >
          <span>{{ $t('Save') }}</span>
        </button>
      </div>
    </lazy-common-modal>
  </div>
</template>
