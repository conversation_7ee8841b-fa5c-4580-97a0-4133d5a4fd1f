<script lang="ts" setup>
defineProps({
  isModal: {
    default: false,
    type: Boolean
  },
  containerClass: {
    default: 'bg-[#f5f5f5] border',
    type: String
  }
})

const toggleOptionMobile = ref(false)

defineExpose({ toggleOptionMobile })
</script>

<template>
  <div
    class="relative w-full transition-all p-1"
    :class="{
      [containerClass]: true,
      '<md:(fixed bottom-0 left-0 z-3) custom-opt-vari-position': !isModal
    }"
    @click="$emit('showDesign')"
  >
    <button
      class="btn center-flex p-2 <md:(absolute -top-10 right-5 z-1 py-1 bg-[#f5f5f5] border-t border-x)"
      @click="toggleOptionMobile = !toggleOptionMobile"
    >
      <span class="font-medium uppercase mr-1">{{ $t('Customize') }}</span>
      <i v-if="$viewport.isGreaterOrEquals(VIEWPORT.tablet) " class="icon-sen-text-box-multiple-outline" />
      <i v-else class="text-2xl block icon-sen-chevron-up transition-transform transform animation" :class="{ 'rotate-180': toggleOptionMobile }" />
    </button>
    <common-collapse
      :when="toggleOptionMobile || $viewport.isGreaterOrEquals(VIEWPORT.tablet) "
      class="transition-default p-1"
    >
      <div class="<md:(max-h-70 overflow-x-hidden overflow-y-auto)">
        <div :id="isModal ? 'artwork_form_selector_modal' : 'artwork_form_selector'" />
      </div>
    </common-collapse>
  </div>
</template>
