<script lang="ts" setup>
import type DefaultCampaignBundleBox from '~/components/default/campaign/bundleBox.vue'

import type DefaultCampaignModalAddToCart from '~/components/default/campaign/modalAddToCart.vue'
import type DefaultCampaignModalConfirmDesign from '~/components/default/campaign/modalConfirmDesign.vue'
import type DefaultCampaignModalSelectSize from '~/components/default/campaign/modalSelectSize.vue'

import { useCampaignStore } from '~~/store/campaign'
import { useCartStore } from '~~/store/cart'
import { useComboCartStore } from '~/store/comboCart'
import { BUNDLE_DISCOUNT_VIEW_PLACE, PERSONALIZED_TYPE, SYSTEM_CAMPAIGN_TYPE } from '~/utils/constant'

const props = defineProps({
  campaignSlug: {
    type: String,
    required: true
  },
  isModal: {
    default: false,
    type: Boolean
  }
})
const cartStore = useCartStore()
const $viewport = useViewport()
const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
// eslint-disable-next-line
let isAddAllToCart:Boolean = false

const modalAddToCart = ref<InstanceType<typeof DefaultCampaignModalAddToCart>>()
const modalSelectSize = ref<InstanceType<typeof DefaultCampaignModalSelectSize>>()
const modalConfirmDesign = ref<InstanceType<typeof DefaultCampaignModalConfirmDesign>>()

const bundleBox = ref<InstanceType<typeof DefaultCampaignBundleBox>>()

const comboCartStore = useComboCartStore()

const {
  campaignId,
  userCampaignOption,
  campaignData,
  isShowProductListDropdown,
  // similarProducts,
  relatedProducts,
  relatedCartProducts,

  productStats,
  campaignPromotions,

  dataDescription,
  dataProductDetail,

  updateProduct,
  updateOption,
  checkOptions,
  resetData,
  getRelatedProduct
} = useCampaign(props.campaignSlug, props.isModal)

await resetData()

const {
  bundleProduct,
  currentBundleProduct,
  totalBundleDiscount,
  saveBundleDiscount,
  getDataBundle,
  checkBundleProduct,
  addBundleProductToCart,
  refetchDataBundle
} = useCampaignBundle(campaignId, false, modalConfirmDesign, BUNDLE_DISCOUNT_VIEW_PLACE.CAMPAIGN_DETAIL)

const {
  personalizeCustom,
  personalizePB,
  personalizeCustomOptions,

  isShowDesign,
  currentPersonalize,
  showDesignHandler,
  getDataCustomDesign,
  updatePersonalizeCustom,
  selectCustomDesign,
  checkPersonalize,
  handlePersonalizeError
} = useCampaignPersonalize(campaignData, userCampaignOption, props.isModal)

const customOptions = computed(() => userCampaignOption.currentProduct?.template_custom_options ?? userCampaignOption.currentProduct?.custom_options ?? campaignData.options)
const commonOptions = computed(() => userCampaignOption.currentProduct?.common_options ?? campaignData.common_options)
const {
  extraCustomFee,
  groupCustomOptionsQuantity,
  userCustomOptions,
  userCommonOptions,
  totalCustomOptionFee,
  updateCustomOptions,
  updateCommonOptions,
  requiredValue,
  updateGroupCustomOptionsQuantity,
  checkPersonalizeCustomOptions,
  resetCommonOption,
  resetOption
} = useCampaignPersonalizeCustomOptions(customOptions, props.isModal, campaignData?.personalized, userCampaignOption.currentProduct?.full_printed, commonOptions)

if (import.meta.client && campaignData.id && (campaignData.personalized === 1 || campaignData.personalized === 2)) {
  // fix error after refresh page. wait new version of nuxt
  setTimeout(async () => {
    await getDataCustomDesign()
  })
}

if (import.meta.client && campaignData.id && !props.isModal) {
  const intervalTracking = setInterval(() => {
    if (window.userActivity) {
      clearInterval(intervalTracking)
      getDataBundle()
    }
  }, 500)
}

/**
 * @param [isConfirmDesign] Passed all 'personalize' checks
 * @param isCheckout
 * @param isForceOpenModal
 */
async function addToCart(isConfirmDesign = false, isCheckout = false, isForceOpenModal = false) {
  userCampaignOption.isAddToCart = true // for personalize_input check state

  if (
    campaignData.system_type === 'combo' // combo campaign
    && comboCartStore.hasSelected
  ) {
    return addComboToCart(isForceOpenModal)
  }

  if (checkOptions()) { // check normal options
    if (userCampaignOption.optionError === 'size') {
      modalSelectSize.value!.isShowModal = true
    }
    else {
      uiManager().createPopup($i18n.t('Please choose {option}', { option: userCampaignOption.optionError }))
    }
    return
  }

  // Check if this is a personalized custom option
  const isPersonalizedCustomOption = campaignData.personalized === 3 || campaignData.personalized === PERSONALIZED_TYPE.CUSTOM_OPTION

  // If this is an AI campaign and the user hasn't confirmed the design yet, call the openPreviewModal method
  if (!isConfirmDesign && isAICampaign.value && isPersonalizedCustomOption) {
    // Try to find the personalizeCustomOptions component using ref
    const personalizeCustomOptionsRef = personalizeCustomOptions.value

    // If we can find the component, call its openPreviewModal method
    if (personalizeCustomOptionsRef && typeof personalizeCustomOptionsRef.openPreviewModal === 'function') {
      await personalizeCustomOptionsRef.openPreviewModal()
      return
    }
    else {
      console.warn('personalizeCustomOptions component or openPreviewModal method not found')
      return
    }
  }

  // If this is an AI campaign with personalize options and the user hasn't confirmed the design yet
  if (!isConfirmDesign && isAICampaign.value && shouldShowPersonalizeOptions.value) {
    // Try to find the personalizeCustomOptions component using ref
    const personalizeCustomOptionsRef = personalizeCustomOptions.value

    // If we can find the component, call its openPreviewModal method
    if (personalizeCustomOptionsRef && typeof personalizeCustomOptionsRef.openPreviewModal === 'function') {
      await personalizeCustomOptionsRef.openPreviewModal()
      return
    }
    else {
      console.warn('personalizeCustomOptions component or openPreviewModal method not found')
      return
    }
  }

  if (!isConfirmDesign) { // check personalize options
    let designUrl: string[] = []
    if (userCampaignOption.currentProduct?.personalized === 1 || userCampaignOption.currentProduct?.personalized === 2) {
      const value = await checkPersonalize()
      if (value.success) {
        designUrl = designUrl.concat(value.designDataUrl || [])
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }
    else if (userCampaignOption.currentProduct?.personalized === 3 || userCampaignOption.currentProduct?.full_printed === 5) {
      const value = checkPersonalizeCustomOptions()
      if (!value.success) {
        return handlePersonalizeError({
          ...value,
          product: userCampaignOption.currentProduct
        }, currentBundleProduct)
      }
    }

    if (isAddAllToCart) {
      const value = await checkBundleProduct(isForceOpenModal)
      if (value.success) {
        designUrl = designUrl.concat(value?.designDataUrl || [])
        currentBundleProduct.value = undefined
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }

    if (designUrl.length) {
      modalConfirmDesign.value!.isShowModal = designUrl
      return
    }
  }

  const cartItem = cartStore.addCartItem(campaignData, userCampaignOption, {
    totalCustomOptionFee: totalCustomOptionFee.value,
    userCustomOptions: userCommonOptions.concat(userCustomOptions),
    groupCustomOptionsQuantity: groupCustomOptionsQuantity.value + (userCommonOptions?.length || 0)
  }, true)

  const listPersonalize: { [key: string]: Personalize } = {}
  if ((campaignData.personalized === 1 || campaignData.personalized === 2) && userCampaignOption?.currentProduct?.personalizeList?.length) {
    listPersonalize[cartItem.id] = userCampaignOption?.currentProduct?.personalizeList[0]
  }

  if (isAddAllToCart) {
    const cartItemBundle = addBundleProductToCart()
    cartItemBundle?.forEach((cartItem) => {
      if ((cartItem.personalized === 1 || cartItem.personalized === 2) && cartItem.product.personalizeList?.length) {
        listPersonalize[cartItem.id] = cartItem?.product?.personalizeList[0]
      }
    })
  }

  if (Object.keys(listPersonalize).length) {
    uiManager().$patch({ isUploadFile: true })
    window.loadingUploadImage = getPersonalizeUpload(listPersonalize)
    window.loadingUploadImage.then((uploadUrl) => {
      uiManager().$patch({ isUploadFile: false })
      Object.keys(uploadUrl).forEach((key) => {
        if (uploadUrl[key]) {
          cartStore.updateCartItemByID(key, { thumb_url: uploadUrl[key] })
        }
      })
    })
  }

  if (isCheckout) {
    await createOrder()
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return
  }

  if ($viewport.isLessThan(VIEWPORT.tablet) || isAddAllToCart) {
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return useRouter().push(localePath('/cart'))
  }

  modalAddToCart.value!.isShowModal = true
  modalAddToCart.value!.cartItem = cartItem
}

const { getVariant, getProductOptions } = useCampaignCombo(campaignData)

async function addComboToCart(isForceOpenModal: boolean) {
  if (isAddAllToCart) { // bundle product case
    const value = await checkBundleProduct(isForceOpenModal)
    if (value.success) {
      currentBundleProduct.value = undefined
    }
    else {
      return handlePersonalizeError(value, currentBundleProduct)
    }

    const cartItemBundle = addBundleProductToCart()
    cartItemBundle.forEach((cartItem) => {
      if ((cartItem.personalized === 1 || cartItem.personalized === 2) && cartItem.product.personalizeList?.length) {
        listPersonalize[cartItem.id] = cartItem?.product?.personalizeList[0]
      }
    })
  }

  userCampaignOption.isAddToCart = true
  const cartItems = [] as CartItem[]
  const comboId = `${campaignData.id}_${comboCartStore.index}`
  const selected = comboCartStore.selected.filter(product => product.isSelected)
  const isEnoughCombo = selected.length === comboCartStore.selected.length
  comboCartStore.index += 1
  selected.map((product) => {
    const productInfo = campaignData.products?.find(item => item.id === product.id)
    return {
      ...productInfo,
      ...product,
      currentVariant: getVariant(productInfo as Product, product.currentOptions),
      optionListFull: getProductOptions(productInfo as Product, true)
    }
  })
    .forEach((product: Product) => {
      const cartItem = cartStore.addCartItem(campaignData, {
        currentOptions: product.currentOptions,
        currentProduct: product,
        currentVariant: product.currentVariant,
        quantity: userCampaignOption.quantity,
        optionListFull: product.optionListFull
      }, {
        comboId: isEnoughCombo ? comboId : undefined, // Chi luu comboId khi du combo
        isCombo: true
      }, true)
      cartItems.push(cartItem)
    })
  comboCartStore.addComboItem(cartItems, campaignData)
  useCampaignStore().$patch({ modalCampaignUrl: '' })
  return useRouter().push(localePath('/cart'))
}

const title = computed(() => {
  const campaignName = campaignData.name
  const productName = userCampaignOption.currentProduct?.name
  const enableProductNameAfter = storeInfo().enable_product_name_after

  if (enableProductNameAfter) {
    return `${campaignName} ${productName}`
  }
  return campaignName
})

const isAICampaign = computed(() => {
  return campaignData.system_type === 'ai_mockup' || campaignData.system_type === SYSTEM_CAMPAIGN_TYPE.AI_MOCKUP
})

// Check if AI campaign should show personalize options
const shouldShowPersonalizeOptions = computed(() => {
  if (!isAICampaign.value)
    return false

  // Parse attributes to check for customer upload options
  let attributes: any = {}
  try {
    attributes = typeof campaignData.attributes === 'string'
      ? JSON.parse(campaignData.attributes)
      : campaignData.attributes || {}
  }
  catch (e) {
    console.error('Error parsing campaign attributes:', e)
  }

  // Check if customer upload is enabled in attributes
  const customerUploadOptions = attributes.customer_upload_options
  const allowCustomerUpload = customerUploadOptions?.allow_customer_upload_image === true

  // Show component if it's AI campaign with customer upload enabled
  // OR if it's AI campaign with custom options
  const hasOptions = !!(campaignData.options || campaignData.common_options)

  return allowCustomerUpload || hasOptions
})

onMounted(getRelatedProduct)

function updateSelectProduct(productName: string, productId: number) {
  updateProduct(productName, productId)
  if (userCampaignOption.currentProduct?.full_printed === 5) {
    resetOption()
    resetCommonOption()
  }
}

const { campaignDescription, thumbnail, reviewSummary } = useCampaignSchema(campaignData, userCampaignOption)

const { isHiddenBottomButton } = useHiddenBottomButton()
</script>

<template>
  <main
    v-if="campaignData.status !== 'blocked'"
    :id="isModal ? 'modalCampaignPage' : 'campaignPage'"
    data-test-id="campaign-container"
    class="container"
    :class="{ 'flex flex-col max-h-100vh md:max-h-80vh ': isModal }"
  >
    <common-campaign-meta-data
      :campaign-data="campaignData"
      :campaign-description="campaignDescription"
      :thumbnail="thumbnail"
      :review-summary="reviewSummary"
      :user-campaign-option="userCampaignOption"
    />
    <h1
      class="md:text-2xl font-medium capitalize py-2"
      :class="{ 'pr-5 mb-2': isModal }"
    >
      {{ title }}
    </h1>
    <div
      class="flex flex-wrap"
      :class="{ 'overflow-y-auto mb-20 overflow-x-hidden': isModal }"
    >
      <div
        class="w-full top-0 md:(w-1/2 pr-4 sticky h-max)"
        :class="{ 'lg:w-13/24 md:top-15': !isModal }"
      >
        <default-campaign-view-box
          v-if="userCampaignOption.currentProduct"
          :is-modal="isModal"
          :campaign-data="campaignData"
          :images-list="userCampaignOption.imagesList"
          :current-product="userCampaignOption.currentProduct"
          :color="userCampaignOption.optionListFull.color?.length > 1 ? userCampaignOption.currentOptions.color : userCampaignOption.optionListFull.color?.[0]"

          :personalize="currentPersonalize"
          :is-show-design="isShowDesign"

          @change-image="isShowDesign = false"
        />
        <common-control-custom-image
          v-if="currentPersonalize?.customImageList?.[0]"
          :key="currentPersonalize.personalizeKey"
          :class="{ invisible: !isShowDesign }"
          class="my-3"
          :custom-image-item="currentPersonalize?.customImageList[0]"
        />
        <div
          class="flex flex-wrap justify-between items-center py-2 <md:hidden"
          :class="{ 'mt-24': isAICampaign }"
        >
          <default-campaign-share-box />
          <div v-if="campaignData.seller && storeInfo().id === 1" class="text-sm lg:text-base">
            <span class="mr-1 text-gray-400">{{ $t('Designed and Sold by') }}</span>
            <nuxt-link v-if="campaignData.seller.slug" class="text-primary" :href="localePath(`/artist/${campaignData.seller.slug}`)">
              {{ campaignData.seller.nickname }}
            </nuxt-link>
            <span v-else class="text-primary">{{ campaignData.seller.nickname }}</span>
          </div>
        </div>
      </div>
      <div
        class="w-full md:w-1/2 md:pl-4"
        :class="{ 'lg:w-11/24': !isModal }"
      >
        <default-campaign-general-info
          :campaign-data="campaignData"
          :user-campaign-option="userCampaignOption"
          :total-custom-option-fee="totalCustomOptionFee"
        />

        <default-campaign-combo-box
          v-if="campaignData.system_type === 'combo'"
          class="mt-3"
          :campaign="campaignData"
          :user-campaign-option="userCampaignOption"
          @submit="addComboToCart"
        />

        <default-campaign-product-list
          v-if="campaignData.products && campaignData.system_type !== 'combo'"
          class="mb-3"
          :is-modal="isModal"
          :products="campaignData.products"
          :current-product="userCampaignOption.currentProduct"
          :is-dropdown-type="isShowProductListDropdown"
          :campaign-slug="campaignData.slug"
          :current-options="userCampaignOption.currentOptions"
          :campaign-system="campaignData.system_type"
          :campaign-thumb-url="campaignData.thumb_url"
          @update-product="updateSelectProduct"
        />

        <default-campaign-option-list
          v-if="campaignData.system_type !== 'combo'"
          :is-modal="isModal"
          :option-list="userCampaignOption.optionList"
          :current-product="userCampaignOption.currentProduct"
          :campaign-slug="campaignData.slug"
          :current-options="userCampaignOption.currentOptions"
          :option-error="userCampaignOption.optionError"
          @click="useTracking().customTracking({
            event: 'interact',
            data: {
              action: 'option_click'
            }
          })"
          @update-option="updateOption"
        />
        <client-only>
          <lazy-default-campaign-personalize-custom-options
            v-if="userCampaignOption.currentProduct?.personalized === 0 && userCampaignOption.currentProduct && userCampaignOption.currentProduct?.custom_options"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="userCampaignOption.currentProduct.template_custom_options"
            :common-options="userCampaignOption.currentProduct.common_options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :user-custom-options="userCustomOptions"
            :extra-custom-fee="extraCustomFee"
            :current-options="userCampaignOption.currentOptions"
            :selected-color="userCampaignOption.currentOptions.color"
            :current-product-thumb-url="userCampaignOption.currentProduct?.thumb_url"
            :current-product-id="userCampaignOption.currentProduct?.id"
            @required-value="requiredValue"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
          />
          <lazy-default-campaign-personalize-custom
            v-if="userCampaignOption.currentProduct?.personalized === 1"
            ref="personalizeCustom"
            :is-modal="isModal"
            :product="userCampaignOption.currentProduct"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-personalize-custom="updatePersonalizeCustom"
            @select-custom-design="selectCustomDesign"
            @show-design="showDesignHandler"
          />

          <lazy-default-campaign-personalize-pb
            v-if="userCampaignOption.currentProduct?.personalized === 2"
            ref="personalizePB"
            :is-modal="isModal"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @show-design="isShowDesign = true"
          />

          <lazy-default-campaign-personalize-custom-options
            v-if="((userCampaignOption.currentProduct?.personalized === 3 || userCampaignOption.currentProduct?.system_type === 'custom' || userCampaignOption.currentProduct?.system_type === 'mockup') && (campaignData.options || campaignData.common_options)) || (isAICampaign && shouldShowPersonalizeOptions)"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="campaignData.options"
            :common-options="campaignData.common_options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :user-custom-options="userCustomOptions"
            :user-common-options="userCommonOptions"
            :extra-custom-fee="extraCustomFee"
            :campaign-data="campaignData"
            :selected-color="userCampaignOption.currentOptions.color"
            :current-product-thumb-url="userCampaignOption.currentProduct?.thumb_url"
            :current-product-id="userCampaignOption.currentProduct?.id"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
            @confirm-design="addToCart(true)"
          />

          <p class="my-4">
            <strong>{{ $t('Quantity') }} </strong>
            <span v-if="userCampaignOption.currentVariant?.out_of_stock" class="text-red-500 ml-2 font-weight-500">
              {{ $t('Out of stock') }}
            </span>
          </p>
          <div class="flex gap-1">
            <div class="text-center">
              <common-dropdown
                class="w-full"
                dropdown-id="quantityDropdown"
                data-test-id="qty-btn"
                btn-class="btn-border h-12 w-full w-16"
                @click="useTracking().customTracking({
                  event: 'interact',
                  data: {
                    action: 'change_quantity'
                  }
                })"
              >
                <span>
                  {{ userCampaignOption.quantity }}
                </span>
                <template #content>
                  <div
                    v-for="item in 50"
                    :key="item"
                    class="min-w-[50vw] md:min-w-16 btn-text px-4"
                    :value="item"
                    :class="{ 'bg-primary text-white': userCampaignOption.quantity === item }"
                    data-test-id="qty-btn-value"
                    @click="userCampaignOption.quantity = item"
                  >
                    {{ item }}
                  </div>
                </template>
              </common-dropdown>
            </div>
            <div
              class="flex w-full transition-all"
              :class="{
                'p-1 border w-full bg-white z-1 bottom-fixed': (isModal || !isHiddenBottomButton)
              }"
            >
              <button
                v-if="isModal"
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock)"
                class="<md:hidden mr-0.5 uppercase font-bold btn-border-fill text-xl w-full h-12"
                data-test-id="buy-it-now"
                @click="isAddAllToCart = false; addToCart(false, true)"
              >
                {{ $t('Buy it now') }}
              </button>
              <button
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock) || (campaignData.system_type === 'combo' && !comboCartStore.hasSelected)"
                class="uppercase font-bold btn-fill text-xl w-full h-12"
                :class="isModal ? 'md:ml-0.5' : 'md:(ml-2)'"
                dusk="add-to-cart-button"
                data-test-id="campaign-add-to-cart"
                @click="isAddAllToCart = false; addToCart()"
              >
                <common-loading-dot v-if="isLoading" />
                <span v-else><i class="icon-sen-cart-plus mr-2 font-bold" />{{ $t('Add to cart') }}</span>
              </button>
            </div>
          </div>

          <template v-if="!isModal">
            <div v-if="productStats?.add_to_cart || productStats?.visit" class="mt-3 flex items-center">
              <span><i class="text-4xl icon-sen-cart-arrow-down" /></span>
              <div class="pl-2">
                <span class="font-medium">{{ $t('Other people want this') }}. </span>
                <span v-if="productStats?.add_to_cart"> {{ $t('There are value people have this in cart right now', { value: productStats.add_to_cart }) }}.</span>
                <span v-else-if="productStats?.visit"> {{ $t('There are value people viewing this', { value: productStats.visit }) }}.</span>
              </div>
            </div>
          </template>
        </client-only>
        <template v-if="!isModal">
          <div
            v-if="storeInfo().store_type !== 'google_ads' && storeInfo().show_payment_button"
            class="border border-[#ced4da] border-2 px-4 mt-8 flex flex-wrap justify-center"
          >
            <div class="bg-white px-6" style="transform: translateY(-50%);">
              {{ $t('GUARANTEED:') }}<strong class="pl-1">{{ $t('SAFE CHECKOUT') }}</strong>
            </div>
            <common-image
              img-class="mt-2 mb-5 w-full"
              :image="{ path: `images/safe_badge.webp` }"
              alt="safe_checkout"
            />
          </div>
          <client-only>
            <lazy-default-campaign-bundle-box
              v-if="bundleProduct?.length && !storeInfo().disable_promotion"
              ref="bundleBox"
              class="mt-3"

              :bundle-product="bundleProduct"
              :user-campaign-option="userCampaignOption"
              :current-bundle-product="currentBundleProduct"

              :total-bundle-discount="totalBundleDiscount"
              :save-bundle-discount="saveBundleDiscount"
              :total-custom-option-fee="totalCustomOptionFee"
              :campaign="campaignData"

              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'bundle_box'
                }
              })"
              @select-bundle-product="currentBundleProduct = $event ? shallowReactive($event) : false"
              @submit="isAddAllToCart = true; addToCart(false, false, bundleProduct[0].personalized === 3 || bundleProduct[0].personalized === 5)"
              @modal-submit="isAddAllToCart ? addToCart() : ''; currentBundleProduct = undefined"
              @disable-add-all-to-cart="isAddAllToCart = false"
              @reset-bundle-product="productIdExclude => refetchDataBundle([productIdExclude])"
            />

            <lazy-common-campaign-description
              class="mt-3"
              :data-description="dataDescription"
              :data-product-detail="dataProductDetail"
              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'description_box'
                }
              })"
            />
            <lazy-default-common-promotions-list
              v-if="campaignPromotions?.length && !storeInfo().disable_promotion"
              :promotions-list="campaignPromotions"
              class="mt-3"
            />
          </client-only>
        </template>
      </div>
    </div>
    <template v-if="!isModal">
      <client-only>
        <div v-if="campaignData.collections?.length && !storeInfo().disable_related_collection" class="mt-4 flex gap-3">
          <span class="font-medium hidden md:block whitespace-nowrap">{{ $t('Related Collections') }}:</span>
          <div class="flex gap-3 overflow-x-auto">
            <lazy-common-collection-item
              v-for="(collection, index) in campaignData.collections"
              :key="index"
              :value="collection.name"
              :slug="`/collection/${collection.slug}`"
            />
          </div>
        </div>

        <lazy-common-product-carousel
          v-if="relatedProducts?.length && !storeInfo().disable_related_product"
          class="container mt-10"
          title-class="text-center uppercase text-3xl"
          title="Frequently bought together"
          :products="relatedProducts"
          static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
        >
          <template #default="{ product, index }">
            <default-common-product-item :product="product" :index="index" class="inline-block" />
          </template>
        </lazy-common-product-carousel>
        <div
          class="mt-3 flex justify-center md:hidden"
          :class="{ 'mt-24': isAICampaign }"
        >
          <default-campaign-share-box />
        </div>
        <div class="mt-3 flex justify-center md:hidden">
          <div v-if="campaignData.seller && storeInfo().id === 1">
            <span class="mr-1 text-gray-400">{{ $t('Designed and Sold by') }}</span>
            <a v-if="campaignData.seller.slug" class="text-primary" :href="localePath(`/artist/${campaignData.seller.slug}`)">{{ campaignData.seller.nickname }}</a>
            <span class="text-primary">{{ campaignData.seller.nickname }}</span>
          </div>
        </div>

        <lazy-default-product-review-campaign-review-section
          v-if="storeInfo().product_review_display !== 'disable'"
          class="mt-10"
          :current-product="userCampaignOption.currentProduct"
        />

        <div class="mt-3 flex justify-center">
          <nuxt-link :to="`/report?campaign=${campaignData.slug}`" class="flex gap-1 btn-text">
            <span><i class="icon-sen-alert-outline" /></span>
            <span>{{ $t('Report a policy violation') }}?</span>
          </nuxt-link>
        </div>
      </client-only>
    </template>

    <client-only>
      <lazy-default-campaign-modal-add-to-cart
        ref="modalAddToCart"
        :related-cart="relatedCartProducts"
      />
      <lazy-default-campaign-modal-select-size
        ref="modalSelectSize"
        :current-product="userCampaignOption.currentProduct"
        :option-list="userCampaignOption.optionList"
        @open-size-guide="uiManager().updateSizeGuideData(userCampaignOption.currentProduct, true)"
        @select-size="updateOption({ key: 'size', value: $event }); addToCart()"
      />
      <lazy-default-campaign-modal-confirm-design
        ref="modalConfirmDesign"
        :campaign-data="campaignData"
        @confirm-design="addToCart(true)"
      />
    </client-only>
  </main>
  <main v-else class="py-10 h-60vh">
    <div class="mt-5 pt-5 text-3xl text-center">
      <h2> {{ $t('This campaign was taken down due to a content violation') }} </h2>
    </div>
  </main>
</template>
