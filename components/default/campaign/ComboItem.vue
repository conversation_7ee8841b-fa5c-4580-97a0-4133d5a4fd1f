<script lang="ts" setup>
interface Item {
  id: string
  product_name: string
  thumb_url: string
  options: Record<string, string>
}
interface Props {
  isNoShip: boolean
  thumb_url: string
  title: string,
  items: Item[],
  quantity: number,
  price: string,
  oldPrice?: string,
}
withDefaults(defineProps<Props>(), {
  isNoShip: false
})
</script>

<template>
  <div
    class="flex justify-between"
    :class="{'bg-[#fff6e6]': isNoShip}"
  >
    <div class="py-2 flex products-center gap-2">
      <div class="relative w-fit">
        <common-image
          img-class="w-22 pr-2"
          :image="{
            path: thumb_url,
            type: 'list',
          }"
          :alt="title"
        />
      </div>
      <div class="w-full max-w-[calc(100%-90px)] relative">
        <div class="text-lg font-medium mb-2 mt-1">
          {{ title }}
        </div>
        <div class="space-y-2 md:space-y-0">
          <div v-for="item in items" :key="item.id" class="flex">
            <common-image
              img-class="max-w-14 mr-2 btn border md:border-none"
              :image="{
                path: item?.thumb_url,
                color: item?.options?.color
              }"
              :alt="item?.product_name"
            />
            <div>
              <div class="text-sm text-nowrap font-medium" style="width: 200px;">
                {{ quantity }} x {{ item.product_name }}
              </div>
              <div class="text-xs text-nowrap text-gray-400 font-medium">
                <span v-if="item.options">
                  <span
                    v-for="(key, optionIndex) in Object.keys(item.options)"
                    :key="optionIndex"
                    class="uppercase"
                  >
                    {{ $t(item.options[key]) }}
                    <span v-if="optionIndex + 1 !== Object.keys(item.options).length">&nbsp;/&nbsp;</span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isNoShip" class="text-sm text-[#ffc107]">
          <i class="icon-sen-alert mr-2" />
          <span>{{ $t("This product variant is not available. Please try other option/product.") }}</span>
          <span
            class="underline cursor-pointer text-red-500 float-right px-1"
            @click="$emit('removeProduct')"
          ><i class="icon-sen-delete" /></span>
          <nuxt-link
            :to="localePath('/cart')"
            :title="$t('Edit')"
          >
            <span class="underline cursor-pointer text-primary float-right px-1">{{ $t('Edit') }}</span>
          </nuxt-link>
        </div>
      </div>
    </div>
    <div class="px-1 mb-1 font-medium mt-4">
      <div v-if="oldPrice" class="text-gray-500">
        <del>{{ oldPrice  }}</del>
      </div>
      <div>
        <span>{{ price }}</span>
      </div>
    </div>
  </div>
</template>
