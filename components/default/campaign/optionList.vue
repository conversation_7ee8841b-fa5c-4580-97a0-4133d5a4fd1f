<script lang="ts" setup>
const { currentOptions, campaignSlug, currentProduct } = defineProps({
  optionList: {
    default: undefined,
    type: Object as PropType<OptionsList>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  optionError: {
    default: '',
    type: [String, Boolean]
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const hasSizeGuide = computed(() => {
  return generalSettings().hasSizeGuide(currentProduct?.template_id)
})

const getOptionUrl = computed(() => {
  return (key: string, value: string) => {
    const newQuery = { ...useRoute().query }
    newQuery[key] = value
    return getCampaignUrl({
      campaignSlug,
      productName: currentProduct?.name,
      query: newQuery
    })
  }
})
</script>

<template>
  <client-only>
    <div v-if="optionList && currentOptions">
      <div v-for="(key, index) in Object.keys(optionList as OptionsList)" :key="index" class="mb-3">
        <span v-if="storeInfo().option_label_enable || (currentProduct && currentProduct?.full_printed === 5)" class="font-semibold capitalize">
          {{ $t(key.replace(/_/g, ' ')) }}
        </span>
        <div
          v-if="isModal"
          class="w-full overflow-auto flex md:flex-wrap"
          :class="{ 'animate__animated animate__headShake': optionError === key }"
        >
          <template v-if="key === 'color'">
            <button
              v-for="(value, optionIndex) in optionList[key]"
              :key="`${key}-${optionIndex}`"
              @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
            >
              <common-color-item
                :color="value"
                :title="value"
                size="xl"
                :active="value === currentOptions[key]"
                data-test-id="product-color"
                sp-action="change_color"
              />
            </button>
          </template>
          <template v-else>
            <button
              v-for="(value, optionIndex) in optionList[key]"
              :key="`${key}-${optionIndex}`"
              @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
            >
              <common-option-item
                :value="value"
                :sp-action="`change_${key}`"
                :data-test-id="`product-change-${key}`"
                :active="value === currentOptions[key]"
              />
            </button>
          </template>
        </div>
        <div
          v-else
          class="w-full overflow-auto flex md:flex-wrap"
          :class="{ 'animate__animated animate__headShake': optionError === key }"
        >
          <template v-if="key === 'color'">
            <nuxt-link
              v-for="(value, optionIndex) in optionList[key]"
              :key="`${key}-${optionIndex}`"
              :to="getOptionUrl(key, value)"
              @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
            >
              <common-color-item
                :color="value"
                :title="value"
                size="xl"
                :active="value === currentOptions[key]"
                data-test-id="product-color"
                sp-action="change_color"
              />
            </nuxt-link>
          </template>
          <template v-else>
            <nuxt-link
              v-for="(value, optionIndex) in optionList[key]"
              :key="`${key}-${optionIndex}`"
              :to="getOptionUrl(key, value)"
              @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
              @touchstart="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
            >
              <common-option-item
                :value="value"
                :sp-action="`change_${key}`"
                :data-test-id="`product-change-${key}`"
                :active="value === currentOptions[key]"
              />
            </nuxt-link>
          </template>
        </div>
        <div
          v-if="key === 'size' && hasSizeGuide"
          class="btn-text cursor-pointer mt-2 text-blue-400 inline-block"
          @click="uiManager().updateSizeGuideData(currentProduct, true)"
        >
          <span class="font-medium">
            {{ $t('Size guide') }}
          </span>
          <span>
            <i class="icon-sen-ruler ml-1" />
          </span>
        </div>
      </div>
    </div>
  </client-only>
</template>
