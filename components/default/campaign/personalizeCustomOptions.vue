<script lang="ts" setup>
import { onMounted } from 'vue'
import { $api, $method, CUSTOM_OPTION_TYPE, PERSONALIZED_TYPE, SYSTEM_CAMPAIGN_TYPE } from '~/utils/constant'

interface PreviewImageData {
  url: string | null
  b64_json?: string
}

const props = defineProps({
  customOptions: {
    default: undefined,
    type: Object as PropType<PersonalizeCustomOptions>
  },
  commonOptions: {
    default: undefined,
    type: Object as PropType<PersonalizeCommonOptions>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  userCustomOptions: {
    default: undefined,
    type: Object as PropType<Array<Array<PersonalizeCustomOptionsItem>>>
  },
  userCommonOptions: {
    default: undefined,
    type: Object as PropType<Array<Array<PersonalizeCustomOptionsItem>>>
  },
  groupCustomOptionsQuantity: {
    default: 1,
    type: Number
  },
  extraCustomFee: {
    default: 0,
    type: Number
  },
  isAddToCart: {
    default: undefined,
    type: Boolean
  },
  isModal: {
    default: false,
    type: Boolean
  },

  headingStyle: {
    default: 'font-medium',
    type: String
  },
  containerClass: {
    default: 'border bg-[#f5f5f5]',
    type: String
  },
  campaignData: {
    default: undefined,
    type: Object
  },
  selectedColor: {
    default: '',
    type: String
  },
  currentProductThumbUrl: {
    default: '',
    type: String
  },
  currentProductId: {
    default: null,
    type: Number
  }
})

const emit = defineEmits(['updateCommonOptions', 'updateCustomOptions', 'updateGroupCustomOptionsQuantity', 'confirmDesign'])

const { $fetchWrite } = useNuxtApp()

const toggleOptionMobile = ref(false)
const isLoading = ref(false)
const errorMessage = ref('')
const modalConfirmDesign = ref<InstanceType<typeof import('~/components/default/campaign/modalConfirmDesign.vue').default>>()
const progressPercentage = ref(0)
const timerInterval = ref<any>(null)
// Store the last personalization values, color, product ID, and generated image to avoid unnecessary API calls
const lastPersonalizationValues = ref<Record<string, any> | null>(null)
const lastSelectedColor = ref<string | null>(null)
const lastSelectedProductId = ref<number | null>(null)
const lastGeneratedImage = ref<string | null>(null)

// File upload related refs
const uploadedFile = ref<File | null>(null)
const fileSelector = ref()
// Store last uploaded file for caching
const lastUploadedFile = ref<File | null>(null)

// Check if this is an AI campaign with personalization
const isAICampaign = computed(() => {
  return (props.campaignData?.system_type === 'ai_mockup'
    || props.campaignData?.system_type === SYSTEM_CAMPAIGN_TYPE.AI_MOCKUP)
  && (props.campaignData?.personalized === 3
    || props.campaignData?.personalized === PERSONALIZED_TYPE.CUSTOM_OPTION)
})

// Parse campaign attributes to get customer upload options
const customerUploadOptions = computed(() => {
  if (!props.campaignData?.attributes)
    return null

  try {
    const attributes = JSON.parse(props.campaignData.attributes)
    return attributes.customer_upload_options || null
  }
  catch (error) {
    console.error('Error parsing campaign attributes:', error)
    return null
  }
})

// Check if customer upload is allowed
const allowCustomerUpload = computed(() => {
  return isAICampaign.value && customerUploadOptions.value?.allow_customer_upload_image === true
})

// Get upload label
const uploadLabel = computed(() => {
  return customerUploadOptions.value?.customer_upload_image_label || 'Upload photo'
})

// Check if we have a valid uploaded file
const hasUploadedFile = computed(() => {
  return uploadedFile.value !== null
})

// Get thumbnail file ID from campaign images
const getThumbnailFileId = computed(() => {
  if (!props.campaignData?.images || !props.currentProductThumbUrl) {
    return null
  }

  const matchingImage = props.campaignData.images.find((image: ImageData) =>
    image.file_url === props.currentProductThumbUrl
  )

  return matchingImage?.id || null
})

// Check if component should be visible (has content to show)
const shouldShowComponent = computed(() => {
  // Show if there are common options
  if (props.commonOptions?.options && props.commonOptions.options.length > 0) {
    return true
  }

  // Show if there are custom options
  if (props.customOptions?.options && props.customOptions.options.length > 0) {
    return true
  }

  // Show if it's AI campaign with customer upload enabled
  if (allowCustomerUpload.value) {
    return true
  }

  return false
})

// Check if any personalization values have been entered
const hasPersonalizationValues = computed(() => {
  // For AI campaigns with customer upload, check if file is uploaded
  if (allowCustomerUpload.value) {
    return hasUploadedFile.value
  }

  if (!props.userCustomOptions || props.userCustomOptions.length === 0) {
    return false
  }

  // Check if any custom option has a value
  return props.userCustomOptions[0].some((option) => {
    if (typeof option.value === 'string') {
      return option.value.trim() !== ''
    }
    return !!option.value
  })
})

// Initialize default values for AI campaign on component creation
onMounted(() => {
  if (isAICampaign.value && props.userCustomOptions && props.userCustomOptions.length > 0 && props.customOptions?.options) {
    props.userCustomOptions[0].forEach((option, index) => {
      // If the option has no value, set the default value from the API (option.value)
      if ((!option.value || option.value === '') && props.customOptions?.options[index]) {
        // We're not directly modifying the option.value here to avoid reactivity issues
        // Instead, we'll emit an event to update it properly
        emit('updateCustomOptions', {
          groupNumber: 1,
          optionIndex: index,
          value: props.customOptions.options[index].value,
          type: 1
        })
      }
    })
  }
})

function onInputFocus() {
  toggleOptionMobile.value = true
}

function handleUpdateCustomOptions(quantity: number, index: number, value: string | File, type: number) {
  emit('updateCustomOptions', {
    groupNumber: quantity,
    optionIndex: index,
    value,
    type
  })
}

function handleUpdateCommonOptions(index: number, value: string | File, type: number) {
  emit('updateCommonOptions', {
    optionIndex: index,
    value,
    type
  })
}

function validateFile(file: File): string | null {
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    return 'Please upload a JPEG, PNG, or WebP image file'
  }

  // Check file size (25MB = 25 * 1024 * 1024 bytes)
  const maxSize = 25 * 1024 * 1024
  if (file.size > maxSize) {
    return 'File size must be less than 25MB'
  }

  return null
}

// Handle file selection (just store the file, don't validate yet)
function handleFileChange(files: FileList) {
  if (files.length === 0) {
    uploadedFile.value = null
    return
  }

  uploadedFile.value = files[0]
  errorMessage.value = '' // Clear any previous errors
}

function shouldUseCachedResult(): boolean {
  if (!lastGeneratedImage.value)
    return false

  const currentColor = props.selectedColor || 'white'
  const currentProductId = props.currentProductId || props.campaignData?.id || null

  // For image upload campaigns, check if the uploaded file matches
  if (allowCustomerUpload.value) {
    return (
      lastUploadedFile.value === uploadedFile.value
      && lastSelectedColor.value === currentColor
      && lastSelectedProductId.value === currentProductId
    )
  }

  // For text personalization campaigns, check personalization values
  const personalizationValues: Record<string, any> = {}
  if (props.userCustomOptions && props.userCustomOptions.length > 0) {
    props.userCustomOptions[0].forEach((option, index) => {
      if (props.customOptions?.options[index]) {
        const label = props.customOptions.options[index].label
        personalizationValues[label] = option.value
      }
    })
  }

  const valuesMatch = lastPersonalizationValues.value
    ? JSON.stringify(personalizationValues) === JSON.stringify(lastPersonalizationValues.value)
    : false

  return (
    valuesMatch
    && lastSelectedColor.value === currentColor
    && lastSelectedProductId.value === currentProductId
  )
}

async function openPreviewModal() {
  // Don't proceed if there are no personalization values
  if (!hasPersonalizationValues.value) {
    return
  }

  // For image upload campaigns, validate the file first
  if (allowCustomerUpload.value && uploadedFile.value) {
    const validationError = validateFile(uploadedFile.value)
    if (validationError) {
      errorMessage.value = validationError
      return
    }
  }

  // Check if we can use cached result
  if (shouldUseCachedResult()) {
    modalConfirmDesign.value!.isShowModal = [lastGeneratedImage.value!]
    return
  }

  try {
    isLoading.value = true
    errorMessage.value = ''
    progressPercentage.value = 0

    // Get personalization values from all custom options
    const personalizationValues: Record<string, any> = {}

    if (props.userCustomOptions && props.userCustomOptions.length > 0) {
      props.userCustomOptions[0].forEach((option, index) => {
        if (props.customOptions?.options[index]) {
          const label = props.customOptions.options[index].label
          personalizationValues[label] = option.value
        }
      })
    }

    // For image upload campaigns without personalization values, still proceed if file is uploaded
    if (allowCustomerUpload.value && Object.keys(personalizationValues).length === 0 && !uploadedFile.value) {
      errorMessage.value = 'Please select an image file first'
      isLoading.value = false
      return
    }

    // If we have no personalization values and no uploaded file, use the default image
    if (Object.keys(personalizationValues).length === 0 && !uploadedFile.value && !props.campaignData) {
      // Show default image in confirm modal
      const { $imgUrl } = useNuxtApp()
      // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
      const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
      modalConfirmDesign.value!.isShowModal = [
        thumbUrl
          ? $imgUrl({
              path: thumbUrl,
              type: 'full'
            })
          : ''
      ]
      isLoading.value = false
      return
    }

    // If values don't match or we don't have a cached image, start timer for API call
    if (timerInterval.value) {
      clearInterval(timerInterval.value)
    }

    timerInterval.value = setInterval(() => {
      // Increment progress by approximately 0.28% every 100ms to reach 99% in 35 seconds
      // 99% / (35 seconds * 10 intervals per second) = 0.28% per interval
      const increment = 99 / (35 * 10)
      progressPercentage.value = Math.min(progressPercentage.value + increment, 99)
    }, 100)

    try {
      const { $imgUrl } = useNuxtApp()

      // Create FormData to send the file and other data
      const formData = new FormData()

      // Always use current product thumbnail as edit_image (image to edit)
      const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
      const imageUrl = $imgUrl({
        path: thumbUrl,
        type: 'full'
      })
      const imageResponse = await fetch(imageUrl)

      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.status} ${imageResponse.statusText}`)
      }

      // Get the image as a blob
      const imageBlob = await imageResponse.blob()

      // Create a File object from the blob
      const imageFile = new File([imageBlob], 'edit_image.png', { type: imageBlob.type })
      formData.append('edit_image', imageFile)

      // If customer uploaded an image, add it as customer_upload_image
      if (allowCustomerUpload.value && uploadedFile.value) {
        formData.append('customer_upload_image', uploadedFile.value)
        formData.append('is_customer_upload_image', 'true')
      }
      else {
        formData.append('is_customer_upload_image', 'false')
      }

      formData.append('campaign_id', props.campaignData?.id?.toString() || '')
      // Use current product ID if available, otherwise fall back to campaign product_id or campaign_id
      formData.append('product_id', (props.currentProductId || props.campaignData?.product_id || props.campaignData?.id)?.toString() || '')
      formData.append('personalization_values', JSON.stringify(personalizationValues))
      formData.append('color', props.selectedColor || props.campaignData?.color || 'white')

      // Add seller_id from campaign data
      if (props.campaignData?.seller_id) {
        formData.append('seller_id', props.campaignData.seller_id.toString())
      }

      // Add thumbnail file ID if available
      if (getThumbnailFileId.value) {
        formData.append('thumbnail_file_id', getThumbnailFileId.value.toString())
      }

      // for testing
      // const result = await $fetchWrite<ResponseData<{ data: Array<PreviewImageData> }>>('http://backend-apis.test/public/ai-mockup/preview', {
      //   method: $method.post,
      //   body: formData
      // })

      const result = await $fetchWrite<ResponseData<{ data: Array<PreviewImageData> }>>($api.API_AI_MOCKUP_PREVIEW, {
        method: $method.post,
        body: formData
      })

      if (result.success && result.data && result.data.data && result.data.data.length > 0) {
        progressPercentage.value = 100

        const imageData = result.data.data[0]

        // Check if we have a base64 encoded image
        if (imageData.b64_json) {
          // Convert base64 to data URL for display
          const dataUrl = `data:image/webp;base64,${imageData.b64_json}`
          modalConfirmDesign.value!.isShowModal = [dataUrl]

          // Store the personalization values, color, product ID, and generated image for future use
          if (allowCustomerUpload.value) {
            lastUploadedFile.value = uploadedFile.value
          }
          else {
            lastPersonalizationValues.value = { ...personalizationValues }
          }
          lastSelectedColor.value = props.selectedColor || props.campaignData?.color || 'white'
          lastSelectedProductId.value = props.currentProductId || props.campaignData?.product_id || props.campaignData?.id
          lastGeneratedImage.value = dataUrl
        }
        // Fallback to URL if available
        else if (imageData.url) {
          progressPercentage.value = 100

          modalConfirmDesign.value!.isShowModal = [imageData.url]

          // Store the personalization values, color, product ID, and generated image for future use
          lastPersonalizationValues.value = { ...personalizationValues }
          lastSelectedColor.value = props.selectedColor || props.campaignData?.color || 'white'
          lastSelectedProductId.value = props.currentProductId || props.campaignData?.product_id || props.campaignData?.id
          lastGeneratedImage.value = imageData.url
        }
        // If neither is available, use default image
        else {
          // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
          const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
          modalConfirmDesign.value!.isShowModal = [
            thumbUrl
              ? $imgUrl({
                  path: thumbUrl,
                  type: 'full'
                })
              : ''
          ]
          errorMessage.value = 'No preview image data available'
        }
      }
      else {
        // If there's an error, use the default image
        // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
        const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
        modalConfirmDesign.value!.isShowModal = [
          thumbUrl
            ? $imgUrl({
                path: thumbUrl,
                type: 'full'
              })
            : ''
        ]
        errorMessage.value = result.message || 'Could not generate preview image'
      }
    }
    catch (fetchError) {
      console.error('Error fetching or processing image:', fetchError)

      // Fallback to the original method if fetching the image fails
      const { $imgUrl } = useNuxtApp()
      // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
      const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
      const fallbackBody: any = {
        edit_image: $imgUrl({
          path: thumbUrl,
          type: 'full'
        }),
        campaign_id: props.campaignData?.id || '',
        // Use current product ID if available, otherwise fall back to campaign product_id or campaign_id
        product_id: props.currentProductId || props.campaignData?.product_id || props.campaignData?.id || '',
        personalization_values: JSON.stringify(personalizationValues),
        color: props.selectedColor || props.campaignData?.color || 'white'
      }

      // Add seller_id from campaign data
      if (props.campaignData?.seller_id) {
        fallbackBody.seller_id = props.campaignData.seller_id
      }

      // Add thumbnail file ID if available
      if (getThumbnailFileId.value) {
        fallbackBody.thumbnail_file_id = getThumbnailFileId.value
      }

      // For fallback, we don't have customer upload image, so always false
      fallbackBody.is_customer_upload_image = 'false'

      const result = await $fetchWrite<ResponseData<{ data: Array<PreviewImageData> }>>($api.API_AI_MOCKUP_PREVIEW, {
        method: $method.post,
        body: fallbackBody
      })

      if (result.success && result.data && result.data.data && result.data.data.length > 0) {
        progressPercentage.value = 100

        const imageData = result.data.data[0]

        // Check if we have a base64 encoded image
        if (imageData.b64_json) {
          // Convert base64 to data URL for display
          const dataUrl = `data:image/webp;base64,${imageData.b64_json}`
          modalConfirmDesign.value!.isShowModal = [dataUrl]

          // Store the personalization values, color, product ID, and generated image for future use
          lastPersonalizationValues.value = { ...personalizationValues }
          lastSelectedColor.value = props.selectedColor || props.campaignData?.color || 'white'
          lastSelectedProductId.value = props.currentProductId || props.campaignData?.product_id || props.campaignData?.id
          lastGeneratedImage.value = dataUrl
        }
        // Fallback to URL if available
        else if (imageData.url) {
          progressPercentage.value = 100

          modalConfirmDesign.value!.isShowModal = [imageData.url]

          // Store the personalization values, color, product ID, and generated image for future use
          lastPersonalizationValues.value = { ...personalizationValues }
          lastSelectedColor.value = props.selectedColor || props.campaignData?.color || 'white'
          lastSelectedProductId.value = props.currentProductId || props.campaignData?.product_id || props.campaignData?.id
          lastGeneratedImage.value = imageData.url
        }
        // If neither is available, use default image
        else {
          // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
          const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
          modalConfirmDesign.value!.isShowModal = [
            thumbUrl
              ? $imgUrl({
                  path: thumbUrl,
                  type: 'full'
                })
              : ''
          ]
          errorMessage.value = 'No preview image data available'
        }
      }
      else {
        // If there's an error, use the default image
        // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
        const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
        modalConfirmDesign.value!.isShowModal = [
          thumbUrl
            ? $imgUrl({
                path: thumbUrl,
                type: 'full'
              })
            : ''
        ]
        errorMessage.value = result.message || 'Could not generate preview image'
      }
    }
  }
  catch (error) {
    console.error('Error generating preview:', error)
    const { $imgUrl } = useNuxtApp()
    // Use the current product's thumbnail URL if available, otherwise fall back to campaign thumb_url
    const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url
    modalConfirmDesign.value!.isShowModal = [
      thumbUrl
        ? $imgUrl({
            path: thumbUrl,
            type: 'full'
          })
        : ''
    ]
    errorMessage.value = 'An error occurred while generating the preview image'
  }
  finally {
    isLoading.value = false

    // Clear timer interval
    if (timerInterval.value) {
      clearInterval(timerInterval.value)
      timerInterval.value = null
    }
  }
}

defineExpose({ toggleOptionMobile, openPreviewModal })
</script>

<template>
  <div
    v-if="shouldShowComponent"
    class="relative w-full transition-all p-3 personalize"
    :class="{
      [containerClass]: true,
      '<md:(fixed bottom-0 left-0 z-3) custom-opt-vari-position': !isModal
    }"
  >
    <button
      class="btn center-flex p-2"
      :class="{
        '<md:(absolute bg-[#f5f5f5] -top-10 right-5 z-1 py-1 border-t border-x)': !isModal
      }"
      @click="toggleOptionMobile = !toggleOptionMobile"
    >
      <span class="uppercase mr-1" :class="headingStyle">{{ $t('Customize') }}</span>
      <i v-if="$viewport.isGreaterOrEquals(VIEWPORT.tablet) && !isModal" class="icon-sen-text-box-multiple-outline" />
      <i v-else class="text-2xl block icon-sen-chevron-up transition-transform transform animation" :class="{ 'rotate-180': toggleOptionMobile }" />
    </button>
    <common-collapse
      :when="isModal || toggleOptionMobile || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
      class="transition-default p-2"
    >
      <template v-for="(option, index) in commonOptions?.options" :key="index">
        <default-common-input
          v-if="option.type === CUSTOM_OPTION_TYPE.text"
          :id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
          :input-type="2"
          :enable-text-counter="true"
          :model-value="(userCommonOptions && userCommonOptions[0]?.[index]?.value as string)"
          :state="!!((userCommonOptions && userCommonOptions[0]?.[index].value) || (userCommonOptions && userCommonOptions.length > 0 && (userCommonOptions[0]?.[index].unrequired === true || userCommonOptions[0]?.[index].unrequired === 1)))"
          :is-required="(userCommonOptions && (userCommonOptions[0][index].unrequired === true || userCommonOptions[0][index].unrequired === 1))"
          :max-length="option?.max_length ? parseInt(String(option.max_length)) : undefined"
          :label="`${$t(option.label)}`"
          :placeholder="userCommonOptions && userCommonOptions[0][index]?.placeholder ? $t(userCommonOptions?.[0][index]?.placeholder as string) : $t(option.label)"
          @update:model-value="handleUpdateCommonOptions(index, $event, 1)"
          @change:model-value="handleUpdateCommonOptions(index, $event, 2)"
          @focus="onInputFocus"
        />
        <div v-if="option.type === CUSTOM_OPTION_TYPE.dropdown" :class="`mt-2 box_${option.type}_${index}`">
          <label
            v-if="(customOptions?.group.limit ?? 0) >= 1"
            class="label text-sm text-overflow-hidden w-full capitalize z-1"
            :class="{ 'text-sm': userCommonOptions && userCommonOptions.length && userCommonOptions[0][index].value }"
          >
            {{ $t(option.label) }}
          </label>
          <common-dropdown
            :input-type="2"
            :dropdown-id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
            :btn-class="`h-10 text-left relative border w-full mt-1 p-1.5 ${((userCommonOptions && userCommonOptions.length > 0 && userCommonOptions[0][index].value) || (userCommonOptions && userCommonOptions.length && (userCommonOptions[0][index].unrequired === true || userCommonOptions[0][index].unrequired === 1))) ? '!border-green-500' : '!border-red-500'}`"
            dropdown-class="w-full"
          >
            <div class="flex items-center px-2 min-w-20">
              <template v-if="userCommonOptions && userCommonOptions[0][index].value">
                {{ userCommonOptions && userCommonOptions.length && userCommonOptions[0][index].value }}
              </template>
              <template v-else-if="userCommonOptions">
                {{ `${$t('Select a')} ${$t(option.label)}` }}
              </template>
            </div>
            <template #content>
              <div
                v-for="optionDropdownItem in option.value"
                :key="optionDropdownItem"
                class="btn-text flex items-center px-4 w-full py-1"
                :class="{ 'bg-primary !text-contrast': userCommonOptions && userCommonOptions[0][index].value === optionDropdownItem }"
                @click="$emit('updateCommonOptions', {
                  optionIndex: index,
                  value: optionDropdownItem
                })"
              >
                {{ optionDropdownItem }}
              </div>
            </template>
          </common-dropdown>
        </div>

        <lazy-common-file-selector
          v-if="option.type === CUSTOM_OPTION_TYPE.image"
          :id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
          :key="userCommonOptions?.[0][index]?.value"
          :input-type="2"
          :file-upload="`${userCommonOptions && (userCommonOptions[0][index]?.value || '')}`"
          :state="userCommonOptions && (userCommonOptions[0][index]?.value?.length > 0 || userCommonOptions[0][index]?.unrequired === true || userCommonOptions[0][index].unrequired === 1)"
          :label="`${$t(option.label)}`"
          browse-text="Upload"
          class="mt-3 w-full"
          @on-change="handleUpdateCommonOptions(index, $event[0], 1)"
        />
      </template>
      <div
        class="px-3 <md:(overflow-x-hidden overflow-y-auto)"
        :class="{
          '<md:(max-h-70)': !isModal,
          'mt-4': commonOptions?.options?.length > 0
        }"
      >
        <label
          v-if="(customOptions?.group.limit ?? 0) > 1"
          class="left-0 top-[-25px] label transition-all duration-150 capitalize z-1 text-sm"
        >
          {{ $t('Number of') }} {{ customOptions?.group.name ? $t(customOptions?.group.name) : $t('custom') }}
        </label>
        <common-dropdown
          v-if="(customOptions?.group.limit || 1) > 1"
          dropdown-id="groupCustomOptionsQuantity"
          btn-class="h-10 relative border w-full p-1.5 mt-1"
          dropdown-class="w-full"
        >
          <div class="flex items-center px-2 min-w-20">
            {{ groupCustomOptionsQuantity }} {{ groupCustomOptionsQuantity > 1 && toValue(extraCustomFee) > 0 ? `(+${$formatPrice(toValue(extraCustomFee) * (groupCustomOptionsQuantity - 1))})` : '' }}
          </div>
          <template #content>
            <div
              v-for="quantity in customOptions?.group.limit"
              :key="quantity"
              class="btn-text flex items-center px-4 w-full py-1"
              :class="{ 'bg-primary !text-contrast': groupCustomOptionsQuantity === quantity }"
              @click="$emit('updateGroupCustomOptionsQuantity', quantity)"
            >
              {{ quantity }} {{ quantity > 1 && extraCustomFee > 0 ? `(+${$formatPrice(extraCustomFee * (quantity - 1))})` : '' }}
            </div>
          </template>
        </common-dropdown>
        <div v-for="quantity in groupCustomOptionsQuantity" :key="quantity" class="py-2">
          <hr v-if="(customOptions?.group.limit || 1) > 1" class="my-3">
          <template v-for="(option, index) in customOptions?.options" :key="index">
            <default-common-input
              v-if="option.type === CUSTOM_OPTION_TYPE.text"
              :id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
              :input-type="2"
              :enable-text-counter="true"
              :model-value="typeof (userCustomOptions && userCustomOptions[quantity - 1]?.[index]?.value) === 'string' ? (userCustomOptions && userCustomOptions[quantity - 1]?.[index]?.value as string) : (isAICampaign && typeof option.value === 'string' ? option.value : '') || ''"
              :state="!!((userCustomOptions && userCustomOptions[quantity - 1][index].value) || (userCustomOptions && userCustomOptions.length > 0 && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1)))"
              class="mt-3"
              :is-required="(userCustomOptions && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1))"
              :max-length="option?.max_length ? parseInt(String(option.max_length)) : undefined"
              :label="isAICampaign ? `${$t(option.label)}` : `${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}`"
              :placeholder="userCustomOptions && userCustomOptions[quantity - 1][index]?.placeholder ? $t(userCustomOptions?.[quantity - 1][index]?.placeholder as string) : $t(option.label)"
              @update:model-value="handleUpdateCustomOptions(quantity, index, $event, 1)"
              @change:model-value="handleUpdateCustomOptions(quantity, index, $event, 2)"
              @focus="onInputFocus"
            />

            <div v-if="option.type === CUSTOM_OPTION_TYPE.dropdown" :class="`mt-2 box_${option.type}_${quantity}_${index}`">
              <label
                v-if="(customOptions?.group.limit ?? 0) >= 1"
                class="label text-sm text-overflow-hidden w-full capitalize z-1"
                :class="{ 'text-sm': userCustomOptions && userCustomOptions.length && userCustomOptions[quantity - 1][index].value }"
              >
                {{ isAICampaign ? $t(option.label) : `${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}` }}
              </label>
              <common-dropdown
                :input-type="2"
                :dropdown-id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
                :btn-class="`h-10 text-left relative border w-full mt-1 p-1.5 ${((userCustomOptions && userCustomOptions.length > 0 && userCustomOptions[quantity - 1][index].value) || (userCustomOptions && userCustomOptions.length && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1))) ? '!border-green-500' : '!border-red-500'}`"
                dropdown-class="w-full"
              >
                <div class="flex items-center px-2 min-w-20">
                  <template v-if="userCustomOptions && userCustomOptions[quantity - 1][index].value">
                    {{ userCustomOptions && userCustomOptions.length && userCustomOptions[quantity - 1][index].value }}
                  </template>
                  <template v-else-if="isAICampaign && option.value">
                    {{ typeof option.value === 'string' ? option.value : '' }}
                  </template>
                  <template v-else-if="userCustomOptions">
                    {{ `${$t('Select a')} ${$t(userCustomOptions[quantity - 1][index].label)}` }}
                  </template>
                </div>
                <template #content>
                  <div
                    v-for="optionDropdownItem in option.value"
                    :key="optionDropdownItem"
                    class="btn-text flex items-center px-4 w-full py-1"
                    :class="{ 'bg-primary !text-contrast': userCustomOptions && userCustomOptions[quantity - 1][index].value === optionDropdownItem }"
                    @click="handleUpdateCustomOptions(quantity, index, optionDropdownItem, 1)"
                  >
                    {{ optionDropdownItem }}
                  </div>
                </template>
              </common-dropdown>
            </div>

            <lazy-common-file-selector
              v-if="option.type === CUSTOM_OPTION_TYPE.image"
              :id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
              :key="userCustomOptions?.[quantity - 1][index]?.value"
              :input-type="2"
              :file-upload="`${userCustomOptions && (userCustomOptions[quantity - 1][index]?.value || '')}`"
              :state="userCustomOptions && (userCustomOptions[quantity - 1][index]?.value?.length > 0 || userCustomOptions[quantity - 1][index]?.unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1)"
              :label="isAICampaign ? `${$t(option.label)}` : `${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}`"
              browse-text="Upload"
              class="mt-3 w-full"
              @on-change="handleUpdateCustomOptions(quantity, index, $event[0], 1)"
            />
          </template>
        </div>
      </div>

      <!-- File Upload Section for AI campaigns with customer upload -->
      <div v-if="allowCustomerUpload" class="mt-3 px-3">
        <Common-FileSelector
          :id="`imageUpload_${isModal ? 'modal' : 'campaign'}`"
          ref="fileSelector"
          :input-type="2"
          file-upload=""
          :state="!!uploadedFile"
          :label="uploadLabel"
          accept="image/jpeg,image/png,image/webp"
          :auto-upload="false"
          browse-text="Upload"
          placeholder="Choose an image file"
          class="w-full"
          label-class="font-bold-2 text-sm uppercase mb-2 block"
          selector-class="flex items-center gap-2"
          @on-change="handleFileChange"
        />

        <div class="mt-2 text-sm text-gray-600 italic">
          {{ $t('Upload an image to use as reference for your design.') }}
        </div>
      </div>

      <!-- Error message -->
      <div v-if="errorMessage" class="mt-2 px-3 text-sm text-red-500">
        {{ errorMessage }}
      </div>

      <!-- Preview Design button for AI campaigns with personalization -->
      <div v-if="isAICampaign" class="mt-6 flex justify-center px-2">
        <button
          class="btn-fill py-3 px-6 inline-block text-base font-medium bg-black text-white"
          :disabled="!hasPersonalizationValues"
          :class="{ 'opacity-50 cursor-not-allowed': !hasPersonalizationValues }"
          @click="openPreviewModal"
        >
          {{ $t('Preview Design') }}
        </button>
      </div>
    </common-collapse>

    <!-- Loading indicator when generating preview -->
    <lazy-default-campaign-ai-preview-loading-shimmer
      v-if="isLoading"
      :progress-percentage="progressPercentage"
      :current-product-thumb-url="currentProductThumbUrl"
      :campaign-data="campaignData"
    />

    <!-- Confirmation modal for design approval -->
    <lazy-default-campaign-modal-confirm-design
      ref="modalConfirmDesign"
      :campaign-data="props.campaignData"
      @confirm-design="$emit('confirmDesign')"
    />
  </div>
</template>
