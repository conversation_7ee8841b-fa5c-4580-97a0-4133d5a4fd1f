<script lang="ts" setup>
const {
  errorMessages,
  form,
  isShowModalCheckReviewOrder,
  errorSubmitted,
  disableSubmit,
  resetForm,
  submit
} = useModalCheckReviewOrder()
</script>

<template>
  <button class="border border-[#28a745] text-[#28a745] capitalize text-xl text-sm px-2 py-1 whitespace-nowrap hover:bg-[#28a745] hover:text-white transition-colors relative float-right" @click="isShowModalCheckReviewOrder = true">
    {{ $t('Write a review') }}
  </button>
  <lazy-common-modal
    modal-id="write-a-review-modal"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[480px] p-3 px-4"
    :model-value="isShowModalCheckReviewOrder"
    :title="$t('Enter your order information')"
    @close-modal="() => { isShowModalCheckReviewOrder = false; resetForm() }"
  >
    <hr class="mt-3">
    <div class="my-2 w-full">
      <div class="relative">
        <div>
          <div v-if="errorMessages.length" class="flex flex-col">
            <span v-for="msg in errorMessages" :key="msg" class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ msg }}</span>
          </div>
          <div v-if="errorSubmitted" class="flex flex-col">
            <span class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ $t("Your order does not exist.") }}</span>
          </div>
          <div class="mt-4">
            <label for="email">
              {{ $t('Email') }} <span class="text-red-500">*</span>
            </label>
            <div class="mt-1 md:flex">
              <input
                id="email"
                v-model="form.email"
                class="border border-gray-300 px-3 py-1 w-full focus:border-blue-500/50"
                type="email"
                :placeholder="$t('Email')"
                required
                autofocus
              >
            </div>
          </div>
          <div class="mt-4">
            <label for="order_number">
              {{ $t('Order Number') }} <span class="text-red-500">*</span>
            </label>
            <div class="mt-1">
              <input
                id="order_number"
                v-model="form.order_number"
                class="border border-gray-300 px-3 py-1 w-full focus:border-blue-500/50"
                type="text"
                :placeholder="$t('Order Number')"
                required
              >
            </div>
          </div>
          <hr class="my-3">
          <div class="w-full">
            <button :disabled="disableSubmit" class="btn-border-fill px-[0.75rem] py-[0.375rem] float-right" @click="submit">
              {{ $t('Write review') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </lazy-common-modal>
</template>
