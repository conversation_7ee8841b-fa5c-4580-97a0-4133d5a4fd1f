<script setup>
const { $imgUrl } = useNuxtApp()
const props = defineProps({
  isActive: {
    type: Boolean,
    default: false
  },
  thumbUrl: {
    type: String,
    required: true
  }
})

defineEmits(['clicked'])

const src = computed(() => $imgUrl({
  path: props.thumbUrl,
  type: 'thumb'
}))
</script>

<template>
  <div
      class="video-player-trigger flex items-center justify-center relative m-1 border border-white"
      :class="{
        '!border-primary': isActive
      }"
      @click="$emit('clicked')"
  >
    <div style="width: 40px; height: 40px; border-radius: 50%; position: absolute; z-index: 2;">
      <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 15 15"
      >
        <g opacity=".54">
          <g>
            <circle cx="7.5" cy="7.5" fill="#040000" r="7.3" />
            <path
                d="m7.5.5c3.9 0 7 3.1 7 7s-3.1 7-7 7-7-3.1-7-7 3.1-7 7-7m0-.5c-4.1 0-7.5 3.4-7.5 7.5s3.4 7.5 7.5 7.5 7.5-3.4 7.5-7.5-3.4-7.5-7.5-7.5z"
                fill="#ffffff"
            />
          </g>
        </g>
        <path
            d="m6.1 5.1c0-.2.1-.3.3-.2l3.3 2.3c.******* 0 .4l-3.3 2.4c-.2.1-.3.1-.3-.2z"
            fill="#ffffff"
        />
      </svg>
    </div>
    <img :src="src" alt="Senprints" class="w-full">
  </div>
</template>

<style scoped>
.video-player-trigger {
  width: 80px;
  height: 100px;
  padding: 2px;
}

.video-player-trigger svg {
  width: 100%;
  height: 100%;
  fill: #fff;
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
