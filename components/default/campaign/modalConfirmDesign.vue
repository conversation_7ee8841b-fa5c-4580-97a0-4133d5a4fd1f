<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'
import { useCartStore } from '~~/store/cart'
import { PERSONALIZED_TYPE, SYSTEM_CAMPAIGN_TYPE } from '~/utils/constant'

const props = defineProps({
  // Campaign data to check if it's an AI campaign
  campaignData: {
    type: Object as PropType<Campaign>,
    default: () => ({})
  }
})

const emit = defineEmits(['confirmDesign'])
const isShowModal = ref<Array<string>>()
defineExpose({
  isShowModal
})
const confirm = ref(true)
const isUploading = ref(false)
const { $i18n } = useNuxtApp()

// Check if this is an AI campaign with personalization
const isAICampaign = computed(() => {
  return (props.campaignData?.system_type === 'ai_mockup'
    || props.campaignData?.system_type === SYSTEM_CAMPAIGN_TYPE.AI_MOCKUP)
  && (props.campaignData?.personalized === 3
    || props.campaignData?.personalized === PERSONALIZED_TYPE.CUSTOM_OPTION
    || hasCustomerUploadOptions.value)
})

// Check if campaign has customer upload options (for image upload campaigns)
const hasCustomerUploadOptions = computed(() => {
  if (!props.campaignData?.attributes) {
    return false
  }

  try {
    const attributes = JSON.parse(props.campaignData.attributes)
    return attributes.customer_upload_options?.allow_customer_upload_image === true
  }
  catch (error) {
    return false
  }
})

// Function to handle Add to Cart and save design image
async function handleAddToCart() {
  try {
    // Only save the design image if this is an AI campaign
    if (isAICampaign.value && isShowModal.value && isShowModal.value.length > 0) {
      isUploading.value = true

      // Get the first image URL (current design)
      const imageUrl = isShowModal.value[0]

      // Check if it's a data URL (base64)
      if (imageUrl.startsWith('data:')) {
        // First emit the confirmDesign event to add to cart
        emit('confirmDesign')

        // Get the cart store to update the cart item
        const cartStore = useCartStore()

        // Wait a short time for the cart item to be created
        setTimeout(() => {
          // Get the last added cart item (should be the one we just added)
          const lastCartItem = cartStore.listCartItem[cartStore.listCartItem.length - 1]

          if (lastCartItem) {
            // Store the base64 image data directly in the cart item
            // This will be saved in localStorage via the cart store's persistence
            cartStore.updateCartItem(lastCartItem, {
              // Keep the original thumb_url for fallback
              original_thumb_url: lastCartItem.thumb_url,
              // Store the base64 image data
              design_image_base64: imageUrl
            })
          }
        }, 500)
      }
      else {
        // Still add to cart even if image format is wrong
        emit('confirmDesign')
      }
    }
    else {
      // If not an AI campaign, just add to cart and close the modal
      emit('confirmDesign')
      isShowModal.value = []
    }
  }
  catch (error) {
    // console.error('Error saving design image:', error)
    uiManager().createPopup($i18n.t('Cannot save design image'))
    // Still add to cart even if there's an error
    emit('confirmDesign')
  }
  finally {
    isUploading.value = false
    isShowModal.value = []
  }
}
</script>

<template>
  <common-modal
    :model-value="isShowModal?.length"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 px-4"
    @close-modal="isShowModal = []"
  >
    <div class="grid grid-cols-2">
      <div class="col-span-2 lg:col-span-1">
        <Splide
          v-if="isShowModal?.length && isShowModal?.length >= 2"
          :options="splideSetting"
        >
          <SplideSlide v-for="(imgUrl, index) in isShowModal" :key="index">
            <div class="p-1">
              <img :src="imgUrl" alt="confirmDesign">
            </div>
          </SplideSlide>
        </Splide>
        <div v-else-if="isShowModal?.length" class="w-full center-flex">
          <common-image
            img-class="w-full"
            :image="{ path: isShowModal[0] }"
            alt="confirmDesign"
          />
        </div>
      </div>
      <div class="col-span-2 lg:col-span-1 flex flex-wrap content-center pl-3">
        <div class="w-full flex relative">
          <input id="confirmDesignCheckBox" v-model="confirm" type="checkbox" name="confirmDesignCheckBox" class="absolute top-1.5">
          <label for="confirmDesignCheckBox" class="font-medium ml-5 cursor-pointer">
            {{ $t('I have reviewed my design and confirm that I have entered the text correctly') }}.
          </label>
        </div>
        <button
          class="w-full btn-fill py-2 mt-3 text-xl font-medium"
          :disabled="!confirm || isUploading"
          @click="handleAddToCart"
        >
          <span v-if="isUploading">{{ $t('Uploading...') }}</span>
          <span v-else>{{ $t('Add to cart') }}</span>
        </button>
      </div>
    </div>
  </common-modal>
</template>
