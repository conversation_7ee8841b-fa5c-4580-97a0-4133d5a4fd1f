<script lang="ts" setup>
const props = defineProps({
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  campaignData: {
    default: undefined,
    type: Object as PropType<Campaign>
  },
  totalCustomOptionFee: {
    default: 0,
    type: Number
  }
})

const { $formatPrice } = useNuxtApp()

const { sellPrice, totalPrice } = useCampaignCombo(props.campaignData)
const currentPrice = computed(() => {
  if (props.campaignData?.system_type === 'combo') {
    return sellPrice.value
  }

  return (props.userCampaignOption?.currentPrice || 0) + props.totalCustomOptionFee
})
const oldPrice = computed(() => {
  if (props.campaignData?.system_type === 'combo') {
    return totalPrice.value
  }

  return (props.userCampaignOption?.currentOldPrice || 0) + props.totalCustomOptionFee
})
</script>

<template>
  <div v-if="userCampaignOption && campaignData">
    <h6 class="font-medium">
      <span>{{ userCampaignOption.currentProduct?.name }} </span>
      <span v-if=" userCampaignOption.currentOptions">
        <span
          v-for="(item, optionIndex) in Object.keys(userCampaignOption.optionList)"
          :key="optionIndex"
          class="uppercase"
        >
          <span>&nbsp;/&nbsp;</span>
          {{ userCampaignOption.currentOptions[item] ? userCampaignOption.currentOptions[item].split('-').join(' ') : $t(item) }}
        </span>
      </span>
    </h6>
    <div class="flex items-center">
      <span class="text-2xl font-medium text-primary" data-test-id="price">{{ $formatPrice(currentPrice, userCampaignOption?.currentProduct?.currency_code) }}</span>
      <span v-if="campaignData.system_type === 'combo' && (oldPrice > currentPrice)" class="block ml-2 text-sm bg-green-700 text-white font-medium px-2 rounded-md">
        {{ $t('Saving') }} {{ $toLocalePrice(totalPrice - sellPrice) }}
      </span>
      <del v-if="storeInfo().store_type !== 'google_ads' && !storeInfo().disable_pre_discount && (oldPrice > currentPrice)" class="ml-3">{{ $formatPrice(oldPrice, userCampaignOption.currentProduct?.currency_code) }}</del>
    </div>
  </div>
</template>
