<script lang="ts" setup>
interface Props {
  progressPercentage: number
  currentProductThumbUrl?: string
  campaignData?: any
}

const props = defineProps<Props>()

// Get the product image URL for the shimmer background
const productImageUrl = computed(() => {
  const { $imgUrl } = useNuxtApp()
  const thumbUrl = props.currentProductThumbUrl || props.campaignData?.thumb_url

  if (!thumbUrl)
    return ''

  return $imgUrl({
    path: thumbUrl,
    type: 'full'
  })
})
</script>

<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-6 rounded-lg flex flex-col items-center max-w-md w-full mx-4">
      <!-- Product image with shimmer overlay -->
      <div class="relative w-64 h-64 mb-4 rounded-lg overflow-hidden bg-gray-200">
        <!-- Background product image -->
        <img
          v-if="productImageUrl"
          :src="productImageUrl"
          alt="Product preview"
          class="w-full h-full object-cover opacity-30"
        >

        <!-- Shimmer overlay -->
        <div class="absolute inset-0 bg-white opacity-60 skeleton-effect-wave" />

        <!-- Loading icon overlay -->
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="bg-white bg-opacity-90 rounded-full p-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500" />
          </div>
        </div>
      </div>

      <!-- Loading text -->
      <div class="text-center">
        <p class="text-gray-700 font-medium mb-3">
          {{ $t('Generating personalized preview...') }}
        </p>

        <!-- Progress bar -->
        <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
          <div
            class="bg-red-500 h-3 rounded-full transition-all duration-300 ease-out"
            :style="{ width: `${progressPercentage}%` }"
          />
        </div>

        <!-- Progress percentage -->
        <p class="text-red-500 font-semibold text-lg mb-2">
          {{ Math.round(progressPercentage) }}%
        </p>

        <p class="text-gray-500 text-sm">
          {{ $t('Average generation time is about 30 seconds') }}
        </p>
      </div>
    </div>
  </div>
</template>
