<script lang="ts" setup>
const props = defineProps({
  products: {
    required: true,
    type: Object as PropType<Array<Product>>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  isDropdownType: {
    default: undefined,
    type: <PERSON>ole<PERSON>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  isModal: {
    default: false,
    type: Boolean
  },
  additionalProductSelectorBtnClass: {
    default: '',
    type: String
  },
  additionalProductSelectorClass: {
    default: '',
    type: String
  },
  campaignSystem: {
    type: String,
    default: null
  }
})
const emit = defineEmits(['updateProduct', 'selectCombo'])
const showListingProduct = ref(false)

const getProductUrl = computed(() => {
  return (productName?: string) => {
    return getCampaignUrl({
      campaignSlug: props.campaignSlug,
      productName,
      query: useRoute().query
    })
  }
})

function onChangeProduct(newProduct: Product) {
  emit('updateProduct', stringHelperToSlug(newProduct.name), props.campaignSystem === 'combo' ? newProduct.id : undefined)
  showListingProduct.value = true
}
</script>

<template>
  <div
    v-if="isDropdownType"
    class="product-list my-2"
  >
    <common-dropdown
      dropdown-id="productDropdownSelect"
      :btn-class="`btn-border w-full py-2 capitalize text-overflow-hidden ${additionalProductSelectorBtnClass}`"
      dropdown-class="w-full"
    >
      <span>{{ currentProduct?.name }}</span>
      <template #content>
        <div
          v-for="(product, index) in products"
          :key="index"
          sp-action="change_product"
          data-test-id="change-product"
          class="block px-3 py-1 btn-text capitalize text-overflow-hidden"
          :class="{
            'bg-primary !text-contrast': product.id === currentProduct?.id,
            [additionalProductSelectorClass]: true
          }"
          @click="onChangeProduct(product)"
        >
          {{ product.name }}
        </div>
      </template>
    </common-dropdown>
  </div>
  <div v-else class="product-list flex md:flex-wrap overflow-x-auto">
    <common-product-thumbnail
      v-for="(product, index) in products"
      :key="index"
      :product="product"
      :url="getProductUrl(product.name)"
      :is-hidden="(index > 3) && !showListingProduct"
      :is-active="product.id === currentProduct?.id"
      :is-modal="isModal"
      :current-options="currentOptions"
      @on-change-product="onChangeProduct"
    />
    <div
      v-if="products?.length && products?.length > 4"
      class="h-[100px] w-[80px] m-1 text-gray-300 hidden hover:(shadow-custom text-gray-700) md:flex justify-center items-center text-6xl border cursor-pointer"
      @click="showListingProduct = !showListingProduct"
    >
      <i :class="showListingProduct ? 'icon-sen-minus' : 'icon-sen-plus'" />
    </div>
  </div>
</template>
