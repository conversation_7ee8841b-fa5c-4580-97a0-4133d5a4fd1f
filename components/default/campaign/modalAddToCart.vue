<script lang="ts" setup>
import { useCampaignStore } from '~~/store/campaign'

defineProps({
  relatedCart: {
    default: undefined,
    type: Object as PropType<Array<Product>>
  }
})
const localePath = useLocalePath()
const isShowModal = ref(false)
const cartItem = ref<CartItem>()
const isUploadFile = computed(() => {
  return uiManager().isUploadFile
})

defineExpose({
  isShowModal,
  cartItem
})

const price = computed(() => {
  return (cartItem.value?.variantPrice || cartItem.value?.price || 0) + (cartItem.value?.extra_custom_fee || 0)
})
</script>

<template>
  <common-modal
    :key="cartItem?.id || 'add_to_cart_default'"
    v-model="isShowModal"
    modal-id="modalAddToCart"
    :title="$t('Added to cart successfully!')"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 px-4"
  >
    <div class=" max-h-[80vh] overflow-y-auto">
      <div class="grid grid-cols-12">
        <div
          class="col-span-12 lg:col-span-7 py-2 flex items-center"
        >
          <!-- Use base64 image if available, otherwise use the regular thumbnail -->
          <img
            v-if="cartItem?.design_image_base64"
            :src="cartItem.design_image_base64"
            :alt="cartItem?.campaign_title"
            class="!w-22"
          >
          <common-image
            v-else
            img-class="!w-22"
            :image="{
              path: cartItem?.thumb_url,
              type: 'list',
              color: cartItem?.options?.color
            }"
            :alt="cartItem?.campaign_title"
          />
          <div class="w-full max-w-[calc(100%-90px)] px-1 ">
            <nuxt-link :to="localePath(cartItem?.product_url as string)" class="w-full block">
              <h5 class="btn-text text-overflow-hidden font-medium" :title="cartItem?.campaign_title">
                {{ cartItem?.campaign_title }}
              </h5>
            </nuxt-link>
            <div>
              <span class="capitalize">{{ $t(cartItem?.product_name || '') }}</span>
            </div>
            <div class="uppercase flex items-center">
              <span
                v-for="(key, optionIndex) in Object.keys(cartItem?.optionList || {}).filter(key => (cartItem?.optionList[key].length ?? 0) > 1)"
                :key="optionIndex"
                class="flex items-center"
              >
                <span v-if="optionIndex > 0">&nbsp;/&nbsp;</span>
                <common-color-item
                  v-if="key === 'color' && cartItem?.options[key]"
                  :color="cartItem?.options[key]"
                  :title="cartItem?.options[key]"
                  size="sm"
                  data-test-id="product-color"
                />
                {{ $t(cartItem?.options && cartItem?.options[key] || '') }}
              </span>
            </div>
            <!-- Hiển thị thông tin cá nhân hóa -->
            <div v-if="cartItem?.customer_custom_options?.length" class="mt-2">
              <ul class="pl-5 list-disc">
                <li v-for="(groupOptions, groupNumber) in cartItem.customer_custom_options" :key="groupNumber" class="capitalize" style="font-size: 13px;">
                  <div v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
                    <div v-if="customOption.type === 'image'">
                      {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                        :href="$imgUrl({ path: customOption.value as string, type: 'full' })"
                        target="_blank"
                        class="btn-text text-blue-900"
                        @click.prevent="uiManager().viewImage(customOption.value as string)"
                      >{{ $t('View image') }}</a>
                    </div>
                    <div v-else class="text-overflow-hidden">
                      {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <!-- Hiển thị custom options cũ -->
            <div v-if="cartItem?.custom_options" class="mt-2">
              <ul class="pl-5 list-disc">
                <li v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
                  <div v-if="key === 'customImage'">
                    {{ $t('Your image') }}:
                    <a
                      :href="$imgUrl({ path: value, type: 'full' })"
                      target="_blank"
                      class="btn-text text-blue-900"
                      @click.prevent="uiManager().viewImage(value)"
                    >{{ $t('View image') }}</a>
                  </div>
                  <div v-else class="text-overflow-hidden">
                    {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
                  </div>
                </li>
              </ul>
            </div>
            <div class="mb-1 font-medium mt-2">
              <span>
                x{{ cartItem?.quantity }}
              </span>
              <span class="ml-3">{{ $formatPrice((cartItem?.quantity || 0) * price, cartItem?.currency_code) }}</span>
            </div>
          </div>
        </div>
        <div class="col-span-12 lg:col-span-5">
          <button
            id=""
            class="btn-border p-2 w-full"
            data-test-id="view-cart-button"
            @click="useCampaignStore().$patch({ modalCampaignUrl: '' });useRouter().push(localePath('/cart'))"
          >
            {{ $t('View cart') }} ({{ totalQuantity() }})
          </button>
          <button
            class="btn-fill p-2 w-full mt-2"
            :disabled="!!isUploadFile"
            data-test-id="proceed-to-checkout-button"
            @click="useCampaignStore().$patch({ modalCampaignUrl: '' }); createOrder()"
          >
            {{ $t('Proceed to checkout') }}
          </button>
        </div>
      </div>

      <lazy-common-product-carousel
        v-if="relatedCart && relatedCart.length"
        :key="cartItem?.id || 'add_to_cart_default_carousel'"
        class="container mt-10"
        title-class="text-center uppercase text-@xl"
        title="Frequently bought together"
        :products="relatedCart"
        static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 md:gap-2 lg:gap-4"
      >
        <template #default="{ product, index }">
          <default-common-product-item :product="product" :index="index" class="inline-block" />
        </template>
      </lazy-common-product-carousel>
    </div>
  </common-modal>
</template>
