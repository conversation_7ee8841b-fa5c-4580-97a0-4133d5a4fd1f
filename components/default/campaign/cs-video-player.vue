<template>
  <div v-if="isMobile !== undefined">
    <div style="height: 100%; width: 100%; position: absolute; top: 0; left: 0; display: flex; align-items: center;">
      <video-player
          class="vjs-custom-skin d-flex justify-content-center align-items-center w-100 h-100"
          :options="playerOptions"
          @play="onPlayerPlay"
          @pause="onPlayerPause"
          @ended="onPlayerEnded"
          @ready="onPlayerReady"
      />
    </div>
  </div>
</template>

<script setup>
import 'video.js/dist/video-js.css'
import { VideoPlayer } from '@videojs-player/vue'

const props = defineProps({
  videoUrl: {
    type: String,
    required: true,
  },
  thumbnailUrl: {
    type: String,
    required: true,
  },
  autoplay: {
    type: Boolean,
    default: false,
  }
})
const { $imgUrl } = useNuxtApp()

const isMobile = ref(undefined)
onMounted(() => {
  const match = window.matchMedia('(pointer:coarse)')
  isMobile.value = match && match.matches
})

const playerOptions = computed(() => ({
  muted: true,
  loop: true,
  autoplay: props.autoplay && !isMobile.value,
  fluid: false,
  controls: true,
  controlBar: {
    children: [
      'playToggle',
      'progressControl',
      'fullscreenToggle',
    ],
  },
  sources: [
    {
      type: 'video/mp4',
      src: props.videoUrl,
    },
  ],
  poster: props.thumbnailUrl ? $imgUrl({ path: props.thumbnailUrl, type: 'full_hd' }) : null,
}))
function onPlayerPlay () {
  console.log('Video is playing')
}
function onPlayerPause () {
  console.log('Video is paused')
}
function onPlayerEnded () {
  console.log('Video has ended')
}
function onPlayerReady () {
  console.log('Player is ready')
}
</script>

<style>
.vjs-poster {
  background: white !important;
}
.video-js {
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}
</style>
