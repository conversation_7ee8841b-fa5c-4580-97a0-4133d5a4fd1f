<script lang="ts" setup>
import { useCampaignCombo } from '~/composables/campaignCombo'
import { useComboCartStore } from '~/store/comboCart'

const props = defineProps({
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  campaign: {
    type: Object as PropType<Campaign>,
    required: true
  }
})

const {
  getVariant,
  getSelectedOptions,
  getProductOptions,

  isSelectedOption,
  isProductSelected
} = useCampaignCombo(props.campaign)
const products = computed(() => props.campaign.products) as Ref<Product[]>
const comboStore = useComboCartStore()
const { updateCurrentImage, currentImageList, totalVideos, updateColorMap } = useViewBoxImage()

const filteredProduct = computed(() => {
  return products.value.map((product: any) => {
    if (product?.currentVariant?.out_of_stock) {
      const fixedOptions = product.currentOptions?.size
      let variantsInStock = null
      if (fixedOptions) {
        const regex = new RegExp(`-${fixedOptions}$`, 'i')
        variantsInStock = product.variantsList.find((variant: any) => {
          return regex.test(variant.variant_key) && variant.out_of_stock === 0
        })
      }
      else {
        variantsInStock = product.variantsList.find((variant: any) => {
          return variant.out_of_stock === 0
        })
      }
      if (variantsInStock) {
        product.currentVariantKeyInStock = variantsInStock?.variant_key?.replace(/_/g, ' ')?.replace(/-/g, ' / ') ?? ''
        product.variantInStock = variantsInStock
        return product
      }
    }
    product.currentVariantKeyInStock = product.currentVariantKey
    product.variantInStock = product.currentVariant
    return product
  })
})

comboStore.updateSelected([...products.value.map((product) => {
  return {
    id: product.id as number,
    isSelected: true,
    currentOptions: initOption(product)
  }
})])
const selected = computed(() => comboStore.selected)

function initOption(product: Product) {
  if (product.currentOptions && Object.keys(product.currentOptions).length > 0) {
    return product.currentOptions
  }
  const options = getProductOptions(product)
  const currentOptions: { [key: string]: string } = {}
  for (const key in options) {
    currentOptions[key] = options[key][0]
  }

  return currentOptions
}

function getPrice(product: Product) {
  const selectedProduct = selected.value.find(item => item.id === product.id)
  return useTestPrice().getPrice(product, getVariant(product, selectedProduct?.currentOptions)) + Number(((product.customFeePrice || 0) * ((product.customOptionGroupNumbers || 1) - 1)))
}

function onClickProduct(product: Product) {
  comboStore.toggleSelect(product)
  const index = getImageIndex(product)
  if (index >= 0) {
    updateCurrentImage(index + totalVideos.value)
  }
}

function getImageIndex(product: Product) {
  return currentImageList.value.findIndex(image => image.file_url === product.thumb_url)
}

function onChangeOption(product: Product, optionType: string, option: string) {
  comboStore.changeOption(product, optionType, option)
  const index = getImageIndex(product)
  if (index < 0) {
    return
  }
  if (optionType === 'color') {
    updateColorMap(index, option)
  }
  updateCurrentImage(index + totalVideos.value)
}

function getColor(product: Product) {
  return selected.value.find(item => item.id === product.id)?.currentOptions?.color
}
</script>

<template>
  <div>
    <div v-for="(product, index) in filteredProduct" :key="index" class="grid grid-cols-3 mt-2 items-center cursor-pointer select-none">
      <div
        :id="`checkbox-combo-product-${product.id}`"
        class="flex col-span-2 items-center"
        @click="onClickProduct(product)"
      >
        <span class="text-2xl text-primary mt-0.5">
          <i :class="isProductSelected(product) ? 'icon-sen-checkbox-marked' : 'icon-sen-checkbox-blank-outline'" />
        </span>
        <div class="flex-shrink-0 w-18 md:w-24">
          <common-image
            :image="{
              path: product.thumb_url,
              color: getColor(product)
            }"
            :alt="product.name"
            img-class="w-40"
          />
        </div>
        <div class="ml-2 pr-4 text-left w-full overflow-hidden">
          <div class="w-full text-overflow-hidden">
            {{ $t(product.name || '') }}
          </div>
          <client-only>
            <span v-if="product.variantInStock && product.variantInStock.out_of_stock" class="text-danger font-weight-500">
              {{ $t('Out of stock') }}
            </span>
            <span v-else>
              {{ $formatPrice(getPrice(product), product.currency_code) }}
            </span>
          </client-only>
        </div>
      </div>
      <div class="space-y-2">
        <common-dropdown
          v-for="optionType in Object.keys(getProductOptions(product))"
          :key="optionType"
          class="col-span-1 w-full"
          btn-class="btn-border px-2 py-1 w-full uppercase text-overflow-hidden"
          dropdown-class="w-full max-w-[90vw]"
        >
          <div class="flex items-center justify-center">
            <div
              v-if="optionType === 'color'"
              class="rounded-full m-0.5 p-0.5 border-2"
            >
              <div
                :style="`background: ${colorVal(getSelectedOptions(product, optionType))}`"
                class="rounded-full relative h-4 w-4"
              />
            </div>
            <div class="text-sm xl:text-base">
              {{ getSelectedOptions(product, optionType) }}
            </div>
          </div>
          <template #content>
            <button
              v-for="option in getProductOptions(product)[optionType]"
              :key="`${optionType}-${option}`"
              class="btn-text uppercase min-w-full py-1 flex items-center justify-center"
              :class="{
                'bg-primary !text-contrast': isSelectedOption(product, optionType, option) && optionType !== 'color',
                '!text-primary': isSelectedOption(product, optionType, option) && optionType === 'color'
              }"
              @click="onChangeOption(product, optionType, option)"
            >
              <div
                v-if="optionType === 'color'"
                class="rounded-full m-0.5 p-0.5 border-2 border-gray-200"
                :class="isSelectedOption(product, optionType, option) ? 'border-primary' : 'border-white'"
              >
                <div
                  :style="`background: ${colorVal(option)}`"
                  class="rounded-full relative h-4 w-4"
                />
              </div>{{ option }}
            </button>
          </template>
        </common-dropdown>
      </div>
    </div>
  </div>
</template>
