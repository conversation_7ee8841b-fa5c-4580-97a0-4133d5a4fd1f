<script lang="ts" setup>
function getIcon(name: string) {
  return `${cdnURL.value}images/share/${name}.svg`
}

const encodedUrl = computed(() => {
  let baseUrl = useRuntimeConfig().public.baseUrl

  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.slice(0, -1)
  }

  return encodeURIComponent(baseUrl + useRoute().fullPath)
})
</script>

<template>
  <div id="share-box">
    <!-- <div class="text-blue-500 mr-2 font-medium">
      {{ $t('Share') }}
    </div> -->
    <div class="flex">
      <div class="w-8 h-8 my-1 mr-1">
        <a id="share-twitter" :href="`https://twitter.com/share?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
          <img :src="getIcon('twitter')" alt="Share on Twitter" loading="lazy">
        </a>
      </div>
      <div class="w-8 h-8 m-1">
        <a id="share-facebook" :href="`https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
          <img :src="getIcon('facebook')" alt="Share on Facebook" loading="lazy">
        </a>
      </div>
      <div v-if="false" class="w-8 h-8 m-1">
        <a id="share-instagram" :href="`https://www.instagram.com/?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
          <img :src="getIcon('instagram')" alt="Share on Instagram" loading="lazy">
        </a>
      </div>
      <div class="w-8 h-8 m-1">
        <a id="share-telegram" :href="`https://t.me/share/url?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
          <img :src="getIcon('telegram')" alt="Share on Telegram" loading="lazy">
        </a>
      </div>
      <div class="w-8 h-8 m-1">
        <a id="share-pinterest" :href="`https://pinterest.com/pin/create/button/?url=${encodedUrl}`" target="_blank" rel="noopener noreferrer nofollow">
          <img :src="getIcon('pinterest')" alt="Share on Pinterest" loading="lazy">
        </a>
      </div>
    </div>
  </div>
</template>
