<script lang="ts" setup>
const { slug } = defineProps({
  slug: {
    default: () => {
      return ''
    },
    type: String
  }
})
const pageData: Ref<PageData> = ref({
  id: 0,
  title: '',
  slug: '',
  content: '',
})

if (slug) {
  pageData.value = (await useCommonFooterPage(slug)).pageData.value
}

onMounted(() => {
  window.scrollTo({ top: 0, behavior: 'smooth' })
})
</script>

<template>
  <main class="container py-6">
    <common-breadcrumb :items="[{ text: 'Home', url: '/' }, { text: pageData.title }]" />
    <div class="grid grid-cols-12 gap-1">
      <div :class="`py-4 col-span-12 ${(slug === 'contact-us') ? (storeInfo().enable_contract_form ? 'lg:col-span-7' : 'lg:col-span-12') : null}`">
        <h1 class="text-4xl mt-6 mb-2 font-medium">
          {{ pageData.title }}
        </h1>
        <div class="main-content-page" v-html="pageData.content" />
      </div>
      <div v-if="slug === 'contact-us' && storeInfo().enable_contract_form" class="col-span-12 lg:col-span-5 p-4">
        <default-page-contact-us-form />
      </div>
    </div>
  </main>
</template>
<style lang="scss">
.main-content-page {
  a, a:hover {
    color: var(--color-primary);
  }

  span#spemail {
    display: inline-block;
  }

  span#spemail span {
    float: right;
  }

  h2, h3, h4, h5 {
    font-weight: 500;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.8rem;
  }

  h4 {
    font-size: 1.5rem;
  }

  h5 {
    font-size: 1.25rem;
  }

  ul, ol {
    padding-inline-start: 40px;
  }
  ul {
    list-style: disc;
  }
  ol {
    list-style: decimal;
  }
}
</style>
