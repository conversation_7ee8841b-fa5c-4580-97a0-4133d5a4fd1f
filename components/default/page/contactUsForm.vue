<script lang="ts" setup>
const fileSelector = ref()

const {
  optionsSelect,
  contactForm,
  submitted,
  invalids,
  isUploadingFiles,
  recaptcha,

  successForm,
  warningForm,
  errorMsg,

  handleSubmit,
  handleStateChange,
} = useContactUsForm(fileSelector)

const { lastOrderUrl } = useLastOrder()
const localePath = useLocalePath()
</script>
<template>
  <div class="page-contact bg-gray-100 p-5 md:p-8 mt-10 rounded-xl">
    <h4 class="mb-4">
      {{ $t('Leave message') }}
    </h4>
    <!-- Name -->
    <input
      id="name"
      v-model="contactForm.fullName"
      autocomplete="name"
      class="border border-gray-400 text-xl py-2 px-4 w-full focus:(text-primary border-primary shadow-custom2)"
      type="text"
      :placeholder="$t('Your full name')"
      autofocus
    >
    <!-- Email -->
    <input
      id="email"
      v-model="contactForm.email"
      autocomplete="email"
      :class="`mt-4 border ${(submitted && (invalids.email_required || invalids.email_invalid)) ? 'border-red-600' : 'border-gray-400'} text-xl py-2 px-4 w-full focus:(text-primary border-primary shadow-custom2)`"
      type="email"
      :placeholder="$t('Your email')"
      autofocus
    >
    <div v-if="submitted" id="email-feedback">
      <span v-if="invalids.email_required" class="text-red-600">{{ $t('Email is required') }}</span>
      <span v-else-if="invalids.email_invalid" class="text-red-600">{{ $t('Email is invalid') }}</span>
    </div>
    <small>({{ $t('contact_us_text-2') }})</small>

    <!-- Type -->
    <common-dropdown dropdown-id="contactFormTypeDropdown" class="mt-4 !block" dropdown-class="w-full" btn-class="text-overflow-hidden btn-border border-radius-override-p2 border-gray-400 w-full text-xl pl-2 pr-4 py-2 bg-white">
      <span class="px-2 float-left">{{ contactForm.type || 'Subject' }}</span>
      <i class="icon-sen-menu-down float-right text-2xl absolute right-0 position-center-y z-0" />
      <template #content>
        <div
          v-for="msg in optionsSelect"
          :key="msg"
          sp-action="contactFormType"
          class="capitalize py-1 px-4 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center cursor-pointer"
          @click="contactForm.type = msg"
        >
          {{ $t(msg) }}
        </div>
      </template>
    </common-dropdown>

    <!-- Order Number -->
    <input
      id="order"
      v-model="contactForm.order"
      class="mt-4 border border-gray-400 text-xl py-2 px-4 w-full focus:(text-primary border-primary shadow-custom2)"
      type="text"
      :placeholder="$t('Order number')"
      autofocus
    >
    <small v-if="lastOrderUrl" class="text-xs">
      ({{ $t('get your order number in your email confirmation') }} {{ $t('or') }} <a :href="`${localePath(lastOrderUrl)}?utm_source=page&utm_medium=contact-us`" target="_blank" rel="noopener noreferrer" v-text="$t('view last order status')" />)
    </small>
    <small v-else class="text-xs">
      ({{ $t('get your order number in your email confirmation') }} {{ $t('and') }} <a :href="localePath('/order/track')" target="_blank" rel="noopener noreferrer" v-text="$t('track your order here')" />)
    </small>

    <!-- Message -->
    <textarea
      id="message"
      v-model="contactForm.message"
      :class="`mt-4 border ${(submitted && (invalids.message_length || invalids.message_required)) ? 'border-red-600' : 'border-gray-400'} text-xl py-2 px-4 w-full resize-none overflow-y-scroll focus:(text-primary border-primary shadow-custom2) focus-visible:outline-none`"
      rows="7"
      :placeholder="$t('Your message')"
    />
    <div v-if="submitted" id="email-feedback">
      <span v-if="invalids.message_required" class="text-red-600">{{ $t('Message is required') }}</span>
      <span v-else-if="invalids.message_length" class="text-red-600">{{ $t('Message must have at least 10 characters') }}</span>
    </div>

    <!-- File -->
    <p class="mb-0 mt-4">
      <span class="font-medium">{{ $t('File Attachment') }}</span>
      <span> ({{ $t('Upload limit is 5 files.') }})</span>
    </p>
    <common-file-selector ref="fileSelector" v-model="contactForm.attachFile" :limit="5" @on-state-change="handleStateChange" />
    <small class="text-xs">({{ $t('contact_us_text-3') }})</small>

    <common-recaptcha ref="recaptcha" />

    <button
      class="mt-4 w-full border border-gray-400 capitalize py-2 px-4 text-xl bg-primary text-contrast"
      type="button"
      :disabled="isUploadingFiles"
      @click="handleSubmit"
    >
      {{ $t('Send over') }}
    </button>

    <div v-if="!(successForm||warningForm)" style="font-size: 12px;" class="mt-2">
      <p><span>{{ $t('Please do not send multiple requests on the same issue.') }}</span></p>
    </div>

    <div v-if="successForm" class="mt-4 p-4 bg-[#d9f6eb] text-[#226d52]">
      <strong>{{ $t('Success') }}!</strong> {{ $t('You will receive a reply from us within 1 business day') }}.
    </div>
    <div v-else-if="warningForm" class="mt-4 p-4 bg-[#fff3cd] text-[#856404]">
      <strong>{{ $t('Error') }}!</strong> {{ errorMsg }}
    </div>
  </div>
</template>
<style scoped>
.page-contact a {
  color: var(--color-primary-light);
}
</style>
