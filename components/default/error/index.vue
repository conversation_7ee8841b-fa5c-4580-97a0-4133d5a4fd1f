<script lang="ts" setup>
import type { NuxtError } from 'nuxt/app'

const { $i18n } = useNuxtApp()
const localePath = useLocalePath()
const props = defineProps({
  error: {
    default: undefined,
    type: Object as PropType<NuxtError>
  }
})
const splitErrorCode = computed(() => (props.error?.statusCode.toString() || '404').split(''))

useHead({ title: `${$i18n.t(props.error?.message || '')} | ${storeInfo().name}` })
</script>

<template>
  <main id="errorPage" class="container text-center h-[65vh] flex flex-col justify-center">
    <div>
      <div class="errorPage">
        <h1>{{ splitErrorCode[0] }}<span>{{ splitErrorCode[1] }}</span>{{ splitErrorCode[2] }}</h1>
      </div>
      <div class="mb-10 text-gray-500 text-xl">
        {{ $t(error?.message || '') }}
      </div>
      <NuxtLink :to="localePath('/')" class="btn-border p-2 text-2xl border">
        {{ $t('Back to Home') }}
      </NuxtLink>
    </div>
  </main>
</template>

<style scoped>

.errorPage h1 {
    color: #ffffff;
    font-size: 220px;
    letter-spacing: 10px;
    margin: 0;
    font-weight: 700;
    text-shadow: 2px 2px 0 var(--color-primary),-2px -2px 0 #5b5b5b;
}

.errorPage h1>span {
    text-shadow: 2px 2px 0 var(--color-primary),-2px -2px 0 var(--color-primary-lighter),0 0 8px var(--color-primary);
}

.errorPage p {
    font-family: josefin sans,sans-serif;
    color: #c9c9c9;
    font-size: 16px;
    font-weight: 400;
    margin-top: 0;
    margin-bottom: 15px
}

@media only screen and (max-width: 480px) {
    .errorPage {
        height:122px;
        line-height: 122px
    }

    .errorPage h1 {
        font-size: 122px
    }
}
</style>
