<script lang="ts" setup>
defineProps({
  promotionsList: {
    default: undefined,
    type: Object as PropType<Array<Promotion>>
  },
  defaultNumberItem: {
    default: 2,
    type: Number
  }
})

const isShowModal = ref(false)
const isListAllItem = ref(false)
const currentPromotion = ref<Promotion>()

const {
  rules,
  type,
  condition,
  countries,
} = usePromotion(currentPromotion)

</script>

<template>
  <div class="border border-2 border-dashed border-primary border-radius-override overflow-y-auto max-h-[80vh]">
    <default-common-promotion-item
      v-for="(promotion, index) in promotionsList"
      :key="index"
      :class="{'hidden': index > (defaultNumberItem || 2) && !isListAllItem}"
      :promotion="promotion"
      @click="currentPromotion = promotion ; isShowModal = true"
      @action="value=>{$emit('updateDiscountApply', value)}"
    />
    <div
      v-if="(promotionsList?.length || 0) > ((defaultNumberItem + 1) || 3)"
      class="cursor-pointer text-center font-weight-500 py-3"
      @click="isListAllItem = !isListAllItem"
    >
      <span>
        {{ isListAllItem ? $t('Show less') : $t('Show more') }}
      </span>
      <span><i :class="isListAllItem?'icon-sen-chevron-up':'icon-sen-chevron-down'" /></span>
    </div>
    <common-modal
      v-model="isShowModal"
      modal-class="w-[90%] md:max-w-[498px] p-5"
      modal-container-class="z-99999"
      :modal-id="`modal-promotion-${currentPromotion?.discount_code}`"
      :title="`${$t('Discount code')}: ${currentPromotion?.discount_code}`"
    >
      <ul class="mt-4 ml-8 list-disc">
        <li v-if="type">
          <span>{{ $t('Type') }}: {{ type }}</span>
        </li>
        <li v-if="condition">
          <span>{{ $t('Condition') }}: {{ condition }}</span>
        </li>
        <li v-if="rules.countries">
          <span>{{ $t('Location') }}: {{ countries }}</span>
        </li>
        <li v-if="currentPromotion?.end_time">
          <span>{{ $t('Expired time') }}: {{ currentPromotion?.end_time.slice(0, -3) }}</span>
        </li>
        <template v-if="rules.tiers && rules.tiers.length">
          <li v-for="(tier, index) in rules.tiers" :key="index">
            <span>{{ $t('Buy') }} {{ tier.qty }} {{ $t('Get') }} {{ tier.discount }}% {{ $t('Off') }}</span>
          </li>
        </template>
      </ul>
      <button
        v-if="isCheckoutPage"
        class="btn-fill py-1 px-2 mt-4"
        @click.stop="
          $emit('updateDiscountApply', currentPromotion?.discount_code);
          isShowModal = false"
      >
        <span>
          {{ $t('Apply Code') }}
        </span>
      </button>
    </common-modal>
  </div>
</template>

<style lang="scss">
</style>
