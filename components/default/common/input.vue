<script lang="ts" setup>
defineProps({
  inputType: {
    default: 1,
    type: Number
  },
  inputLabelStyle: {
    default: 'text-sm',
    type: String
  },
  id: {
    default: undefined,
    type: String
  },
  type: {
    default: undefined,
    type: String
  },
  label: {
    default: undefined,
    type: String
  },
  placeholder: {
    default: undefined,
    type: String
  },
  pattern: {
    default: undefined,
    type: String
  },
  maxLength: {
    default: undefined,
    type: Number
  },
  modelValue: {
    default: undefined,
    type: String
  },
  state: {
    default: undefined,
    type: [String, Boolean]
  },
  isRequired: {
    default: undefined,
    type: [String, Boolean]
  },
  message: {
    default: undefined,
    type: String
  },
  inputClass: {
    default: undefined,
    type: String
  },
  autocomplete: {
    default: undefined,
    type: String
  },
  enableTextCounter: {
    default: false,
    type: Boolean
  }
})

const input = ref()
const isInputFocusing = ref(false)

defineExpose({
  focus
})

function focus() {
  input.value.focus()
}

function parseIntValue(value: any, defaultValue = 0) {
  return Number.isNaN(value) ? defaultValue : Number(value)
}
</script>

<template>
  <div v-if="inputType === 1" class="relative">
    <label
      v-if="label"
      :for="id"
      class="absolute label px-3 transition-all duration-150 text-overflow-hidden w-full capitalize text-gray-500"
      :class="[(modelValue || isInputFocusing) ? 'text-sm' : 'py-3']"
    >
      {{ label }}
    </label>
    <input
      :id="id"
      ref="input"
      :name="id"
      :autocomplete="autocomplete || id"
      :type="type"
      :pattern="pattern"
      :value="modelValue"
      :maxlength="maxLength"
      class="relative bg-transparent w-full px-3 h-12 border focus:(border-primary shadow-custom2)"
      :class="[
        inputClass,
        (modelValue || isInputFocusing) ? 'pt-4' : '',
        state === true ? '!border-green-500 input-success' : '',
        state === false ? '!border-red-500 input-error' : '']"
      :placeholder="placeholder"
      @input="$emit('update:modelValue', $event.target?.value); $emit('input', $event.target?.value)"
      @focus="$emit('focus', $event); isInputFocusing = true"
      @blur="$emit('blur', $event); isInputFocusing = false"
    >
    <span
      v-if="state !== undefined && message"
      class="mt-1 text-sm"
      :class="{
        '!text-orange-500': state === 'warning',
        '!text-green-500': state === true,
        '!text-red-500': state === false,
        'cursor-pointer select-none': (message.toLowerCase().includes('click'))
      }"
      @click="$emit('click:feedback')"
    >{{ $t(message) }}</span>
    <slot />
  </div>
  <div v-else-if="inputType === 2" class="relative">
    <label
      v-if="label"
      :for="id"
      class="label text-overflow-hidden w-full capitalize z-1"
      :class="inputLabelStyle"
    >
      {{ label }}
    </label>
    <div class="relative">
      <input
        :id="id"
        ref="input"
        :name="id"
        :autocomplete="autocomplete || id"
        :type="type"
        :pattern="pattern"
        :value="modelValue"
        :maxlength="parseIntValue(maxLength, 255)"
        class="relative bg-transparent w-full px-3 h-10 border mt-1 focus:(border-primary shadow-custom2)"
        :class="[
          inputClass,
          state === true ? '!border-green-500' : '',
          state === false ? '!border-red-500 input-error' : ''
        ]"
        :placeholder="placeholder"
        @input="$emit('update:modelValue', $event.target?.value); $emit('input', $event.target?.value)"
        @change="$emit('change:modelValue', $event.target?.value); isInputFocusing = false"
        @focus="$emit('focus', $event); isInputFocusing = true"
        @blur="$emit('blur', $event); isInputFocusing = false"
      >
      <small v-if="enableTextCounter && ((modelValue?.length ?? 0) > 0 || isRequired === true)" class="absolute flex items-center text-counter">
        {{ modelValue?.length ?? 0 }}{{ (maxLength ? ` / ${maxLength}` : null) }}
      </small>
    </div>
    <span
      v-if="state !== undefined && message"
      class="mt-1 text-sm"
      :class="{
        '!text-orange-500': state === 'warning',
        '!text-green-500': state === true,
        '!text-red-500': state === false,
        'cursor-pointer select-none': (message.toLowerCase().includes('click'))
      }"
      @click="$emit('click:feedback')"
    >{{ $t(message) }}</span>
    <slot />
  </div>
</template>

<style lang="scss">
.text-counter {
  right: 10px;
  top: 55%;
  transform: translateY(-50%);
  z-index: 0;
}
</style>
