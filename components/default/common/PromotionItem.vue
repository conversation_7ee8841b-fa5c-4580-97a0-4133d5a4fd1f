<script lang="ts" setup>
const props = defineProps({
  promotion: {
    default: undefined,
    type: Object as PropType<Promotion>
  }
})
const {
  stringRule,
  isCopy,
  copyCode
} = usePromotion(toRef(props.promotion))

</script>

<template>
  <div
    :id="`promotion-item-${promotion?.discount_code}}`"
    class="flex cursor-pointer items-center bg-transparent hover:(bg-yellow-50)"
  >
    <div class="p-4">
      <span :class="promotion?.type==='FS' ? 'bg-blue-500' : 'bg-red-500'" class="rounded p-2 text-white">
        <i :class="promotion?.type==='FS' ? 'icon-sen-bike-fast' : 'icon-sen-tag'" />
      </span>
    </div>
    <div class="pr-4 py-3 border-b w-full">
      <div class="flex justify-between w-full">
        <span class="font-semibold">{{ promotion?.discount_code }}</span>
        <small v-if="isCheckoutPage" class="btn-text" @click.stop="$emit('action', promotion?.discount_code)">
          <span class="font-semibold">
            {{ $t('Apply Code') }}
          </span>
        </small>
        <small v-else class="btn-text flex items-center" @click.stop="copyCode">
          <span class="font-semibold mr-1">
            {{ isCopy ? $t('Copied') : $t('Copy code') }}
          </span>
          <i class="icon-sen-text-box-multiple" />
        </small>
      </div>
      <div>{{ stringRule }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
</style>
