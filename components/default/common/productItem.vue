<script lang="ts" setup>
import { type PropType } from 'vue'
import { useCampaignStore } from '~~/store/campaign'
const { $formatPrice } = useNuxtApp()

const campaignStore = useCampaignStore()
const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
const $router = useRouter()
const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  index: {
    default: undefined,
    type: Number
  },
})
const loadingGetCampaign = ref(false)
const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `/${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `?color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))
const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))

function goToPage () {
  $router.push(localePath(campaignUrl.value || '/'))
}

async function openCampaignModal ($event:Event) {
  $event.stopImmediatePropagation()
  loadingGetCampaign.value = true
  const campaignData = await campaignStore.getCampaignBySlug(props.product?.slug as string)
  loadingGetCampaign.value = false

  if (campaignData?.id && campaignData.products?.length) {
    campaignStore.$patch({ modalCampaignUrl: props.product?.slug })
  } else {
    uiManager().createPopup($i18n.t('Missing campaign data'))
  }
}

function productInfo () {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}
</script>

<template>
  <div class="relative product-list-item select-none overflow-hidden w-full hover:shadow-custom2" data-test-id="product-list-item">
    <div v-click-not-drag="goToPage">
      <nuxt-link :to="localePath(campaignUrl)">
        <div
          class="w-full pt-[125%]"
        >
          <common-image
            :image="{
              path: product?.thumb_url,
              type:'list',
              color
            }"
            img-class="transition-transform duration-200 absolute top-0 left-0"
            :alt="product?.name"
            :title="productTitle"
            :fetchpriority="(index === 0) ? 'high' : ''"
          />
        </div>
      </nuxt-link>
    </div>
    <div class="p-1 md:p-2">
      <h5 v-click-not-drag="goToPage" class="btn-text md:text-lg font-medium text-overflow-hidden text-center" :title="productTitle">
        <nuxt-link :to="localePath(campaignUrl)">
          {{ productTitle }}
        </nuxt-link>
      </h5>
      <h6 class="block h-12 flex flex-wrap justify-center gap-x-2 items-center">
        <client-only>
          <span class="text-primary font-medium">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
        </client-only>
        <del v-if="storeInfo().store_type !== 'google_ads' && productOldPrice > productPrice" class="old-price">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
      </h6>
      <button v-if="storeInfo().enable_add_to_cart" class="<md:text-sm btn-fill w-full py-2 uppercase font-semibold" data-test-id="product-list-item-btn" @click.stop.prevent="openCampaignModal">
        <common-loading-dot v-if="loadingGetCampaign" />
        <span v-else> {{ $t('Add to cart') }}  </span>
      </button>
    </div>
    <default-common-personalize-tag v-if="product?.personalized" />
  </div>
</template>
