<script lang="ts" setup>
const props = defineProps({
  size: {
    default: 'md',
    type: String as PropType<'xs' | 'sm' | 'md' | 'lg'>
  },
  clickFocusPersonalizeInput: {
    default: false,
    type: Boolean
  }
})

function onPersonalizeTagClick() {
  if (!props.clickFocusPersonalizeInput) { return }

  document.querySelector('.personalize input')?.focus()
}
</script>

<template>
  <div
    class="italic absolute left-0 top-0 bg-primary text-contrast opacity-70 text-sm py-1 px-2 select-none"
    data-test-id="personalize-tag"
    :class="{
      'text-xs py-0.5 px-1': size === 'xs',
      'md:(text-md py-2 px-3)': size === 'md',
      'md:(text-md py-2 px-3) lg:(text-lg py-3 px-4)': size === 'lg',
      'cursor-pointer': clickFocusPersonalizeInput
    }"
    @click="onPersonalizeTagClick"
  >
    {{ $t('Personalize it!') }}
    <i class="icon-sen-lead-pencil" />
  </div>
</template>

<style>
</style>
