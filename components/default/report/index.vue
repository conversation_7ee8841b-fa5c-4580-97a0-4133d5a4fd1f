<script lang="ts" setup>
const countryFilter = ref()
const emailConfirm = ref()
const countryDropdownLabel = ref()
const {
  tabsList,
  selectedTab,
  campaignSlug,
  isFileUploading,

  userCountry,
  currentCountry,
  filterCountryText,
  filterCountryArray,
  formIntellectual,
  formPolicy,

  handleStateChange,
  submit,

  errors,
} = useCampaignReportPage(emailConfirm, countryDropdownLabel)
</script>
<template>
  <main class="container" dusk="campaign-report-page">
    <common-breadcrumb :items="[{ text: 'Home', url: '/' }, { text: $t('Report') }]" />
    <div class="mt-8">
      <div class="border-b-1 border-gray-300 font-medium">
        <ul class="flex">
          <li
            v-for="(item, index) in tabsList"
            :key="item"
            class="cursor-pointer py-2"
            :class="{
              'text-primary': selectedTab === item.value,
              'mx-2 md:mx-6': index
            }"
            @click="selectedTab = item.value"
          >
            {{ $t(item.name) }}
          </li>
        </ul>
      </div>
      <form v-if="selectedTab === 'intellectual'" class="mt-4" @submit.prevent="submit">
        <div>
          <label for="campaignUrl" class="font-medium">{{ $t('Campaign URL') }} <span class="text-red-600">*</span></label>
          <input id="campaignUrl" v-model="campaignSlug" class="campaign-url border border-gray-300 w-full mt-2 focus:border-blue-500/50" type="text" required>
        </div>
        <div class="mt-4">
          <h4 class="text-2xl font-medium border-b-1 border-gray-300 pb-1">
            {{ $t('Your Contact Information') }}
          </h4>

          <div class="grid grid-cols-2 gap-4 mt-4">
            <div class="col-span-2 md:col-span-1">
              <label for="first-name" class="ml-2 font-medium">{{ $t('First Name') }} <span class="text-red-600">*</span></label>
              <input
                id="first-name"
                v-model="formIntellectual.firstName"
                autocomplete="given-name"
                type="text"
                name="first-name"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="last-name" class="ml-2 font-medium">{{ $t('Last Name') }} <span class="text-red-600">*</span></label>
              <input
                id="last-name"
                v-model="formIntellectual.lastName"
                autocomplete="family-name"
                type="text"
                name="last-name"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="address1" class="ml-2 font-medium">{{ $t('Address Line', { no: 1 }) }} <span class="text-red-600">*</span></label>
              <input
                id="address1"
                v-model="formIntellectual.address1"
                autocomplete="address-line1"
                type="text"
                name="address1"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="address2" class="ml-2 font-medium">{{ $t('Address Line', { no: 2 }) }} <span class="text-red-600">*</span></label>
              <input
                id="address2"
                v-model="formIntellectual.address2"
                autocomplete="address-line2"
                type="text"
                name="address2"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="state" class="ml-2 font-medium">{{ $t('State') }} <span>({{ $t('Optional') }})</span></label>
              <input
                id="state"
                v-model="formIntellectual.state"
                type="text"
                name="state"
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label ref="countryDropdownLabel" for="country-dropdown" class="ml-2 font-medium">{{ $t('Country') }} <span class="text-red-600">*</span></label>
              <common-dropdown
                id="country-dropdown"
                ref="countryDropdown"
                dropdown-id="countryDropdown"
                class="mt-2"
                btn-class="w-full text-left border px-[0.75rem] py-[0.375rem]"
                dropdown-class="max-w-[80vw] w-full pt-12"
                :close-on-click="true"
                @shown="countryFilter.focus()"
              >
                <span class="inline-block vti__flag" :class="currentCountry?.code.toLowerCase()" />
                <span class="ml-2">
                  {{ currentCountry?.name }}
                </span>
                <template #content>
                  <default-common-input
                    id="countryFilter"
                    ref="countryFilter"
                    v-model="filterCountryText"
                    :label="$t('Find your country')"
                    class="!absolute top-0 w-full px-5 py-1"
                  />
                  <template v-if="filterCountryArray?.length">
                    <div
                      v-for="(country, index) in filterCountryArray"
                      :key="index"
                      class="btn-text py-1 px-3"
                      :class="{
                        'bg-primary text-contrast': country.code === userInfo?.country,
                      }"
                      @click="userCountry = country.code"
                    >
                      <span class="vti__flag inline-block" :class="country.code.toLowerCase()" />
                      <span class="ml-2">
                        {{ country.name }}
                      </span>
                    </div>
                  </template>
                  <div v-else class="text-center">
                    {{ $t('Sorry, no matching country') }}
                  </div>
                </template>
              </common-dropdown>
              <span v-if="errors.noCountry" class="text-red-600 mt-2">{{ $t('Country is required') }}</span>
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="email" class="ml-2 font-medium">{{ $t('E-mail Address') }} <span class="text-red-600">*</span></label>
              <input
                id="email"
                v-model="formIntellectual.email"
                autocomplete="email"
                type="text"
                name="email"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
                :class="{ 'border-red-600': errors.unmatchedEmail }"
              >
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="email-confirm" class="ml-2 font-medium">{{ $t('Confirm E-mail Address') }} <span class="text-red-600">*</span></label>
              <input
                id="email-confirm"
                ref="emailConfirm"
                v-model="formIntellectual.emailConfirm"
                autocomplete="email"
                type="text"
                name="email-confirm"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
                :class="{ 'border-red-600': errors.unmatchedEmail }"
              >
              <span v-if="errors.unmatchedEmail" class="text-red-600 mt-2">{{ $t('Email does not match') }}</span>
            </div>
            <div class="col-span-2 md:col-span-1">
              <label for="phone" class="ml-2 font-medium">{{ $t('Phone Number') }} <span class="text-red-600">*</span></label>
              <input
                id="phone"
                v-model="formIntellectual.phone"
                autocomplete="tel"
                type="text"
                name="phone"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>
          </div>
        </div>
        <div class="mt-4">
          <h4 class="text-2xl font-medium border-b-1 border-gray-300 pb-1">
            {{ $t('IP Claim Details') }}
          </h4>

          <div class="grid grid-cols-2 gap-4">
            <div class="col-span-2 md:col-span-1 mt-4">
              <p>{{ $t('Are you the Rights Owner or an Agent') }}?</p>
              <div class="grid grid-cols-2 mt-3">
                <div class="col-span-2 md:col-span-1">
                  <input id="userType1" v-model="formIntellectual.userType" type="radio" name="user-type" value="1">
                  <label for="userType1" class="ml-2 font-medium">{{ $t('Right Owner') }}</label>
                </div>
                <div class="col-span-2 md:col-span-1">
                  <input id="userType2" v-model="formIntellectual.userType" type="radio" name="user-type" value="2">
                  <label for="userType2" class="ml-2 font-medium">{{ $t('Agent') }}</label>
                </div>
              </div>
            </div>
            <div class="col-span-2 md:col-span-1 mt-4">
              <label for="legal-name" class="ml-2 font-medium">{{ $t('IP Owner') }} <span>({{ $t('Legal Name') }})</span> <span class="text-red-600">*</span></label>
              <input
                id="legal-name"
                v-model="formIntellectual.legalName"
                type="text"
                name="legal-name"
                required
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50"
              >
            </div>

            <div class="col-span-2">
              <label for="reason" class="ml-2 font-medium">{{ $t('The specific concern is') }} <span class="text-red-600">*</span></label>
              <textarea
                id="reason"
                v-model="formIntellectual.additionalInfo.specificConcern"
                type="text"
                name="reason"
                required
                rows="3"
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50 focus-visible:outline-none"
              />
            </div>
            <div class="col-span-2">
              <label for="original" class="ml-2 font-medium">{{ $t('URL(s) to original work') }} <span class="text-red-600">*</span></label>
              <textarea
                id="original"
                v-model="formIntellectual.additionalInfo.originalWork"
                type="text"
                name="original"
                required
                rows="3"
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50 focus-visible:outline-none"
              />
            </div>
            <div class="col-span-2">
              <label for="additional-info" class="ml-2 font-medium">{{ $t('Additional Information (Optional)') }}</label>
              <textarea
                id="additional-info"
                v-model="formIntellectual.additionalInfo.otherInfo"
                type="text"
                name="additional-info"
                rows="3"
                class="border border-gray-300 w-full mt-2 focus:border-blue-500/50 focus-visible:outline-none"
              />
            </div>
            <div class="col-span-2">
              <p class="mb-0 mt-4">
                <span class="font-medium">{{ $t('File Attachment') }}</span>
                <span> ({{ $t('Upload limit is 5 files.') }})</span>
              </p>
              <common-file-selector v-model="formIntellectual.attachFile" :limit="5" @on-state-change="handleStateChange" />
              <p class="mb-0">
                {{ $t('fileUpload-text-1') }}.
              </p>
              <p>{{ $t('fileUpload-text-2') }}.</p>
            </div>
          </div>
        </div>
        <div class="mt-4">
          <h4 class="text-2xl font-medium border-b-1 border-gray-300 pb-1">
            {{ $t('Statements') }}
          </h4>
          <p>"{{ $t('Statements-text-1') }}."</p>
          <p>"{{ $t('Statements-text-2') }}."</p>

          <div class="mt-4">
            <input id="statement" v-model="formIntellectual.acceptStatement" type="checkbox">
            <label for="statement" class="ml-2 font-bold">{{ $t('Statements-text-3') }}</label>
          </div>
        </div>
        <button type="submit" class="text-white bg-[#dc3545] border-[#dc3545] px-6 py-2 font-medium mt-4 border-radius-override-p2" :disabled="!formIntellectual.acceptStatement || isFileUploading">
          {{ $t('Submit Claim') }}
        </button>
      </form>
      <form v-if="selectedTab === 'policy'" class="mt-4" @submit.prevent="submit">
        <div>
          <label for="campaignUrl2" class="font-medium">{{ $t('Campaign URL') }} <span class="text-red-600">*</span></label>
          <input id="campaignUrl2" v-model="campaignSlug" class="campaign-url border border-gray-300 w-full mt-2 focus:border-blue-500/50" type="text" required>
        </div>
        <div class="mt-4 flex flex-col gap-3">
          <div>
            <input id="hate-speech" v-model="formPolicy.hate" type="checkbox">
            <label class="ml-2" for="hate-speech">{{ $t('policy-text-1') }}</label>
          </div>
          <div>
            <input id="sexual" v-model="formPolicy.sexual" type="checkbox">
            <label class="ml-2" for="sexual">{{ $t('policy-text-2') }}</label>
          </div>
          <div>
            <input id="advocates-violence" v-model="formPolicy.violence" type="checkbox">
            <label class="ml-2" for="advocates-violence">{{ $t('policy-text-3') }}</label>
          </div>
          <div>
            <input id="misleading" v-model="formPolicy.misleading" type="checkbox">
            <label class="ml-2" for="misleading">{{ $t('policy-text-4') }}</label>
          </div>
          <div>
            <input id="advocates-illegal" v-model="formPolicy.illegal" type="checkbox">
            <label class="ml-2" for="advocates-illegal">{{ $t('policy-text-5') }}</label>
          </div>
        </div>
        <button type="submit" class="text-white bg-[#dc3545] border-[#dc3545] px-6 py-2 font-medium mt-4 border-radius-override-p2">
          {{ $t('Report') }}
        </button>
      </form>
    </div>
  </main>
</template>
<style scoped>
input[type='text'],
textarea {
  padding: 0.375rem 0.75rem;
}
</style>
