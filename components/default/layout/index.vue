<script lang="ts" setup>
import { useCampaignStore } from '~~/store/campaign'
import {handleDevtoolDetector} from "~/composables/common";

const { isShowCookieConsent, confirmCookieConsent } = useCookieConsent()
const campaignStore = useCampaignStore()
const campaignSlug = computed(() => {
  return campaignStore.modalCampaignUrl
})

const keyedRendering = ref(0)
watch(isCheckoutPage, () => {
  keyedRendering.value += 1
})

onMounted(() => {
  keyedRendering.value += 1

  const modalCampaignUrl = useRoute().query.campaign as string
  if (modalCampaignUrl) {
    campaignStore.$patch({modalCampaignUrl})
  }
  handleDevtoolDetector()
})
</script>

<template>
  <div id="default_layout">
    <default-header-checkout v-if="isCheckoutPage" :key="keyedRendering" />
    <default-header v-else />
    <slot />
    <default-footer-checkout v-if="isCheckoutPage" :key="keyedRendering" />
    <default-footer v-else />

    <lazy-cookie-consent v-if="isShowCookieConsent" @confirm="confirmCookieConsent" />
    <lazy-common-modal
      :key="campaignSlug"
      modal-class="<md:(w-full h-full) <lg:(w-[80vw]) lg:w-[798px] max-h-100vh md:max-h-[80vh]"
      :model-value="!!campaignSlug"
      @close-modal="campaignStore.$patch({modalCampaignUrl: ''})"
    >
      <lazy-default-campaign :key="campaignSlug" :campaign-slug="campaignSlug" :is-modal="true" />
    </lazy-common-modal>
  </div>
</template>

<style>
@import url('~/assets/themes/default/index.css');
</style>
