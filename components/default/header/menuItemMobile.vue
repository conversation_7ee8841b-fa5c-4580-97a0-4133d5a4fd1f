<script lang="ts" setup>
const localePath = useLocalePath()
const activeSubMenu = ref<boolean|number>()
const $viewport = useViewport()
defineProps({
  menu: {
    default: undefined,
    type: Object
  },
  menuLevel: {
    default: 0,
    type: Number
  },
  activeHeaderMenu: {
    default: false,
    type: Boolean
  }
})

function conditionalToggleHeaderMenu () {
  if (uiManager().showHeaderMenu && !$viewport.isGreaterOrEquals(VIEWPORT.tablet)) {
    uiManager().toggleHeaderMenu()
  }
}
</script>

<template>
  <li class="relative menu-item text-lg">
    <div class="flex justify-between items-center">
      <nuxt-link
        v-if="menu?.url.includes('/' + $getHost()) || menu?.url.startsWith('/')"
        :to="localePath(menu?.url)"
        class="btn-text w-full text-overflow-hidden py-1"
        @click="conditionalToggleHeaderMenu"
      >
        {{ $t( menu?.title ) }}
      </nuxt-link>
      <span
        v-else-if="menu?.url === '#'"
        :to="localePath(menu?.url)"
        class="btn-text w-full text-overflow-hidden py-1"
        @click="conditionalToggleHeaderMenu"
      >
        {{ $t( menu?.title ) }}
      </span>
      <a
        v-else
        :href="menu?.url"
        class="btn-text w-full text-overflow-hidden py-1"
        @click="conditionalToggleHeaderMenu"
      />
      <i
        v-if="menu?.submenu"
        class="btn-text text-2xl md:text-xl icon-sen-chevron-right transition-default"
        :class="{'transform rotate-90': activeHeaderMenu}"
        @click="$emit('updateActiveHeaderMenu')"
      />
    </div>
    <common-collapse
      v-if="menu?.submenu"
      as="ul"
      :when="activeHeaderMenu"
      class="sub-menu bg-[#e8e8e8] pl-4 overflow-hidden transition-default !md:(pl-0 h-auto bg-white absolute shadow-custom overflow-visible) z-3"
      :class="{
        'left-[100%] top-0': menuLevel,
      }"
    >
      <default-header-menu-item-mobile
        v-for="(menuItem, index) in menu?.submenu"
        :key="index"
        :menu="menuItem"
        :menu-level="menuLevel+1"
        :active-header-menu="activeSubMenu === index"
        @update-active-header-menu="activeSubMenu = activeSubMenu === index ? false : index"
      />
    </common-collapse>
  </li>
</template>
