<script lang="ts" setup>
const localePath = useLocalePath()
defineProps({
  menu: {
    default: undefined,
    type: Object
  },
  menuLevel: {
    default: 0,
    type: Number
  },
})

</script>

<template>
  <li class="relative menu-item text-lg">
    <div class="flex justify-between items-center">
      <nuxt-link
        v-if="menu?.url.includes('/' + $getHost()) || menu?.url.startsWith('/')"
        :to="localePath(menu?.url)"
        class="btn-text w-full text-overflow-hidden py-1"
      >
        {{ $t( menu?.title ) }}
      </nuxt-link>
      <span
        v-else-if="menu?.url === '#'"
        :to="localePath(menu?.url)"
        class="btn-text w-full text-overflow-hidden py-1"
      >
        {{ $t( menu?.title ) }}
      </span>
      <a
        v-else
        :href="menu?.url"
        class="btn-text w-full text-overflow-hidden py-1"
      />
      <i
        v-if="menu?.submenu"
        class="btn-text text-2xl md:text-xl icon-sen-chevron-right transition-default min-w-[20px]"
        :class="{'transform rotate-90': !menuLevel}"
      />
    </div>
    <ul
      v-if="menu?.submenu"
      class="sub-menu bg-[#e8e8e8] pl-4 transition-default !md:(pl-0 h-auto bg-white absolute shadow-custom max-h-[50vh]) z-3 hidden"
      :class="{
        'left-[100%] top-0': menuLevel,
      }"
    >
      <default-header-menu-item
        v-for="(menuItem, index) in menu?.submenu"
        :key="index"
        :menu="menuItem"
        :menu-level="menuLevel+1"
      />
    </ul>
  </li>
</template>
