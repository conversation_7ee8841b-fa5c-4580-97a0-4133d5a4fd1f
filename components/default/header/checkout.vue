<script lang="ts" setup>
const localePath = useLocalePath()
const { name: storeName, logo_url: logoUrl } = storeInfo()
</script>

<template>
  <header
    id="PageHeaderCheckout"
    class="sticky top-0 bg-white transition-default z-3"
  >
    <nav class="z-1 bg-white relative container flex items-center justify-center md:justify-start py-3 md:py-1 px-3">
      <nuxt-link :to="localePath('/')">
        <common-image
          v-if="logoUrl"
          img-class="h-6 md:h-8 rounded-none"
          img-id="headerLogo"
          :image="{path: logoUrl, type: 'logo'}"
          :alt="storeName"
          aria-label="home"
        />
        <div v-else>
          <span class="text-2xl md:text-3xl font-bold">{{ storeName }}</span>
        </div>
      </nuxt-link>
    </nav>
  </header>
</template>
