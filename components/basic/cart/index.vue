<script lang="ts" setup>
import { useCampaignStore } from '~/store/campaign'
import { useListingStore } from '~/store/listing'
import { useCartStore } from '~~/store/cart'
const { $i18n } = useNuxtApp()

const localePath = useLocalePath()

const listCartItem = computed(() => useCartStore().listCartItem)

const totalPrice = computed(() => useCartStore().getTotalPrice)

const sidebarVisibility = ref(false)
const editCartItemIntermediate = ref<CartItem | null>(null)
const editingCartItem = computed(() => {
  if (editCartItemIntermediate.value) {
    return useCartItem(editCartItemIntermediate.value)
  }

  return null
})
let itemLastEditedState: Partial<CartItem> | undefined = undefined

const spriteUrl = computed(() => {
  return `--sprite-url: url("${cdnURL.value}images/logo_cart_sprite.webp")`
})

const promotionsList = ref<Promotion[]>()
const relatedProducts = ref<Product[]>()
const campaignIds = computed(() => {
  return listCartItem.value.map((item) => item.campaign_id)
})

onMounted(async () => {
  // Related Products
  const listRelatedProducts: RelatedProductPostData = {
    type: 'post_sale',
    filter: listCartItem.value.map((item) => {
      return {
        product_id: item.product_id,
        campaign_id: item.campaign_id,
        template_id: item.template_id,
      }
    })
  }

  if (listRelatedProducts.filter.length > 0) {
    relatedProducts.value = await useListingStore().postRelatedProduct(listRelatedProducts)
  }

  promotionsList.value = await useCampaignStore().getPromotion(campaignIds)
})

function startEditCartItem (cartItem: CartItem) {
  itemLastEditedState = undefined
  editCartItemIntermediate.value = {...cartItem}
  sidebarVisibility.value = true
}
async function editItemOptionProxy (key, value) {
  itemLastEditedState = await editingCartItem.value?.updateCartItem(key, value)
}
function saveEditItem() {
  if (editCartItemIntermediate.value?.id && itemLastEditedState) {
    useCartStore().updateCartItemByID(editCartItemIntermediate.value.id, itemLastEditedState)
  } else {
    uiManager().createPopup($i18n.t('Unable to update cart item, please reload and try edit the item again'))
  }
  closeEditItem()
}
function closeEditItem () {
  sidebarVisibility.value = false
  editCartItemIntermediate.value = null
  itemLastEditedState = undefined
}

const isHiddenBottomCheckout = ref(true)
function checkBottomButton () {
  if (import.meta.server) { return }
  if (window.innerWidth >= 768) { return }
  const buttonRect = document.getElementById('proceedToCheckoutButton')?.getBoundingClientRect()
  isHiddenBottomCheckout.value = (buttonRect?.top || 0) >= 0
}
onMounted(() => {
  document.addEventListener('scroll', checkBottomButton)
})
onUnmounted(() => {
  document.removeEventListener('scroll', checkBottomButton)
})
</script>
<template>
  <main id="cartPage" :style="spriteUrl" class="container min-h-95 mt-6 mb-24">
    <client-only>
      <h1 class="font-bold-2 uppercase text-2xl md:text-4xl my-8 text-left">
        {{ $t('Your shopping bag') }}
      </h1>
      <div v-if="listCartItem.length" class="grid grid-cols-12 gap-6 xl:gap-24">
        <div class="col-span-12 md:col-span-8 product-list">
          <basic-cart-item
            v-for="(cartItem, index) in listCartItem"
            :key="cartItem.id"
            :index="index"
            :cart-item="cartItem"
            @edit-item="startEditCartItem(cartItem)"
          />
          <lazy-basic-cart-bundle-box
            v-if="!storeInfo().disable_promotion"
            class="mt-3"
            :campaign-ids="campaignIds"
          />
          <lazy-basic-common-promotions-list v-if="promotionsList?.length && !storeInfo().disable_promotion" :promotions-list="promotionsList" class="mt-4 mb-8" />
        </div>
        <div class="col-span-12 md:col-span-4 md:h-max sticky z-1 top-[100px] flex flex-col gap-4">
          <div class="py-7 px-5 border flex flex-col gap-4">
            <h3 class="uppercase font-bold-2 text-xl">{{ $t('Order Summary') }} | {{ $t('item', { count: totalQuantity().value }) }}</h3>
            <h3 class="uppercase font-bold-2 text-xl flex justify-between"><span>{{ $t('Subtotal') }}</span><span>{{ $formatPrice(totalPrice) }}</span></h3>
            <hr>
            <p class="text-md flex items-center justify-between">
              <span>{{ $t('Shipping & fees') }}</span> <span class="italic text-xs text-gray-500">{{ $t('Calculated at checkout') }}</span>
            </p>
          </div>
          <div class="flex flex-col gap-4">
            <button
              id="proceedToCheckoutButton"
              class="uppercase font-bold-2 text-white bg-red-500 border border-red-500 hover:(opacity-70) text-xl py-2 px-1"
              dusk="proceed-to-checkout-button"
              data-test-id="cart-proceed-to-checkout-button"
              @click.stop.prevent="createOrder()"
            >
              <common-loading-dot v-if="!!isLoading" />
              <span v-else>{{ $t('Proceed to checkout') }}</span>
            </button>
            <nuxt-link class="uppercase font-bold-2 border border-black hover:(opacity-70) text-xl py-2 px-1 text-center" :to="localePath('/')">
              {{ $t('Continue Shopping') }}
            </nuxt-link>
          </div>
        </div>
      </div>
      <div v-else>
        <h6 class="py-3 text-lg" data-test-id="cart-is-empty">
          {{ $t('Your cart is empty') }} :(
        </h6>
        <div class="relative">
          <nuxt-link class="py-2 px-1 min-h-[45px] font-bold-2 text-lg uppercase absolute text-white bg-[#1b1b1b] text-center mt-6 hover:(opacity-70) <md:(w-full) md:(min-w-[288px])" :to="localePath('/')">
            {{ $t('Continue Shopping') }}
          </nuxt-link>
        </div>
      </div>

      <div
        v-if="listCartItem.length"
        class="bg-white border-t bottom-checkout z-1"
        :class="{
          'hidden': isHiddenBottomCheckout,
          '<lg:(bottom-fixed w-full)': !isHiddenBottomCheckout
        }"
      >
        <h4 class="font-bold-2 text-xl uppercase container my-4">{{ $t('Checkout') }}</h4>
        <div class="border-t grid grid-cols-2">
          <div class="col-span-1 font-bold-2 uppercase flex justify-center items-center gap-2">
            <span class="text-[var(--color-gray-2)] text-md">{{ $t('Subtotal') }}</span> <span class="text-lg">{{ $formatPrice(totalPrice) }}</span>
          </div>
          <button
              id="proceedToCheckoutButton"
              class="uppercase font-bold-2 text-white bg-red-500 border border-red-500 hover:(opacity-70) text-xl py-2 px-1"
              dusk="proceed-to-checkout-button"
              data-test-id="cart-proceed-to-checkout-button"
              @click.stop.prevent="createOrder()"
            >
              <common-loading-dot v-if="!!isLoading" />
              <span v-else>{{ $t('Checkout') }}</span>
            </button>
        </div>
      </div>

      <lazy-common-product-carousel
        v-if="relatedProducts?.length"
        class="mt-16"
        title-class="text-center uppercase md:text-4xl text-xl font-bold-2 mb-2"
        title="Frequently bought together"
        :products="relatedProducts"
        :splide-settings="basicProductSplideSetting"
        :splide-hide-arrow-on-ends="true"
      >
        <template #default="{ product, index }">
          <basic-common-product-item :product="product" :index="index" />
        </template>
      </lazy-common-product-carousel>
    </client-only>
  </main>
  <common-sidebar
    side="right"
    class="editCartItemSidebar"
    :additional-container-class="'z-4'"
    :close-button-icon="'icon-basic-close'"
    :visible="sidebarVisibility"
    @update:visible="(visibility) => { sidebarVisibility = visibility }"
  >
    <div class="px-6 py-4 overflow-y-auto h-full w-full">
      <div v-if="editingCartItem" class="flex flex-col gap-4">
        <common-image
          img-class="w-full btn hover:shadow-custom"
          :image="{
            path: editCartItemIntermediate?.thumb_url,
            color: editCartItemIntermediate?.options?.color,
            type: 'full_hd'
          }"
          :alt="editCartItemIntermediate?.campaign_title"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(editCartItemIntermediate?.thumb_url)"
        />

        <template v-if="editingCartItem.campaignData.products">
          <span
            v-if="editingCartItem.campaignData.products.length===1 || editCartItemIntermediate?.personalized === 1 || editCartItemIntermediate?.personalized === 2"
            class="col-span-4 md:col-span-2 capitalize"
            data-test-id="cart-item-product-name"
            data-test-prop="false"
          >
            {{ editCartItemIntermediate?.product_name }}
          </span>
          <common-dropdown
            v-else
            class="col-span-4 md:col-span-2"
            btn-class="text-overflow-hidden btn-border w-full text-sm pl-2 py-1 z-1"
            dropdown-class="md:min-w-full"
            data-test-id="cart-item-product-name"
            data-test-prop="true"
          >
            <span>{{ editCartItemIntermediate?.product_name }}</span>
            <template #content>
              <div
                v-for="(product, productIndex) in editingCartItem.campaignData.products"
                :key="productIndex"
                sp-action="change_product"
                data-test-id="change-product"
                class="py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast)"
                :class="{'bg-primary text-contrast': product.id === editCartItemIntermediate?.product_id}"
                @click="product.id === editCartItemIntermediate?.product_id ? '' : editItemOptionProxy('product', product)"
              >
                {{ product.name }}
              </div>
            </template>
          </common-dropdown>
        </template>

        <template
          v-if="editCartItemIntermediate?.optionList && editCartItemIntermediate.options"
        >
          <template
            v-for="(key, optionListIndex) in Object.keys(editCartItemIntermediate?.optionList)"
          >
            <span
              v-if="editCartItemIntermediate?.optionList[key].length > 1 && (editCartItemIntermediate?.personalized === 1 || editCartItemIntermediate?.personalized === 2)"
              :key="`text-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1 capitalize"
              data-test-id="cart-item-option"
              data-test-prop="false"
            >
              {{ editCartItemIntermediate?.options[key] }}
            </span>
            <common-dropdown
              v-else-if="editCartItemIntermediate?.optionList[key].length > 1"
              :key="`dropdown-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1"
              btn-class="capitalize text-overflow-hidden btn-border w-full text-sm pl-2 py-1"
              dropdown-class="md:min-w-full"
              data-test-id="cart-item-option"
              :data-test-prop="key"
            >
              <span>{{ key?.replace(/_/g, ' ') }}: {{ editCartItemIntermediate.options[key]?.replace(/_/g, ' ') }}</span>
              <template #content>
                <div
                  v-for="(optionItem, optionIndex) in editCartItemIntermediate?.optionList[key]"
                  :key="optionIndex"
                  :sp-action="`change_${key}`"
                  class="capitalize py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center"
                  :class="{'bg-primary text-contrast': optionItem === editCartItemIntermediate.options[key]}"
                  :data-test-id="`cart-item-option-change-${key}`"
                  @click="optionItem === (editCartItemIntermediate?.options && editCartItemIntermediate.options[key]) ? '' : editItemOptionProxy('option', {optionName: key, optionItem})"
                >
                  <lazy-common-color-item v-if="key==='color'" :color="optionItem" size="sm" class="inline-block mr-2" />
                  {{ optionItem }}
                </div>
              </template>
            </common-dropdown>
          </template>
        </template>

        <div v-if="editCartItemIntermediate?.custom_options" class="col-span-4">
          <ul class="pl-5 list-disc">
            <li v-for="(value, key) in editCartItemIntermediate.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
              <div v-if="key === 'customImage'">
                {{ $t('Your image') }}:
                <a
                  :href="$imgUrl({path: value, type: 'full'})"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(value)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
              </div>
            </li>
          </ul>
        </div>
        <div v-if="editCartItemIntermediate?.customer_custom_options?.length" class="col-span-4 <md:text-center">
          <ul v-for="(groupOptions, groupNumber) in editCartItemIntermediate.customer_custom_options" :key="groupNumber" class="pl-5 list-disc mt-3">
            <li v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
              <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                  :href="$imgUrl({path: customOption.value as string, type: 'full'})"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(customOption.value as string)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
              </div>
            </li>
          </ul>
        </div>

        <div class="relative">
          <span class="btn-vintage-shadow w-full text-center py-2 text-white font-bold rounded-full relative block bg-[#222] cursor-pointer z-1" @click="saveEditItem">{{ $t('Save') }}</span>
        </div>
      </div>
      <div v-else class="flex flex-col justify-center items-center gap-4">
        <span>{{ $t('An error has occurred. Please try edit the item again.') }}</span>
        <div class="relative w-full">
          <span class="btn-vintage-shadow w-full text-center py-2 text-white font-bold rounded-full relative block bg-[#222] cursor-pointer z-1" @click="closeEditItem">{{ $t('Close') }}</span>
        </div>
      </div>
    </div>
  </common-sidebar>
</template>
<style scoped>
.bottom-checkout {
  box-shadow: 0 0 4px 0 rgba(0,0,0,0.4);
}
</style>
