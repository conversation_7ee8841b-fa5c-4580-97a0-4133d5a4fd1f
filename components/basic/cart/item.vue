<script lang="ts" setup>
const props = defineProps({
  index: {
    default: 0,
    type: Number
  },
  cartItem: {
    default: undefined,
    type: Object as PropType<CartItem>
  }
})
const $emit = defineEmits(['editItem'])
const localePath = useLocalePath()
const $viewport = useViewport()
const {
  isHasCampaignData,
  isEditCart,
  isShowModalDeleteCartItem,
  currentVariant,
  removeCartItem,
  updateQuantity,
  totalPriceBundleWithCustomOptionFee,
  priceWithCustomOptionFee,
  totalPriceWithCustomOptionFee
} = useCartItem(props.cartItem as CartItem)

function emitEditItem() {
  $emit('editItem')
}

const yesButtonRef = ref()
function focusButton() {
  nextTick(() => {
    yesButtonRef.value?.focus()
  })
}
</script>

<template>
  <div class="py-4 border-b" data-test-id="cart-item">
    <template v-if="isHasCampaignData">
      <div class="flex gap-2">
        <!-- Use base64 image if available for AI mockup campaigns -->
        <img
          v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
          :src="cartItem.design_image_base64"
          :alt="cartItem?.campaign_title"
          :title="$t('Click to view image')"
          class="w-32 h-35 mr-2 btn hover:shadow-custom"
          @click="uiManager().viewImage(cartItem.design_image_base64)"
        >
        <common-image
          v-else
          img-class="w-32 h-35 mr-2 btn hover:shadow-custom"
          :image="{
            path: cartItem?.thumb_url,
            color: cartItem?.options?.color
          }"
          :alt="cartItem?.campaign_title"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(cartItem?.thumb_url)"
        />
        <div class="w-full max-w-[calc(100%-90px)] grid grid-cols-12 h-min gap-2">
          <div class="col-span-12">
            <div class="flex justify-between">
              <nuxt-link :to="localePath(getProductUrl(cartItem?.product_url))" class="text-overflow-hidden">
                <h5 class="text-overflow-hidden font-bold-2 text-md lg:text-xl" :title="cartItem?.campaign_title">
                  {{ cartItem?.campaign_title }}
                </h5>
              </nuxt-link>

              <span class="hover:opacity-60 cursor-pointer" data-test-id="cart-item-remove" @click="isShowModalDeleteCartItem = true"><i class="icon-basic-close text-2xl md:text-3xl" /></span>
            </div>

            <div class="text-gray-500 text-sm">
              <template v-if="cartItem?.product_name">
                <p
                  class="col-span-4 md:col-span-2 capitalize text-md"
                  data-test-id="cart-item-product-name"
                  data-test-prop="false"
                >
                  {{ cartItem?.product_name }}
                </p>
              </template>
              <template
                v-for="(key, optionListIndex) in Object.keys(cartItem?.optionList)"
              >
                <span
                  v-if="cartItem?.optionList[key].length > 1 && (cartItem?.personalized === 1 || cartItem?.personalized === 2)"
                  :key="`text-${optionListIndex}`"
                  class="col-span-4 sm:col-span-2 md:col-span-1 capitalize text-md"
                  data-test-id="cart-item-option"
                  data-test-prop="false"
                >
                  {{ key }}: {{ cartItem?.options[key] }}
                </span>
                <p
                  v-else-if="cartItem?.optionList[key].length > 1"
                  :key="`dropdown-${optionListIndex}`"
                  class="col-span-4 sm:col-span-2 md:col-span-1 capitalize text-overflow-hidden text-sm"
                >
                  <span>{{ key?.replace(/_/g, ' ') }}: {{ cartItem?.options[key] }}</span>
                </p>
              </template>
              <div v-if="cartItem?.custom_options">
                <div v-for="(value, key) in cartItem.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
                  <div v-if="key === 'customImage'">
                    {{ $t('Your image') }}:
                    <a
                      :href="$imgUrl({ path: value, type: 'full' })"
                      target="_blank"
                      class="btn-text text-blue-900"
                      @click.prevent="uiManager().viewImage(value)"
                    >{{ $t('View image') }}</a>
                  </div>
                  <div v-else class="text-overflow-hidden">
                    {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
                  </div>
                </div>
              </div>
              <div v-if="cartItem?.customer_custom_options.length">
                <div v-for="(groupOptions, groupNumber) in cartItem.customer_custom_options" :key="groupNumber" class="capitalize mt-3" style="font-size: 13px;">
                  <div v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
                    <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
                      {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                        :href="$imgUrl({ path: customOption.value as string, type: 'full' })"
                        target="_blank"
                        class="btn-text text-blue-900"
                        @click.prevent="uiManager().viewImage(customOption.value as string)"
                      >{{ $t('View image') }}</a>
                    </div>
                    <div v-else class="text-overflow-hidden">
                      {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
                    </div>
                  </div>
                </div>
              </div>

              <button class="mt-2 text-black" @click="emitEditItem">
                <i class="icon-sen-lead-pencil" /> <span class="text-xs underline">{{ $t('Edit') }}</span>
              </button>
            </div>

            <div class="col-span-12 order-1">
              <template v-if="currentVariant?.out_of_stock">
                <span class="text-red-500 font-bold" data-test-id="cart-item-oos">
                  {{ $t("Out of stock") }}
                </span>
              </template>
              <template v-else>
                <div class="relative flex gap-4 <lg:flex-col lg:(mt-8 justify-between)">
                  <div class="flex lg:flex-col">
                    <h4 class="hidden lg:block uppercase text-lg font-bold-2">
                      {{ $t('Quantity') }}
                    </h4>
                    <common-dropdown
                      btn-class="<lg:(border-b border-black py-1) lg:btn-border py-2 w-full pl-4 mt-4"
                      data-test-id="cart-item-qty-button"
                    >
                      <span data-test-id="cart-item-qty" class="mr-4 lg:mr-16 text-md">
                        {{ cartItem?.quantity }}
                      </span>
                      <template #content>
                        <div
                          v-for="item in 50"
                          :key="item"
                          class="min-w-[50vw] md:min-w-16 btn-text px-4"
                          :value="item"
                          :class="{ 'bg-primary text-contrast': cartItem?.quantity === item }"
                          @click="updateQuantity(item)"
                        >
                          {{ item }}
                        </div>
                      </template>
                    </common-dropdown>
                  </div>
                  <div class="font-bold-2 flex gap-2 text-md uppercase items-end lg:mb-3">
                    <p class="<lg:text-[var(--color-gray-2)]">
                      {{ $t('Subtotal') }}:
                    </p>
                    <div data-test-id="cart-item-total-price" class="text-xl">
                      <div v-if="totalPriceBundleWithCustomOptionFee" class="flex flex-col">
                        <del class="text-gray-500">
                          {{ $formatPrice(totalPriceWithCustomOptionFee, cartItem?.currency_code) }}
                        </del>
                        <span>
                          {{ $formatPrice(totalPriceBundleWithCustomOptionFee, cartItem?.currency_code) }}
                        </span>
                      </div>
                      <span v-else>
                        {{ $formatPrice(totalPriceWithCustomOptionFee, cartItem?.currency_code) }}
                      </span>
                    </div>
                    <div v-if="cartItem?.quantity > 1" data-test-id="cart-item-price" class="text-gray-500 flex">
                      (<div v-if="totalPriceBundleWithCustomOptionFee">
                        <span>
                          {{ $formatPrice(priceWithCustomOptionFee * (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100, cartItem?.currency_code) }}
                        </span>
                      </div>
                      <span v-else>
                        {{ $formatPrice(priceWithCustomOptionFee, cartItem?.currency_code) }}
                      </span>&nbsp;
                      {{ $t('each') }})
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <common-modal
          :model-value="isShowModalDeleteCartItem"
          :close-icon="false"
          @close-modal="isShowModalDeleteCartItem = false"
          @shown="focusButton"
        >
          <div
            class="w-150 max-w-[90vw] text-center p-8"
            data-test-id="modal-confirm-remove-item"
          >
            <h4 class="mb-4 font-medium text-xl md:text-2xl">
              {{ $t('Are you sure you want to remove this item?') }}
            </h4>
            <div class="grid grid-cols-2 gap-4">
              <button
                ref="yesButtonRef"
                class="col-span-1 uppercase text-contrast font-bold-2 text-lg px-3 py-2 border border-black bg-[var(--color-black-1)] hover:opacity-70"
                data-test-id="accept-remove-item"
                @click="removeCartItem"
              >
                {{ $t('Remove') }}
              </button>
              <button
                class="col-span-1 uppercase font-bold-2 text-lg px-3 py-2 border border-black hover:opacity-70"
                data-test-id="decline-remove-item"
                @click="isShowModalDeleteCartItem = false"
              >
                {{ $t('Cancel') }}
              </button>
            </div>
          </div>
        </common-modal>
      </div>
    </template>
    <template v-else>
      <common-image
        img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
        :image="{
          path: cartItem?.thumb_url,
          color: cartItem?.options?.color
        }"
        :alt="cartItem?.campaign_title"
        :title="$t('Click to view image')"
        @click="uiManager().viewImage(cartItem?.thumb_url)"
      />
      <div v-if="isEditCart || $viewport.isGreaterOrEquals(VIEWPORT.tablet)" class="w-full max-w-[calc(100%-90px)]" data-test-id="cart-item-not-exist">
        <h3 class="font-semibold w-full">
          {{ $t('The campaign does not exist or has been deleted. Kindly remove the product.') }}
        </h3>
        <button class="btn-border p-2 py-1 text-xl mt-3" data-test-id="cart-item-remove" @click="removeCartItem">
          {{ $t('Remove') }}
        </button>
      </div>
    </template>
  </div>
</template>
