<script lang="ts" setup>

const props = defineProps({
  order: {
    type: Object as PropType<Order>,
    default: () => ({})
  }
})

const $emit = defineEmits(['updateCheckoutDiscount'])
const reEvaluateFlag: Ref<Boolean> = ref(false)

const {
  discountList,
  DISCOUNT_RETURN_CODE,

  userTrigger,
  promotionShow,
  isShowModalDiscountList,
  // discountErrorCode,
  discountCode,
  discountInput,
  getDiscountList,
  evaluateDiscountCode
} = useCheckoutDiscount(props.order)

const {
  discountErrorCode
} = useDiscount()

onMounted(async () => {
  getDiscountList()
  const { query } = useRoute()
  if (query.discount) {
    discountCode.value = query.discount as string
    await nextTick()
    $emit('updateCheckoutDiscount', discountCode.value)
  }
  if (props.order.discount_code) {
    evaluateDiscountCode()
  }
})

function updateDiscount (discountCodeFromList?: string) {
  discountCode.value = discountCodeFromList || discountCode.value
  isShowModalDiscountList.value = false
  $emit('updateCheckoutDiscount', discountCode.value , reEvaluateFlag)
}

function reloadDiscountCode () {
  evaluateDiscountCode ()
}

watch(
  [
    () => props.order.discount_code,
    () => props.order.total_discount,
    () => reEvaluateFlag.value,
  ], reloadDiscountCode
)

</script>
<template>
  <div>
    <button
      class="btn-text text-primary"
      @click="userTrigger = true; promotionShow = !promotionShow"
    >
      {{ $t('Have a discount code?') }}
    </button>
    <div v-if="discountCode || promotionShow" id="coupon-form" class="flex gap-4 mt-1 mb-4">
      <div class="w-full">
        <div class="flex <md:flex-wrap gap-3 md:gap-2 justify-center">
          <input
            ref="discountInput"
            v-model="discountCode"
            class="<md:(w-full) bg-[var(--input-bg)] border-b border-[var(--color-gray-4)] flex-grow py-2 px-3 transition-all"
            :class="{
              'input-error': (discountErrorCode && discountErrorCode > 0),
            }"
            type="text"
            :placeholder="$t('Discount Code')"
          >
          <button
            class="<md:flex-grow bg-[var(--color-black-1)] hover:opacity-70 text-contrast font-bold-2 uppercase py-2 min-w-25"
            type="button"
            @click="updateDiscount()"
          >
            {{ $t('Apply') }}
          </button>
          <button
            class="<md:flex-grow hover:(opacity-70 bg-[var(--color-black-1)] text-contrast) border border-black transition-all font-bold-2 uppercase py-2 px-3 min-w-max"
            type="button"
            @click="isShowModalDiscountList = true"
          >
            {{ $t('View code') }}
          </button>
        </div>
        <div v-if="discountErrorCode !== null" class="mt-2">
          <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.SUCCESS" class="text-green-500">{{ $t('Discount applied') }}</span>
          <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.INVALID" class="text-red-600">{{ $t('Discount code is invalid') }}</span>
          <span v-if="discountErrorCode === DISCOUNT_RETURN_CODE.CONDITION_NOT_MET" class="text-red-600">{{ $t("Discount conditions have not been met") }}. {{ $t("Do you want to add more items?") }}</span>
        </div>
      </div>
      <lazy-common-modal
        :model-value="isShowModalDiscountList"
        :title="$t('Select a discount code')"
        title-class="text-2xl"
        modal-class="py-2 px-5 md:py-4 md:px-10 <md:w-full <lg:(w-[80vw]) lg:w-[798px] max-h-100vh max-h-[80vh]"
        modal-container-class="z-9999"
        @close-modal="isShowModalDiscountList = false"
      >
        <basic-common-promotions-list
          v-if="discountList.length"
          :promotions-list="discountList"
          :is-checkout-page="true"
          class="mt-4 mb-8"
          @update-discount-apply="updateDiscount"
        />
      </lazy-common-modal>
    </div>
  </div>
</template>
<style scoped>
input:focus {
  border-color: var(--input-focus-color);
  box-shadow: 0 1px 0 0 var(--input-focus-color);
}

input.input-error {
  border-color: red;
  box-shadow: 0 1px 0 0 red;
}
</style>