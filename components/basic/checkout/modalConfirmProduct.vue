<script lang="ts" setup>
import { useCartStore } from '~/store/cart'

defineProps({
  order: {
    required: true,
    type: Object as PropType<Order>
  }
})

const cartStore = useCartStore()
const isShow = ref(false)
const confirmAble = ref(false)
const cloneProduct = ref<CartItem[]>([])

const $emit = defineEmits(['submit'])

onMounted(() => {
  if (cartStore.token === useRoute().params.token) {
    if (cartStore.getTotalQuantity >= 2) {
      cloneProduct.value = cartStore.listCartItem
        .filter(item => !item.isCheckQuantity)
        .map(item => JSON.parse(JSON.stringify(item)) as CartItem)
      cloneProduct.value.forEach((item) => { item.isCheckQuantity = true })
      isShow.value = !!cloneProduct.value.length
    }
  } else {
    isShow.value = false
  }
})

async function submit () {
  if (confirmAble.value) {
    cartStore.$patch({ listCartItem: cloneProduct.value })
    await createOrder()
    $emit('submit')
    isShow.value = false
  } else {
    isShow.value = false
  }
}
</script>
<template>
  <common-modal
    v-model="isShow"
    modal-id="modalConfirmProduct"
    :title="$t('Is this quantity correct?')"
    :close-icon="'icon-basic-close'"
    :title-container-class="'border-b border-[var(--color-gray-5)] p-3'"
    modal-class="pb-20 <md:(w-9/10 h-9/10) min-w-[20vw] max-w-[100vw] max-h-[66vh] flex flex-col"
    modal-container-class="z-9999"
  >
    <div class="overflow-y-auto p-3">
      <default-cart-item-update-quantity
        v-for="(cartItem, index) in cloneProduct"
        :key="index"
        :cart-item="cartItem"
        :convert-currency-code="order.currency_code"
        :convert-currency-rate="order.currency_rate"
        @update-quantity="cartItem.quantity = $event; confirmAble = true"
      />
    </div>
    <div class="bottom-fixed p-1 text-2xl w-full">
      <button
        class="font-bold-2 bg-[var(--color-black-1)] text-white uppercase hover:opacity-70 transition-all py-2 border-t w-full"
        :disabled=" !!isLoading"
        @click="submit"
      >
        <common-loading-dot v-if="isLoading" />
        <span v-else-if="confirmAble">
          {{ $t('Save changes') }}
        </span>
        <span v-else>
          {{ $t("Confirm") }}
        </span>
      </button>
    </div>
  </common-modal>
</template>
