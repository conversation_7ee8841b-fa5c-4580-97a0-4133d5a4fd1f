<script lang="ts" setup>
const { listProductData } = await useHomePage()
</script>
<template>
  <main id="homePage" class="mb-10">
    <default-home-banner-carousel v-if="storeInfo().banners.length" />
    <lazy-default-home-collection-banner-carousel
      v-if="storeInfo().collection_banners.length"
      :splide-settings="basicSplideCollectionBannerSplideSetting"
    />
    <template v-for="(productsData, index) in listProductData">
      <common-product-carousel
        v-if="productsData?.products?.length"
        :key="index"
        class="container mt-15"
        title-class="font-bold-2 uppercase md:text-4xl text-xl font-bold mb-6"
        :title="productsData.name"
        :products="productsData.products"
        :force-static-grid="true"
      >
        <template #default="{ product, index: itemIndex }">
          <basic-common-product-item :product="product" :index="itemIndex" />
        </template>
      </common-product-carousel>
    </template>
  </main>
</template>
