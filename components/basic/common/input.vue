<script lang="ts" setup>
defineProps({
  id: {
    default: undefined,
    type: String
  },
  type: {
    default: undefined,
    type: String
  },
  label: {
    default: undefined,
    type: String
  },
  placeholder: {
    default: undefined,
    type: String
  },
  pattern: {
    default: undefined,
    type: String
  },
  maxLength: {
    default: undefined,
    type: Number
  },
  modelValue: {
    default: undefined,
    type: String
  },
  state: {
    default: undefined,
    type: [String, Boolean]
  },
  message: {
    default: undefined,
    type: String
  },
  inputClass: {
    default: undefined,
    type: String
  },
  autocomplete: {
    default: undefined,
    type: String,
  },
  additionalLabel: {
    default: undefined,
    type: String,
  },
})

const input = ref()
const isInputFocusing = ref(false)

defineExpose({
  focus
})

function focus () {
  input.value.focus()
}
</script>

<template>
  <div class="grid grid-cols-3">
    <label
      v-if="label"
      :for="id"
      class="col-span-3 text-overflow-hidden uppercase font-bold-2 flex items-center text-md"
      :class="{
        'lg:col-span-1': label
      }"
    >
      {{ label }}
    </label>
    <input
      :id="id"
      ref="input"
      :name="id"
      :autocomplete="autocomplete || id"
      :type="type"
      :pattern="pattern"
      :value="modelValue"
      :maxlength="maxLength"
      class="col-span-3 bg-transparent w-full px-3 h-12 bg-[var(--input-bg)] border-b border-[var(--color-gray-4)] transition-all"
      :class="[
        inputClass,
        label ? 'lg:col-span-2' : '',
        state === false ? 'input-error' : '']"
      :placeholder="placeholder"
      @input="$emit('update:modelValue', $event.target?.value); $emit('input', $event.target?.value)"
      @focus="$emit('focus', $event); isInputFocusing = true"
      @blur="$emit('blur', $event); isInputFocusing = false"
    >

    <div v-if="additionalLabel" class="lg:col-span-1" />
    <span v-if="additionalLabel" class="col-span-3 lg:col-span-2 text-[var(--color-gray-2)] my-2">{{ additionalLabel }}</span>

    <div v-if="state !== undefined && message" class="lg:col-span-1" />
    <span
      v-if="state !== undefined && message"
      class="mt-1 text-sm"
      :class="{
        '!text-orange-500': state === 'warning',
        '!text-green-500': state === true,
        '!text-red-500': state === false,
        'lg:col-span-2': label,
        'cursor-pointer select-none': (message.toLowerCase().includes('click'))
      }"
      @click="$emit('click:feedback')"
    >{{ $t(message) }}</span>
    <slot />
  </div>
</template>
<style scoped>
input:focus {
  border-color: var(--input-focus-color);
  box-shadow: 0 1px 0 0 var(--input-focus-color);
}

input.input-error {
  border-color: red;
  box-shadow: 0 1px 0 0 red;
}

label:has(+ input:focus) {
  color: var(--input-focus-color);
}
</style>