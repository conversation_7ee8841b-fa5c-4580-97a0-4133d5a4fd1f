<script lang="ts" setup>
import { type PropType } from 'vue'
const { $formatPrice } = useNuxtApp()
const $viewport = useViewport()

const localePath = useLocalePath()
const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  index: {
    default: undefined,
    type: Number
  }
})

const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `/${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `?color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))
const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))

const hoverColor = ref('')
const dynamicProductColor = computed(() => {
  if (props.color) {
    return props.color
  }

  return hoverColor.value
})

function productInfo () {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}

const productColors = computed(() => {
  const productColorsProp = props.product?.product_options?.color
  if (!productColorsProp) {
    return {
      plusIcon: false,
      colors: [],
    }
  }

  const colorLimitCounts = $viewport.isGreaterOrEquals(VIEWPORT.tablet) ? 7 : 4
  const colors = productColorsProp.slice(0, colorLimitCounts)
  const plusIcon = productColorsProp.length > colorLimitCounts

  return {
    plusIcon,
    colors,
  }
  // return props.product.options.color.slice(0, 7)
})

const productSizesRange = computed(() => {
  if (!props.product?.product_options?.size) {
    return null
  }

  if (props.product.product_options.size.length === 1) {
    return props.product.product_options.size[0]
  }

  const sizes = props.product.product_options.size

  return `${sizes[0]} - ${sizes[sizes.length - 1]}`
})
</script>

<template>
  <nuxt-link class="relative product-list-item select-none overflow-hidden w-full" :to="localePath(campaignUrl)" data-test-id="product-list-item" :title="productTitle">
    <div class="w-full pt-[125%]">
      <common-image
        :key="dynamicProductColor"
        :image="{
          path: product?.thumb_url,
          type:'list',
          color: dynamicProductColor
        }"
        :alt="product?.name"
        :title="productTitle"
        :fetchpriority="(index === 0) ? 'high' : ''"
        class="absolute top-0 left-0"
      />
    </div>
    <div class="pt-2 md:pt-6 max-w-[95%]">
      <div v-if="productColors.colors?.length" class="flex gap-2">
        <basic-common-color-item v-for="pColor in productColors.colors" :key="pColor" :color="pColor" :size="'xsm'" :rounded="true" @mouseover="hoverColor = pColor" />
        <template v-if="productColors.plusIcon">
          <i class="icon-basic-plus h-4 w-4 font-bold text-[var(--color-gray-4)]" />
        </template>
      </div>
      <div class="flex justify-between items-center mt-2 text-[var(--color-gray-6)] uppercase text-xs">
        <p v-if="product?.product_name" class="truncate">
          {{ product.product_name }}
        </p>
        <p v-if="productSizesRange" class="truncate">
          {{ productSizesRange }}
        </p>
      </div>
      <h4 class="text-md mt-1 truncate-lines">{{ productTitle }}</h4>

      <template v-if="storeInfo().store_type !== 'google_ads' && productOldPrice > productPrice">
        <div class="mt-4 flex flex-col gap-1">
          <del class="old-price font-bold-2 leading-none">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
          <span class="font-regular text-xl text-[red] leading-none">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
          <span class="text-sm text-[red] mt-2">{{ $t('Sale') }}</span>
        </div>
      </template>
      <template v-else>
        <h6 class="font-bold-2 text-lg mt-4">
          {{ $formatPrice(productPrice, product?.currency_code) }}
        </h6>
      </template>
    </div>
  </nuxt-link>
</template>
