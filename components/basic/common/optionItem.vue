<script lang="ts" setup>
defineProps({
  value: {
    default: '',
    type: String
  },
  active: {
    default: false,
    type: Boolean
  },
  disabled: {
    default: 0,
    type: Number,
  },
})
</script>

<template>
  <div
    class="p-0.5 relative"
    :class="{
      'border-[var(--color-black-1)] rounded-[var(--sp-border-radius-2)] border-2': active,
      'border-transparent border-1': !active,
    }"
  >
    <div
      class="min-w-9 min-h-9 font-regular text-sm uppercase border border-[var(--color-gray-4)] flex justify-center items-center px-2"
      :class="{
        'opt-item-hover': !active,
        'text-contrast bg-[var(--color-black-1)]': active,
      }"
    >
      {{ $t(value) }}
    </div>
    <div v-if="disabled" class="absolute top-0 left-0 w-full h-full p-1">
      <svg width="100%" height="100%"><line y1="100%" x2="100%" stroke="#DADADA"/><line y1="100%" x2="100%" stroke="#1B1B1B" stroke-opacity="0.6" transform="translate(.5 .5)"/></svg>
    </div>
  </div>
</template>
