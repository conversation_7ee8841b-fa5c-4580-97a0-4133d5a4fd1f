<script lang="ts" setup>

const props = defineProps({
  color: {
    required: true,
    type: String
  },
  size: {
    default: 'md',
    type: String
  },
  active: {
    default: false,
    type: Boolean
  },
  rounded: {
    default: false,
    type: Boolean,
  },
  selectable: {
    default: false,
    type: Boolean
  }
})
const hexColor = computed(() => {
  return colorVal(props.color)
})

</script>

<template>
  <div
    :class="{
      'rounded-full': rounded,
      'p-0.5 border-[var(--color-black-1)]': active,
      'border-transparent opt-item-hover-2': !active,
      'border-2': selectable,
    }"
  >
    <div
      :style="`background: ${hexColor}`"
      class="relative border border-[var(--color-gray-5)]"
      :class="{
        'rounded-full': rounded,
        'h-4 w-4': size==='xs',
        'h-4.5 w-4.5': size==='xsm',
        'h-5 w-5': size==='sm',
        'h-6 w-6': size==='md',
        'h-7 w-7': size==='lg',
        'h-8 w-8': size==='xl',
        'h-9 w-9': size==='2xl',
      }"
    />
  </div>
</template>
