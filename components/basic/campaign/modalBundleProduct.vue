<script lang="ts" setup>
const props = defineProps({
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  isAddToCart: {
    default: undefined,
    type: Boolean
  }
})

function updateOption({ key, value }: any) {
  if (props.currentProduct) {
    let optionsListFull = props.currentProduct?.options
    if (typeof optionsListFull === 'string') {
      optionsListFull = JSON.parse(optionsListFull)
    }
    const optionKeys = Object.keys(optionsListFull || {})
    const currentOptions: { [key: string]: string } = { ...props.currentProduct?.currentOptions }
    if ((optionsListFull as OptionsList)[key].includes(value)) {
      currentOptions[key] = value
    }
    const variantKey = optionKeys.map(key => currentOptions[key] || (optionsListFull as OptionsList)[key][0]).join(' / ')
    const currentVariant = props.currentProduct?.variantsList?.find((item) => {
      return item.variant_key.replace(/_/, ' ').replace(/-/, ' / ') === variantKey
    })
    const currentExistVariant = props.currentProduct.existVariantList?.find(variant => variant.key === variantKey)

    Object.assign(props.currentProduct, {
      currentOptions,
      currentVariant,
      currentVariantKey: currentExistVariant?.value
    })
  }
}

const {
  isShowDesign,
  currentPersonalize,
  getDataCustomDesign,
  updatePersonalizeCustom,
  selectCustomDesign
} = props.currentProduct!.personalizeManager || {}

if ((props.currentProduct?.personalized === 1 || props.currentProduct?.personalized === 2) && getDataCustomDesign) {
  await getDataCustomDesign()
}

const {
  extraCustomFee,
  groupCustomOptionsQuantity,
  userCustomOptions,
  userCommonOptions,
  updateCustomOptions,
  updateCommonOptions,
  updateGroupCustomOptionsQuantity
} = props.currentProduct!.personalizeCustomManager || {}
</script>

<template>
  <common-modal
    :modal-id="`bundleProduct-${currentProduct?.id}`"
    :model-value="currentProduct?.slug"
    :modal-class="`<md:(w-[100vw] h-[100vh]) p-3 ${currentProduct?.personalized || (currentProduct?.personalized === 0 && currentProduct?.custom_options) ? 'md:w-[768px] <md:pb-10' : 'md:w-[425px]'} flex flex-col md:max-h-80vh`"
    :title="$t(currentProduct?.name || '')"
    close-icon="icon-basic-close"
    @close-modal="$emit('closeModal')"
  >
    <div
      class="grid grid-cols-2 pt-5 overflow-y-auto pb-20 overflow-x-hidden"
    >
      <div
        class="col-span-2"
        :class="{ 'md:col-span-1 md:(h-max sticky top-0)': currentProduct?.personalized || (currentProduct?.personalized === 0 && currentProduct?.custom_options) }"
      >
        <lazy-basic-campaign-view-box
          :is-modal="true"
          :color="currentProduct?.currentOptions?.color"
          :current-product="currentProduct"
          :personalize="currentPersonalize"
          :is-show-design="isShowDesign"
        />
      </div>
      <div
        class="col-span-2"
        :class="{ 'md:col-span-1': currentProduct?.personalized || (currentProduct?.personalized === 0 && currentProduct?.custom_options) }"
      >
        <lazy-basic-campaign-option-list
          :is-modal="true"
          :option-list="currentProduct?.optionsList"
          :current-product="currentProduct"
          :campaign-slug="currentProduct?.slug"
          :current-options="currentProduct?.currentOptions"
          :option-error="currentProduct?.optionError"

          @update-option="updateOption"
        />

        <lazy-basic-campaign-personalize-custom-options
          v-if="currentProduct?.personalized === 0 && currentProduct?.custom_options"
          :is-modal="true"
          :is-add-to-cart="isAddToCart"
          :custom-options="currentProduct.template_custom_options"
          :common-options="currentProduct.common_options"
          :group-custom-options-quantity="groupCustomOptionsQuantity"
          :user-custom-options="userCustomOptions"
          :extra-custom-fee="extraCustomFee"
          :current-options="currentProduct?.currentOptions"
          heading-style="font-bold-2 text-lg"
          @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
          @update-custom-options="updateCustomOptions"
        />

        <lazy-default-campaign-personalize-custom
          v-if="currentProduct?.personalized === 1"
          :is-modal="true"
          :product="currentProduct"
          :is-add-to-cart="isAddToCart"
          container-class="border bg-white"
          @update-personalize-custom="updatePersonalizeCustom"
          @select-custom-design="selectCustomDesign"
          @show-design="isShowDesign = true"
        />

        <lazy-default-campaign-personalize-pb
          v-if="currentProduct?.personalized === 2"
          :is-modal="true"
          container-class="border bg-white"
          @show-design="isShowDesign = true"
        />

        <lazy-basic-campaign-personalize-custom-options
          v-if="currentProduct?.personalized === 3"
          :is-modal="true"
          :custom-options="currentProduct.customOptions"
          :common-options="currentProduct.commonOptions"
          :group-custom-options-quantity="groupCustomOptionsQuantity"
          :user-custom-options="userCustomOptions"
          :user-common-options="userCommonOptions"
          :is-add-to-cart="isAddToCart"
          :extra-custom-fee="extraCustomFee"
          heading-style="font-bold-2 text-lg"
          :campaign-data="currentProduct"
          @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
          @update-custom-options="updateCustomOptions"
          @update-common-options="updateCommonOptions"
        />
      </div>
    </div>
    <div class="bottom-absolute w-full p-1 bg-white border-t z-3">
      <button class="btn-fill w-full text-2xl py-2 font-medium uppercase" @click="$emit('submit')">
        {{ $t('Save') }}
      </button>
    </div>
  </common-modal>
</template>
