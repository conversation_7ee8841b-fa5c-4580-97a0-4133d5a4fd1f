<script lang="ts" setup>
defineProps({
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  campaignData: {
    default: undefined,
    type: Object as PropType<Campaign>
  },
  totalCustomOptionFee: {
    default: 0,
    type: Number
  }
})

</script>

<template>
  <div v-if="userCampaignOption && campaignData">
    <div class="mt-4 text-md lg:text-lg">
      {{ campaignData.name }}
    </div>

    <lazy-vintage-campaign-countdown
      v-if="storeInfo().store_type !== 'google_ads' && campaignData.show_countdown && (campaignData.end_time || campaignData.show_countdown > 1)"
      :show-countdown="campaignData.show_countdown"
      :end-time="campaignData.end_time"
    />
  </div>
</template>
