<script lang="ts" setup>
defineProps({
  customOptions: {
    default: undefined,
    type: Object as PropType<PersonalizeCustomOptions>
  },
  commonOptions: {
    default: undefined,
    type: Object as PropType<PersonalizeCommonOptions>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  userCustomOptions: {
    default: undefined,
    type: Object as PropType<Array<Array<PersonalizeCustomOptionsItem>>>
  },
  userCommonOptions: {
    default: undefined,
    type: Object as PropType<Array<Array<PersonalizeCustomOptionsItem>>>
  },
  groupCustomOptionsQuantity: {
    default: 1,
    type: Number
  },
  extraCustomFee: {
    default: 0,
    type: Number
  },
  isAddToCart: {
    default: undefined,
    type: Boolean
  },
  isModal: {
    default: false,
    type: Boolean
  },

  headingStyle: {
    default: 'font-medium',
    type: String
  },
  campaignData: {
    default: undefined,
    type: Object
  },
  selectedColor: {
    default: '',
    type: String
  }
})

const toggleOptionMobile = ref(false)

function onInputFocus() {
  toggleOptionMobile.value = true
}

defineExpose({ toggleOptionMobile })
</script>

<template>
  <div
    class="relative border w-full transition-all p-1 <md:bg-[#f5f5f5] personalize"
    :class="{
      '<md:(fixed bottom-0 left-0 z-3) custom-opt-vari-position': !isModal
    }"
  >
    <button
      class="btn center-flex p-2"
      :class="{
        '<md:(absolute bg-[#f5f5f5] -top-10 right-5 z-1 py-1 border-t border-x)': !isModal
      }"
      @click="toggleOptionMobile = !toggleOptionMobile"
    >
      <span class="uppercase mr-1" :class="headingStyle">{{ $t('Customize') }}</span>
      <i v-if="$viewport.isGreaterOrEquals(VIEWPORT.tablet) && !isModal" class="icon-sen-text-box-multiple-outline" />
      <i v-else class="text-2xl block icon-sen-chevron-up transition-transform transform animation" :class="{ 'rotate-180': toggleOptionMobile }" />
    </button>
    <common-collapse
      :when="isModal || toggleOptionMobile || $viewport.isGreaterOrEquals(VIEWPORT.tablet)"
      class="transition-default p-1"
    >
      <template v-for="(option, index) in commonOptions?.options" :key="index">
        <basic-common-input
          v-if="option.type === CUSTOM_OPTION_TYPE.text"
          :id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
          :input-type="2"
          :enable-text-counter="true"
          :model-value="(userCommonOptions && userCommonOptions[0]?.[index]?.value as string)"
          :state="!!((userCommonOptions && userCommonOptions[0]?.[index].value) || (userCommonOptions && userCommonOptions.length > 0 && (userCommonOptions[0]?.[index].unrequired === true || userCommonOptions[0]?.[index].unrequired === 1)))"
          :is-required="(userCommonOptions && (userCommonOptions[0][index].unrequired === true || userCommonOptions[0][index].unrequired === 1))"
          :max-length="option?.max_length ? parseInt(String(option.max_length)) : undefined"
          :label="`${$t(option.label)}`"
          :placeholder="userCommonOptions && userCommonOptions[0][index]?.placeholder ? $t(userCommonOptions?.[0][index]?.placeholder as string) : $t(option.label)"
          @update:model-value="$emit('updateCommonOptions', {
            optionIndex: index,
            value: $event,
            type: 1
          })"
          @change:model-value="$emit('updateCommonOptions', {
            optionIndex: index,
            value: $event,
            type: 2
          })"
          @focus="onInputFocus"
        />
        <div v-if="option.type === CUSTOM_OPTION_TYPE.dropdown" :class="`grid grid-cols-3 gap-2 flex items-center mt-2 box_${option.type}_${index}`">
          <label
            v-if="(customOptions?.group.limit ?? 0) >= 1"
            class="label text-sm break-all w-full z-1 col-span-3 lg:col-span-1 font-bold-2 uppercase"
            :class="{ 'text-sm': userCommonOptions && userCommonOptions.length && userCommonOptions[0][index].value }"
          >
            {{ $t(option.label) }}
          </label>
          <common-dropdown
            :input-type="2"
            :dropdown-id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
            :btn-class="`h-10 text-left relative border-b w-full transition-all mt-1 ${((userCommonOptions && userCommonOptions.length > 0 && userCommonOptions[0][index].value) || (userCommonOptions && userCommonOptions.length && (userCommonOptions[0][index].unrequired === true || userCommonOptions[0][index].unrequired === 1))) ? '!border-green-500' : '!border-red-500'}`"
            class="col-span-3 lg:col-span-2"
            dropdown-class="w-full"
          >
            <div class="flex items-center px-2 min-w-20 truncate">
              <template v-if="userCommonOptions && userCommonOptions[0][index].value">
                {{ userCommonOptions && userCommonOptions.length && userCommonOptions[0][index].value }}
              </template>
              <template v-else-if="userCommonOptions">
                {{ `${$t('Select a')} ${$t(option.label)}` }}
              </template>
            </div>
            <template #content>
              <div
                v-for="optionDropdownItem in option.value"
                :key="optionDropdownItem"
                class="btn-text flex items-center px-4 w-full py-1"
                :class="{ 'bg-primary !text-contrast': userCommonOptions && userCommonOptions[0][index].value === optionDropdownItem }"
                @click="$emit('updateCommonOptions', {
                  optionIndex: index,
                  value: optionDropdownItem
                })"
              >
                {{ optionDropdownItem }}
              </div>
            </template>
          </common-dropdown>
        </div>

        <lazy-common-file-selector
          v-if="option.type === CUSTOM_OPTION_TYPE.image"
          :id="`commonOption_${isModal ? 'modal' : 'campaign'}_${option.type}_1_${index}`"
          :key="userCommonOptions?.[0][index]?.value"
          :input-type="2"
          :file-upload="`${userCommonOptions && (userCommonOptions[0][index]?.value || '')}`"
          :state="userCommonOptions && (userCommonOptions[0][index]?.value?.length > 0 || userCommonOptions[0][index]?.unrequired === true || userCommonOptions[0][index].unrequired === 1)"
          :label="`${$t(option.label)}`"
          browse-text="Upload"
          class="mt-3 w-full grid grid-cols-3 gap-2 flex items-center"
          label-class="font-bold-2 text-sm uppercase col-span-3 lg:col-span-1"
          selector-class="col-span-3 lg:col-span-2"
          @on-change="$emit('updateCommonOptions', {
            optionIndex: index,
            value: $event[0]
          })"
        />
      </template>
      <div
        class="px-1 <md:(overflow-x-hidden overflow-y-auto)"
        :class="{
          '<md:(max-h-70)': !isModal,
          'mt-3': commonOptions?.options?.length > 0
        }"
      >
        <div class="grid grid-cols-3 gap-2 flex items-center">
          <label
            v-if="(customOptions?.group.limit ?? 0) > 1"
            class="left-0 top-[-25px] label transition-all duration-150 z-1 text-sm col-span-3 lg:col-span-1 font-bold-2 uppercase"
          >
            {{ $t('Number of') }} {{ customOptions?.group.name ? $t(customOptions?.group.name) : $t('custom') }}
          </label>
          <!-- btn-class="h-10 relative border w-full p-1.5 mt-1" -->
          <common-dropdown
            v-if="(customOptions?.group.limit || 1) > 1"
            dropdown-id="groupCustomOptionsQuantity"
            btn-class="w-full h-12 border-b border-[var(--color-gray-4)] transition-all"
            dropdown-class="w-full"
            class="col-span-3 lg:col-span-2"
            show-data-class=""
          >
            <div class="flex items-center px-2 min-w-20">
              {{ groupCustomOptionsQuantity }} {{ groupCustomOptionsQuantity > 1 && toValue(extraCustomFee) > 0 ? `(+${$formatPrice(toValue(extraCustomFee) * (groupCustomOptionsQuantity - 1))})` : '' }}
            </div>
            <template #content>
              <div
                v-for="quantity in customOptions?.group.limit"
                :key="quantity"
                class="btn-text flex items-center px-4 w-full py-1"
                :class="{ 'bg-primary !text-contrast': groupCustomOptionsQuantity === quantity }"
                @click="$emit('updateGroupCustomOptionsQuantity', quantity)"
              >
                {{ quantity }} {{ quantity > 1 && extraCustomFee > 0 ? `(+${$formatPrice(extraCustomFee * (quantity - 1))})` : '' }}
              </div>
            </template>
          </common-dropdown>
        </div>
        <div v-for="quantity in groupCustomOptionsQuantity" :key="quantity" class="py-2">
          <hr v-if="(customOptions?.group.limit || 1) > 1" class="my-3">
          <template v-for="(option, index) in customOptions?.options" :key="index">
            <basic-common-input
              v-if="option.type === CUSTOM_OPTION_TYPE.text"
              :id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
              :input-type="2"
              :enable-text-counter="true"
              :model-value="(userCustomOptions && userCustomOptions[quantity - 1]?.[index]?.value as string)"
              :state="!!((userCustomOptions && userCustomOptions[quantity - 1][index].value) || (userCustomOptions && userCustomOptions.length > 0 && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1)))"
              class="mt-3"
              :is-required="(userCustomOptions && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1))"
              :max-length="option?.max_length ? parseInt(String(option.max_length)) : undefined"
              :label="`${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}`"
              :placeholder="userCustomOptions && userCustomOptions[quantity - 1][index]?.placeholder ? $t(userCustomOptions?.[quantity - 1][index]?.placeholder as string) : $t(option.label)"
              @update:model-value="$emit('updateCustomOptions', {
                groupNumber: quantity,
                optionIndex: index,
                value: $event,
                type: 1
              })"
              @change:model-value="$emit('updateCustomOptions', {
                groupNumber: quantity,
                optionIndex: index,
                value: $event,
                type: 2
              })"
              @focus="onInputFocus"
            />

            <div v-if="option.type === CUSTOM_OPTION_TYPE.dropdown" :class="`mt-2 grid grid-cols-3 gap-2 flex items-center box_${option.type}_${quantity}_${index}`">
              <label
                v-if="(customOptions?.group.limit ?? 0) >= 1"
                class="label text-sm break-all w-full z-1 col-span-3 lg:col-span-1 font-bold-2 uppercase"
                :class="{ 'text-sm': userCustomOptions && userCustomOptions.length && userCustomOptions[quantity - 1][index].value }"
              >
                {{ `${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}` }}
              </label>
              <common-dropdown
                :input-type="2"
                :dropdown-id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
                :btn-class="`h-10 text-left relative border-b w-full mt-1 p-1.5 ${((userCustomOptions && userCustomOptions.length > 0 && userCustomOptions[quantity - 1][index].value) || (userCustomOptions && userCustomOptions.length && (userCustomOptions[quantity - 1][index].unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1))) ? '!border-green-500' : '!border-red-500'}`"
                class="col-span-3 lg:col-span-2"
                dropdown-class="w-full"
              >
                <div class="flex items-center px-2 min-w-20 truncate">
                  <template v-if="userCustomOptions && userCustomOptions[quantity - 1][index].value">
                    {{ userCustomOptions && userCustomOptions.length && userCustomOptions[quantity - 1][index].value }}
                  </template>
                  <template v-else-if="userCustomOptions">
                    {{ `${$t('Select a')} ${$t(userCustomOptions[quantity - 1][index].label)}` }}
                  </template>
                </div>
                <template #content>
                  <div
                    v-for="optionDropdownItem in option.value"
                    :key="optionDropdownItem"
                    class="btn-text flex items-center px-4 w-full py-1"
                    :class="{ 'bg-primary !text-contrast': userCustomOptions && userCustomOptions[quantity - 1][index].value === optionDropdownItem }"
                    @click="$emit('updateCustomOptions', {
                      groupNumber: quantity,
                      optionIndex: index,
                      value: optionDropdownItem
                    })"
                  >
                    {{ optionDropdownItem }}
                  </div>
                </template>
              </common-dropdown>
            </div>

            <lazy-common-file-selector
              v-if="option.type === CUSTOM_OPTION_TYPE.image"
              :id="`customOption_${isModal ? 'modal' : 'campaign'}_${option.type}_${quantity}_${index}`"
              :key="userCustomOptions?.[quantity - 1][index]?.value"
              :input-type="2"
              :file-upload="`${userCustomOptions && (userCustomOptions[quantity - 1][index]?.value || '')}`"
              :state="userCustomOptions && (userCustomOptions[quantity - 1][index]?.value?.length > 0 || userCustomOptions[quantity - 1][index]?.unrequired === true || userCustomOptions[quantity - 1][index].unrequired === 1)"
              :label="`${$t(customOptions?.group.name ?? option.label)} ${groupCustomOptionsQuantity > 1 ? ` ${quantity}` : ''}`"
              browse-text="Upload"
              class="mt-3 w-full grid grid-cols-3 gap-2 flex items-center"
              label-class="font-bold-2 text-sm uppercase col-span-3 lg:col-span-1"
              selector-class="col-span-3 lg:col-span-2"
              @on-change="$emit('updateCustomOptions', {
                groupNumber: quantity,
                optionIndex: index,
                value: $event[0]
              })"
            />
          </template>
        </div>
      </div>
    </common-collapse>
  </div>
</template>
