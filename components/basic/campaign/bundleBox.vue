<script lang="ts" setup>
const props = defineProps({
  bundleProduct: {
    default: undefined,
    type: Object as PropType<Product[]>
  },
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  currentBundleProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  totalBundleDiscount: {
    default: 0,
    type: Number
  },
  saveBundleDiscount: {
    default: 0,
    type: Number
  },
  totalCustomOptionFee: {
    default: 0,
    type: Number
  }
})

const $emit = defineEmits(['resetBundleProduct'])

watch(() => props.bundleProduct, () => {
  handleCurrentVariants()
})

onMounted(async () => {
  handleCurrentVariants()
})

const bundleProductsInStock: Ref<any[] | undefined> = ref([])
const refetchTimes: Ref<number> = ref(0)
function selectVariant(product: Product, { key, value }: { key: string, value: string }) {
  let optionsListFull = product?.options
  if (typeof optionsListFull === 'string') {
    optionsListFull = JSON.parse(optionsListFull)
  }
  const optionKeys = Object.keys(optionsListFull as OptionsList)
  const optionValue = key.split(' / ')

  const currentVariant = product?.variantsList?.find((item) => {
    return item.variant_key.replace(/_/, ' ').replace(/-/, ' / ') === key
  })

  const currentOptions: { [key: string]: string } = {}
  optionKeys.forEach((item, index) => {
    if (!(product?.optionsList as OptionsList)[item]) {
      return
    }
    if ((product?.optionsList as OptionsList)[item]?.includes(optionValue[index])) {
      currentOptions[item] = optionValue[index]
    }
    else {
      currentOptions[item] = (product?.optionsList as OptionsList)[item][0]
    }
  })

  Object.assign(product, {
    currentOptions,
    currentVariant,
    options: optionsListFull,
    currentVariantKey: value
  })
}

function handleCurrentVariants() {
  bundleProductsInStock.value = props.bundleProduct?.map((product: any) => {
    if (product?.currentVariant?.out_of_stock) {
      const fixedOptions = product.currentOptions?.size
      let variantsInStock = null
      if (fixedOptions) {
        const regex = new RegExp(`-${fixedOptions}$`, 'i')
        variantsInStock = product.variantsList.find((variant: any) => {
          return regex.test(variant.variant_key) && variant.out_of_stock === 0
        })
      }
      else {
        variantsInStock = product.variantsList.find((variant: any) => {
          return variant.out_of_stock === 0
        })
      }
      if (variantsInStock) {
        product.currentVariantKeyInStock = variantsInStock?.variant_key?.replace(/_/g, ' ')?.replace(/-/g, ' / ') ?? ''
        product.variantInStock = variantsInStock
        return product
      }
      if (refetchTimes.value < 3) {
        $emit('resetBundleProduct', product.id)
        refetchTimes.value += 1
      }
    }
    product.currentVariantKeyInStock = product.currentVariantKey
    product.variantInStock = product.currentVariant
    return product
  })
}
</script>

<template>
  <div>
    <p class="font-bold-2 uppercase mt-8 text-lg">
      {{ $t('Frequently bought together') }}
    </p>
    <div class="flex overflow-auto gap-1 items-center py-1">
      <div class="relative w-28 min-w-28 h-35">
        <common-image
          :image="{
            path: userCampaignOption?.currentProduct?.thumb_url || userCampaignOption?.imagesList[0]?.file_url,
            color: userCampaignOption?.currentOptions.color
          }"
          :alt="userCampaignOption?.currentProduct?.name"
        />
        <default-common-personalize-tag v-if="userCampaignOption?.currentProduct?.personalized" size="xs" />
      </div>
      <template v-for="(product, index) in bundleProduct" :key="index">
        <i class="icon-sen-plus-circle text-primary text-3xl" />

        <div
          v-if="index < 3"
          :id="`image-bundleProduct-${product.id}`"
          class="relative w-28 min-w-28 h-35 hover:shadow-custom cursor-pointer"
          :title="$t('Click to update options')"
          @click="$emit('selectBundleProduct', product)"
        >
          <common-image
            :image="{
              path: product.thumb_url,
              color: product.currentOptions?.color
            }"
            :alt="product.name"
          />

          <default-common-personalize-tag v-if="product && product.personalized" size="xs" />
        </div>
      </template>
    </div>

    <div v-for="(product, index) in bundleProductsInStock" :key="index" class="grid grid-cols-2 xl:grid-cols-3 mt-2">
      <button
        :id="`checkbox-bundleProduct-${product.id}`"
        class="flex col-span-1 xl:col-span-2"
        @click="Object.assign(product, { isSelected: !product.isSelected })"
      >
        <span class="text-2xl text-primary mt-0.5">
          <i :class="product.isSelected ? 'icon-sen-checkbox-marked' : 'icon-sen-checkbox-blank-outline'" />
        </span>
        <div class="ml-2 pr-10 text-left w-full">
          <div class="w-full text-overflow-hidden">
            {{ $t(product.name || '') }}
          </div>
          <div>
            <span v-if="product.variantInStock && product.variantInStock.out_of_stock" class="text-danger font-weight-500">
              {{ $t('Out of stock') }}
            </span>
            <span v-else>
              {{ $formatPrice(useTestPrice().getPrice(product, product.currentVariant) + Number(((product.customFeePrice || 0) * ((product.customOptionGroupNumbers || 1) - 1))), product.currency_code) }}
            </span>
          </div>
        </div>
      </button>
      <common-dropdown
        v-if="(product.existVariantList?.length || 0) > 1"
        class="col-span-1 w-full"
        btn-class="btn-border px-2 py-1 w-full uppercase text-overflow-hidden"
        dropdown-class="w-full max-w-[90vw]"
      >
        <span>{{ product.currentVariantKey }}</span>
        <template #content>
          <button
            v-for="(variant, variantIndex) in product.existVariantList"
            :key="variantIndex"
            class="btn-text uppercase min-w-full py-1"
            :class="{ 'bg-primary !text-contrast': product?.currentVariantKeyInStock === variant.value }"
            @click="selectVariant(product, variant)"
          >
            {{ variant.value }}
          </button>
        </template>
      </common-dropdown>
    </div>

    <hr class="my-3">

    <div class="flex justify-between">
      <div v-if="saveBundleDiscount">
        <span class="uppercase">{{ $t('Total') }}: </span>
        <del v-if="storeInfo().store_type !== 'google_ads'" class="font-semibold">{{ $toLocalePrice($convertPrice((userCampaignOption?.currentPrice || 0) + totalCustomOptionFee, userCampaignOption?.currentProduct?.currency_code).value + totalCustomOptionFee + totalBundleDiscount) }}</del>
      </div>
      <div>
        <span class="uppercase">{{ $t('Total price') }}: </span>
        <span class="font-semibold text-primary">{{ $toLocalePrice($convertPrice((userCampaignOption?.currentPrice || 0) + totalCustomOptionFee, userCampaignOption?.currentProduct?.currency_code).value + totalCustomOptionFee + totalBundleDiscount - saveBundleDiscount) }}</span>
      </div>
    </div>

    <div v-if="saveBundleDiscount" class="flex justify-end mt-1">
      <span class="text-sm bg-green-700 text-white font-medium px-2 rounded-md">
        {{ $t('Saving') }} {{ $toLocalePrice(saveBundleDiscount) }}
      </span>
    </div>

    <button
      class="btn-fill uppercase font-bold-2 h-12 w-full text-xl mt-2"
      :disabled="!!userCampaignOption?.currentVariant?.out_of_stock || !!isLoading || !bundleProduct?.find((item, index) => index < 3 && item.isSelected)"
      @click="$emit('submit')"
    >
      <common-loading-dot v-if="isLoading" />
      <span v-else><i class="icon-sen-cart-plus mr-2 font-bold" />{{ $t('Add all to cart') }}</span>
    </button>

    <lazy-basic-campaign-modal-bundle-product
      v-if="currentBundleProduct"
      :current-product="currentBundleProduct"
      :is-add-to-cart="userCampaignOption?.isAddToCart"
      @close-modal="$emit('selectBundleProduct', false); $emit('disableAddAllToCart') "
      @submit="$emit('modalSubmit')"
    />
  </div>
</template>
