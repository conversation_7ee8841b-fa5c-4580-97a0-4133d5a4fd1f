<script lang="ts" setup>
import { useCampaignStore } from '~~/store/campaign'
import { useCartStore } from '~~/store/cart'
const localePath = useLocalePath()

const isShowModal = ref(false)
const cartItem = ref<CartItem>()

const isUploadFile = computed(() => {
  return uiManager().isUploadFile
})
const totalPrice = computed(() => useCartStore().getTotalPrice)
const price = computed(() => {
  return (cartItem.value?.variantPrice || cartItem.value?.price || 0) + (cartItem.value?.extra_custom_fee || 0)
})

defineProps({
  relatedCart: {
    default: undefined,
    type: Object as PropType<Array<Product>>
  }
})

defineExpose({
  isShowModal,
  cartItem
})
</script>
<template>
  <common-modal
    :key="cartItem?.id || 'add_to_cart_basic'"
    v-model="isShowModal"
    modal-id="modalAddToCart"
    :title="$t('Added to cart successfully!')"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px]"
    :close-icon="'icon-basic-close'"
    :title-class="'font-medium text-lg'"
    :title-container-class="'flex justify-between items-center px-4 py-4'"
    :close-btn-class="'btn-text text-3xl leading-0'"
  >
    <div class="border-t border-[var(--color-gray-5)] max-h-[80vh] overflow-y-auto container">
      <div v-if="relatedCart?.length" class="flex justify-between mx-2 mt-4">
        <h4>
          <span class="font-bold border-r text-md border-black pr-2 mr-2">{{$t('Subtotal')}}</span>
          <span class="text-lg">{{ $t('item', { count: totalQuantity().value }) }}</span>
        </h4>
        <h4 class="font-bold text-lg">{{ $formatPrice(totalPrice) }}</h4>
      </div>
      <div
        v-else
        class="py-2 flex items-center"
      >
        <common-image
          img-class="!w-22"
          :image="{
            path: cartItem?.thumb_url,
            type: 'list',
            color: cartItem?.options?.color
          }"
          :alt="cartItem?.campaign_title"
        />
        <div class="w-full max-w-[calc(100%-90px)] px-1 ">
          <nuxt-link :to="localePath(cartItem?.product_url as string)" class="w-full block">
            <h5 class="btn-text font-bold-2 uppercase text-overflow-hidden font-medium" :title="cartItem?.campaign_title">
              {{ cartItem?.campaign_title }}
            </h5>
          </nuxt-link>
          <div>
            <span class="capitalize">{{ $t(cartItem?.product_name || '') }}</span>
          </div>
          <div class="uppercase">
            <span
              v-for="(key, optionIndex) in Object.keys(cartItem?.optionList || {}).filter(key => (cartItem?.optionList[key].length ?? 0) > 1)"
              :key="optionIndex"
            >
              <span v-if="optionIndex > 0">&nbsp;/&nbsp;</span>
              {{ $t(cartItem?.options && cartItem?.options[key] || '') }}
            </span>
          </div>
          <div class="mb-1 font-medium">
            <span>
              x{{ cartItem?.quantity }}
            </span>
            <span class="ml-3">{{ $formatPrice((cartItem?.quantity || 0) * price, cartItem?.currency_code) }}</span>
          </div>
        </div>
      </div>

      <lazy-common-product-carousel
        v-if="relatedCart?.length"
        class="my-10"
        title-class="text-center md:text-2xl text-xl mb-2"
        title="Item purchased together"
        :products="relatedCart"
        :splide-settings="basicProductSplideSetting"
        :splide-hide-arrow-on-ends="true"
        :static-grid="'grid-cols-2 md:grid-cols-3 gap-1 md:gap-2 lg:gap-4'"
      >
        <template #default="{ product, index }">
          <basic-common-product-item :product="product" :index="index" />
        </template>
      </lazy-common-product-carousel>
    </div>
    <div class="border-t border-[var(--color-gray-5)] grid grid-cols-2 gap-4 py-6 px-4 text-xl">
      <button
        class="btn-border py-1 px-2 lg:p-3 w-full rounded-full uppercase text-sm"
        data-test-id="view-cart-button"
        @click="useCampaignStore().$patch({ modalCampaignUrl: '' });useRouter().push(localePath('/cart'))"
      >
        {{ $t('View cart') }}
      </button>
      <button
        class="py-1 px-2 lg:p-3 w-full rounded-full uppercase text-sm bg-black hover:opacity-70 text-contrast"
        :disabled="!!isUploadFile"
        data-test-id="proceed-to-checkout-button"
        @click="useCampaignStore().$patch({ modalCampaignUrl: '' }); createOrder()"
      >
        {{ $t('Proceed to checkout') }}
      </button>
    </div>
  </common-modal>
</template>
