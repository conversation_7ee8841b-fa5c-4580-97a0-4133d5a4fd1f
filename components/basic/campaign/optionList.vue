<script lang="ts" setup>
const { currentOptions, currentProduct, optionList } = defineProps({
  optionList: {
    default: undefined,
    type: Object as PropType<OptionsList>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  optionError: {
    default: '',
    type: [String, Boolean]
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const { $getProductVariantsBySelectedKeyAndCountry, $userCountryForPricing } = useNuxtApp()

const hasSizeGuide = computed(() => {
  return generalSettings().hasSizeGuide(currentProduct?.template_id)
})

const computedSizesWithAvail = computed(() => {
  if (!currentProduct?.variantsList?.length || !currentOptions?.color || !optionList?.color || !optionList?.size) {
    return []
  }

  const sizes = []
  for (const size of optionList.size) {
    sizes.push({
      value: size,
      key: `${currentOptions.color}-${size}`,
      out_of_stock: $getProductVariantsBySelectedKeyAndCountry(currentProduct.variantsList, `${currentOptions.color}-${size}`, currentProduct.id, $userCountryForPricing())?.out_of_stock
    })
  }

  return sizes
})
</script>

<template>
  <client-only>
    <div v-if="optionList && currentOptions">
      <div v-for="(key, index) in Object.keys(optionList as OptionsList)" :key="index" class="mb-3">
        <span v-if="storeInfo().option_label_enable || (currentProduct && currentProduct?.full_printed === 5)" class="font-semibold capitalize">
          {{ $t(key.replace(/_/g, ' ')) }}
        </span>
        <div
          class="w-full overflow-auto flex md:flex-wrap"
          :class="{ 'animate__animated animate__headShake': optionError === key }"
        >
          <template v-if="key === 'color'">
            <div>
              <div class="flex flex-wrap gap-2 mx-0.5">
                <button
                  v-for="(value, optionIndex) in optionList[key]"
                  :key="`${key}-${optionIndex}`"
                  @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
                >
                  <basic-common-color-item
                    :color="value"
                    :title="value"
                    size="2xl"
                    :active="value === currentOptions[key]"
                    :rounded="true"
                    :selectable="true"
                    data-test-id="product-color"
                    sp-action="change_color"
                  />
                </button>
              </div>
              <p class="text-[var(--color-gray-6)] capitalize text-xs">
                {{ key }}:
                <span class="uppercase">{{ currentOptions[key] }}</span>
              </p>
            </div>
          </template>
          <template v-else-if="key === 'size' && Object.keys(computedSizesWithAvail).length">
            <div>
              <div class="flex flex-wrap gap-1 mx-0.5">
                <button
                  v-for="(size, optionIndex) in computedSizesWithAvail"
                  :key="`${key}-${optionIndex}`"
                  :disabled="size.out_of_stock"
                  @click="size.value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value: size.value })"
                >
                  <basic-common-option-item
                    :value="size.value"
                    :sp-action="`change_${key}`"
                    :data-test-id="`product-change-${key}`"
                    :active="size.value === currentOptions[key]"
                    :disabled="size.out_of_stock"
                  />
                </button>
              </div>
              <p class="text-[var(--color-gray-6)] capitalize text-xs">
                {{ key }}:
                <span class="uppercase">{{ currentOptions[key] }}</span>
              </p>
            </div>
          </template>
          <template v-else>
            <div>
              <div class="flex flex-wrap gap-1 mx-0.5">
                <button
                  v-for="(value, optionIndex) in optionList[key]"
                  :key="`${key}-${optionIndex}`"
                  @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
                >
                  <basic-common-option-item
                    :value="value"
                    :sp-action="`change_${key}`"
                    :data-test-id="`product-change-${key}`"
                    :active="value === currentOptions[key]"
                  />
                </button>
              </div>
              <p class="text-[var(--color-gray-6)] capitalize text-xs">
                {{ key }}:
                <span class="uppercase">{{ currentOptions[key] }}</span>
              </p>
            </div>
          </template>
        </div>
        <div class="flex justify-end">
          <button
            v-if="key === 'size' && hasSizeGuide"
            class="cursor-pointer mt-2 text-[var(--color-blue-1)] flex items-center"
            @click="uiManager().updateSizeGuideData(currentProduct, true)"
          >
            <i class="icon-basic-size-chart mr-2 text-2xl leading-0 text-black" />
            <span class="font-medium">
              {{ $t('Size guide') }}
            </span>
          </button>
        </div>
      </div>
    </div>
  </client-only>
</template>
