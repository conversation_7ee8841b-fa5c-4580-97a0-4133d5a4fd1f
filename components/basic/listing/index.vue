<script lang="ts" setup>
const $router = useRouter()

const products = ref<Product[]>([])
const fetchingNextPage = ref(false)
const sidebarVisibility = ref(false)

const localePath = useLocalePath()
const {
  filterValue,
  listingProduct,
  filterProduct,
  resetData,
  updatePrice,
  filtersCount,
  currentProductCategory,
  currentPriceRange,
} = useListing()

await resetData()
  .then(() => {
    products.value = [
      ...products.value,
      ...listingProduct.products,
    ]
  })

watch(filterProduct, () => {
  if (fetchingNextPage.value) {
    fetchingNextPage.value = false
    products.value = [
      ...products.value,
      ...listingProduct.products,
    ]
  } else {
    // filters change
    products.value = listingProduct.products
  }
})

function fetchNextPage() {
  if (listingProduct.currentPage === listingProduct.lastPage) { return }

  fetchingNextPage.value = true
  $router.push(getFilterUrl.value('page', listingProduct.currentPage + 1))
}
</script>

<template>
  <basic-listing-collection-slider
    v-if="filterValue.pageType !== 'artist' && filterProduct.collection_group.length > 1 && !storeInfo().disable_related_product"
    class="container mt-6"
    :items="filterProduct.collection_group"
  />

  <h1 class="capitalize font-medium text-2xl py-3 lg:(text-4xl) container">{{ filterValue.title }}</h1>

  <div class="container">
    <div class="flex flex-nowrap items-center gap-4 mt-2">
      <div>
        <button v-if="storeInfo().isShowFilterBox1" class="hover:opacity-70 flex relative " @click.stop.prevent="sidebarVisibility = true">
          <i class="icon-basic-filter text-2xl" />
          <span v-if="filtersCount" class="badge -top-2 text-xs bg-[var(--color-black-1)] font-light-2">{{ filtersCount }}</span>
        </button>
      </div>
      <div class="flex gap-2 ml-2">
        <common-dropdown
          v-if="filterValue.filterTemplates && filterValue.filterTemplates.length"
          :input-type="2"
          :show-data-class="''"
          :dropdown-icon="'icon-basic-chevron-down text-lg'"
          :dropdown-class="'mt-2'"
          class="rounded-full pl-3 pr-2 py-1 border hover:(bg-[var(--color-gray-1)]) transition-all duration-1000"
          :class="{
            'border-black': filterValue?.template,
          }"
          :close-on-click="false"
        >
          <div class="text-md flex items-center gap-1">
            <template v-if="currentProductCategory">
              {{ currentProductCategory }}
            </template>
            <template v-else>{{ $t('Product') }}</template>
          </div>
          <template #content>
            <div class="px-5 py-4 min-w-100">
              <p class="mb-3 pb-3 border-b capitalize <md:text-center">
                {{ $t('Product') }}
                <nuxt-link
                  class="text-[var(--color-blue-1)] hover:opacity-70 text-xs ml-4"
                  :to="getFilterUrl('product', '')"
                >{{ $t('Reset') }}</nuxt-link>
              </p>
              <div>
                <vintage-common-template-filter-item
                  v-for="(template, index) in filterValue.filterTemplates"
                  :key="index"
                  :template="template"
                  :current-template="filterValue.template"
                />
              </div>
            </div>
          </template>
        </common-dropdown>
        <common-dropdown
          :input-type="2"
          :show-data-class="''"
          :dropdown-icon="'icon-basic-chevron-down text-lg'"
          :close-on-click="false"
          :dropdown-class="'mt-2'"
          class="rounded-full pl-3 pr-2 py-1 border hover:(bg-[var(--color-gray-1)]) transition-all duration-1000"
          :class="{
            'border-black': currentPriceRange.show
          }"
        >
          <div class="text-md flex items-center gap-1">
            <template v-if="currentPriceRange.show">
              {{ $formatPrice(filterValue.priceFilter[0]) }} - {{ $formatPrice(filterValue.priceFilter[1]) }}
            </template>
            <template v-else>{{ $t('Price') }}</template>
          </div>
          <template #content>
            <div class="min-w-100 px-5 py-3">
              <p class="font-12x mb-3 pb-3 border-b capitalize <md:text-center">
                {{ $t('Price') }}
                <nuxt-link
                  class="text-[var(--color-blue-1)] hover:opacity-70 text-xs ml-4"
                  :to="getFilterUrl('price', '')"
                >{{ $t('Reset') }}</nuxt-link>
              </p>
              <p class="text-center  text-555 mb-3">
                {{ $formatPrice(filterValue.priceFilter[0]) }} - {{ $formatPrice(filterValue.priceFilter[1]) }}
              </p>
              
              <common-listing-price-slider
                v-model="filterValue.priceFilter"
                :min="filterProduct.min_price"
                :max="filterProduct.max_price"
                @change="updatePrice"
              />
            </div>
          </template>
        </common-dropdown>
        <common-dropdown
          :input-type="2"
          :show-data-class="''"
          :dropdown-icon="'icon-basic-chevron-down text-lg'"
          class="rounded-full pl-3 pr-2 py-1 border hover:(bg-[var(--color-gray-1)]) transition-all duration-1000"
          :class="{
            'border-black': filterValue?.color?.name
          }"
          :dropdown-class="'mt-2'"
          :close-on-click="false"
        >
          <div class="text-md pr-2">
            <template v-if="filterValue.color">
              <span class="capitalize">{{ filterValue.color.name }}</span>
            </template>
            <template v-else>{{ $t('Color') }}</template>
          </div>
          <template #content>
            <div class="min-w-100 lg:min-w-150 px-5 py-3">
              <p class="text-lg mb-3 pb-3 capitalize <md:text-center border-b">
                {{ $t('Color') }}
                <nuxt-link
                  class="text-[var(--color-blue-1)] hover:opacity-70 text-xs ml-4"
                  :to="getFilterUrl('color', '')"
                >{{ $t('Reset') }}</nuxt-link>
              </p>
              <div class="flex flex-wrap gap-1">
                <nuxt-link
                  v-for="(color, index) in filterProduct.color_group"
                  :key="index"
                  :to="getFilterUrl('color', filterValue.color?.name === color ? '' : color)"
                  sp-action="change_color"
                >
                  <basic-common-color-item
                    :color="color"
                    size="xl"
                    :rounded="true"
                    :selectable="true"
                    class="hover:shadow-color-active"
                    :active="filterValue.color?.name === color"
                  />
                </nuxt-link>
              </div>
            </div>
          </template>
        </common-dropdown>
      </div>
    </div>
    <div class="flex justify-between">
      <h4 class="capitalize text-md my-3">{{ $t('item', { count: listingProduct.total || 0 }) }}</h4>

      <common-dropdown
        :show-dropdown-icon="false"
        :input-type="2"
        :show-data-class="''"
      >
        <div class="text-md flex items-center gap-1">
          <i class="icon-basic-sort text-xl" />{{ $t('Sort by') }}
        </div>
        <template #content>
          <div class="px-4">
            <nuxt-link
              v-for="(sortItem, index) in filterValue.sortTypeList"
              :key="sortItem.value"
              sp-action="sort-listing"
              :to="getFilterUrl('sort', sortItem.value)"
              class="w-full min-w-55 py-3 block hover:bg-[var(--color-gray-1)] flex justify-between items-center"
              :class="{
                'font-bold': filterValue.sort === sortItem.value,
                'border-t': index,
              }"
            >
              {{ $t(sortItem.text) }}
              <i v-if="filterValue.sort === sortItem.value" class="icon-basic-checked text-2xl" />
            </nuxt-link>
          </div>
        </template>
      </common-dropdown>
    </div>
  </div>

  <main id="listingPage" class="container flex flex-wrap">
    <div class="md:order-2 w-full lg:pl-2">
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        <div
          v-for="(product, index) in products"
          :key="index"
          class="py-1 md:py-3"
        >
          <basic-common-product-item
            :product="product"
            :color="filterValue.color?.name"
          />
        </div>
      </div>
      <div
        class="border-t py-4 mt-2"
        :class="{
          'hidden': listingProduct.lastPage === listingProduct.currentPage
        }"
      >
        <common-loading-dot v-if="fetchingNextPage" :variant="'bg-black !w-4 !h-4'" class="flex justify-center gap-2" />
        <button v-else class="uppercase font-bold-2 w-full hover:opacity-60 flex justify-center gap-2" @click="fetchNextPage">{{ $t('View more') }} <span class="icon-basic-chevron-down text-[var(--color-gray-4)] text-2xl" /></button>
      </div>
    </div>
  </main>

  <common-sidebar
    :visible="sidebarVisibility"
    :close-button-icon="'icon-basic-close text-4xl'"
    :close-button-style="'--color-highlight: #e5e7eb;'"
    class="z-4"
    @update:visible="(visibility) => { sidebarVisibility = visibility }"
  >
    <div v-if="storeInfo().enable_search" class="h-full w-full p-6 overflow-auto">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-3xl font-medium font-secondary">
          {{ $t('Filters') }}
        </h1>
        <nuxt-link
          :to="localePath($route.path)"
          class="text-sm btn-vintage-shadow py-1.5 px-4 font-semibold relative rounded-full z-99999"
        >
          {{ $t('Reset') }}
        </nuxt-link>
      </div>

      <div v-if="filterValue.filterCategories && filterValue.filterCategories.length">
        <div class="mb-8">
          <vintage-common-category-filter-item
            v-for="(category, index) in filterValue.filterCategories"
            :key="index"
            :category="category"
            :current-category="filterValue.category"
          />
        </div>
      </div>

      <div v-if="filterValue.filterTemplates && filterValue.filterTemplates.length">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Product') }}
        </p>
        <div class="mb-8">
          <vintage-common-template-filter-item
            v-for="(template, index) in filterValue.filterTemplates"
            :key="index"
            :template="template"
            :current-template="filterValue.template"
          />
        </div>
      </div>

      <div>
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Price') }}
        </p>
        <p class="text-center  text-555 mb-3">
          {{ $formatPrice(filterValue.priceFilter[0]) }} - {{ $formatPrice(filterValue.priceFilter[1]) }}
        </p>

        <common-listing-price-slider
          v-model="filterValue.priceFilter"
          :min="filterProduct.min_price"
          :max="filterProduct.max_price"
          @change="updatePrice"
        />
      </div>

      <div v-if="filterProduct && filterProduct.color_group && filterProduct.color_group.length > 0" class="filter-color mt-3">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Color') }}
        </p>
        <div class="flex flex-wrap gap-1">
          <nuxt-link
            v-for="(color, index) in filterProduct.color_group"
            :key="index"
            :to="getFilterUrl('color', filterValue.color?.name === color ? '' : color)"
            sp-action="change_color"
          >
            <basic-common-color-item
              :color="color"
              size="xl"
              :rounded="true"
              :selectable="true"
              class="hover:shadow-color-active"
              :active="filterValue.color?.name === color"
            />
          </nuxt-link>
        </div>
      </div>
    </div>
  </common-sidebar>
</template>
