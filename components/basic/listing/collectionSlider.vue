<script lang="ts" setup>
const localePath = useLocalePath()

const { items } = defineProps({
  items: {
    type: Array<any>,
    default: () => []
  }
})

const slideWrapper = ref()
const slideContent = ref()
const slidePosition = ref(0)
const scrollPoint = ref({
  overallWidth: 0,
  clientWidth: 0,

  // maximum position we can scroll to the right (note: on the result it will be a negative number)
  maxRight: 0,
})

const activeCollection = computed(() => useRoute().params?.collection)

function slideLeft() {
  const tempNewPos = slidePosition.value - scrollPoint.value.clientWidth
  slidePosition.value = (tempNewPos > 0) ? tempNewPos : 0
}
function slideRight() {
  const tempNewPos = slidePosition.value + scrollPoint.value.clientWidth
  slidePosition.value = (tempNewPos > scrollPoint.value.maxRight) ? scrollPoint.value.maxRight : tempNewPos
}

onMounted(() => {
  scrollPoint.value = {
    overallWidth: slideContent.value.scrollWidth,
    clientWidth: slideContent.value.clientWidth,
    maxRight: (slideContent.value.scrollWidth - slideContent.value.clientWidth),
  }
})
</script>
<template>
  <div ref="slideWrapper" class="transition-all overflow-hidden relative">
    <div v-show="slidePosition !== 0" class="absolute top-0 left-0 pl-2 pt-0.5 cursor-pointer" @click="slideLeft">
      <div class="overlay overlay-left" />
      <i class="icon-basic-chevron-left relative text-lg z-2" />
    </div>

    <div ref="slideContent" class="flex gap-8 transform transition-all duration-500" :style="`--tw-translate-x: -${slidePosition}px;`">
      <nuxt-link
        v-for="(collection, index) in items"
        :key="index"
        :to="localePath({ path: `/collection/${collection.slug}` })"
        :title="collection.name"
        class="whitespace-nowrap pb-3"
        :class="{
          'text-[var(--color-gray-6)]': activeCollection !== collection.slug,
          'border-b border-black text-black': activeCollection === collection.slug,
        }"
      >{{ collection.name }}</nuxt-link>
    </div>

    <div v-show="slidePosition < scrollPoint.maxRight" class="absolute top-0 right-0 pr-2 pt-0.5 cursor-pointer" @click="slideRight">
      <div class="overlay overlay-right" />
      <i class="icon-basic-chevron-right relative text-lg z-2" />
    </div>
  </div>
</template>
<style scoped>
.overlay {
  z-index: 1;
  width: 3rem;
  height: 100%;
  position: absolute;
}
.overlay-left {
  background: linear-gradient(90deg, #fff 0%, #fff 41.67%, rgba(255, 255, 255, 0) 100%);
  left: 0;
}
.overlay-right {
  background: linear-gradient(270deg, #fff 0%, #fff 41.67%, rgba(255, 255, 255, 0) 100%);
  right: 0;
}
</style>