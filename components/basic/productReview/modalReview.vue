<script lang="ts" setup>
const { orderStatus, pos, item, customerName } = defineProps({
  orderStatus: {
    type: String,
    default: ''
  },
  pos: {
    type: Number,
    default: 0
  },
  item: {
    default: () => ({}),
    type: Object as PropType<OrderProduct>
  },
  customerName: {
    default: '',
    type: String
  },
})

const { $i18n, $imgUrl } = useNuxtApp()
const $t = $i18n.t

const {
  errorMessages,
  uploadErrorMessages,

  allowedExtensions,
  allowedTypes,
  uploadMaxSize,

  averageRating,
  form,
  isShowModalReviewProduct,
  isShowModalReviewCoupon,
  isFormLoading,

  storeDetail,
  shopNowPath,
  disableSubmit,
  canSubmitReview,

  resetForm,
  getVariant,
  removeUploadedItem,
  submit,

  fileInputOnChangeState,
  fileInputOnValidatorError,
} = useModalProductReview(orderStatus, pos, item, customerName)
</script>
<template>
  <div class="review-box absolute top-0 right-0">
    <template v-if="averageRating !== 0">
      <default-product-review-stars v-model="averageRating" disabled />
    </template>
    <template v-else>
      <button
        class="border border-black text-black text-sm px-2 py-1 whitespace-nowrap font-bold-2 uppercase hover:(bg-[var(--color-black-1)] text-white) transition-colors relative float-right"
        @click="isShowModalReviewProduct = true"
      >
        {{ $t('Review Product') }}
      </button>
      <lazy-common-modal
        :modal-id="'product-review-modal-' + item.id"
        :modal-class="'py-2 w-200 max-w-[90vw]'"
        :model-value="isShowModalReviewProduct"
        :close-icon="'icon-basic-close'"
        :title-container-class="'px-6 mb-4'"
        :title="$t('Product Review')"
        @close-modal="() => { isShowModalReviewProduct = false; resetForm() }"
      >
        <div class="relative px-6 border-t-1 border-[var(--color-gray-5)]">
          <div v-if="isFormLoading" class="absolute top:0 left-0 w-full h-full flex justify-center items-center bg-[rgba(215,215,215,0.7)] z-9">
            <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
          </div>
          <div class="mt-4">
            <div v-if="!canSubmitReview" class="flex flex-col my-1">
              <span class="my-1 px-4 py-2 font-medium bg-[#FF9802] color-[#721c24]">{{ $t("Your review is welcome once you've received the product.") }}</span>
            </div>
            <div v-if="errorMessages.length" class="flex flex-col my-1">
              <span v-for="msg in errorMessages" :key="msg" class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ msg }}</span>
            </div>
            <div class="grid grid-cols-2 mb-4">
              <div class="col-span-1 <md:col-span-2">
                <div class="font-medium">
                  {{ item.product_name }} / {{ getVariant(item) }}
                </div>
                <div>
                  <common-image
                    img-class="!w-22 pr-2"
                    :image="{
                      path: item?.thumb_url,
                      type: 'list',
                    }"
                    :alt="item?.campaign_title"
                  />
                </div>
              </div>
              <div class="col-span-1 <md:col-span-2">
                <div class="grid grid-cols-12 flex items-center py-1 gap-2">
                  <span class="col-span-5 <md:col-span-12 text-[14px]">{{ $t('Print Quality') }}</span>
                  <div class="col-span-7 <md:col-span-12">
                    <default-product-review-stars v-model="form.print_quality_rating" :common-class="'text-[#ffc107] px-2'" />
                  </div>
                </div>
                <div class="grid grid-cols-12 flex items-center py-1 gap-2">
                  <span class="col-span-5 <md:col-span-12 text-[14px]">{{ $t('Product Quality') }}</span>
                  <div class="col-span-7 <md:col-span-12">
                    <default-product-review-stars v-model="form.product_quality_rating" :common-class="'text-[#ffc107] px-2'" />
                  </div>
                </div>
                <div class="grid grid-cols-12 flex items-center py-1 gap-2">
                  <span class="col-span-5 <md:col-span-12 text-[14px]">{{ $t('Customer Support') }}</span>
                  <div class="col-span-7 <md:col-span-12">
                    <default-product-review-stars v-model="form.customer_support_rating" :common-class="'text-[#ffc107] px-2'" />
                  </div>
                </div>
              </div>
            </div>

            <div v-if="form.comment.length > 300" class="flex flex-col">
              <span class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ $t('The review may not be greater than 300 characters') }}.</span>
            </div>
            <textarea v-model="form.comment" rows="7" class="w-full border border-gray-300 mb-2 px-3 py-2" :placeholder="$t('Your Review')" />

            <div v-if="uploadErrorMessages.length" class="flex flex-col">
              <span v-for="msg in uploadErrorMessages" :key="msg" class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ msg }}</span>
            </div>
            <lazy-common-file-selector
              v-model="form.assets"
              :accept="allowedTypes.toString()"
              :accept-file-extensions="allowedExtensions"
              :return-media-type="true"
              :browse-text="'Upload'"
              :placeholder="'Upload product photo/video or drop it here...'"
              :size-limit="uploadMaxSize * 1024 * 1024"
              :limit="5"
              :is-push-to-value="true"
              @on-state-change="fileInputOnChangeState"
              @on-validator-error="fileInputOnValidatorError"
              @on-file-input-change="(lfilesLen) => { if (lfilesLen) { uploadErrorMessages.length = 0 } }"
              @on-error="fileInputOnValidatorError"
            />
            <div v-if="form.assets.length">
              <div class="mt-5 flex flex-wrap gap-4">
                <div v-for="(asset, index) in form.assets" :key="index" class="relative">
                  <img
                    :src="$imgUrl({ path: (asset.type === 'video') ? asset.thumb : asset.url, type: 'logo' })"
                    height="80"
                    width="80"
                    class="object-cover w-[80px] h-[80px]"
                  >
                  <div v-if="asset.type ==='video'" class="bg-[rgba(0,0,0,0.54)] absolute px-1 bottom-0">
                    <i class="icon-sen-play-circle-outline text-white" /> <span class="text-white">{{ $t('Video') }}</span>
                  </div>
                  <button class="rounded-1 px-1 border border-2 border-gray-600 font-medium text-xl absolute right-0 top-0 bg-white file-rm" @click="removeUploadedItem(index)">
                    ×
                  </button>
                </div>
              </div>
            </div>
            <hr class="my-3">
            <div class="w-full">
              <span class="float-left">
                <span class="text-red-600">* {{ $t('Video is about 30s to 1 minute. Maximum is 5 files.') }}</span>
                <br>
                <span class="text-red-600">* {{ $t('Upload your product photo/video to receive a discount coupon') }}</span>
              </span>
              <button v-if="canSubmitReview" :disabled="disableSubmit" class="border border-black text-black px-3 py-2 float-right transition-all hover:(bg-[var(--color-black-1)] text-white cursor-pointer)" @click="submit">
                {{ $t('Send') }}
              </button>
            </div>
          </div>
        </div>
      </lazy-common-modal>
      <lazy-common-modal
        v-if="storeDetail.product_review_coupon && form.assets.length > 0"
        :modal-id="'thank-you-modal-' + item.id"
        :model-value="isShowModalReviewCoupon"
        :close-icon="'icon-basic-close'"
        @close-modal="isShowModalReviewCoupon = false"
      >
        <div class="w-140 p-4 max-w-[90vw] mt-6">
          <div class="flex flex-col">
            <h1 class="font-medium text-2xl text-center">
              {{ storeDetail.name }}
            </h1>
            <p class="mx-4 mt-4 text-center text-lg">
              {{ storeDetail.product_review_thank_you_message }}
            </p>
            <h1 class="font-medium text-3xl text-center my-6">
              {{ storeDetail.product_review_coupon }}
            </h1>
            <a :href="shopNowPath" class="text-center border py-2 w-full font-bold-2 uppercase bg-[var(--color-black-1)] text-white transition-all text-2xl hover:opacity-70">
              {{ $t('Shop Now') }}
            </a>
          </div>
        </div>
      </lazy-common-modal>
    </template>
  </div>
</template>
<style scoped>
@media (max-width: 991px) {
  .review-box {
    position: relative;
    margin-top: 0.5rem;
    text-align: end!important;
  }
}
.file-rm {
  transform: translate(40%, -40%);
  line-height: 1;
}
</style>
