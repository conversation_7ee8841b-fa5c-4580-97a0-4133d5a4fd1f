<script lang="ts" setup>
const {
  errorMessages,
  form,
  isShowModalCheckReviewOrder,
  errorSubmitted,
  disableSubmit,
  resetForm,
  submit
} = useModalCheckReviewOrder()
</script>

<template>
  <button class="border border-[var(--color-black-1)] capitalize text-xl text-sm px-2 py-1 whitespace-nowrap relative float-right transition-all hover:(bg-[var(--color-black-1)] opacity-80 text-white)" @click="isShowModalCheckReviewOrder = true">
    {{ $t('Write a review') }}
  </button>
  <lazy-common-modal
    modal-id="write-a-review-modal"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[480px] p-3 px-4"
    :model-value="isShowModalCheckReviewOrder"
    :title="$t('Enter your order information')"
    close-icon="icon-basic-close"
    @close-modal="() => { isShowModalCheckReviewOrder = false; resetForm() }"
  >
    <hr class="mt-3">
    <div class="my-2 w-full">
      <div class="relative">
        <div>
          <div v-if="errorMessages.length" class="flex flex-col">
            <span v-for="msg in errorMessages" :key="msg" class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ msg }}</span>
          </div>
          <div v-if="errorSubmitted" class="flex flex-col">
            <span class="my-1 px-4 py-2 font-medium bg-[#f8d7da] color-[#721c24]">{{ $t("Your order does not exist.") }}</span>
          </div>

          <basic-common-input
            id="email"
            v-model="form.email"
            :label="`${$t('Email')} *`"
            :placeholder="`${$t('Email')} *`"
            input-label-style="font-medium"
            type="email"
            pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
          />
          <basic-common-input
            id="order_number"
            v-model="form.order_number"
            :label="`${$t('Order Number')} *`"
            :placeholder="`${$t('Order Number')} *`"
            input-label-style="font-medium"
            type="text"
            class="mt-4"
          />

          <hr class="my-3">
          <div class="w-full">
            <button :disabled="disableSubmit" class="btn-border-fill transition-all px-[0.75rem] py-[0.375rem] float-right" @click="submit">
              {{ $t('Write review') }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </lazy-common-modal>
</template>
