<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

const props = defineProps({
  currentProduct: {
    type: Object as PropType<Product>,
    default: undefined
  }
})

const currentProduct = toRef(() => props.currentProduct)

const {
  isLoadingReviews,
  productReviews,

  changePage,
} = useCampaignReview(currentProduct)

function viewImage (imageList:{src: string, type: any}[], index:number) {
  uiManager().viewImage(imageList.map(item => item?.src), index)
}
</script>
<template>
  <div v-if="productReviews?.reviewSummary.summary.review_count > 0" id="productReviewBox" class="mb-6">
    <div class="grid grid-cols-12 review-header border-b pb-4">
      <div class="col-span-8 flex gap-4 flex-col">
        <h4 class="capitalize font-medium text-2xl font-secondary">
          {{ $t('review', { count: productReviews.reviewSummary.summary.review_count }).split(' ')?.[1] }}
        </h4>
        <div class="flex gap-2 items-center">
          <default-product-review-stars
            :model-value="parseFloat(productReviews.reviewSummary.summary.average_rating || 5)"
            :container-class="'flex justify-around'"
            :common-class="'px-1 text-sm'"
            disabled
          />
          <span class="font-bold-2">{{ productReviews.reviewSummary.summary.average_rating }}</span>
          <span class="text-[var(--color-blue-1)]">({{ productReviews.reviewSummary.summary.review_count }})</span>
        </div>
      </div>
      <div class="col-span-4">
        <lazy-basic-product-review-modal-check-review-order />
      </div>
    </div>
    <div class="mt-4 relative">
      <div v-if="isLoadingReviews" class="absolute w-full h-full">
        <div class="flex w-full h-full justify-center items-center bg-[rgba(221,221,221,0.6)]">
          <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
        </div>
      </div>
      <template v-if="productReviews.reviews?.data?.length">
        <div v-for="(review, index) in productReviews.reviews.data" :key="index" class="flex flex-col gap-4 mt-6 pb-6 border-b border-gray-300">
          <div class="flex justify-between items-center">
            <default-product-review-stars
              :model-value="parseFloat(review.average_rating)"
              :container-class="'flex justify-around'"
              :common-class="'px-1 text-xs'"
              disabled
            />
            <span class="text-[var(--color-gray-6)] text-sm">{{ review.created_at }}</span>
          </div>
          <div>
            <div>
              <nuxt-link
                v-if="review.product_url"
                :to="review.product_url"
                class="text-sm hover:bg-[rgba(75,178,50,0.2)] transition"
              >
                {{ review.product_name }}
              </nuxt-link>
              <span
                v-else
                class="text-sm"
              >
                {{ review.product_name }}
              </span>
            </div>

            <span
              v-if="review.product_size"
              class="text-sm"
            >{{ $t('Size') }}: <span class="uppercase">{{ review.product_size }}</span></span>
            <span
              v-if="review.product_color"
              class="flex items-center gap-2 text-sm"
            ><span>{{ $t('Color') }}:</span><span class="rounded-1 w-[0.8em] h-[0.8em] border-gray-300 border-[0.1px]" :style="`background: ${colorVal(review.product_color)}`" /></span>
          </div>
          <p class="text-sm" v-text="review.comment" />
          <div class="flex gap-2 items-center text-sm">
            <img
              :src="review.avatar_url || `${cdnURL}images/default-user-icon.webp`"
              onerror="this.src = `${cdnURL}images/default-user-icon.webp`"
              loading="lazy"
              :alt="review.customer_name"
              class="max-w-[1.5rem] max-h-[1.5rem] border-radius-override"
            >
            <span class="text-[var(--color-gray-6)]">{{ review.customer_name }}</span>
          </div>
        </div>
        <BasicCommonPagination
          class="flex justify-start"
          :paginator="productReviews.reviews"
          :emit-page="true"
          size="sm"
          @input="(pageNumber) => { changePage(pageNumber) }"
        />
        <client-only v-if="productReviews.reviewSummary.files?.length">
          <div class="mt-12">
          <p class="text-lg mb-4">
            {{ $t('Photos from reviews') }}
          </p>
            <Splide :options="productReviewSplideSetting">
              <SplideSlide v-for="(file, index) in productReviews.reviewSummary.files" :key="index">
                <common-image
                  :key="index"
                  img-class="object-cover cursor-zoom-in w-[100%] h-[200px] rounded-lg"
                  :image="{ path: file.src, type: (file.type === 'video') ? 'product-review-video' : 'product-review-image'}"
                  @click="viewImage(productReviews.reviewSummary.files, index)"
                />
              </SplideSlide>
            </Splide>
          </div>
        </client-only>
      </template>
      <div v-else class="mt-6" align="center">
        {{ $t('There are no reviews yet') }}
      </div>
    </div>
  </div>
</template>
