<script lang="ts" setup>
const fileSelector = ref()

const {
  optionsSelect,
  contactForm,
  submitted,
  invalids,
  isUploadingFiles,
  recaptcha,

  successForm,
  warningForm,
  errorMsg,

  handleSubmit,
  handleStateChange,
} = useContactUsForm(fileSelector)

const { lastOrderUrl } = useLastOrder()
const localePath = useLocalePath()

const computedEmailState = computed(() => {
  if (!submitted.value) { return '' }

  return !(invalids.value.email_invalid || invalids.value.email_invalid)
})
</script>
<template>
  <div class="page-contact bg-gray-100 p-5 md:p-8 mt-10 rounded-xl">
    <h4 class="mb-4 text-2xl">
      {{ $t('Leave message') }}
    </h4>
    <!-- Name -->
    <basic-common-input
      id="name"
      v-model="contactForm.fullName"
      type="text"
      :label="$t('Your full name')"
      :placeholder="$t('Your full name')"
    />
    <!-- Email -->
    <basic-common-input
      id="email"
      v-model="contactForm.email"
      type="text"
      :label="$t('Your email')"
      :placeholder="$t('Your email')"
      class="mt-2"
      :state="computedEmailState"
    >
      <i class="lg:col-span-1" />
      <small class="col-span-3 lg:col-span-2">({{ $t('contact_us_text-2') }})</small>
    </basic-common-input>
    <div v-if="submitted" id="email-feedback">
      <span v-if="invalids.email_required" class="text-red-600">{{ $t('Email is required') }}</span>
      <span v-else-if="invalids.email_invalid" class="text-red-600">{{ $t('Email is invalid') }}</span>
    </div>

    <!-- Type -->
    <div class="grid grid-cols-3 mt-4 flex items-center">
      <label class="col-span-3 lg:col-span-1 font-bold-2 text-md uppercase">{{ $t('Subject') }}</label>
      <common-dropdown
        dropdown-id="contactFormTypeDropdown"
        dropdown-icon="icon-basic-chevron-down"
        dropdown-class="w-full"
        btn-class="w-full h-12 border-b border-[var(--color-gray-4)] transition-all"
        class="col-span-3 lg:col-span-2"
        show-data-class=""
      >
        <span class="px-3 float-left">{{ contactForm.type || 'Subject' }}</span>
        <template #content>
          <div
            v-for="msg in optionsSelect"
            :key="msg"
            sp-action="contactFormType"
            class="capitalize py-1 px-4 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center cursor-pointer"
            @click="contactForm.type = msg"
          >
            {{ $t(msg) }}
          </div>
        </template>
      </common-dropdown>
    </div>

    <!-- Order Number -->
    <basic-common-input
      id="order"
      v-model="contactForm.order"
      type="text"
      :label="$t('Order Number')"
      :placeholder="$t('Order Number')"
      class="mt-4"
    />
    <small v-if="lastOrderUrl" class="text-xs">
      ({{ $t('get your order number in your email confirmation') }} {{ $t('or') }} <a :href="`${localePath(lastOrderUrl)}?utm_source=page&utm_medium=contact-us`" target="_blank" rel="noopener noreferrer" v-text="$t('view last order status')" />)
    </small>
    <small v-else class="text-xs">
      ({{ $t('get your order number in your email confirmation') }} {{ $t('and') }} <a :href="localePath('/order/track')" target="_blank" rel="noopener noreferrer" v-text="$t('track your order here')" />)
    </small>

    <!-- Message -->
    <textarea
      id="message"
      v-model="contactForm.message"
      :class="`mt-4 border ${(submitted && (invalids.message_length || invalids.message_required)) ? 'border-red-600' : 'border-gray-400'} text-xl py-2 px-4 w-full resize-none overflow-y-scroll focus:(text-primary border-primary shadow-custom2) focus-visible:outline-none`"
      rows="7"
      :placeholder="$t('Your message')"
    />
    <div v-if="submitted" id="email-feedback">
      <span v-if="invalids.message_required" class="text-red-600">{{ $t('Message is required') }}</span>
      <span v-else-if="invalids.message_length" class="text-red-600">{{ $t('Message must have at least 10 characters') }}</span>
    </div>

    <!-- File -->
    <p class="mb-0 mt-4">
      <span class="font-medium">{{ $t('File Attachment') }}</span>
      <span> ({{ $t('Upload limit is 5 files.') }})</span>
    </p>
    <common-file-selector ref="fileSelector" v-model="contactForm.attachFile" :limit="5" @on-state-change="handleStateChange" />
    <small class="text-xs">({{ $t('contact_us_text-3') }})</small>

    <common-recaptcha ref="recaptcha" />

    <button
      class="transition-all bg-[var(--color-black-1)] text-white hover:(opacity-70) font-bold-2 w-full py-2 px-4 text-xl uppercase mt-4"
      type="button"
      :disabled="isUploadingFiles"
      @click="handleSubmit"
    >
      {{ $t('Send over') }}
    </button>

    <div v-if="!(successForm||warningForm)" style="font-size: 12px;" class="mt-2">
      <p><span>{{ $t('Please do not send multiple requests on the same issue.') }}</span></p>
    </div>

    <div v-if="successForm" class="mt-4 p-4 bg-[#d9f6eb] text-[#226d52]">
      <strong>{{ $t('Success') }}!</strong> {{ $t('You will receive a reply from us within 1 business day') }}.
    </div>
    <div v-else-if="warningForm" class="mt-4 p-4 bg-[#fff3cd] text-[#856404]">
      <strong>{{ $t('Error') }}!</strong> {{ errorMsg }}
    </div>
  </div>
</template>
<style scoped>
.page-contact a {
  color: var(--color-primary-light);
}
</style>
