<!-- <script lang="ts" setup>
const localePath = useLocalePath()
const { logo_url: logoUrl, name: storeName } = storeInfo()
</script>
<template>
  <header
    id="PageHeader"
    class="sticky top-0 bg-white transition-default z-2"
    :class="{
      'border-b': (isCheckoutPage || isCartPage)
    }"
  >
    <nav class="z-1 bg-white relative container flex gap-8 items-center justify-between py-2 px-4 md:(py-3 px-10)">
      <nuxt-link id="headerLinkLogo" :to="localePath('/')">
        <common-image
          v-if="logoUrl"
          img-class="h-6 md:h-8"
          img-id="headerLogo"
          :image="{path: logoUrl, type: 'logo'}"
          :alt="storeName"
          aria-label="home"
        />
        <div v-else>
          <span class="text-2xl md:text-3xl font-bold">{{ storeName }}</span>
        </div>
      </nuxt-link>
      <div class="flex items-center gap-8">
        <common-language-select
          :btn-class="''"
          :show-flag-in-button="false"
          :show-language="false"
          :use-svg="true"
          :show-dropdown-icon="false"
          :expandable-lang="true"
          :show-on-hover="false"
        >
          <template #btn>
            <span class="icon-basic-globe text-2xl"/>
          </template>
        </common-language-select>
        <nuxt-link
          id="headerCartButton"
          :to="localePath('/cart')"
          :title="$t('View cart')"
          data-test-id="header-cart-button"
          class="btn-text uppercase text-xl font-semibold relative"
        >
          <i class="icon-basic-cart text-2xl" style="line-height: 1.3;" />
          <client-only>
            <span v-if="totalQuantity().value" class="badge bg-black font-light">{{ totalQuantity() }}</span>
          </client-only>
        </nuxt-link>
      </div>
    </nav>
  </header>
</template> -->


<script lang="ts" setup>
const localePath = useLocalePath()
const { headerMenu, logo_url: logoUrl, enable_search: enableSearch, name: storeName } = storeInfo()

const {
  isSearching,
  searchKeyword,
  searchInputRef,
  activeMenu,
  showMenu,
  searchAction
} = useHeader()

const { location: deliverToLocation, initDeliverTo } = useDeliverToLocation()

onMounted(() => {
  initDeliverTo()
  // Hotkey to focus search input
  document.body.addEventListener('keydown', (e) => {
    if (!window.searchInput) {
      window.searchInput = document.querySelector('#headerSearchInput')
    }

    if (e.ctrlKey && e.key === '/') {
      window.searchInput?.focus()
    }
  })
})

const ctaLink = ref('https://sen.lc/regnewseller')
</script>

<template>
  <header
    id="PageHeader"
    class="sticky top-0 bg-white transition-default z-3"
    :class="{
      'border-b pb-2': isCheckoutPage,
      'show-menu': showMenu,
    }"
  >
    <nav class="z-1 bg-white relative container flex gap-8 items-center justify-center md:justify-start py-3 md:py-1 px-3">
      <button
        id="headerShowMenu"
        type="button"
        aria-label="menu"
        class="md:hidden absolute left-2 btn-text text-2xl icon-sen-menu"
        :class="{
          hidden: isCheckoutPage
        }"
        @click="uiManager().toggleHeaderMenu()"
      />
      <nuxt-link
        id="headerPageFAQ"
        :to="localePath('/page/faq')"
        :title="$t('FAQ')"
        class="md:hidden absolute left-10 btn-text uppercase text-xl icon-sen-info-outline"
        :class="{
          hidden: isCheckoutPage
        }"
      />
      <nuxt-link id="headerLinkLogo" :to="localePath('/')">
        <common-image
          v-if="logoUrl"
          img-class="h-6 md:h-8 md:my-2"
          img-id="headerLogo"
          :image="{path: logoUrl, type: 'logo'}"
          :alt="storeName"
          aria-label="home"
        />
        <div v-else>
          <span class="text-2xl md:text-3xl font-bold">{{ storeName }}</span>
        </div>
      </nuxt-link>
      <template v-if="!isCheckoutPage">
        <div v-if="enableSearch === 0" class="<md:hidden w-full" />
        <client-only>
          <div
            v-if="deliverToLocation"
            class="hidden gap-2 select-none deliver-to-location"
            :class="[
              (storeInfo().enable_deliver_to) ? 'lg:flex' : 'md:flex'
            ]"
          >
            <template v-if="storeInfo().enable_deliver_to">
              <i class="icon-sen-delivery-truck-2 text-5xl" />
              <span class="whitespace-nowrap flex flex-col justify-center">{{ $t('Deliver to') }}<br><strong>{{ deliverToLocation }}</strong></span>
            </template>
            <div class="flex items-center">
              <common-language-select
                :btn-class="''"
                :show-language="false"
                :use-svg="true"
                :show-dropdown-icon="false"
                :expandable-lang="true"
                :show-on-hover="false"
              />
            </div>
          </div>
          <form
            class="<md:hidden flex flex-grow focus-within:outline outline-1 outline-black border-radius-override"
            :class="{ 'hidden': enableSearch === 0 }"
            @submit.prevent.stop="searchAction"
          >
            <input
              id="headerSearchInput"
              ref="searchInputRef"
              v-model="searchKeyword"
              :placeholder="`${$t('Find your favourite topics')} (Ctrl + /)`"
              type="search"
              class="w-full bg-[#e8e8e8] px-2 py-2 border-radius-override !rounded-r-none"
              @mouseover="searchInputRef?.focus()"
            >
            <button
              id="headerSearchButton"
              aria-label="search"
              type="submit"
              class="btn-text bg-[#e8e8e8] px-2 text-2xl border-radius-override !rounded-l-none"
            >
              <div
                class="flex items-center justify-center"
                :class="{
                  'animate-spin': isSearching
                }"
              >
                <span :class="[ (isSearching) ? 'icon-sen-loading' : 'icon-basic-search' ]" />
              </div>
            </button>
          </form>
          <a
            v-if="storeInfo().id === 1"
            id="headerSellerLogin"
            target="_blank"
            :href="ctaLink"
            class="<md:hidden btn-text min-w-[fit-content] font-semibold uppercase text-[13px]"
          >
            {{ $t('Seller login') }}
          </a>
          <div class="<md:(absolute right-3) min-w-[fit-content] flex gap-2">
            <common-language-select
              class="justify-self-end md:hidden"
              :btn-class="''"
              :show-language="false"
              :use-svg="true"
              :show-dropdown-icon="false"
              :expandable-lang="true"
              :show-on-hover="false"
            />
            <nuxt-link
              id="headerPageFAQ"
              :to="localePath('/page/faq')"
              :title="$t('FAQ')"
              class="btn-text uppercase text-xl icon-sen-info-outline <md:hidden"
            />
            <nuxt-link
              id="headerCartButton"
              :to="localePath('/cart')"
              :title="$t('View cart')"
              data-test-id="header-cart-button"
              class="btn-text uppercase text-xl font-semibold relative"
            >
              <i class="icon-basic-cart text-2xl" style="line-height: 1.3;" />
              <client-only>
                <span class="badge bg-[var(--color-black-1)] font-light-2">{{ totalQuantity() }}</span>
              </client-only>
            </nuxt-link>
          </div>
        </client-only>
      </template>
    </nav>
    <template v-if="!isCheckoutPage">
      <div
        class="border-t border-b md:hidden transform transition"
        :class="{ 'hidden': enableSearch === 0,
                  '<md:translate-y-[-100%]': showMenu}"
      >
        <form
          class="w-full flex focus-within:outline outline-1 outline-primary container"
          @submit.prevent.stop="searchAction"
        >
          <input
            id="headerSearchInput"
            v-model="searchKeyword"
            :placeholder="`${$t('Find your favourite topics')}`"
            type="search"
            :enterkeyhint="`${$t('Search')}`"
            class="w-full py-2"
          >
          <button
            id="headerSearchButton"
            aria-label="search"
            type="submit"
            class="btn-text font-bold text-2xl"
          >
            <div
              class="flex items-center justify-center"
              :class="{
                'animate-spin': isSearching
              }"
            >
              <span :class="[ (isSearching) ? 'icon-sen-loading' : 'icon-basic-search' ]" />
            </div>
          </button>
        </form>
      </div>
      <common-collapse
        id="headerMenuMobile"
        as="ul"
        :when="showMenu"
        class="absolute container w-full px-3 bg-[#e8e8e8] max-h-[70vh] overflow-auto transition-default transform md:hidden"
        :class="{ 'translate-y-[-42px]': showMenu}"
      >
        <default-header-menu-item-mobile
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
          class="md:mr-3"
          :active-header-menu="activeMenu === index"
          @update-active-header-menu="activeMenu = activeMenu === index ? false : index"
        />
        <a
          v-if="storeInfo().id === 1"
          id="headerSellerLogin"
          target="_blank"
          :href="ctaLink"
          class="md:hidden btn-text text-lg mr-3"
        >
          {{ $t('Seller login') }}
        </a>
      </common-collapse>

      <ul
        id="headerMenu"
        class="absolute container w-full px-3 bg-[#e8e8e8] max-h-[70vh] <md:hidden transition-default transform md:(relative flex py-2 bg-white flex-wrap)"
      >
        <default-header-menu-item
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
          class="mr-3"
        />
      </ul>
      <client-only>
        <div
          v-if="deliverToLocation"
          class="hidden bg-[#2f3237] gap-4 items-center py-2 px-4"
          :class="[
            (storeInfo().enable_deliver_to) ? 'md:flex lg:hidden' : ''
          ]"
        >
          <template v-if="storeInfo().enable_deliver_to">
            <i class="icon-sen-delivery-truck-2 text-4xl text-white" />
            <span class="whitespace-nowrap text-white">{{ $t('Deliver to') }} <strong>{{ deliverToLocation }}</strong></span>
          </template>
          <div class="w-full grid">
            <common-language-select
              class="justify-self-end"
              :btn-class="''"
              :show-language="false"
              :use-svg="true"
              :show-dropdown-icon="false"
              :expandable-lang="true"
              :show-on-hover="false"
            />
          </div>
        </div>
      </client-only>
    </template>
  </header>
</template>
