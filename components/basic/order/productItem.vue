<script lang="ts" setup>
const localePath = useLocalePath()
const props = defineProps({
  convertCurrencyRate: {
    default: 1,
    type: Number
  },
  convertCurrencyCode: {
    default: undefined,
    type: String as PropType<CurrencyCode>
  },
  product: {
    default: undefined,
    type: Object as PropType<OrderProduct>
  },
  orderStatus: {
    default: '',
    type: String
  },
  pos: {
    default: 0,
    type: Number
  },
  customerName: {
    default: '',
    type: String
  },
  page: {
    default: '',
    type: String
  },
  showImg: {
    default: true,
    type: Boolean
  }
})

const options = JSON.parse(props.product?.options as string)

</script>

<template>
  <div
    class="py-2 flex products-center"
    :class="{'bg-[#fff6e6]': product?.fulfill_status === 'no_ship'}"
  >
    <common-image
      v-if="showImg"
      img-class="w-60 pr-2"
      :image="{
        path: product?.thumb_url,
        type: 'list',
        color: options?.color
      }"
      :alt="product?.campaign_title"
    />
    <div class="w-full max-w-[calc(100%-90px)] relative">
      <nuxt-link :to="localePath({path: product?.product_url || ''})" class="w-full px-1 block">
        <h5 class="btn-text font-bold-2 text-lg uppercase text-md text-overflow-hidden" :title="product?.campaign_title">
          {{ product?.product_name }}
        </h5>
      </nuxt-link>
      <div v-if="options" class="px-1 font-medium">
        <div
          v-for="(key, optionIndex) in Object.keys(options).filter(key=>(!options[key].startsWith('__')))"
          :key="optionIndex"
          class="capitalize"
        >
          {{ key }}: {{ options[key] }}
        </div>
      </div>
      <div class="px-1 mb-1 font-medium">
          {{ $t('Quantity') }}: {{ product?.quantity }}
      </div>
      <template v-if="product?.custom_options">
        <div v-if="product?.personalized === 3 || product?.full_printed === 5" class="col-12">
          <ul v-for="(v, i) in JSON.parse(product?.custom_options)" :key="i" class="pl-2 mb-2 list-disc">
            <template v-for="(vC, iC) in v">
              <li v-if="vC" :key="iC" class="capitalize" style="font-size: 13px;">
                <div>
                  {{ $t(vC.label) }}:
                  <a v-if="vC.type === 'image' && vC.value" :href="$imgUrl({path: vC.value, type: 'full'})" target="_blank" class="btn-text text-blue-900" @click.prevent="uiManager().viewImage(vC.value)">{{ $t('View image') }}</a>
                  <span v-else>{{ vC.value }}</span>
                </div>
              </li>
            </template>
          </ul>
        </div>
        <div v-else-if="product?.personalized === 1" class="col-12 px-0">
          <div v-for="(value, key) in JSON.parse(product?.custom_options)" :key="key" class="text-capitalize text-overflow-hidden">
            <span>{{ key.replace(/_/g, ' ') }}</span>: <span>{{ value }}</span>
          </div>
        </div>
      </template>
      <div v-if="product?.fulfill_status === 'no_ship'" class="text-sm text-[#ffc107]">
        <i class="icon-sen-alert mr-2" />
        <span>{{ $t("This product variant is not available. Please try other option/product.") }}</span>
        <span
          class="underline cursor-pointer text-red-500 float-right px-1"
          @click="$emit('removeProduct')"
        ><i class="icon-sen-delete" /></span>
        <nuxt-link
          :to="localePath('/cart')"
          :title="$t('Edit')"
        >
          <span class="underline cursor-pointer text-primary float-right px-1">{{ $t('Edit') }}</span>
        </nuxt-link>
      </div>
      <lazy-basic-product-review-modal-review
        v-if="page !== 'checkout'"
        :order-status="orderStatus"
        :pos="pos"
        :item="product"
        :customer-name="customerName"
      />
    </div>
  </div>
</template>
