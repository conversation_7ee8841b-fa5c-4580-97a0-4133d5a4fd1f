<script lang="ts" setup>

defineProps({
  order: {
    required: true,
    type: Object as PropType<Order>
  }
})
</script>

<template>
  <div class="flex justify-between mb-4 font-bold-2 uppercase text-xl mt-6">
    <span class="font-semibold"> {{ $t('Subtotal') }}:</span>
    <span data-test-id="summary-charge-subtotal">{{ $formatPriceByCurrency(order.total_product_amount, order.currency_rate , order.currency_code) }}  </span>
  </div>
  <div v-if="order.total_shipping_amount" class="flex justify-between mb-1">
    <span> {{ $t('Shipping') }} </span>
    <span data-test-id="summary-charge-shipping">{{ $formatPriceByCurrency(order.total_shipping_amount, order.currency_rate , order.currency_code) }} </span>
  </div>
  <div v-if="order.insurance_fee" class="flex justify-between mb-1">
    <span>{{ $t('Shipping insurance') }}</span>
    <span data-test-id="summary-charge-insurance">{{ $formatPriceByCurrency(order.insurance_fee, order.currency_rate , order.currency_code) }} </span>
  </div>
  <div v-if="order.total_discount" class="flex justify-between mb-1">
    <div>
      <span>
        {{ $t('Discount') }}
      </span>
      <span v-if="order.discount_code" class="uppercase text-green-600 border border-green-600 p-2 ml-2">{{ order.discount_code }}</span>
    </div>
    <span class="text-custom">- {{ $formatPriceByCurrency(order.total_discount, order.currency_rate , order.currency_code) }}</span>
  </div>
  <div v-if="order.payment_discount" class="flex justify-between mb-1">
    <span>{{ $t('Payment discount') }}</span>
    <span data-test-id="summary-charge-discount">{{ $formatPriceByCurrency(order.payment_discount, order.currency_rate , order.currency_code) }}</span>
  </div>
  <div v-if="order.total_tax_amount" class="flex justify-between mb-1">
    <span>{{ $t('Tax') }}</span>
    <span data-test-id="summary-charge-tax">{{ $formatPriceByCurrency(order.total_tax_amount, order.currency_rate , order.currency_code) }}</span>
  </div>
  <div v-if="order.tip_amount" class="flex justify-between mb-3">
    <span>{{ $t('Tip') }}</span>
    <span data-test-id="summary-charge-tip">{{ $formatPriceByCurrency(order.tip_amount, order.currency_rate , order.currency_code) }}</span>
  </div>
  <div class="flex justify-between mt-4 font-bold-2 text-xl uppercase">
    <span class="font-semibold">{{ $t('Total') }}</span>
    <span data-test-id="summary-charge-total">{{ $formatPriceByCurrency(order.total_amount, order.currency_rate , order.currency_code) }}</span>
  </div>
  <div v-if="order.currency_code !== 'USD' && order.currency_code !== 'EUR' && order.country !== 'GB'" class="text-center text-lg py-3 bg-[var(--color-gray-5)] border-[#bee5eb] mt-3">
    {{ $t('You will be charged in USD') }} <span class="font-weight-500">&nbsp;${{ Number(order.total_amount.toFixed(2)) }}</span>
  </div>
</template>

<style>
</style>
