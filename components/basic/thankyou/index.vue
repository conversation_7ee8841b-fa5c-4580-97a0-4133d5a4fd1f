<script lang="ts" setup>
const localePath = useLocalePath()

const {
  relatedProducts,
  currentCountry,
  currentShippingMethod,
  order,

  canEditAddressInfo,
  hasHouseNumber,
  hasMailboxNumber,
  hasNote,

  isShowModalConfirmAddress,
  isShowModalEditInfo,
} = await useThankYouPage()

const showOrderItems = ref(false)
</script>
<template>
  <main class="container mt-5" dusk="thank-you-page">
    <div class="grid grid-cols-12 gap-6 mb-10">
      <div class="col-span-12 md:col-span-7">
        <h3 class="font-bold-2 uppercase text-3xl mb-5" dusk="h3-receive-your-order">
          {{ $t('We’ve receive your order') }}
        </h3>
        <h5 class="font-medium text-xl mb-4">
          <span>
            {{ $t('Your order number is') }}
          </span>
          <span :id="`order-id-${order.order_number}`" class="text-primary">&nbsp;{{ order.order_number }}</span>
        </h5>
        <a
          :href="localePath(`/order/status/${order.access_token}`)"
          target="_blank"
          rel="noopener"
          class="font-bold-2 text-lg text-black p-3 uppercase border border-black transition-all hover:(bg-[var(--color-black-1)] text-white)"
        >{{ $t('Track Your Order') }}</a>
        <br>
        <br>
        <i>
          {{ $t('Your order confirmation is sent to') }}
          <span id="email-purchase">{{ order.customer_email }}</span>
          <br>
          <small>
            {{ $t("Please check your spam folder if you haven't received the email.") }}
            <br>
            <a :href="localePath('/page/contact-us')" target="_blank" rel="noopener" class="text-primary">{{ $t("Or contact us to edit your email address") }}</a>
            {{ $t('if it is incorrect.') }}
          </small>
        </i>

        <div v-if="currentShippingMethod" class="mt-4 p-4 border border-[var(--color-gray-5)]">
          <h5 class="font-bold-2 uppercase text-xl">
            {{ $t('Shipping method') }}
          </h5>
          <span class="capitalize">{{ $t(currentShippingMethod.description) }}</span>
          <span v-if="currentShippingMethod.name === SHIPPING_METHOD.express" class="text-red-600 font-medium"> ({{ $t('No cancellation allowed') }})</span>
        </div>

        <div class="mt-4 p-4 border border-[var(--color-gray-5)]">
          <h5 class="font-bold-2 uppercase text-xl">
            {{ $t('Shipping to') }}
          </h5>
          <div class="mt-4">
            <a v-if="canEditAddressInfo" href="#" class="btn-text edit-address float-right" @click.prevent="isShowModalEditInfo = true">{{ $t('Edit') }}</a>
            <p v-if="order.customer_name" class="mb-2">
              <i><strong id="full-name-purchase">{{ order.customer_name }}</strong></i>
            </p>
            <p v-if="order.address" class="mb-2">
              <i id="address-purchase">{{ order.address }} {{ order.address_2 || '' }}</i>
            </p>
            <p v-if="order.city" class="mb-2">
              <i><span id="city-purchase">{{ order.city }}</span>&nbsp;<span id="region-purchase">{{ order.state || '' }}</span>&nbsp;<span id="postal-code-purchase">{{ order.postcode || '' }}</span></i>
            </p>
            <p v-if="hasHouseNumber" class="mb-2">
              <i id="house-number-purchase">
                <span id="house-number-purchase">
                  {{ $t('House number') }}: {{ order.house_number }}
                </span>
              </i>
            </p>
            <p v-if="hasMailboxNumber" class="mb-2">
              <i id="mailbox-number-purchase">
                <span id="mailbox-number-purchase">
                  {{ $t('Mailbox number') }}: {{ order.mailbox_number }}
                </span>
              </i>
            </p>
            <p v-if="currentCountry && currentCountry.name" class="mb-2">
              <i id="country-purchase">{{ currentCountry.name }}</i>
            </p>
            <p v-if="order.customer_phone" class="mb-2">
              <i id="phone-purchase">{{ order.customer_phone }}</i>
            </p>
          </div>
        </div>
        <!-- ReprintsRefundPolicy -->

        <div class="my-8 card border border-[var(--color-gray-5)]">
          <div class="flex justify-between items-center p-4 border-b border-[var(--color-gray-5)]">
            <h3
              class="font-bold-2 text-lg md:text-2xl uppercase select-none cursor-pointer flex items-center gap-2"
              @click="showOrderItems = !showOrderItems"
            >
              {{ $t('Order Item(s)') }}
              <span class="transition-all transform leading-0" :class="[ showOrderItems ? 'rotate-180' : 'rotate-0' ]">
                <i class="icon-basic-chevron-down text-3xl leading-0" />
              </span>
            </h3>
          </div>
          <common-collapse :when="showOrderItems" class="transition-default">
            <basic-order-product-item
              v-for="(product, index) in order.products"
              :key="index"
              class="m-4 border-[var(--color-gray-5)] py-4"
              :class="{
                'border-t': index,
              }"
              :product="product"
              :convert-currency-code="order.currency_code"
              :convert-currency-rate="order.currency_rate"
              :show-img="true"
              page="checkout"
            />
          </common-collapse>
        </div>

        <div class="mt-4 grid gap-2">
          <div v-if="hasNote" class="bg-[#f6f7f9] p-4 grid gap-2" style="line-height: 1.3;">
            <h6 class="font-medium uppercase">
              {{ $t('Order note') }}
            </h6>
            <p class="text-[0.875em]">
              {{ order.order_note }}
            </p>
          </div>
          <div class="bg-[#f6f7f9] p-4 grid gap-2">
            <h6 class="font-medium uppercase">
              {{ $t('Shop with confidence') }}
            </h6>
            <p class="font-bold text-[0.875em]">
              {{ $t('Shopping on {domain} are safe and secure. Guaranteed!', {domain: $getHost()}) }}
            </p>
            <p class="text-[0.875em]">
              {{ $t("You'll pay nothing if unauthorized charges are made to your credit card as a result of shopping at {domain}", {domain: $getHost()}) }}
            </p>
          </div>
          <div class="bg-[#f6f7f9] p-4 grid gap-2">
            <h6 class="font-medium uppercase">
              {{ $t('Generic reprints & refund policies') }}
            </h6>
            <p class="text-[0.875em]">
              {{ $t('If, for any encounter product quality issues or defects. We can offer reproductions or refunds for your orders if there are order mistakes.') }}
            </p>
            <a :href="localePath('/page/return-policy')" target="_blank" class="btn-text text-[#17a2b8]">
              <small>{{ $t('Read our shipping and return policies') }}</small>
            </a>
          </div>
        </div>

        <lazy-common-thankyou-promotion class="mt-10 border-black" btn-class="bg-[var(--color-black-1)] text-white transition-all font-bold-2 uppercase hover:opacity-70" />
      </div>
      <div class="col-span-12 md:col-span-5">
        <div class="md:(col-span-5 order-last sticky h-max top-26) lg:(col-span-4 top-29) mb-4 border border-[var(--color-gray-5)] p-6">
          <h3 class="font-bold-2 text-xl uppercase">
            {{ $t('Order Summary') }} | {{ $t('item', { count: order.products.length }) }}
          </h3>

          <basic-order-calculate-price :order="order" />
        </div>
      </div>
    </div>

    <lazy-common-product-carousel
        v-if="relatedProducts?.length"
        class="my-16"
        title-class="text-center uppercase md:text-4xl text-xl font-bold-2 mb-2"
        title="You may be interested in these products"
        :products="relatedProducts"
        :splide-settings="basicProductSplideSetting"
        :splide-hide-arrow-on-ends="true"
      >
        <template #default="{ product, index }">
          <basic-common-product-item :product="product" :index="index" />
        </template>
      </lazy-common-product-carousel>

    <lazy-basic-order-modal-edit-info
      :order="order"
      :is-show="isShowModalEditInfo"
      @refresh-order="useRouter().go(0)"
      @is-show-modal="(val) => { isShowModalEditInfo = val }"
    />
    <lazy-basic-order-modal-confirm-address
      :order="order"
      :is-show="isShowModalConfirmAddress"
      @is-show-modal="(val) => { isShowModalConfirmAddress = val; }"
      @show-edit-address="() => { isShowModalEditInfo = true; }"
    />
  </main>
</template>
<style scoped>
.order-id {
  color: rgba(var(--color-primary), 0.8);
}
</style>
