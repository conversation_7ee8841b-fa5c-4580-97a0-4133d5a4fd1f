<script lang="ts" setup>
defineProps({
  userCampaignOption: {
    default: undefined,
    type: Object as PropType<UserCampaignOption>
  },
  campaignData: {
    default: undefined,
    type: Object as PropType<Campaign>
  },
  totalCustomOptionFee: {
    default: 0,
    type: Number
  }
})

const { $formatPrice } = useNuxtApp()
</script>

<template>
  <div v-if="userCampaignOption && campaignData">
    <div>
      <span class="text-2xl font-medium text-primary" data-test-id="price">{{ $formatPrice((userCampaignOption?.currentPrice || 0) + totalCustomOptionFee, userCampaignOption?.currentProduct?.currency_code) }}</span>

      <del v-if="storeInfo().store_type !== 'google_ads' && !storeInfo().disable_pre_discount && userCampaignOption.currentOldPrice" class="ml-3">{{ $formatPrice((userCampaignOption.currentOldPrice || 0) + totalCustomOptionFee, userCampaignOption.currentProduct?.currency_code) }}</del>
    </div>

    <div class="mt-4">
      {{ campaignData.name }}
    </div>

    <lazy-vintage-campaign-countdown
      v-if="storeInfo().store_type !== 'google_ads' && campaignData.show_countdown && (campaignData.end_time || campaignData.show_countdown > 1)"
      :show-countdown="campaignData.show_countdown"
      :end-time="campaignData.end_time"
    />
    <!-- <h6 class="mt-2">
      <span>{{ userCampaignOption.currentProduct?.name }} </span>
      <span v-if=" userCampaignOption.currentOptions">
        <span
          v-for="(item, optionIndex) in Object.keys(userCampaignOption.optionList)"
          :key="optionIndex"
          class="uppercase"
        >
          <span>&nbsp;/&nbsp;</span>
          {{ userCampaignOption.currentOptions[item] ? userCampaignOption.currentOptions[item].split('-').join(' '): $t(item) }}
        </span>
      </span>
    </h6> -->
  </div>
</template>
