<script lang="ts" setup>
import type DefaultCampaignBundleBox from '~/components/default/campaign/bundleBox.vue'

import type DefaultCampaignModalAddToCart from '~/components/default/campaign/modalAddToCart.vue'
import type DefaultCampaignModalConfirmDesign from '~/components/default/campaign/modalConfirmDesign.vue'
import type DefaultCampaignModalSelectSize from '~/components/default/campaign/modalSelectSize.vue'

import { useCampaignStore } from '~~/store/campaign'
import { useCartStore } from '~~/store/cart'
import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/utils/constant'

const props = defineProps({
  campaignSlug: {
    type: String,
    required: true
  },
  isModal: {
    default: false,
    type: Boolean
  }
})
const cartStore = useCartStore()
const $viewport = useViewport()
const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
// eslint-disable-next-line
let isAddAllToCart:Boolean = false
// const personalizeCustom = ref<InstanceType<typeof PersonalizeCustom>>()
// const personalizePB = ref<InstanceType<typeof PersonalizePB>>()
// const personalizeCustomOptions = ref<InstanceType<typeof PersonalizeCustomOptions>>()

const modalAddToCart = ref<InstanceType<typeof DefaultCampaignModalAddToCart>>()
const modalSelectSize = ref<InstanceType<typeof DefaultCampaignModalSelectSize>>()
const modalConfirmDesign = ref<InstanceType<typeof DefaultCampaignModalConfirmDesign>>()

const bundleBox = ref<InstanceType<typeof DefaultCampaignBundleBox>>()

const {
  campaignId,
  userCampaignOption,
  campaignData,
  // similarProducts,
  relatedProducts,
  relatedCartProducts,

  productStats,
  campaignPromotions,

  dataDescription,
  dataProductDetail,

  updateProduct,
  updateOption,
  checkOptions,
  resetData,
  getRelatedProduct
} = useCampaign(props.campaignSlug, props.isModal)

await resetData()

const {
  bundleProduct,
  currentBundleProduct,
  totalBundleDiscount,
  saveBundleDiscount,
  getDataBundle,
  checkBundleProduct,
  addBundleProductToCart,
  refetchDataBundle
} = useCampaignBundle(campaignId, false, modalConfirmDesign, BUNDLE_DISCOUNT_VIEW_PLACE.CAMPAIGN_DETAIL)

const {
  personalizeCustom,
  personalizePB,
  personalizeCustomOptions,

  isShowDesign,
  currentPersonalize,
  showDesignHandler,
  getDataCustomDesign,
  updatePersonalizeCustom,
  selectCustomDesign,
  checkPersonalize,
  handlePersonalizeError
} = useCampaignPersonalize(campaignData, userCampaignOption, props.isModal)

const customOptions = computed(() => userCampaignOption.currentProduct?.template_custom_options ?? userCampaignOption.currentProduct?.custom_options ?? campaignData.options)
const commonOptions = computed(() => userCampaignOption.currentProduct?.common_options ?? campaignData.common_options)
const {
  extraCustomFee,
  groupCustomOptionsQuantity,
  userCustomOptions,
  userCommonOptions,
  totalCustomOptionFee,
  updateCustomOptions,
  updateCommonOptions,
  requiredValue,
  updateGroupCustomOptionsQuantity,
  checkPersonalizeCustomOptions,
  resetCommonOption,
  resetOption
} = useCampaignPersonalizeCustomOptions(customOptions, props.isModal, campaignData?.personalized, userCampaignOption.currentProduct?.full_printed, commonOptions)

if (import.meta.client && campaignData.id && (campaignData.personalized === 1 || campaignData.personalized === 2)) {
  // fix error after refresh page. wait new version of nuxt
  setTimeout(async () => {
    await getDataCustomDesign()
  })
}

if (import.meta.client && campaignData.id && !props.isModal) {
  const intervalTracking = setInterval(() => {
    if (window.userActivity) {
      clearInterval(intervalTracking)
      getDataBundle()
    }
  }, 500)
}

/**
 * @param [isConfirmDesign] Passed all 'personalize' checks
 * @param isCheckout
 * @param isForceOpenModal
 */
async function addToCart(isConfirmDesign = false, isCheckout = false, isForceOpenModal = false) {
  userCampaignOption.isAddToCart = true // for personalize_input check state
  if (checkOptions()) { // check normal options
    if (userCampaignOption.optionError === 'size') {
      modalSelectSize.value!.isShowModal = true
    }
    else {
      uiManager().createPopup($i18n.t('Please choose {option}', { option: userCampaignOption.optionError }))
    }
    return
  }

  if (!isConfirmDesign) { // check personalize options
    let designUrl: string[] = []
    if (userCampaignOption.currentProduct?.personalized === 1 || userCampaignOption.currentProduct?.personalized === 2) {
      const value = await checkPersonalize()
      if (value.success) {
        designUrl = designUrl.concat(value.designDataUrl || [])
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }
    else if (userCampaignOption.currentProduct?.personalized === 3 || userCampaignOption.currentProduct?.full_printed === 5) {
      const value = checkPersonalizeCustomOptions()
      if (!value.success) {
        return handlePersonalizeError({
          ...value,
          product: userCampaignOption.currentProduct
        }, currentBundleProduct)
      }
    }

    if (isAddAllToCart) {
      const value = await checkBundleProduct(isForceOpenModal)
      if (value.success) {
        designUrl = designUrl.concat(value?.designDataUrl || [])
        currentBundleProduct.value = undefined
      }
      else {
        return handlePersonalizeError(value, currentBundleProduct)
      }
    }

    if (designUrl.length) {
      modalConfirmDesign.value!.isShowModal = designUrl
      return
    }
  }

  const cartItem = cartStore.addCartItem(campaignData, userCampaignOption, {
    totalCustomOptionFee: totalCustomOptionFee.value,
    userCustomOptions: userCommonOptions.concat(userCustomOptions),
    groupCustomOptionsQuantity: groupCustomOptionsQuantity.value + (userCommonOptions?.length || 0)
  }, true)

  const listPersonalize: { [key: string]: Personalize } = {}
  if ((campaignData.personalized === 1 || campaignData.personalized === 2) && userCampaignOption?.currentProduct?.personalizeList?.length) {
    listPersonalize[cartItem.id] = userCampaignOption?.currentProduct?.personalizeList[0]
  }

  if (isAddAllToCart) {
    const cartItemBundle = addBundleProductToCart()
    cartItemBundle.forEach((cartItem) => {
      if ((cartItem.personalized === 1 || cartItem.personalized === 2) && cartItem.product.personalizeList?.length) {
        listPersonalize[cartItem.id] = cartItem?.product?.personalizeList[0]
      }
    })
  }

  if (Object.keys(listPersonalize).length) {
    uiManager().$patch({ isUploadFile: true })
    window.loadingUploadImage = getPersonalizeUpload(listPersonalize)
    window.loadingUploadImage.then((uploadUrl) => {
      uiManager().$patch({ isUploadFile: false })
      Object.keys(uploadUrl).forEach((key) => {
        if (uploadUrl[key]) {
          cartStore.updateCartItemByID(key, { thumb_url: uploadUrl[key] })
        }
      })
    })
  }

  if (isCheckout) {
    await createOrder()
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return
  }

  if ($viewport.isLessThan(VIEWPORT.tablet) || isAddAllToCart) {
    if (props.isModal) {
      useCampaignStore().$patch({ modalCampaignUrl: '' })
    }
    return useRouter().push(localePath('/cart'))
  }

  modalAddToCart.value!.isShowModal = true
  modalAddToCart.value!.cartItem = cartItem
}

const title = computed(() => {
  const campaignName = campaignData.name
  const productName = userCampaignOption.currentProduct?.name
  const enableProductNameAfter = storeInfo().enable_product_name_after

  if (enableProductNameAfter) {
    return `${campaignName} ${productName}`
  }
  return campaignName
})

onMounted(() => {
  getRelatedProduct()
})

function updateSelectProduct(productName: string, productId?: number) {
  updateProduct(productName, productId)
  if (userCampaignOption.currentProduct?.full_printed === 5) {
    resetOption()
    resetCommonOption()
  }
}

const { campaignDescription, thumbnail, reviewSummary } = useCampaignSchema(campaignData, userCampaignOption)

const isHiddenBottomButton = ref(true)
function checkBottomButton() {
  if (import.meta.server) { return }
  if (window.innerWidth >= 768) { return }
  const buttonRect = document.getElementById('quantityDropdown')?.getBoundingClientRect()
  isHiddenBottomButton.value = (buttonRect?.top || 0) >= 0
}
onMounted(() => {
  document.addEventListener('scroll', checkBottomButton)
})
onUnmounted(() => {
  document.removeEventListener('scroll', checkBottomButton)
})
</script>

<template>
  <main
    v-if="campaignData.status !== 'blocked'"
    :id="isModal ? 'modalCampaignPage' : 'campaignPage'"
    data-test-id="campaign-container"
    class="container"
    :class="{ 'flex flex-col max-h-100vh md:max-h-80vh ': isModal }"
  >
    <div itemscope itemtype="http://schema.org/Product">
      <meta itemprop="brand" :content="campaignData.store_name">
      <meta itemprop="name" :content="campaignData.name">
      <meta itemprop="description" :content="campaignDescription">
      <meta itemprop="productID" :content="campaignData.id">
      <meta itemprop="url" :content="`https://${storeInfo().domain}/${campaignData.slug}`">
      <meta itemprop="image" :content="thumbnail">
      <div v-if="reviewSummary && storeInfo().product_review_display !== 'disable' && reviewSummary.review_count > 0" itemprop="aggregateRating" itemscope itemtype="https://schema.org/AggregateRating">
        <meta itemprop="ratingValue" :content="reviewSummary.average_rating">
        <meta itemprop="reviewCount" :content="reviewSummary.review_count">
      </div>
      <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
        <link itemprop="availability" href="http://schema.org/InStock">
        <link itemprop="itemCondition" href="http://schema.org/NewCondition">
        <meta itemprop="price" :content="userCampaignOption.currentProduct?.price">
        <meta itemprop="priceCurrency" :content="userCampaignOption.currentProduct?.currency_code">
      </div>
    </div>
    <div class="flex justify-center w-full mt-6">
      <common-breadcrumb
        :items="[{ text: 'Home', url: '/' }, { text: title || '', url: `/${campaignData.slug}` }]"
        separator-icon="text-gray-400 icon-vintage-chevron-right text-sm"
      />
    </div>
    <div
      class="flex flex-wrap mt-6"
      :class="{ 'overflow-y-auto mb-20 overflow-x-hidden': isModal }"
    >
      <div
        class="w-full top-0 md:(w-1/2 pr-4 sticky h-max)"
        :class="{ 'lg:w-16/24 md:top-15': !isModal }"
      >
        <vintage-campaign-view-box
          v-if="userCampaignOption.currentProduct"
          :is-modal="isModal"
          :campaign-data="campaignData"
          :images-list="userCampaignOption.imagesList"
          :current-product="userCampaignOption.currentProduct"
          :color="userCampaignOption.optionListFull.color?.length > 1 ? userCampaignOption.currentOptions.color : userCampaignOption.optionListFull.color?.[0]"

          :personalize="currentPersonalize"
          :is-show-design="isShowDesign"

          @change-image="isShowDesign = false"
        />
        <common-control-custom-image
          v-if="currentPersonalize?.customImageList?.[0]"
          :key="currentPersonalize.personalizeKey"
          :class="{ invisible: !isShowDesign }"
          class="my-3"
          :custom-image-item="currentPersonalize?.customImageList[0]"
        />
        <div
          class="flex flex-wrap items-center py-2 mt-20 <md:hidden"
          :class="[
            (campaignData.seller && storeInfo().id === 1) ? 'justify-between' : 'justify-end'
          ]"
        >
          <div v-if="campaignData.seller && storeInfo().id === 1" class="text-sm lg:text-base">
            <span class="mr-1 text-gray-400">{{ $t('Designed and Sold by') }}</span>
            <nuxt-link v-if="campaignData.seller.slug" class="text-primary" :href="localePath(`/artist/${campaignData.seller.slug}`)">
              {{ campaignData.seller.nickname }}
            </nuxt-link>
            <span v-else class="text-primary">{{ campaignData.seller.nickname }}</span>
          </div>
          <nuxt-link v-if="!isModal" :to="`/report?campaign=${campaignData.slug}`" class="btn-vintage relative p-2 font-medium flex gap-1">
            <span><i class="icon-sen-alert-outline" /></span>
            <span>{{ $t('Report a policy violation') }}</span>
          </nuxt-link>
        </div>
        <lazy-vintage-product-review-campaign-review-section
          v-if="storeInfo().product_review_display !== 'disable' && !isModal"
          class="mt-10 hidden md:block"
          :current-product="userCampaignOption.currentProduct"
        />
      </div>
      <div
        class="w-full md:w-1/2 md:pl-4"
        :class="{ 'lg:w-8/24': !isModal }"
      >
        <vintage-campaign-general-info
          :campaign-data="campaignData"
          :user-campaign-option="userCampaignOption"
          :total-custom-option-fee="totalCustomOptionFee"
        />

        <default-campaign-product-list
          v-if="campaignData.products"
          class="mb-3"
          :is-modal="isModal"
          :products="campaignData.products"
          :current-product="userCampaignOption.currentProduct"
          :is-dropdown-type="true"
          :campaign-slug="campaignData.slug"
          :current-options="userCampaignOption.currentOptions"
          additional-product-selector-btn-class="shadow-custom4 flex pl-[5%]"
          :campaign-system="campaignData.system_type"
          @update-product="updateSelectProduct"
        />

        <vintage-campaign-option-list
          :is-modal="isModal"
          :option-list="userCampaignOption.optionList"
          :current-product="userCampaignOption.currentProduct"
          :campaign-slug="campaignData.slug"
          :current-options="userCampaignOption.currentOptions"
          :option-error="userCampaignOption.optionError"
          @click="useTracking().customTracking({
            event: 'interact',
            data: {
              action: 'option_click'
            }
          })"
          @update-option="updateOption"
        />

        <client-only>
          <lazy-default-campaign-personalize-custom-options
            v-if="userCampaignOption.currentProduct?.personalized === 0 && userCampaignOption.currentProduct && userCampaignOption.currentProduct?.custom_options"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="userCampaignOption.currentProduct.template_custom_options"
            :common-options="userCampaignOption.currentProduct.common_options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :user-custom-options="userCustomOptions"
            :extra-custom-fee="extraCustomFee"
            :current-options="userCampaignOption.currentOptions"
            :current-product-thumb-url="userCampaignOption.currentProduct?.thumb_url"
            :current-product-id="userCampaignOption.currentProduct?.id"
            @required-value="requiredValue"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
          />
          <lazy-default-campaign-personalize-custom
            v-if="userCampaignOption.currentProduct?.personalized === 1"
            ref="personalizeCustom"
            :is-modal="isModal"
            :product="userCampaignOption.currentProduct"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-personalize-custom="updatePersonalizeCustom"
            @select-custom-design="selectCustomDesign"
            @show-design="showDesignHandler"
          />

          <lazy-default-campaign-personalize-pb
            v-if="userCampaignOption.currentProduct?.personalized === 2"
            ref="personalizePB"
            :is-modal="isModal"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @show-design="isShowDesign = true"
          />

          <lazy-default-campaign-personalize-custom-options
            v-if="userCampaignOption.currentProduct?.personalized === 3"
            ref="personalizeCustomOptions"
            :is-add-to-cart="userCampaignOption.isAddToCart"
            :custom-options="campaignData.options"
            :group-custom-options-quantity="groupCustomOptionsQuantity"
            :common-options="campaignData.common_options"
            :user-custom-options="userCustomOptions"
            :user-common-options="userCommonOptions"
            :extra-custom-fee="extraCustomFee"
            :campaign-data="campaignData"
            :selected-color="userCampaignOption.currentOptions.color"
            :current-product-thumb-url="userCampaignOption.currentProduct?.thumb_url"
            :current-product-id="userCampaignOption.currentProduct?.id"
            @click="useTracking().customTracking({
              event: 'interact',
              data: {
                action: 'personalize_click'
              }
            })"
            @update-group-custom-options-quantity="updateGroupCustomOptionsQuantity"
            @update-custom-options="updateCustomOptions"
            @update-common-options="updateCommonOptions"
          />

          <p class="my-4">
            <span v-if="userCampaignOption.currentVariant?.out_of_stock" class="text-red-500 ml-2 font-weight-500">
              {{ $t('Out of stock') }}
            </span>
          </p>
          <div class="flex gap-1">
            <div
              class="flex w-full transition-all gap-2"
              :class="{
                'p-1 border bottom-fixed w-full bg-white z-1': isModal,
                'p-1 border w-full bg-white z-1 bottom-fixed': !isHiddenBottomButton
              }"
            >
              <button
                v-if="isModal"
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock)"
                class="btn-vintage-shadow py-2 px-4 font-semibold relative rounded-full w-full"
                data-test-id="buy-it-now"
                @click="isAddAllToCart = false; addToCart(false, true)"
              >
                {{ $t('Buy it now') }}
              </button>
              <button
                :disabled="!!isLoading || !!(userCampaignOption.currentVariant?.out_of_stock)"
                class="btn-vintage-shadow py-2.5 px-4 font-semibold relative rounded-full w-full text-white"
                style="--sp-btn-background: #222;"
                dusk="add-to-cart-button"
                data-test-id="campaign-add-to-cart"
                @click="isAddAllToCart = false; addToCart()"
              >
                <common-loading-dot v-if="isLoading" />
                <span v-else>{{ $t('Add to cart') }}</span>
              </button>
            </div>
          </div>

          <template v-if="!isModal">
            <div v-if="productStats?.add_to_cart || productStats?.visit" class="mt-3 flex items-center">
              <span><i class="text-4xl icon-sen-cart-arrow-down" /></span>
              <div class="pl-2">
                <span class="font-medium">{{ $t('Other people want this') }}. </span>
                <span v-if="productStats?.add_to_cart"> {{ $t('There are value people have this in cart right now', { value: productStats.add_to_cart }) }}.</span>
                <span v-else-if="productStats?.visit"> {{ $t('There are value people viewing this', { value: productStats.visit }) }}.</span>
              </div>
            </div>
          </template>
        </client-only>
        <template v-if="!isModal">
          <div
            v-if="storeInfo().store_type !== 'google_ads' && storeInfo().show_payment_button"
            class="border border-[#ced4da] border-2 px-4 mt-8 flex flex-wrap justify-center"
          >
            <div class="bg-white px-6" style="transform: translateY(-50%);">
              {{ $t('GUARANTEED:') }}<strong class="pl-1">{{ $t('SAFE CHECKOUT') }}</strong>
            </div>
            <common-image
              img-class="mt-2 mb-5 w-full"
              :image="{ path: `images/safe_badge.webp` }"
              alt="safe_checkout"
            />
          </div>
          <client-only>
            <lazy-default-campaign-bundle-box
              v-if="bundleProduct?.length && !storeInfo().disable_promotion"
              ref="bundleBox"
              class="mt-3"

              :bundle-product="bundleProduct"
              :user-campaign-option="userCampaignOption"
              :current-bundle-product="currentBundleProduct"

              :total-bundle-discount="totalBundleDiscount"
              :save-bundle-discount="saveBundleDiscount"
              :total-custom-option-fee="totalCustomOptionFee"

              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'bundle_box'
                }
              })"
              @select-bundle-product="currentBundleProduct = $event ? shallowReactive($event) : false"
              @submit="isAddAllToCart = true; addToCart(false, false, bundleProduct[0].personalized === 3 || bundleProduct[0].personalized === 5)"
              @modal-submit="isAddAllToCart ? addToCart() : ''; currentBundleProduct = undefined"
              @disable-add-all-to-cart="isAddAllToCart = false"
              @reset-bundle-product="productIdExclude => refetchDataBundle([productIdExclude])"
            />

            <lazy-common-campaign-description
              :data-description="dataDescription"
              :data-product-detail="dataProductDetail"
              collapse-class="mt-1"
              collapse-class-extended="mb-5"
              title-class="relative btn-vintage font-medium"
              :default-expanded="['dataProductDetail', 'shippingInfo']"
              :multi-expand="true"
              @click="useTracking().customTracking({
                event: 'interact',
                data: {
                  action: 'description_box'
                }
              })"
            />
            <lazy-default-common-promotions-list
              v-if="campaignPromotions?.length && !storeInfo().disable_promotion"
              :promotions-list="campaignPromotions"
              class="mt-3"
            />
          </client-only>
        </template>
      </div>
    </div>
    <template v-if="!isModal">
      <client-only>
        <lazy-vintage-product-review-campaign-review-section
          v-if="storeInfo().product_review_display !== 'disable'"
          class="mt-10 block md:hidden"
          :current-product="userCampaignOption.currentProduct"
        />

        <lazy-common-product-carousel
          v-if="relatedProducts?.length"
          class="mt-10"
          title-class="text-3xl font-secondary font-light mb-4 pl-2"
          title="Frequently bought together"
          :products="relatedProducts.slice(0, 6)"
          :force-static-grid="true"
          static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6"
        >
          <template #default="{ product }">
            <vintage-common-product-item :product="product" class="inline-block" :show-add-to-cart="false" />
          </template>
        </lazy-common-product-carousel>

        <div
          v-if="campaignData.collections?.length && !storeInfo().disable_related_collection"
          class="mt-10"
        >
          <h1 class="text-xl font-medium">
            {{ $t('Related collections') }}
          </h1>
          <div class="flex gap-2 flex-wrap mt-4">
            <nuxt-link
              v-for="(collection, index) in campaignData.collections"
              :key="index"
              :to="`/collection/${collection.slug}`"
              class="text-md font-medium relative btn-vintage py-3 pl-4 pr-5 rounded-full whitespace-nowrap flex justify-center"
              style="--hover-scale: 1; --bg: #0e0e0e17;"
            >
              {{ collection.name }} <i class="icon-vintage-arrow-right text-2xl" />
            </nuxt-link>
          </div>
        </div>

        <div class="mt-3 flex justify-end md:hidden">
          <div v-if="campaignData.seller && storeInfo().id === 1">
            <span class="mr-1 text-gray-400">{{ $t('Designed and Sold by') }}</span>
            <a v-if="campaignData.seller.slug" class="text-primary" :href="localePath(`/artist/${campaignData.seller.slug}`)">{{ campaignData.seller.nickname }}</a>
            <span class="text-primary">{{ campaignData.seller.nickname }}</span>
          </div>
        </div>
      </client-only>
    </template>

    <client-only>
      <lazy-vintage-campaign-modal-add-to-cart
        ref="modalAddToCart"
        :related-cart="relatedCartProducts"
      />
      <lazy-default-campaign-modal-select-size
        ref="modalSelectSize"
        :current-product="userCampaignOption.currentProduct"
        :option-list="userCampaignOption.optionList"
        @open-size-guide="uiManager().updateSizeGuideData(userCampaignOption.currentProduct, true)"
        @select-size="updateOption({ key: 'size', value: $event }); addToCart()"
      />
      <lazy-default-campaign-modal-confirm-design
        ref="modalConfirmDesign"
        @confirm-design="addToCart(true)"
      />
    </client-only>
  </main>
  <main v-else class="py-10 h-60vh">
    <div class="mt-5 pt-5 text-3xl text-center">
      <h2> {{ $t('This campaign was taken down due to a content violation') }} </h2>
    </div>
  </main>
</template>
