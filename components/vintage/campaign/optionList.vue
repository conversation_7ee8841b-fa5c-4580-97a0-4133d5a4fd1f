<script lang="ts" setup>
const { currentOptions, currentProduct } = defineProps({
  optionList: {
    default: undefined,
    type: Object as PropType<OptionsList>
  },
  currentOptions: {
    default: undefined,
    type: Object as PropType<{ [key: string]: string }>
  },
  currentProduct: {
    default: undefined,
    type: Object as PropType<Product>
  },
  campaignSlug: {
    default: '',
    type: String
  },
  optionError: {
    default: '',
    type: [String, Boolean]
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const hasSizeGuide = computed(() => {
  return generalSettings().hasSizeGuide(currentProduct?.template_id)
})
</script>

<template>
  <client-only>
    <div v-if="optionList && currentOptions" class="flex flex-col gap-3 mb-3">
      <div
        v-for="(key, index) in Object.keys(optionList as OptionsList)"
        :key="index"
      >
        <div>
          <span v-if="storeInfo().option_label_enable" class="text-xs capitalize">
            {{ `${$t('Choose your product')} ${$t(key.replace(/_/g, ' '))}` }} <span class="text-red-600 font-semibold text-xl">*</span>
          </span>
          <template v-if="key === 'color'">
            <common-dropdown
              :dropdown-id="`${key}-dropdown`"
              btn-class="btn-border w-full py-0.5 capitalize text-overflow-hidden shadow-custom4"
              dropdown-class="w-full"
            >
              <div class="flex pl-[5%] items-center gap-1">
                <common-color-item
                  :color="currentOptions[key]"
                  :title="currentOptions[key]"
                  size="md"
                />
                <span class="capitalize">{{ currentOptions[key] }}</span>
              </div>
              <template #content>
                <li
                  v-for="(value, optionIndex) in optionList[key]"
                  :key="`${key}-${optionIndex}`"
                  class="flex pl-[5%] py-1 items-center gap-1 hover:(bg-primary !text-contrast cursor-pointer)"
                  data-test-id="product-color"
                  sp-action="change_color"
                  @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
                >
                  <common-color-item
                    :color="value"
                    :title="value"
                    size="md"
                  />
                  <span class="capitalize">{{ value }}</span>
                </li>
              </template>
            </common-dropdown>
          </template>
          <template v-else>
            <common-dropdown
              :dropdown-id="`${key}-dropdown`"
              btn-class="btn-border w-full py-2 capitalize text-overflow-hidden shadow-custom4 flex pl-[5%]"
              dropdown-class="w-full"
            >
              <span class="uppercase">{{ currentOptions[key] }}</span>
              <template #content>
                <li
                  v-for="(value, optionIndex) in optionList[key]"
                  :key="`${key}-${optionIndex}`"
                  class="flex pl-[5%] items-center py-1 hover:(bg-primary !text-contrast cursor-pointer) uppercase"
                  :sp-action="`change_${key}`"
                  :data-test-id="`product-change-${key}`"
                  @click="value === (currentOptions && currentOptions[key]) ? '' : $emit('updateOption', { key, value })"
                >
                  {{ value }}
                </li>
              </template>
            </common-dropdown>
          </template>
        </div>
      </div>
    </div>
    <div
      v-if="hasSizeGuide"
      class="btn-text cursor-pointer mt-2 text-blue-400"
      @click="uiManager().updateSizeGuideData(currentProduct, true)"
    >
      <span class="font-medium">
        {{ $t('Size guide') }}
      </span>
      <span>
        <i class="icon-sen-ruler ml-1" />
      </span>
    </div>
  </client-only>
</template>
