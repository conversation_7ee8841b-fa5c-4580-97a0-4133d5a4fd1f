<script lang="ts" setup>
const { showCountdown, endTime } = defineProps({
  showCountdown: {
    default: undefined,
    type: Number
  },
  endTime: {
    default: undefined,
    type: Number
  }
})

const {
  times,
  changeTime,
} = useCampaignCountdown(showCountdown, endTime)
</script>
<template>
  <div
    v-if="changeTime > 0"
    class="flex gap-1 flex-wrap font-medium text-primary"
  >
    <span>{{ $t('Sale ends in') }}</span>
    <span
      v-for="(time, index) in times"
      :key="index"
      :class="{hidden: time.time <= 0 && index === 0}"
    >
      {{ time.time }} {{ $t(time.text, { count: time.time }) }}
    </span>
  </div>
</template>
