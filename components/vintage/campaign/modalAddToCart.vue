<script lang="ts" setup>
import { useCampaignStore } from '~~/store/campaign'

defineProps({
  relatedCart: {
    default: undefined,
    type: Object as PropType<Array<Product>>
  }
})
const localePath = useLocalePath()
const isShowModal = ref(false)
const cartItem = ref<CartItem>()
const isUploadFile = computed(() => {
  return uiManager().isUploadFile
})

defineExpose({
  isShowModal,
  cartItem
})
</script>

<template>
  <common-modal
    :key="cartItem?.id || 'add_to_cart_default'"
    v-model="isShowModal"
    modal-id="modalAddToCart"
    :title="$t('Added to cart successfully!')"
    modal-class="w-[90%] md:max-w-[498px] lg:max-w-[798px] p-3 px-4"
  >
    <div class=" max-h-[80vh] overflow-y-auto">
      <div class="grid grid-cols-12">
        <div
          class="col-span-12 lg:col-span-7 py-2 flex items-center"
        >
          <common-image
            img-class="!w-22"
            :image="{
              path: cartItem?.thumb_url,
              type: 'list',
              color: cartItem?.options?.color
            }"
            :alt="cartItem?.campaign_title"
          />
          <div class="w-full max-w-[calc(100%-90px)] px-1 ">
            <nuxt-link :to="localePath(cartItem?.product_url as string)" class="w-full block">
              <h5 class="btn-text text-overflow-hidden font-medium" :title="cartItem?.campaign_title">
                {{ cartItem?.campaign_title }}
              </h5>
            </nuxt-link>
            <div>
              <span class="capitalize">{{ $t(cartItem?.product_name || '') }}</span>
            </div>
            <div class="uppercase">
              <span
                v-for="(key, optionIndex) in Object.keys(cartItem?.optionList || {}).filter(key => (cartItem?.optionList[key].length ?? 0) > 1)"
                :key="optionIndex"
              >
                <span v-if="optionIndex > 0">&nbsp;/&nbsp;</span>
                {{ $t(cartItem?.options && cartItem?.options[key] || '') }}
              </span>
            </div>
            <div class="mb-1 font-medium">
              <span>
                x{{ cartItem?.quantity }}
              </span>
              <span class="ml-3">{{ $formatPrice((cartItem?.quantity || 0) * (cartItem?.variantPrice || cartItem?.price || 0), cartItem?.currency_code) }}</span>
            </div>
          </div>
        </div>
        <div class="col-span-12 lg:col-span-5">
          <button
            id=""
            class="btn-border p-2 w-full"
            data-test-id="view-cart-button"
            style="--sp-border-radius-2: var(--sp-border-radius-1);"
            @click="useCampaignStore().$patch({ modalCampaignUrl: '' });useRouter().push(localePath('/cart'))"
          >
            {{ $t('View cart') }} ({{ totalQuantity() }})
          </button>
          <button
            class="btn-fill p-2 w-full mt-2"
            :disabled="!!isUploadFile"
            data-test-id="proceed-to-checkout-button"
            @click="useCampaignStore().$patch({ modalCampaignUrl: '' }); createOrder()"
          >
            {{ $t('Proceed to checkout') }}
          </button>
        </div>
      </div>

      <lazy-common-product-carousel
        v-if="relatedCart && relatedCart.length"
        :key="cartItem?.id || 'add_to_cart_default_carousel'"
        class="mt-10"
        title-class="text-3xl font-secondary font-light pl-2"
        title="Frequently bought together"
        :products="relatedCart"
        :force-static-grid="true"
        static-grid="grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4"
      >
        <template #default="{ product, index }">
          <vintage-common-product-item :product="product" :index="index" class="inline-block" />
        </template>
      </lazy-common-product-carousel>
    </div>
  </common-modal>
</template>
