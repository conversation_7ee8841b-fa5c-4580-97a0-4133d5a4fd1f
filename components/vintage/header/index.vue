<script lang="ts" setup>
import { useCartStore } from '~~/store/cart'

const localePath = useLocalePath()
const { headerMenu, logo_url: logoUrl, enable_search: enableSearch, name: storeName } = storeInfo()

const listCartItem = computed(() => useCartStore().listCartItem)

const totalPrice = computed(() => {
  return useCartStore().getTotalPrice
})

const {
  isSearching,
  searchKeyword,
  searchInputRef,
  activeMenu,
  showMenu,
  searchAction
} = useHeader()

// const { location: deliverToLocation, initDeliverTo } = useDeliverToLocation()

onMounted(() => {
  // initDeliverTo()
  // Hotkey to focus search input
  document.body.addEventListener('keydown', (e) => {
    if (!window.searchInput) {
      window.searchInput = document.querySelector('#headerSearchInput')
    }

    if (e.ctrlKey && e.key === '/') {
      window.searchInput?.focus()
    }
  })
})
</script>
<template>
  <!-- TODO: burger button "Categories" && menus in it -->
  <header
    id="PageHeader"
    class="bg-white transition-default z-2 <md:pb-2 border-b"
    :class="{'show-menu':showMenu}"
  >
    <nav class="z-1 bg-white relative container flex gap-8 items-center justify-center py-3 px-3 md:(justify-start)">
      <button
        id="headerShowMenu"
        type="button"
        aria-label="menu"
        class="md:hidden absolute left-2 btn-text text-2xl icon-sen-menu"
        :class="{
          hidden: isCheckoutPage
        }"
        @click="uiManager().toggleHeaderMenu()"
      />
      <!-- <nuxt-link
        id="headerPageFAQ"
        :to="localePath('/page/faq')"
        :title="$t('FAQ')"
        class="md:hidden absolute left-10 btn-text uppercase text-xl icon-sen-info-outline"
        :class="{
          hidden: isCheckoutPage
        }"
      /> -->
      <nuxt-link id="headerLinkLogo" :to="localePath('/')">
        <common-image
          v-if="logoUrl"
          img-class="h-6 md:h-8 rounded-none md:my-2"
          img-id="headerLogo"
          :image="{path: logoUrl, type: 'logo'}"
          :alt="storeName"
          aria-label="home"
        />
        <div v-else>
          <span class="text-2xl md:text-3xl font-bold">{{ storeInfo().name }}</span>
        </div>
      </nuxt-link>
      <template v-if="!isCheckoutPage">
        <div v-if="enableSearch === 0" class="<md:hidden w-full" />
        <client-only>
          <!-- <div v-if="deliverToLocation" class="hidden lg:flex gap-2 select-none deliver-to-location">
            <i class="icon-sen-delivery-truck-2 text-5xl" />
            <span class="whitespace-nowrap flex flex-col justify-center">{{ $t('Deliver to') }}<br><strong>{{ deliverToLocation }}</strong></span>
            <div class="flex items-center">
              <common-language-select
                :btn-class="''"
                :show-language="false"
                :use-svg="true"
                :show-dropdown-icon="false"
                :expandable-lang="true"
                :show-on-hover="false"
              />
            </div>
          </div> -->
          <form
            id="searchForm"
            class="<md:hidden flex flex-grow transition-all outline outline-2 border-radius-override bg-white"
            :class="{ 'hidden': (enableSearch === 0 || isCheckoutPage) }"
            @submit.prevent.stop="searchAction"
          >
            <input
              id="headerSearchInput"
              ref="searchInputRef"
              v-model="searchKeyword"
              :placeholder="`${$t('Find your favourite topics')} (Ctrl + /)`"
              type="search"
              class="w-full pl-4 pr-2 py-2 border-radius-override !rounded-r-none"
              @mouseover="searchInputRef?.focus()"
            >
            <button
              id="headerSearchButton"
              aria-label="search"
              type="submit"
              class="btn-text transition-all text-xl"
            >
              <div
                class="flex items-center justify-center py-1"
                :class="{
                  'animate-spin': isSearching
                }"
              >
                <span :class="[ (isSearching) ? 'icon-sen-loading' : 'icon-sen-search' ]" />
              </div>
            </button>
          </form>
          <div v-if="storeInfo().id === 1" class="relative px-2 py-1">
            <a
              id="headerSellerLogin"
              target="_blank"
              href="https://seller.senprints.com/auth/login"
              class="<md:hidden font-semibold uppercase text-[13px] btn-vintage"
            >
              {{ $t('Seller login') }}
            </a>
          </div>
          <div class="<md:(absolute right-2) min-w-[fit-content] flex gap-2">
            <common-language-select
              class="justify-self-end md:hidden center-flex"
              :btn-class="''"
              :show-language="false"
              :use-svg="true"
              :show-dropdown-icon="false"
              :expandable-lang="true"
              :show-on-hover="false"
            />
            <!-- <nuxt-link
              id="headerPageFAQ"
              :to="localePath('/page/faq')"
              :title="$t('FAQ')"
              class="btn-text uppercase text-xl relative <md:hidden btn-vintage h-10 w-10 rounded-full center-flex -mx-1"
            >
              <i class="icon-sen-info-outline" />
            </nuxt-link> -->
            <nuxt-link
              id="headerCartButton"
              :to="localePath('/cart')"
              :title="$t('View cart')"
              data-test-id="header-cart-button"
              class="btn-text uppercase text-xl font-semibold relative btn-vintage h-10 w-10 rounded-full center-flex -mx-1"
            >
              <i class="icon-vintage-cart" style="line-height: 1.3;" />
              <client-only>
                <span class="badge bg-red-600 mt-1 mr-2">{{ totalQuantity() }}</span>
              </client-only>
            </nuxt-link>
            <div v-if="listCartItem?.length && !useRoute().path.includes('cart')" id="mini-cart-overlay" class="absolute transition-all">
              <div class="mini-cart-header-point" />
              <div class="mini-cart-body border pb-2">
                <div class="px-3 py-4 mini-cart-header">
                  <nuxt-link :to="localePath('/cart?ref=mini-cart')" class="font-medium">
                    {{ $t('Cart') }} ({{ $t('item', { count: totalQuantity().value }) }})
                  </nuxt-link>
                </div>
                <hr>
                <div class="px-3 overflow-y-auto max-h-[510px] flex flex-col gap-2 py-2">
                  <vintage-cart-mini-cart-item
                    v-for="(cartItem, index) in listCartItem"
                    :key="cartItem.id"
                    class="flex gap-1"
                    :index="index"
                    :cart-item="cartItem"
                  />
                </div>
                <div class="px-3 font-medium mt-1">
                  {{ $t('Subtotal') }} <span class="float-right">{{ $formatPrice(totalPrice) }}</span>
                </div>
                <div class="px-3">
                  <span class="italic text-xs">{{ $t('Shipping & fees') }} <span class="lowercase">{{ $t('Calculated at checkout') }}</span></span>
                </div>
                <div class="flex px-3 mt-2">
                  <nuxt-link :to="localePath('/cart?ref=mini-cart')" class="btn-vintage-shadow mini-cart-btn w-full text-center py-2 text-white font-bold rounded-full relative">
                    {{ $t('View cart & check out') }}
                  </nuxt-link>
                </div>
              </div>
            </div>
          </div>
        </client-only>
      </template>
    </nav>
    <template v-if="!isCheckoutPage">
      <div
        class="md:hidden transform transition px-2 mt-1"
        :class="{
          'hidden': enableSearch === 0,
          '<md:translate-y-[-130%]': showMenu
        }"
      >
        <form
          id="searchForm"
          class="flex flex-grow transition-all outline outline-2 border-radius-override"
          :class="{ 'hidden': (enableSearch === 0 || isCheckoutPage) }"
          @submit.prevent.stop="searchAction"
        >
          <input
            id="headerSearchInput"
            ref="searchInputRef"
            v-model="searchKeyword"
            :placeholder="$t('Find your favourite topics')"
            type="search"
            :enterkeyhint="`${$t('Search')}`"
            class="w-full pl-4 pr-2 py-2 border-radius-override !rounded-r-none"
            @mouseover="searchInputRef?.focus()"
          >
          <button
            id="headerSearchButton"
            aria-label="search"
            type="submit"
            class="btn-text transition-all text-xl"
          >
            <div
              class="flex items-center justify-center py-1"
              :class="{
                'animate-spin': isSearching
              }"
            >
              <span :class="[ (isSearching) ? 'icon-sen-loading' : 'icon-sen-search' ]" />
            </div>
          </button>
        </form>
      </div>
      <common-collapse
        id="headerMenuMobile"
        as="ul"
        :when="showMenu"
        class="absolute container w-full px-3 bg-[#e8e8e8] max-h-[70vh] overflow-auto transition-default transform md:hidden z-2"
        :class="{ 'translate-y-[-42px]': showMenu}"
      >
        <default-header-menu-item-mobile
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
          class="md:mr-3"
          :active-header-menu="activeMenu === index"
          @update-active-header-menu="activeMenu = activeMenu === index ? false : index"
        />
        <a
          v-if="storeInfo().id === 1"
          id="headerSellerLogin"
          target="_blank"
          href="https://seller.senprints.com/auth/login"
          class="md:hidden btn-text text-lg mr-3"
        >
          {{ $t('Seller login') }}
        </a>
      </common-collapse>

      <ul
        id="headerMenu"
        class="absolute container w-full px-3 bg-[#e8e8e8] max-h-[70vh] <md:hidden transition-default transform hover:z-2 md:(relative flex pb-2 bg-white flex-wrap)"
      >
        <default-header-menu-item
          v-for="(menu, index) in headerMenu"
          :key="index"
          :menu="menu"
          class="mr-3"
        />
      </ul>
      <!-- <client-only>
        <div v-if="deliverToLocation" class="deliver-to-location-mobile bg-[#2f3237] gap-4 items-center py-2 px-4">
          <i class="icon-sen-delivery-truck-2 text-4xl text-white" />
          <span class="whitespace-nowrap text-white">{{ $t('Deliver to') }} <strong>{{ deliverToLocation }}</strong></span>
          <div class="w-full grid">
            <common-language-select
              class="justify-self-end"
              :btn-class="''"
              :show-language="false"
              :use-svg="true"
              :show-dropdown-icon="false"
              :expandable-lang="true"
              :show-on-hover="false"
            />
          </div>
        </div>
      </client-only> -->
    </template>
  </header>
</template>
<style scoped>
#headerSearchButton {
  margin: 0.25rem;
  color: #FFF;
  background: var(--color-primary);
  border-radius: 99999px;
  padding: 0.25rem 0.5rem;
}

#searchForm:hover #headerSearchButton,
#searchForm:focus-within #headerSearchButton,
#headerSearchButton:hover {
  margin: unset;
  padding: 0.5rem 0.75rem;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#mini-cart-overlay {
  --mini-cart-border-color: #0e0e0e2e;
  --mini-cart-header-color: #ccebff;
  display: none;
  visibility: hidden;
  opacity: 0;
  left: 100%;
  transform: translateX(-100%) translateY(40px);
}

.mini-cart-header-point {
  position: absolute;
  width: 12px;
  height: 12px;
  border-top: 1px solid var(--mini-cart-border-color);
  border-left: 1px solid var(--mini-cart-border-color);
  background: var(--mini-cart-header-color);
  left: 94%;
  top: 0px;
  transform: translateX(-95%) rotate(45deg);
  z-index: 2;
}

.mini-cart-body {
  --sp-border-radius-2: 0.8rem;
  background: #FFF;
  border: 1px solid var(--mini-cart-border-color);
  position: relative;
  top: 5px;
  left: 90%;
  transform: translateX(-85%);
  box-shadow: var(--clg-effect-sem-shadow-elevation-3,0px 1px 3px 0px #0000004d,0px 4px 8px 3px #00000026)
}

.mini-cart-header {
  background: var(--mini-cart-header-color);
  border-top-left-radius: var(--sp-border-radius-2);
  border-top-right-radius: var(--sp-border-radius-2);
}

.mini-cart-body {
  width: 353px;
}

#headerCartButton:hover + #mini-cart-overlay,
#mini-cart-overlay:hover {
  display: block;
  visibility: visible;
  opacity: 1;
}

#headerCartButton:has(+ #mini-cart-overlay:hover) {
  background: rgba(14,14,14,.0901960784);
  transform: scale(1);
}

.mini-cart-btn {
  --sp-btn-background: #222;
}
</style>
