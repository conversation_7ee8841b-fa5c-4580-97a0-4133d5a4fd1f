<script lang="ts" setup>
const localePath = useLocalePath()
const {
  filterValue,
  listingProduct,
  filterProduct,
  currentSortType,
  resetData,
  updatePrice,
} = useListing()

const sidebarVisibility = ref(false)

await resetData()
</script>

<template>
  <main id="listingPage" class="container flex flex-wrap">
    <div class="md:order-2 w-full lg:pl-2 pt-4">
      <div class="mb-2 mt-4 px-2 flex <md:(flex-col items-end) md:(justify-between items-center) gap-2">
        <div>
          <button v-if="storeInfo().isShowFilterBox1" class="text-sm btn-vintage-shadow py-2 px-4 font-semibold relative rounded-full" @click.stop.prevent="sidebarVisibility = true">
            <i class="icon-vintage-filters" /><span class="hidden md:inline-block">{{ $t('All Filters') }}</span>
          </button>
        </div>
        <div class="flex flex-wrap gap-2 items-center">
          <div v-if="storeInfo().enable_search" class="text-sm">
            {{ $t('result', { count: listingProduct.total || 0 }) }}
          </div>
          <div v-if="storeInfo().enable_search" class="hidden md:inline-block">
            <common-dropdown
              :btn-class="'rounded-full'"
            >
              <div class="text-sm btn-vintage-shadow py-2 px-4 rounded-full">
                {{ $t('Sort by') }}: <span class="font-medium">{{ $t(currentSortType?.text) }}</span>
              </div>
              <template #content>
                <nuxt-link
                  v-for="sortItem in filterValue.sortTypeList"
                  :key="sortItem.value"
                  sp-action="sort-listing"
                  :to="getFilterUrl('sort', sortItem.value)"
                  class="w-full min-w-70 py-2 px-2 block hover:bg-[#eaeaea] flex justify-between items-center"
                  :class="{'bg-[#0e0e0e0d]': filterValue.sort === sortItem.value}"
                >
                  {{ $t(sortItem.text) }}
                  <i v-if="filterValue.sort === sortItem.value" class="icon-vintage-check text-3xl" />
                </nuxt-link>
              </template>
            </common-dropdown>
          </div>
        </div>
      </div>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        <div
          v-for="(product, index) in listingProduct.products"
          :key="index"
        >
          <vintage-common-product-item
            :product="product"
            :color="filterValue.color?.name"
            :show-add-to-cart-icon="true"
          />
        </div>
      </div>
      <VintageCommonPagination v-if="listingProduct" class="flex justify-center mt-8" :paginator="listingProduct" />

      <div
        v-if="filterValue.pageType !== 'artist' && filterProduct.collection_group.length > 0 && !storeInfo().disable_related_collection"
        class="mt-10"
      >
        <h1 class="text-xl font-medium">
          {{ $t('Related collections') }}
        </h1>
        <div class="flex gap-2 flex-wrap mt-4">
          <nuxt-link
            v-for="(collection, index) in filterProduct.collection_group"
            :key="index"
            :to="`/collection/${collection.slug}`"
            class="text-md font-medium relative btn-vintage py-3 pl-4 pr-5 rounded-full whitespace-nowrap flex justify-center"
            style="--hover-scale: 1; --bg: #0e0e0e17;"
          >
            {{ collection.name }} <i class="icon-vintage-arrow-right text-2xl" />
          </nuxt-link>
        </div>
      </div>
    </div>
  </main>
  <common-sidebar
    :visible="sidebarVisibility"
    :close-button-icon="'icon-vintage-close'"
    :close-button-class="'btn-vintage'"
    :close-button-style="'--color-highlight: #e5e7eb;'"
    @update:visible="(visibility) => { sidebarVisibility = visibility }"
  >
    <div v-if="storeInfo().isShowFilterBox1" class="h-full w-full p-6 overflow-auto">
      <div class="flex justify-between items-center mb-4">
        <h1 class="text-3xl font-medium font-secondary">
          {{ $t('Filters') }}
        </h1>
        <nuxt-link
          :to="localePath($route.path)"
          class="text-sm btn-vintage-shadow py-1.5 px-4 font-semibold relative rounded-full z-99999"
        >
          {{ $t('Reset') }}
        </nuxt-link>
      </div>

      <div v-if="filterValue.filterCategories && filterValue.filterCategories.length">
        <div class="mb-8">
          <vintage-common-category-filter-item
            v-for="(category, index) in filterValue.filterCategories"
            :key="index"
            :category="category"
            :current-category="filterValue.category"
          />
        </div>
      </div>

      <div v-if="filterValue.filterTemplates && filterValue.filterTemplates.length">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Product') }}
        </p>
        <div class="mb-8">
          <vintage-common-template-filter-item
            v-for="(template, index) in filterValue.filterTemplates"
            :key="index"
            :template="template"
            :current-template="filterValue.template"
          />
        </div>
      </div>

      <div>
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Price') }}
        </p>
        <p class="text-center  text-555 mb-3">
          {{ $formatPrice(filterValue.priceFilter[0]) }} - {{ $formatPrice(filterValue.priceFilter[1]) }}
        </p>

        <common-listing-price-slider
          v-model="filterValue.priceFilter"
          :min="filterProduct.min_price"
          :max="filterProduct.max_price"
          @change="updatePrice"
        />
      </div>

      <div v-if="filterProduct && filterProduct.color_group && filterProduct.color_group.length > 0" class="filter-color mt-3">
        <p class="font-12x mb-3 pb-3 border-b uppercase <md:text-center">
          {{ $t('Color') }}
        </p>
        <div class="flex flex-wrap">
          <nuxt-link
            v-for="(color, index) in filterProduct.color_group"
            :key="index"
            :to="getFilterUrl('color', filterValue.color?.name === color ? '' : color)"
            sp-action="change_color"
          >
            <common-color-item
              :color="color"
              size="xl"
              class="hover:shadow-color-active"
              :active="filterValue.color?.name === color"
            />
          </nuxt-link>
        </div>
      </div>
    </div>
  </common-sidebar>
</template>
