<script lang="ts" setup>
import { useCampaignStore } from '~~/store/campaign'
import {handleDevtoolDetector} from "~/composables/common";

const { isShowCookieConsent, confirmCookieConsent } = useCookieConsent()
const campaignStore = useCampaignStore()
const campaignSlug = computed(() => {
  return campaignStore.modalCampaignUrl
})

onMounted(() => {
  const modalCampaignUrl = useRoute().query.campaign as string
  if (modalCampaignUrl) {
    campaignStore.$patch({ modalCampaignUrl })
  }
  handleDevtoolDetector()
})
</script>

<template>
  <div>
    <default-header-checkout v-if="isCheckoutPage" />
    <vintage-header v-else />
    <slot />
    <default-footer-checkout v-if="isCheckoutPage" />
    <vintage-footer v-else />

    <lazy-cookie-consent v-if="isShowCookieConsent" @confirm="confirmCookieConsent" />
    <lazy-common-modal
      :key="campaignSlug"
      modal-class="<md:(w-full h-full) <lg:(w-[80vw]) lg:w-[50vw] max-h-100vh md:max-h-[80vh]"
      :model-value="!!campaignSlug"
      @close-modal="campaignStore.$patch({modalCampaignUrl: ''})"
    >
      <lazy-vintage-campaign :key="campaignSlug" :campaign-slug="campaignSlug" :is-modal="true" />
    </lazy-common-modal>
  </div>
</template>
<style>
@import url('~/assets/themes/vintage/index.css');
@import url('~/assets/icons/vintage/style.css');
</style>
