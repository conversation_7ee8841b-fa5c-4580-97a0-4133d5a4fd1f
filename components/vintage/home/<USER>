<script lang="ts" setup>
const localePath = useLocalePath()
const $router = useRouter()
const { banner, index } = defineProps({
  banner: {
    default: ():Banner => {
      return {}
    },
    type: Object
  },
  index: {
    default: null,
    type: Number || null
  }
})

function goToPage () {
  $router.push(localePath(banner.banner_link || '/'))
}
</script>

<template>
  <div v-click-not-drag="goToPage" class="select-none max-w-1/4 lg:max-w-1/8 vintage_banner_item">
    <nuxt-link :to="localePath((banner.banner_link !== '#') ? banner.banner_link : '/')">
      <common-image
        :alt="$t(banner.banner_text) || storeInfo().name"
        :image="{
          path:banner.banner_url,
          type: 'full',
        }"
        :fetchpriority="(index === 0) ? 'high' : ''"
        mobile-type="banner_mobile"
        img-class="rounded-full transition-all"
      />
      <h3 v-if="banner.banner_text" class="font-medium mt-4">
        {{ banner.banner_text }}
      </h3>
    </nuxt-link>
  </div>
</template>
<style>
.vintage_banner_item a:hover img,
.vintage_banner_item a:focus-within img {
  transform: scale(1.1);
}

.vintage_banner_item img {
  aspect-ratio: 1/1;
  object-fit: cover;
}

.vintage_banner_item :hover h3,
.vintage_banner_item a:focus-within h3{
  border-bottom-color: #000;
}

.vintage_banner_item h3 {
  border-bottom: 2px solid transparent;
  text-align: center;
}
</style>
