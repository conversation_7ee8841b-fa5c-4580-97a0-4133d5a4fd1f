<script lang="ts" setup>
const { listProductData } = await useHomePage()
</script>

<template>
  <main id="homePage">
    <!-- <default-home-banner-carousel
      v-if="storeInfo().banners.length"
      class="container"
    /> -->
    <div v-if="storeInfo().collection_banners.length" class="container mt-8 flex justify-center gap-12 flex-wrap">
      <vintage-home-banner-item
        v-for="(banner, index) in storeInfo().collection_banners"
        :key="index"
        :banner="banner"
        :index="index"
      />
    </div>
    <template v-for="(productsData, index) in listProductData">
      <common-product-carousel
        v-if="productsData?.products?.length"
        :key="index"
        class="container mt-10"
        title-class="text-2xl font-semibold"
        :title="productsData.name"
        :products="productsData.products"
        :force-static-grid="true"
        :static-grid="'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 pt-4'"
      >
        <template #default="{ product, index: itemIndex }">
          <vintage-common-product-item :product="product" :index="itemIndex" class="inline-block" :product-img-only="true" />
        </template>
      </common-product-carousel>
    </template>
  </main>
</template>
