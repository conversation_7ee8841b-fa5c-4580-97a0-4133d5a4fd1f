<script lang="ts" setup>
import type { PropType, Ref } from 'vue'
import { VueTelInput } from 'vue-tel-input'
import { useGeneralSettings } from '~/store/generalSettings'

const { userInfo, isPhoneNumberRequired } = defineProps({
  userInfo: {
    default: () => {},
    type: Object as PropType<Partial<UserInfo>>
  },
  isPhoneNumberRequired: {
    default: false,
    type: Boolean
  },
  isCountrySelectHidden: {
    default: false,
    type: Boolean
  },
  isModal: {
    default: false,
    type: Boolean
  }
})

const {
  advancedEmailChecking,
  computedEmailValidateText,

  isShowCountrySelect,
  filterCountryText,
  countryDropdown,
  phoneInput,
  warningAddress,
  filterCountryArray,
  isShowValidate,
  validate,

  currentCountry,
  isCountryUsingAlphanumericZipcode,
  computedStateSelect,
  countryState,

  selectCountry,
  checkPhone,
  updateUserInfo,
  onChangeEmailAddress
} = useCheckoutForm(userInfo)
const generalSettings = useGeneralSettings()

const countryFilter = ref()
const emailRef = ref()
const nameRef = ref()
const addressRef = ref()
const cityRef = ref()
const stateRef = ref()
const zipcodeRef = ref()
const phoneRef = ref()
const houseNumberRef = ref()
const inputRef: { [key: string]: Ref<any> } = {
  emailRef,
  nameRef,
  addressRef,
  cityRef,
  stateRef,
  zipcodeRef,
  phoneRef,
  houseNumberRef
}
function checkUserInfo() {
  isShowValidate.value = true

  const checkKeyArray = ['email', 'name', 'address', 'city', 'zipcode', 'phone']
  if (isHasField('state') && isRequired('state')) {
    checkKeyArray.push('state')
  }
  if (isRequired('houseNumber', false)) {
    checkKeyArray.push('house_number')
  }
  let check = true
  checkKeyArray.every((key) => {
    if (validate[key]) {
      check = false
      key = key === 'house_number' ? 'houseNumber' : key
      inputRef[`${key}Ref` as string]?.value.focus()
      return false
    }
    updateUserInfo(userInfo)
    return true
  })

  return check
}

function isFormValid() {
  const checkKeyArray = ['email', 'name', 'address', 'city', 'zipcode', 'phone']
  if (isHasField('state') && isRequired('state')) {
    checkKeyArray.push('state')
  }
  let check = true
  checkKeyArray.every((key) => {
    if (validate[key]) {
      check = false
      return false
    }
    return true
  })
  return check
}

const computedPhoneNumberRequired = computed(() => {
  if (isPhoneNumberRequired) {
    return true
  }

  return (userInfo.country !== 'US')
})
const {
  isHasField,
  isRequired,
  getClass,

  addressLabel1,
  addressLabel2,
  stateLabel,
  cityLabel,
  zipcodeLabel,
  phoneLabel,
  houseNumberLabel,
  mailboxLabel
} = useCheckoutDynamicForm(userInfo, computedPhoneNumberRequired.value)
onMounted(() => {
  if (userInfo.phone) {
    phoneInput.value = userInfo.phone.replace(/^\+/g, '')
  }

  if (userInfo.email) {
    nameRef.value.focus()
  }
  else {
    emailRef.value.focus()
  }
})

watch(currentCountry, (country) => {
  if (country?.code) {
    phoneRef.value.choose(country.code)
  }
})

function checkInput(string: string, { valid, number }: any) {
  checkPhone(string, { valid, number }, { isPhoneNumberRequired: computedPhoneNumberRequired.value })
}
defineExpose({
  checkUserInfo,
  isFormValid,
  advancedEmailChecking,
  emailRef
})
</script>

<template>
  <form id="CheckoutForm" action="#" @submit.prevent.stop>
    <h6 class="mt-3 font-medium">
      {{ $t('Contact info') }}
    </h6>
    <default-common-input
      id="email"
      ref="emailRef"
      :label="`${$t('Email address')} *`"
      :model-value="userInfo?.email"
      :state="computedEmailValidateText.level"
      :message="computedEmailValidateText.message"
      :input-type="2"
      input-label-style="font-medium"
      type="email"
      pattern="^[\S]+@[\S]+\.[A-Za-z]{2,6}$"
      class="mt-3"
      @update:model-value="updateUserInfo({ email: $event })"
      @change="onChangeEmailAddress"
      @click:feedback="() => { advancedEmailChecking.hasMistyped && (updateUserInfo({ email: advancedEmailChecking.mistypedAutocorrect }), onChangeEmailAddress()) }"
    />
    <div
      id="EmailConfirmCheckbox"
      class="cursor-pointer email-checkbox flex items-center mt-2 mb-6 md:mb-4"
      @click="updateUserInfo({ subscribed: userInfo.subscribed ? 0 : 1 })"
    >
      <i class="mr-1" :class="userInfo.subscribed ? 'icon-sen-checkbox-marked-outline' : 'icon-sen-checkbox-blank-outline'" />
      <span class="text-sm">{{ $t('Email me with news and offers') }}</span>
    </div>

    <slot name="pre_shipping_info" />

    <h6 class="mt-3 font-medium">
      {{ $t('Shipping info') }}
    </h6>

    <template v-if="!isCountrySelectHidden">
      <common-dropdown
        v-if="!currentCountry || isShowCountrySelect"
        ref="countryDropdown"
        dropdown-id="countryDropdown"
        class="mt-3"
        btn-class="w-full text-left border p-3"
        dropdown-class="max-w-[80vw] w-full pt-12"
        :close-on-click="false"
        @shown="countryFilter.focus()"
      >
        <span class="inline-block vti__flag" :class="currentCountry?.code.toLowerCase()" />
        <span class="ml-2">
          {{ currentCountry?.name }}
        </span>
        <template #content>
          <default-common-input
            id="countryFilter"
            ref="countryFilter"
            v-model="filterCountryText"
            :label="$t('Find your country')"
            class="!absolute top-0 w-full px-5 py-1"
          />
          <template v-if="filterCountryArray?.length">
            <div
              v-for="(country, index) in filterCountryArray"
              :key="index"
              class="btn-text py-1 px-3"
              :class="{
                'bg-gray-100 cursor-not-allowed hover:text-black': generalSettings.getCountryDisabledCheckout.includes(country.code),
                'bg-primary text-contrast': country.code === userInfo?.country
              }"
              tabindex="0"
              @click="selectCountry(country.code)"
              @keyup.space="selectCountry(country.code)"
              @keyup.enter="selectCountry(country.code)"
            >
              <span class="vti__flag inline-block" :class="country.code.toLowerCase()" />
              <span class="ml-2">
                {{ country.name }}
              </span>
            </div>
          </template>
          <div v-else class="text-center">
            {{ $t('Sorry, no matching country') }}
          </div>
        </template>
      </common-dropdown>
      <div v-else class="mt-3 flex items-center">
        <span>
          {{ $t('Country') }}:
        </span>
        <span :class="`vti__flag ${currentCountry?.code.toLowerCase()}`" />
        <span>
          {{ (currentCountry && currentCountry.name) || '' }}
        </span>
        <a class="btn-text pl-1" href="#" data-test-id="checkout-change-country-button" @click.prevent="isShowCountrySelect = true">({{ $t('Change') }})</a>
      </div>
    </template>

    <default-common-input
      id="name"
      ref="nameRef"
      class="mt-3"
      :input-type="2"
      input-label-style="font-medium"
      :label="`${$t('Full Name')} *`"
      :model-value="userInfo?.name"
      :state="isShowValidate ? !validate.name : undefined"
      :message="validate.name"
      @update:model-value="updateUserInfo({ name: $event })"
    />

    <div class="grid grid-cols-12 mt-3 gap-y-3">
      <div :class="getClass('address_1')">
        <default-common-input
          id="address"
          ref="addressRef"
          class="w-full"
          :label="addressLabel1"
          :model-value="userInfo?.address"
          :state="isShowValidate ? !validate.address : undefined"
          :message="validate.address"
          :input-type="2"
          input-label-style="font-medium"
          @update:model-value="updateUserInfo({ address: $event })"
        />

        <default-common-input
          v-if="isHasField('address_2')"
          id="address_2"
          autocomplete="address-line2"
          class="mt-3"
          :label="addressLabel2"
          :model-value="userInfo?.address_2"
          :state="((warningAddress && !userInfo.address_2) ? 'warning' : undefined)"
          :message="$t('Please add your unit/room number if applicable')"
          :input-type="2"
          input-label-style="font-medium"
          @update:model-value="updateUserInfo({ address_2: $event })"
        />
      </div>
      <default-common-input
        v-if="isHasField('city')"
        id="city"
        ref="cityRef"
        :class="getClass('city')"
        :label="cityLabel"
        :model-value="userInfo?.city"
        autocomplete="address-level2"
        :state="isShowValidate ? !validate.city : undefined"
        :message="validate.city"
        :input-type="2"
        input-label-style="font-medium"
        @update:model-value="updateUserInfo({ city: $event })"
      />

      <template v-if="isHasField('state')">
        <template v-if="countryState?.length">
          <div class="relative" :class="getClass('state')">
            <label
              for="state"
              class="label text-overflow-hidden w-full capitalize z-1 font-medium"
            >{{ stateLabel }}</label>
            <div class="relative">
              <select
                id="state"
                ref="stateRef"
                v-model="computedStateSelect"
                name="state"
                autocomplete="address-level1"
                class="h-10 w-full bg-transparent border focus:outline-none focus:border-primary focus:shadow-custom2 mt-1"
                :class="[
                  (isShowValidate && validate.state) ? '!border-red-500' : (isShowValidate && !validate.state) ? '!border-green-500' : '',
                  (userInfo.state) ? 'px-2' : ''
                ]"
              >
                <option
                  v-for="(state, index) in countryState"
                  :key="index"
                  :value="state.value"
                >
                  {{ state.text }}
                </option>
              </select>
              <span
                v-if="isShowValidate && validate.state"
                class="mt-1 text-sm"
                :class="[(isShowValidate && validate.state) ? '!text-red-500' : '']"
              >{{ $t(validate.state) }}</span>
            </div>
          </div>
        </template>
        <template v-else>
          <default-common-input
            id="state"
            ref="stateRef"
            autocomplete="address-level1"
            :class="getClass('state')"
            :label="stateLabel"
            :model-value="userInfo?.state"
            :state="isShowValidate ? !validate.state : undefined"
            :message="validate.state"
            :input-type="2"
            input-label-style="font-medium"
            @update:model-value="updateUserInfo({ state: $event })"
          />
        </template>
      </template>

      <default-common-input
        id="zipcode"
        ref="zipcodeRef"
        :class="getClass('postCode')"
        autocomplete="postal-code"
        :label="zipcodeLabel"
        :model-value="userInfo?.zipcode || userInfo?.postcode"
        :state="isShowValidate ? !validate.zipcode : undefined"
        :message="validate.zipcode"
        :input-type="2"
        input-label-style="font-medium"
        :type="(isCountryUsingAlphanumericZipcode) ? '' : 'number'"
        @update:model-value="updateUserInfo({ zipcode: $event })"
      />

      <default-common-input
        v-if="isHasField('houseNumber', false)"
        id="houseNumber"
        ref="houseNumberRef"
        :class="getClass('houseNumber')"
        autocomplete="house-number"
        :label="houseNumberLabel"
        :model-value="userInfo?.house_number"
        :state="isShowValidate ? !validate.house_number : undefined"
        :message="validate.house_number"
        :input-type="2"
        input-label-style="font-medium"
        @update:model-value="updateUserInfo({ house_number: $event })"
      />
      <default-common-input
        v-if="isHasField('mailboxNumber', false)"
        id="mailbox"
        :class="getClass('mailboxNumber')"
        autocomplete="mailbox"
        :label="mailboxLabel"
        :model-value="userInfo?.mailbox_number"
        :input-type="2"
        input-label-style="font-medium"
        @update:model-value="updateUserInfo({ mailbox_number: $event })"
      />
    </div>
    <div v-if="isHasField('deliveryNote', false)" class="relative">
      <h6 v-if="!isModal" class="mb-0 mt-3">
        {{ $t('Add delivery instructions (optional)') }}
      </h6>
      <textarea
        :model-value="userInfo.note"
        class="border mt-2 !border-gray-200 py-1 px-2 w-full resize-none overflow-y-scroll focus:(border-primary shadow-custom2) focus-visible:outline-none"
        rows="3"
        maxlength="255"
        :placeholder="$t('Provide details such as building description, a nearby landmark, or other navigation instructions')"
        @update:modelValue="updateUserInfo({ note: $event })"
      />
    </div>
    <VueTelInput
      ref="phoneRef"
      v-model="phoneInput"
      :default-country="userInfo?.country"
      class="mt-3 h-12 border focus-within:(shadow-custom2 border-primary-focus)"
      :class="{
        '!border-green-500': isShowValidate && !validate.phone && phoneInput,
        '!border-red-500': isShowValidate && validate.phone,
        '!border-gray-200': !isShowValidate
      }"
      :dropdown-options="{
        showDialCodeInSelection: true,
        showDialCodeInList: true,
        showFlags: true,
        showSearchBox: true,
        width: '400px'
      }"
      :input-options="{
        id: 'phone',
        name: 'phone',
        autocomplete: 'phone',
        styleClasses: 'placeholder-gray-500 rounded-r-xl',
        placeholder: phoneLabel,
        required: computedPhoneNumberRequired,
        type: 'tel'
      }"
      @on-input="checkInput"
    >
      <template #icon-right>
        <div class="phone-tooltip relative z-1">
          <i class="icon-sen-info-outline absolute right-4 position-center-y center-flex" />
          <div class="tooltip-content">
            <p class="mb-1 text-white">
              {{ $t('In case we need to contact') }} {{ $t('you about your order') }}
            </p>
          </div>
        </div>
      </template>
    </VueTelInput>
    <span
      v-if="isShowValidate && validate.phone"
      class="mt-1 text-sm text-red-500"
    >{{ $t(validate.phone) }}</span>
  </form>
</template>

<style>
.vue-tel-input {
  border-radius: 0;
}

body.classic .vue-tel-input {
  border-radius: 0.3rem;
}

/* .vue-tel-input:focus-within {
  border-color: rgba(var(--color-primary), 0.3)!important;
} */

.tooltip-content {
  display: none;
  position: absolute;
  background-color: rgb(66, 66, 66);
  width: -moz-max-content;
  width: max-content;
  border-radius: 10px;
  padding: 0.5rem;
  color: white;
  transform: translate(-56%, -100%);
}

.tooltip-content::after {
  content: '';
  position: absolute;
  bottom: -9px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid rgb(66, 66, 66);
  left: 50%;
  transform: translateX(-50%);
}

.phone-tooltip:hover .tooltip-content {
  display: block;
}

@media (max-width: 767.9px) {
  .tooltip-content {
    transform: translate(-100%, -100%);
  }
  .tooltip-content::after {
    left: 91%;
    transform: translateX(-0%);
  }
}
</style>
