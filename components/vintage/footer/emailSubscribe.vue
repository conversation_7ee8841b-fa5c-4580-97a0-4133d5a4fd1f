<script lang="ts" setup>
const { emailNewsletter, subscribeEmail } = useFooter()
</script>
<template>
  <div class="bg-[#ccebff] text-black">
    <div class="container py-8 gap-4">
      <h3 class="font-medium text-center">
        {{ $t('Yes! Send me exclusive offers, unique gift ideas, and personalized tips for shopping and selling on') }} {{ storeInfo().name }}
      </h3>
      <div class="w-full">
        <form class="text-[1rem] transition-all flex justify-center" @submit.prevent="subscribeEmail">
          <input
            id="newsletter_email"
            v-model="emailNewsletter"
            autocomplete="email"
            type="email"
            name="newsletter_email"
            class="min-w-[50%] lg:min-w-[30%]"
            :placeholder="$t('Enter your email')"
          >
          <button type="submit" class="transition-all">
            {{ $t('Subscribe') }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
form {
  --border-color: #0e0e0e91;
  padding-top: 1.5rem;

  input, button {
    padding: 9px 12px;
    height: 48px;
    padding: 0 18px;
  }

  input {
    border-radius: var(--sp-border-radius-1) 0 0 var(--sp-border-radius-1);
    border: 1px solid var(--border-color);
    border-right: 0;
  }

  button {
    background: #FFF;
    color: #000;
    font-weight: bold;
    white-space: nowrap;
    text-transform: capitalize;
    border: 1px solid var(--border-color);
    border-radius: 0 var(--sp-border-radius-1) var(--sp-border-radius-1) 0;
    border-left: 0;
  }
}

form:has(*:hover),
form:focus-within {
  --border-color: #000;

  button {
    background: #222;
    color: #FFF;
  }
}
</style>
