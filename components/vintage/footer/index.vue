<script lang="ts" setup>
import { useGeneralSettings } from '~/store/generalSettings'
import { useUserSession } from '~/store/userSession'

const localePath = useLocalePath()

const {
  socialsLink,
  storeContact,
  logo_url: logoUrl,
  name: storeName,
} = storeInfo()

const {
  contactPhoneList,
  currentContactPhoneIndex,
  enableContactForm,
  canHasExtraInfo,
  getSocialLink,
  encode,
  decode,
  copyrightText
} = useFooter()

const currentCountry = computed(() => {
  return useGeneralSettings().countries.find((item: any) => item.code.includes(useUserSession().visitInfo.country))
})

const footerSelectorOpts = {
  btnClass: '',
  showOnHover: false,
  showDropdownIcon: false,
  showFlagInButton: false,
}
</script>
<template>
  <footer id="footer" class="text-white mt-5 bg-[#2638c0]">
    <!-- Does NOT DISPLAY this el if current page is "campaign" or "checkout" or "category" (search/listing) -->
    <vintage-footer-email-subscribe v-if="!storeInfo().disable_promotion && !(isCampaignPage || isCategoryPage || isCheckoutPage || isCartPage)" />

    <common-html-head-tag :for-position="'footer_start'" />

    <div v-if="!isCartPage" class="container flex flex-wrap text-center lg:text-left pb-10">
      <div class="w-full lg:w-[40%] lg:order-2 flex flex-wrap text-center lg:text-left mt-10">
        <div class="pt-3 hidden">
          <nuxt-link exact :to="localePath('/bot')">
            {{ $t('Bot') }}
          </nuxt-link>
        </div>
        <div v-for="menuList in compoundFooter" :key="menuList.title" class="w-full md:w-1/2 <md:my-5">
          <p class="uppercase font-semibold">
            {{ $t(menuList.title) }}
          </p>
          <div
            v-for="(item, index) in menuList.menuData"
            :key="index"
            class="pt-3 capitalize"
          >
            <nuxt-link exact :to="localePath(item.url)" class="btn-text">
              {{ $t(item.name) }}
            </nuxt-link>
          </div>
        </div>
      </div>
      <div class="w-full md:w-1/2 lg:w-[30%] lg:order-1 text-sm mt-10">
        <div class="flex <lg:justify-center my-5">
          <nuxt-link :to="localePath('/')">
            <common-image
              v-if="logoUrl"
              :image="{
                path: logoUrl,
                type: 'logo',
              }"
              img-id="footerLogo"
              :alt="storeName"
              img-class="h-10 -z-1 rounded-none"
            />
            <div v-else>
              <span class="text-2xl md:text-3xl font-bold">{{ storeInfo().name }}</span>
            </div>
          </nuxt-link>
        </div>
        <p v-if="canHasExtraInfo && storeContact.phone" class="font-medium mt-3">
          {{ storeName }}
        </p>
        <p v-if="canHasExtraInfo && storeContact.email_info">
          <span>{{ $t('Email') }}: </span>
          <span class="ml-2">{{ storeContact.email }}</span>
        </p>
        <p v-if="storeContact.address" class="mt-2">
          <span>{{ $t('Address') }}: </span>
          <span class="ml-2">{{ storeContact.address }}</span>
        </p>
        <div class="flex <lg:justify-center items-center">
          <span>{{ $t('Phone number') }}: </span>
          <span v-if="canHasExtraInfo && storeContact.phone" class="ml-2">
            {{ decode(encode(storeContact.phone)) }}
          </span>
          <common-dropdown
            v-else
            class="ml-0.5"
            dropdown-id="contactPhoneDropdown"
            btn-class="px-3 py-1"
          >
            <div class="flex items-center">
              <img
                v-for="(flag, flagIndex) in contactPhoneList[currentContactPhoneIndex].flags"
                :key="flagIndex"
                :src="`${cdnURL}${flag}`"
                alt="flag"
                class="mr-1 h-4"
                loading="lazy"
              >
              <span>{{ decode(encode(contactPhoneList[currentContactPhoneIndex].phone)) }}</span>
            </div>
            <template #content>
              <li
                v-for="(contactPhone, index) in contactPhoneList"
                :key="index"
                class="btn-text flex items-center px-4 min-w-full w-max py-1 text-black"
                :class="{'bg-primary !text-contrast': index === currentContactPhoneIndex}"
                @click="currentContactPhoneIndex = index"
              >
                <img
                  v-for="(flag, flagIndex) in contactPhone.flags"
                  :key="flagIndex"
                  :src="`${cdnURL}${flag}`"
                  alt="flag"
                  class="mr-1 h-4"
                  loading="lazy"
                >
                <span>{{ decode(encode(contactPhone.phone)) }}</span>
              </li>
            </template>
          </common-dropdown>
        </div>
        <div v-if="enableContactForm">
          <span>
            {{ $t('Need support?') }}
            <nuxt-link class="hover:underline" :to="localePath('/page/contact-us')">{{ $t('Submit a ticket') }}</nuxt-link>
          </span>
        </div>
      </div>
      <div class="w-full md:w-1/2 lg:w-[30%] lg:order-3 mt-10">
        <common-payment-gateway-accept />

        <!-- social -->
        <template v-if="socialsLink">
          <p
            v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.google || socialsLink.youtube"
            class="font-semibold mt-5"
          >
            {{ $t('Follow us') }}
          </p>
          <div v-if="socialsLink.facebook || socialsLink.instagram || socialsLink.skype || socialsLink.pinterest || socialsLink.twitter || socialsLink.google || socialsLink.youtube" class="contact-icon my-3 text-4xl">
            <a
              v-if="socialsLink.facebook"
              :href="getSocialLink(socialsLink.facebook)"
              target="_blank"
              class="mr-2"
              aria-label="facebook"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-facebook" /></span>
            </a>
            <a
              v-if="socialsLink.instagram"
              :href="getSocialLink(socialsLink.instagram)"
              target="_blank"
              class="mr-2"
              aria-label="instagram"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-instagram" /></span>
            </a>
            <a
              v-if="socialsLink.skype"
              :href="getSocialLink(socialsLink.skype)"
              target="_blank"
              class="mr-2"
              aria-label="skype"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-skype-business" /></span>
            </a>
            <a
              v-if="socialsLink.pinterest"
              :href="getSocialLink(socialsLink.pinterest)"
              target="_blank"
              class="mr-2"
              aria-label="pinterest"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-pinterest" /></span>
            </a>
            <a
              v-if="socialsLink.twitter"
              :href="getSocialLink(socialsLink.twitter)"
              target="_blank"
              class="mr-2"
              aria-label="twitter"
              rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-twitter" /></span>
            </a>
            <a
                v-if="socialsLink.google"
                :href="getSocialLink(socialsLink.google)"
                target="_blank"
                class="mr-2"
                aria-label="google"
                rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-google" /></span>
            </a>
            <a
                v-if="socialsLink.youtube"
                :href="getSocialLink(socialsLink.youtube)"
                target="_blank"
                class="mr-2"
                aria-label="youtube"
                rel="noopener noreferrer nofollow"
            >
              <span><i class="icon-sen-youtube" /></span>
            </a>
          </div>
        </template>
      </div>
    </div>

    <common-html-head-tag :for-position="'footer_middle'" />

    <div
      id="copyright"
      :key="isCartPage"
      class="py-4 bg-[#222]"
    >
      <div class="container px-3 flex flex-wrap md:justify-between items-center justify-center gap-4">
        <div class="flex flex-wrap items-center gap-2 justify-center <md:(w-full order-0)">
          <client-only>
            <i :class="`vti__flag ${currentCountry?.code.toLowerCase()}`" />
            <span>{{ currentCountry?.name }}</span>
          </client-only>
          <span>|</span>
          <common-language-select v-bind="{ ...footerSelectorOpts }" />
          <span>|</span>
          <common-currency-select v-if="!isOrderPage" v-bind="footerSelectorOpts" />
        </div>
        <small class="<md:(order-1 w-full) text-center" v-html="copyrightText" />
      </div>
    </div>
    <common-html-head-tag :for-position="'footer_end'" />
  </footer>
</template>
