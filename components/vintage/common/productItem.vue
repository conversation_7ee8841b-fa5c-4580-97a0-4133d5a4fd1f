<script lang="ts" setup>
import { type PropType } from 'vue'
import { useCampaignStore } from '~~/store/campaign'
const { $formatPrice } = useNuxtApp()

const campaignStore = useCampaignStore()
const localePath = useLocalePath()
const { $i18n } = useNuxtApp()
const $router = useRouter()
const props = defineProps({
  product: {
    default: undefined,
    type: Object as PropType<Product>
  },
  color: {
    default: undefined,
    type: String
  },
  productImgOnly: {
    default: false,
    type: Boolean
  },
  showAddToCart: {
    default: true,
    type: <PERSON>olean
  },
  showAddToCartIcon: {
    default: false,
    type: Boolean,
  },
  index: {
    default: undefined,
    type: Number
  }
})
const loadingGetCampaign = ref(false)
const productTitle = computed(() => props.product?.campaign_name ? `${props.product.campaign_name} - ${props.product.name}` : props.product?.name)
const campaignUrl = computed(() => {
  let campaignUrl = `/${props.product?.slug}`
  if (props.product?.product_type === 'product' && props.product.campaign_id) {
    campaignUrl += `?product=${stringHelperToSlug(props.product?.name)}`
    if (props.color) {
      campaignUrl += `&color=${props.color.replace(/ /g, '-')}`
    }
  }
  return campaignUrl
})

const productPrice = computed(() => useTestPrice().getPrice(productInfo()))
const productOldPrice = computed(() => useTestPrice().getOldPrice(productInfo()))

function goToPage () {
  $router.push(localePath(campaignUrl.value || '/'))
}

async function openCampaignModal ($event:Event) {
  $event.stopImmediatePropagation()
  loadingGetCampaign.value = true
  const campaignData = await campaignStore.getCampaignBySlug(props.product?.slug as string)
  loadingGetCampaign.value = false

  if (campaignData?.id && campaignData.products?.length) {
    campaignStore.$patch({ modalCampaignUrl: props.product?.slug })
  } else {
    uiManager().createPopup($i18n.t('Missing campaign data'))
  }
}

function productInfo () {
  if (props.product) {
    const productInfo = props.product
    productInfo.currentOptions = props.product.variant_options
    productInfo.variantsList = props.product.default_variants
    return productInfo
  }
  return undefined
}
</script>

<template>
  <div
    class="relative product-list-item-vintage select-none overflow-hidden w-full overflow-visible p-3"
    :class="{
      'hover:shadow-custom2 cursor-pointer rounded-[8px]': !showAddToCart || productImgOnly
    }"
    data-test-id="product-list-item"
  >
    <div v-click-not-drag="goToPage">
      <nuxt-link :to="localePath(campaignUrl)" class="relative">
        <div
          class="w-full pt-[125%]"
        >
          <common-image
            :image="{
              path: product?.thumb_url,
              type:'list',
              color
            }"
            img-class="absolute top-0 left-0 border"
            :alt="product?.name"
            :title="productTitle"
            :fetchpriority="(index === 0) ? 'high' : ''"
          />
        </div>
      </nuxt-link>
    </div>
    <template v-if="!productImgOnly">
      <div class="mb-2">
        <h5 class="btn-text text-overflow-hidden" :title="productTitle">
          <nuxt-link :to="localePath(campaignUrl)">
            {{ productTitle }}
          </nuxt-link>
        </h5>
        <h6 class="block flex flex-wrap justify-start gap-x-2 items-start">
          <span class="font-medium text-lg">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
          <del v-if="storeInfo().store_type !== 'google_ads' && productOldPrice > productPrice" class="old-price font-light text-sm mt-0.5">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
        </h6>
      </div>

      <button v-if="storeInfo().enable_add_to_cart && showAddToCart" class="text-sm btn-vintage-shadow py-2 px-4 font-semibold relative rounded-full" data-test-id="product-list-item-btn" @click.stop.prevent="openCampaignModal">
        <common-loading-dot v-if="loadingGetCampaign" variant="bg-black" class="z-2" />
        <span v-else class="flex gap-1 items-center"><i v-if="showAddToCartIcon" class="icon-vintage-plus" />{{ $t('Add to cart') }}  </span>
      </button>
    </template>
    <template v-else>
      <div class="border rounded-full absolute bottom-0 left-0 ml-5 mb-5 gap-2 flex bg-white px-2">
        <span class="text-md">{{ $formatPrice(productPrice, product?.currency_code) }}</span>
        <del v-if="storeInfo().store_type !== 'google_ads' && productOldPrice > productPrice" class="old-price font-light text-sm mt-0.5">{{ $formatPrice(productOldPrice, product?.currency_code) }}</del>
      </div>
    </template>
    <default-common-personalize-tag v-if="product?.personalized" class="mt-5 ml-5 rounded-full !py-1" size="sm" />
  </div>
</template>
