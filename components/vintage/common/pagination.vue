<script lang="ts" setup>
const props = defineProps({
  paginator: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  emitPage: {
    // emit page number and let the parent component handle
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: '',
  }
})

const $emit = defineEmits(['input'])

const computedPaginator = computed(() => {
  // this computed property is just for guaranteeing other properties exists
  return {
    links: [],
    current_page: (props.paginator.current_page || props.paginator.currentPage || 1),
    per_page: (props.paginator.per_page || props.paginator.perPage || 24),
    last_page: (props.paginator.last_page || props.paginator.lastPage || 1),
    total: 1,
    from: 1,
    to: 1,

    ...props.paginator,
  }
})

const computedPageNumber = computed(() => {
  const limit = 5 // number of paginator pages will display
  const lowerBound = 1
  const upperBound = computedPaginator.value.last_page
  const current = computedPaginator.value.current_page

  const results: number[] = []
  let startPoint = current - Math.floor(limit / 2)
  if (startPoint < lowerBound) {
    startPoint = lowerBound
  }

  for (let i = 0; i < limit; i++) {
    const generatedCurrentPage = startPoint + i
    if (generatedCurrentPage > upperBound) {
      break
    }
    results.push(generatedCurrentPage)
  }

  return results
})

const computedBtnSize = computed(() => {
  if (props.size === 'sm') { return 'sm' }
  return ''
})

function generateFilterUrl (page: number) {
  if (
    page < 1 ||
    page > computedPaginator.value.lastPage ||
    page === computedPaginator.value.currentPage
  ) {
    return ''
  }

  return (props.emitPage) ? '' : getFilterUrl.value('page', page)
}
</script>

<template>
  <nav v-if="computedPaginator.current_page" class="-space-x-px flex gap-2 mt-4">
    <nuxt-link
      :to="generateFilterUrl(computedPaginator.current_page - 1)"
      class="pagination-btn"
      :class="{
        'disabled': computedPaginator.current_page === 1,
        [computedBtnSize]: true,
      }"
      @click="$emit('input', computedPaginator.current_page - 1)"
    >
      <i class="icon-vintage-arrow-left" />
    </nuxt-link>
    <nuxt-link
      v-for="pageNumber in computedPageNumber"
      :key="pageNumber"
      :to="generateFilterUrl(pageNumber)"
      class="pagination-btn"
      :class="{
        'active': pageNumber === computedPaginator.current_page,
        [computedBtnSize]: true,
      }"
      @click="$emit('input', pageNumber)"
    >
      {{ pageNumber }}
    </nuxt-link>
    <nuxt-link
      :to="generateFilterUrl(computedPaginator.current_page + 1)"
      class="pagination-btn"
      :class="{
        'disabled': computedPaginator.current_page === computedPaginator.last_page,
        [computedBtnSize]: true,
      }"
      @click="$emit('input', computedPaginator.current_page + 1)"
    >
      <i class="icon-vintage-arrow-right" />
    </nuxt-link>
  </nav>
</template>

<style lang="scss" scoped>
.pagination-btn {
  font-size: 1rem;
  cursor: pointer;
  position: relative;
  border-radius: var(--sp-border-radius-1);
  width: var(--pagination-btn-size, 48px);
  height: var(--pagination-btn-size, 48px);
  border: 2px solid #0000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;

  &.active {
    cursor: unset;
    border: 2px solid #222;
  }

  &:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: #0e0e0e17;
    border-radius: var(--sp-border-radius-1);
    transform: scale(1);
    z-index: -1;
  }
  &:hover:after {
    transform: scaleX(1.035) scaleY(1.035) perspective(1px);
    transition: transform 200ms cubic-bezier(0.345, 0.115, 0.135, 1.42), background 150ms ease-out, box-shadow 200ms ease-out;
  }

  &.sm {
    --pagination-btn-size: 36px;
    font-size: 0.75rem;
  }

  &.sm > i {
    font-size: 1rem;
  }
  i {
    font-size: 1.5rem;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  &.disabled:hover {
    opacity: 1;
  }
}
</style>
