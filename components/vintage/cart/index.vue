<script lang="ts" setup>
import { useCampaignStore } from '~/store/campaign'
import { useListingStore } from '~/store/listing'
import { useCartStore } from '~~/store/cart'
const { $i18n } = useNuxtApp()

const localePath = useLocalePath()

const listCartItem = computed(() => useCartStore().listCartItem)

const totalPrice = computed(() => useCartStore().getTotalPrice)

const sidebarVisibility = ref(false)
const editCartItemIntermediate = ref<CartItem | null>(null)
const editingCartItem = computed(() => {
  if (editCartItemIntermediate.value) {
    return useCartItem(editCartItemIntermediate.value)
  }

  return null
})
let itemLastEditedState: Partial<CartItem> | undefined = undefined

const spriteUrl = computed(() => {
  return `--sprite-url: url("${cdnURL.value}images/logo_cart_sprite.webp")`
})

const promotionsList = ref<Promotion[]>()
const relatedProducts = ref<Product[]>()
  const campaignIds = computed(() => {
  return listCartItem.value.map((item) => item.campaign_id)
})

onMounted(async () => {
  // Related Products
  const listRelatedProducts: RelatedProductPostData = {
    type: 'post_sale',
    filter: listCartItem.value.map((item) => {
      return {
        product_id: item.product_id,
        campaign_id: item.campaign_id,
        template_id: item.template_id,
      }
    })
  }

  if (listRelatedProducts.filter.length > 0) {
    relatedProducts.value = await useListingStore().postRelatedProduct(listRelatedProducts)
  }

  promotionsList.value = await useCampaignStore().getPromotion(campaignIds)
})

function startEditCartItem (cartItem: CartItem) {
  itemLastEditedState = undefined
  editCartItemIntermediate.value = {...cartItem}
  sidebarVisibility.value = true
}
async function editItemOptionProxy (key, value) {
  itemLastEditedState = await editingCartItem.value?.updateCartItem(key, value)
}
function saveEditItem() {
  if (editCartItemIntermediate.value?.id && itemLastEditedState) {
    useCartStore().updateCartItemByID(editCartItemIntermediate.value.id, itemLastEditedState)
  } else {
    uiManager().createPopup($i18n.t('Unable to update cart item, please reload and try edit the item again'))
  }
  closeEditItem()
}
function closeEditItem () {
  sidebarVisibility.value = false
  editCartItemIntermediate.value = null
  itemLastEditedState = undefined
}
</script>

<template>
  <main id="cartPage" :style="spriteUrl" class="container min-h-95 mt-4">
    <client-only>
    <h1 v-if="totalQuantity().value" class="font-secondary text-3xl font-light">
      {{ $t('item', { count: totalQuantity().value }) }} {{ $t('in your cart') }}
    </h1>
      <div v-if="listCartItem.length" class="grid grid-cols-12 mt-6 gap-6">
        <div class="col-span-12 md:col-span-8 xl:col-span-9 md:pr-2">
          <div class="flex flex-col gap-4">
            <vintage-cart-item
              v-for="(cartItem, index) in listCartItem"
              :key="cartItem.id"
              :index="index"
              :cart-item="cartItem"
              @edit-item="startEditCartItem(cartItem)"
            />
          </div>
          <!-- <default-cart-item
            v-for="(cartItem, index) in listCartItem"
            :key="cartItem.id"
            :index="index"
            :cart-item="cartItem"
          /> -->
          <lazy-default-cart-bundle-box
            v-if="!storeInfo().disable_promotion"
            class="mt-3"
            :campaign-ids="campaignIds"
          />
          <lazy-default-common-promotions-list v-if="promotionsList?.length && !storeInfo().disable_promotion" :promotions-list="promotionsList" class="mt-4 mb-8" />
        </div>
        <div class="col-span-12 md:col-span-4 xl:col-span-3 md:pl-2 md:h-max sticky top-[100px] z-1">
          <p class="font-medium mb-4">
            {{ $t('Subtotal') }} <span class="float-right font-semibold" data-test-id="cart-subtotal">{{ $formatPrice(totalPrice) }}</span>
          </p>
          <p class="mb-4 text-sm">
            <span>{{ $t('Total quantity') }}</span> <span class="float-right font-semibold" data-test-id="cart-total-qty">{{ $t('item', { count: totalQuantity().value }) }}</span>
          </p>
          <hr class="my-2">
          <p class="mb-6 text-md flex items-center justify-between">
            <span>{{ $t('Shipping & fees') }}</span> <span class="italic text-xs text-gray-500">{{ $t('Calculated at checkout') }}</span>
          </p>

          <div class="<md:(bottom-fixed border p-1 z-3) w-full <md:bg-white">
            <button
              class="btn-vintage-shadow py-2.5 px-4 font-semibold relative rounded-full w-full text-white"
              style="--sp-btn-background: #222;"
              dusk="proceed-to-checkout-button"
              data-test-id="cart-proceed-to-checkout-button"
              @click.stop.prevent="createOrder()"
            >
              <common-loading-dot v-if="!!isLoading" />
              <span v-else>{{ $t('Proceed to checkout') }}</span>
            </button>
          </div>

          <common-payment-gateway-accept />
        </div>
      </div>

      <div v-else class="text-center">
        <h1 class="text-3xl font-secondary mb-6" data-test-id="cart-is-empty">
          {{ $t('Your cart is empty') }} :(
        </h1>
        <nuxt-link class="btn-border-fill bg-white px-3 py-2" :to="localePath('/')">
          {{ $t('Continue Shopping') }}
        </nuxt-link>
      </div>

      <lazy-common-product-carousel
          v-if="relatedProducts?.length"
          class="mt-10"
          title-class="text-3xl font-secondary font-light pl-2"
          title="Frequently bought together"
          :products="relatedProducts"
          :force-static-grid="true"
          :static-grid="'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6'"
        >
          <template #default="{ product, index }">
            <vintage-common-product-item :product="product" :index="index" class="inline-block" />
          </template>
        </lazy-common-product-carousel>
    </client-only>
  </main>
  <common-sidebar
    side="right"
    class="editCartItemSidebar"
    :close-button-icon="'icon-vintage-close'"
    :close-button-class="'btn-vintage'"
    :close-button-style="'--color-highlight: #e5e7eb;'"
    :visible="sidebarVisibility"
    @update:visible="(visibility) => { sidebarVisibility = visibility }"
  >
    <div class="px-6 py-4 overflow-y-auto h-full w-full">
      <div v-if="editingCartItem" class="flex flex-col gap-4">
        <common-image
          img-class="w-full btn hover:shadow-custom"
          :image="{
            path: editCartItemIntermediate?.thumb_url,
            color: editCartItemIntermediate?.options?.color,
            type: 'full_hd'
          }"
          :alt="editCartItemIntermediate?.campaign_title"
          :title="$t('Click to view image')"
          @click="uiManager().viewImage(editCartItemIntermediate?.thumb_url)"
        />

        <template v-if="editingCartItem.campaignData.products">
          <span
            v-if="editingCartItem.campaignData.products.length===1 || editCartItemIntermediate?.personalized === 1 || editCartItemIntermediate?.personalized === 2"
            class="col-span-4 md:col-span-2 capitalize"
            data-test-id="cart-item-product-name"
            data-test-prop="false"
          >
            {{ editCartItemIntermediate?.product_name }}
          </span>
          <common-dropdown
            v-else
            class="col-span-4 md:col-span-2"
            btn-class="text-overflow-hidden btn-border w-full text-sm pl-2 py-1 z-1"
            dropdown-class="md:min-w-full"
            data-test-id="cart-item-product-name"
            data-test-prop="true"
          >
            <span>{{ editCartItemIntermediate?.product_name }}</span>
            <template #content>
              <div
                v-for="(product, productIndex) in editingCartItem.campaignData.products"
                :key="productIndex"
                sp-action="change_product"
                data-test-id="change-product"
                class="py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast)"
                :class="{'bg-primary text-contrast': product.id === editCartItemIntermediate?.product_id}"
                @click="product.id === editCartItemIntermediate?.product_id ? '' : editItemOptionProxy('product', product)"
              >
                {{ product.name }}
              </div>
            </template>
          </common-dropdown>
        </template>

        <template
          v-if="editCartItemIntermediate?.optionList && editCartItemIntermediate.options"
        >
          <template
            v-for="(key, optionListIndex) in Object.keys(editCartItemIntermediate?.optionList)"
          >
            <span
              v-if="editCartItemIntermediate?.optionList[key].length > 1 && (editCartItemIntermediate?.personalized === 1 || editCartItemIntermediate?.personalized === 2)"
              :key="`text-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1 capitalize"
              data-test-id="cart-item-option"
              data-test-prop="false"
            >
              {{ editCartItemIntermediate?.options[key] }}
            </span>
            <common-dropdown
              v-else-if="editCartItemIntermediate?.optionList[key].length > 1"
              :key="`dropdown-${optionListIndex}`"
              class="col-span-4 sm:col-span-2 md:col-span-1"
              btn-class="capitalize text-overflow-hidden btn-border w-full text-sm pl-2 py-1"
              dropdown-class="md:min-w-full"
              data-test-id="cart-item-option"
              :data-test-prop="key"
            >
              <span>{{ key?.replace(/_/g, ' ') }}: {{ editCartItemIntermediate.options[key]?.replace(/_/g, ' ') }}</span>
              <template #content>
                <div
                  v-for="(optionItem, optionIndex) in editCartItemIntermediate?.optionList[key]"
                  :key="optionIndex"
                  :sp-action="`change_${key}`"
                  class="capitalize py-1 px-2 text-overflow-hidden hover:(bg-primary-hover text-contrast) flex items-center"
                  :class="{'bg-primary text-contrast': optionItem === editCartItemIntermediate.options[key]}"
                  :data-test-id="`cart-item-option-change-${key}`"
                  @click="optionItem === (editCartItemIntermediate?.options && editCartItemIntermediate.options[key]) ? '' : editItemOptionProxy('option', {optionName: key, optionItem})"
                >
                  <lazy-common-color-item v-if="key==='color'" :color="optionItem" size="sm" class="inline-block mr-2" />
                  {{ optionItem }}
                </div>
              </template>
            </common-dropdown>
          </template>
        </template>

        <div v-if="editCartItemIntermediate?.custom_options" class="col-span-4">
          <ul class="pl-5 list-disc">
            <li v-for="(value, key) in editCartItemIntermediate.custom_options" :key="key" class="capitalize" style="font-size: 13px;">
              <div v-if="key === 'customImage'">
                {{ $t('Your image') }}:
                <a
                  :href="$imgUrl({path: value, type: 'full'})"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(value)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (key as string).replace(/_/g, ' ') }}: {{ value }}
              </div>
            </li>
          </ul>
        </div>
        <div v-if="editCartItemIntermediate?.customer_custom_options?.length" class="col-span-4 <md:text-center">
          <ul v-for="(groupOptions, groupNumber) in editCartItemIntermediate.customer_custom_options" :key="groupNumber" class="pl-5 list-disc mt-3">
            <li v-for="(customOption, optionIndex) in groupOptions" :key="optionIndex" class="capitalize" style="font-size: 13px;">
              <div v-if="customOption.type === CUSTOM_OPTION_TYPE.image">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: <a
                  :href="$imgUrl({path: customOption.value as string, type: 'full'})"
                  target="_blank"
                  class="btn-text text-blue-900"
                  @click.prevent="uiManager().viewImage(customOption.value as string)"
                >{{ $t('View image') }}</a>
              </div>
              <div v-else class="text-overflow-hidden">
                {{ (customOption.label as string).replace(/_/g, ' ') }}: {{ customOption.value }}
              </div>
            </li>
          </ul>
        </div>

        <div class="relative">
          <span class="btn-vintage-shadow w-full text-center py-2 text-white font-bold rounded-full relative block bg-[#222] cursor-pointer z-1" @click="saveEditItem">{{ $t('Save') }}</span>
        </div>
      </div>
      <div v-else class="flex flex-col justify-center items-center gap-4">
        <span>{{ $t('An error has occurred. Please try edit the item again.') }}</span>
        <div class="relative w-full">
          <span class="btn-vintage-shadow w-full text-center py-2 text-white font-bold rounded-full relative block bg-[#222] cursor-pointer z-1" @click="closeEditItem">{{ $t('Close') }}</span>
        </div>
      </div>
    </div>
  </common-sidebar>
</template>
<style>
div:has(main#cartPage + div.editCartItemSidebar:not(.invisible)) {
  overflow: hidden;
  height: 100vh;
}
</style>
