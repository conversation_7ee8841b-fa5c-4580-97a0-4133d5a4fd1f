<script lang="ts" setup>
const props = defineProps({
  index: {
    default: 0,
    type: Number
  },
  cartItem: {
    default: undefined,
    type: Object as PropType<CartItem>
  }
})
const localePath = useLocalePath()
const $viewport = useViewport()
const {
  isHasCampaignData,
  totalPrice,
  totalPriceBundle,
  getProductUrl,
  isEditCart,
  removeCartItem
} = useCartItem(props.cartItem as CartItem)
</script>

<template>
  <div v-if="isHasCampaignData" class="py-2 flex gap-2 border-b" data-test-id="cart-item">
    <!-- Use base64 image if available for AI mockup campaigns -->
    <img
      v-if="cartItem?.campaign_system_type === 'ai_mockup' && cartItem?.design_image_base64"
      :src="cartItem.design_image_base64"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      @click="uiManager().viewImage(cartItem.design_image_base64)"
    >
    <common-image
      v-else
      img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: cartItem?.thumb_url,
        color: cartItem?.options?.color
      }"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(cartItem?.thumb_url)"
    />
    <div class="w-full max-w-[calc(100%-90px)] grid grid-cols-4 h-min gap-2">
      <div class="col-span-3 grid grid-cols-1 gap-2">
        <div class="col-span-4 font-medium">
          <div v-if="totalPriceBundle" class="center-flex flex-wrap gap-x-2">
            <span>
              {{ $formatPrice(totalPriceBundle, cartItem?.currency_code) }}
            </span>
            <del class="text-gray-500">
              {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
            </del>
          </div>
          <span v-else>
            {{ $formatPrice(totalPrice, cartItem?.currency_code) }}
          </span>
        </div>
        <div class="col-span-4 flex">
          <nuxt-link
            :to="localePath(getProductUrl(cartItem?.product_url))"
            class="w-full btn-text"
          >
            <h5 class="text-overflow-hidden" :title="cartItem?.campaign_title">
              {{ cartItem?.campaign_title }}
            </h5>
          </nuxt-link>
        </div>
      </div>
      <div class="col-span-1 flex justify-end">
        <div class="text-center gap-2">
          <span class="rounded-full p-2 flex items-center justify-center transition bg-gray-200 cursor-pointer hover:(bg-gray-300 shadow-xl)" @click="removeCartItem"><i class="icon-sen-delete" /></span>
        </div>
      </div>
    </div>
  </div>
  <div v-else class="p-4 flex border-b gap-4" data-test-id="cart-item">
    <common-image
      img-class="!w-22 h-25 mr-2 btn hover:shadow-custom"
      :image="{
        path: cartItem?.thumb_url,
        color: cartItem?.options?.color
      }"
      :alt="cartItem?.campaign_title"
      :title="$t('Click to view image')"
      @click="uiManager().viewImage(cartItem?.thumb_url)"
    />
    <div v-if="isEditCart || $viewport.isGreaterOrEquals(VIEWPORT.tablet)" class="w-full max-w-[calc(100%-110px)]" data-test-id="cart-item-not-exist">
      <h3 class="font-semibold w-full">
        {{ $t('The campaign does not exist or has been deleted. Kindly remove the product.') }}
      </h3>
      <button class="btn-border p-2 py-1 text-xl mt-3" data-test-id="cart-item-remove" @click="removeCartItem">
        {{ $t('Remove') }}
      </button>
    </div>
  </div>
</template>
