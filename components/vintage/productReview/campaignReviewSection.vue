<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'

const props = defineProps({
  currentProduct: {
    type: Object as PropType<Product>,
    default: undefined
  }
})

const currentProduct = toRef(() => props.currentProduct)

const {
  isLoadingReviews,
  productReviews,

  changePage,
} = useCampaignReview(currentProduct)

function viewImage (imageList:{src: string, type: any}[], index:number) {
  uiManager().viewImage(imageList.map(item => item?.src), index)
}
</script>
<template>
  <div v-if="productReviews?.reviewSummary?.summary?.review_count > 0" id="productReviewBox" class="mb-6">
    <div class="grid grid-cols-12 review-header">
      <div class="col-span-8 flex gap-4 flex-wrap items-center">
        <h4 class="uppercase font-medium text-2xl font-secondary">
          {{ $t('review', { count: productReviews.reviewSummary.summary.review_count }) }}
        </h4>
        <default-product-review-stars
          :model-value="parseFloat(productReviews.reviewSummary.summary.average_rating || 5)"
          :container-class="'flex justify-around'"
          :common-class="'px-1 text-md'"
          disabled
        />
      </div>
      <div class="col-span-4">
        <lazy-default-campaign-modal-check-review-order />
      </div>
    </div>
    <div class="mt-4 relative">
      <div v-if="isLoadingReviews" class="absolute w-full h-full">
        <div class="flex w-full h-full justify-center items-center bg-[rgba(221,221,221,0.6)]">
          <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
        </div>
      </div>
      <template v-if="productReviews.reviews?.data?.length">
        <div v-for="(review, index) in productReviews.reviews.data" :key="index" class="flex flex-col gap-4 mt-6 pb-6 border-b border-gray-300">
          <div class="flex">
            <default-product-review-stars
              :model-value="parseFloat(review.average_rating)"
              :container-class="'flex justify-around'"
              :common-class="'px-1 text-xl'"
              disabled
            />
          </div>
          <div class="flex gap-2 flex-wrap">
            <nuxt-link
              v-if="review.product_url"
              :to="review.product_url"
              class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold hover:bg-[rgba(75,178,50,0.2)] transition"
            >
              {{ review.product_name }}
            </nuxt-link>
            <span
              v-else
              class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
            >{{ review.product_name }}</span>
            <span
              v-if="review.product_size"
              class="text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
            >{{ $t('Size') }} <span class="uppercase">{{ review.product_size }}</span></span>
            <span
              v-if="review.product_color"
              class="flex items-center gap-2 text-[#4BB232] bg-transparent border-[1px] border-solid border-[#4BB232] px-[0.6em] rounded-[10rem] font-bold"
            ><span>{{ $t('Color') }}:</span><span class="rounded-1 w-[0.8em] h-[0.8em] border-gray-300 border-[0.1px]" :style="`background: ${colorVal(review.product_color)}`" /></span>
          </div>
          <p v-text="review.comment" />
          <div class="flex gap-2 items-center text-sm">
            <img
              :src="review.avatar_url || `${cdnURL}images/default-user-icon.webp`"
              onerror="this.src = `${cdnURL}images/default-user-icon.webp`"
              loading="lazy"
              :alt="review.customer_name"
              class="max-w-[1.5rem] max-h-[1.5rem] border-radius-override"
            >
            <span class="underline" v-text="review.customer_name" />
            <span class="text-[#595959]">{{ review.created_at }}</span>
          </div>
        </div>
        <VintageCommonPagination
          class="flex justify-start"
          :paginator="productReviews.reviews"
          :emit-page="true"
          size="sm"
          @input="(pageNumber) => { changePage(pageNumber) }"
        />
        <client-only v-if="productReviews.reviewSummary.files?.length">
          <div class="mt-12">
          <p class="text-lg mb-4">
            {{ $t('Photos from reviews') }}
          </p>
            <Splide :options="productReviewSplideSetting">
              <SplideSlide v-for="(file, index) in productReviews.reviewSummary.files" :key="index">
                <common-image
                  :key="index"
                  img-class="object-cover cursor-zoom-in w-[100%] h-[200px] rounded-lg"
                  :image="{ path: file.src, type: (file.type === 'video') ? 'product-review-video' : 'product-review-image'}"
                  @click="viewImage(productReviews.reviewSummary.files, index)"
                />
              </SplideSlide>
            </Splide>
          </div>
        </client-only>
      </template>
      <div v-else class="mt-6" align="center">
        {{ $t('There are no reviews yet') }}
      </div>
    </div>
  </div>
</template>
