import { fileURLToPath } from 'node:url'
import { isDev, setup } from '@nuxt/test-utils-edge'
import { describe, expect, it } from 'vitest'

describe('example', async () => {
  await setup({
    rootDir: fileURLToPath(new URL('..', import.meta.url)),
    server: true
  })

  it('renders Nuxt 3 Awesome Starter', async () => {
    const { $fetchDefault } = useNuxtApp()
    expect(await $fetchDefault('/')).toMatch('Nuxt 3 Awesome Starter')
  })

  if (isDev()) {
    it('[dev] ensure vite client script is added', async () => {
      const { $fetchDefault } = useNuxtApp()
      expect(await $fetchDefault('/')).toMatch('/_nuxt/@vite/client"')
    })
  }
})
