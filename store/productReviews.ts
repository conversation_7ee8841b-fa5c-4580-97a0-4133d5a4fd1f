import { defineStore } from 'pinia'

export const useProductReviewsStore = defineStore('productReviews', {
  state: (): ProductReviewStore => ({
    reviewSummary: {},
    reviews: {}
  }),
  actions: {
    async getReviewSummary({ campaignId, templateId }: { campaignId: number, templateId: number }) {
      const key = `${campaignId}_${templateId}`

      if (this.reviewSummary[key]) {
        return {
          success: true,
          data: this.reviewSummary[key]
        }
      }

      const { $fetchDefault } = useNuxtApp()

      return await $fetchDefault(`${$api.API_PRODUCT_REVIEW}/${encodeURIComponent(templateId)}/summary`, {
        query: {
          campaignId
        }
      })
        .then((result: ResponseData<ProductReviewSummary>) => {
          if (result?.success) {
            Object.assign(this.reviewSummary, { [key]: result.data })

            return {
              success: true,
              data: result.data
            }
          }

          return result
        })
    },
    async getReviews({ campaignId, templateId, filter, page }: { campaignId: number, templateId: number, filter: ProductReviewFilter, page: number | string }) {
      const { $fetchDefault } = useNuxtApp()

      const key = `${campaignId}_${templateId}_${filter}_${page}`

      if (this.reviews[key]) {
        return {
          success: true,
          data: this.reviews[key]
        }
      }

      return await $fetchDefault(`${$api.API_PRODUCT_REVIEW}/${encodeURIComponent(templateId)}/reviews`, {
        query: {
          campaignId,
          filter,
          page
        }
      })
        .then((result: ResponseData<PaginatorResponse<ProductReview[]>>) => {
          if (result?.success) {
            Object.assign(this.reviews, { [key]: result.data })

            return {
              success: true,
              data: result.data
            }
          }

          return result
        })
    }
  }
})
