import { defineStore } from 'pinia'
import { useCartStore } from "~/store/cart"
import { useUserSession } from './userSession'

interface ComboCartStore {
  list: Array<ComboItem>,
  selected: ComboSelected[],
  index: number
}

interface ComboSelected {
  id: number,
  isSelected: boolean
  currentOptions: Record<string, string>
}

export const useComboCartStore = defineStore('comboCart', {
    state: (): ComboCartStore => ({
      list: [] as ComboItem[],
      selected: [] as ComboSelected[],
      index: 0,
    }),
    getters: {
      isSelectedAll: (state) => {
        return state.selected.every(item => item.isSelected)
      },
      hasSelected: (state) => {
        return state.selected.some(item => item.isSelected)
      },
      productIdsInCombo: (state) => {
        return state.list.map(item => item.product_ids).flat()
      },
      enoughCombo: (state) => {
        return state.list.filter((combo) => combo.items[0].campaign_product_ids.length === combo.items.length)
      }
    },
    actions: {
      addComboItem (items: CartItem[], campaign: Campaign) {
        const user = useUserSession()
        const comboItem: ComboItem = {
          id: `${Date.now()}_${this.list.length}`,
          campaign_id: campaign.id as number,
          quantity: items[0].quantity, // all items in combo have the same quantity
          thumb_url: campaign.thumb_url as string,
          title: campaign.name as string,
          product_ids: items.map(item => item.product_id),
          items: items,
          combo_price: campaign.combo_price as number,
          products: campaign.products as Product[],
          country: user.countryCode as string
        }

        this.list.push(comboItem)
      },
      duplicateComboItem (comboItem: ComboItem, campaignId: number) {
        const cartStore = useCartStore()
        const cartItems = []
        const comboID = `${campaignId}_${this.index}`
        this.index += 1
        for (const item of comboItem.items) {
          const index = cartStore.listCartItem.findIndex(cartItem => cartItem.product_id === item.product_id)
          const newItem = cartStore.duplicateCartItem(index, comboID)
          cartItems.push(newItem)
        }
        const newComboItem = {
          ...comboItem,
          id: comboID,
          items: cartItems,
          quantity: 1
        }
        this.list.push(newComboItem)
      },
      removeComboItem (comboItem: ComboItem) {
        const cartStore = useCartStore()
        for (const _ of comboItem.items) {
          const index = cartStore.listCartItem.findIndex((cartItem) => cartItem.combo_id === comboItem.id)
          cartStore.removeCartItem(index)
        }
        const index = this.list.findIndex(item => item.id === comboItem.id)
        this.list.splice(index, 1)
      },
      removeItem(cartItem: CartItem) {
        const comboItem = this.list.find((comboItem) => {
          return comboItem.items.some((i) => i.id === cartItem.id)
        })
        if (comboItem) {
          const index = comboItem.items.findIndex((item) => item.id === cartItem.id)
          comboItem.items.splice(index, 1)
          if (comboItem.items.length === 0) {
            this.removeComboItem(comboItem)
          }
        }
      },
      updateQuantity (comboItem: ComboItem, quantity: number) {
        const cartStore = useCartStore()
        const diff = quantity - comboItem.quantity

        for (const item of comboItem.items) {
          const cartItem = cartStore.listCartItem.find((cartItem) => cartItem.product_id === item.product_id && cartItem.campaign_id === item.campaign_id)
          if (cartItem) {
            cartStore.updateCartItem(cartItem, { quantity: cartItem.quantity + diff })
          }
        }

        comboItem.quantity = quantity
      },
      updateSelected(selected: ComboSelected[]) {
        this.selected = selected
      },
      toggleSelect(product: Product) {
        const modifyProduct = this.selected.find((item) => item.id === product.id)
        if (modifyProduct) {
          modifyProduct.isSelected = !modifyProduct.isSelected
        }
      },
      changeOption(product: Product, optionType: string, option: string) {
        const modifyProduct = this.selected.find((item) => item.id === product.id)
        if (modifyProduct) {
          if (!modifyProduct.currentOptions) {
            modifyProduct.currentOptions = {}
          }

          modifyProduct.currentOptions[optionType] = option
        }
      },
      reset() {
        this.list = []
        this.selected = []
        this.index = 0
      }
    },
    persist: {
      storage: persistedState.localStorage
    },
    share: {
      enable: true,
      initialize: true,
    }
})
