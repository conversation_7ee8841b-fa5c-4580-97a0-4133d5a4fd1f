import FingerprintJS from '@fingerprintjs/fingerprintjs'
import { defineStore } from 'pinia'
import UAParser from 'ua-parser-js'
import { v4 as uuidV4 } from 'uuid'

export const useUserSession = defineStore('userSession', {
  state: (): UserSession => ({
    userInfo: {
      name: '',
      email: '',
      address: '',
      address_2: '',
      city: '',
      state: '',
      zipcode: '',
      phone: '',
      country: '',
      subscribed: 0
    },
    visitInfo: {
      clientIp: '',
      country: '',
      currency_code: '',
      device: '',
      device_detail: '',
      device_id: '',
      email: '',
      seller_id: '',
      session_id: '',
      spsid: '',
      ad_source: '',
      visitTime: 0,
      csrfToken: '',
      user_id: '',
      dd: false
    },
    clientInfo: {
      ip: '',
      ts: '',
      sliver: '',
      http: '',
      tls: '',
      warp: '',
      gateway: '',
      rbi: '',
      kex: '',
      country: '',
      url: '',
      queries: {

      },
      browser: {
        name: '',
        version: '',
        major: ''
      },
      device: {

      },
      os: {
        name: '',
        version: ''
      },
      useragent: '',
      seller_id: ''
    },
    userBehavior: {
      softType: 'popular',
      size: '',
      sizeByProductName: {},
      color: '',
      colorByProductName: {},
      currencyCode: 'USD',
      correct_price: false,
      cookieConsentConfirmed: false
    },
    lastOrder: {
      order_number: '',
      order_token: ''
    },
    orderKey: {
      order_token: '',
      cart_key: ''
    },
    currentSellerId: 0,
    isTestPriceApply: false,
    sessionExpireTime: 0,
    nocacheExpireTime: 0
  }),
  getters: {
    currentCurrency(): Currency | undefined {
      return generalSettings().getCurrencyByCode(this.userBehavior.currencyCode || USD_CODE)
    },
    getLastOption: (state) => {
      return (optionName: string, productName?: string) => {
        let lastOption = state.userBehavior[optionName] as string
        if (!productName) {
          return lastOption
        }
        switch (optionName) {
          case 'size':
            lastOption = state.userBehavior.sizeByProductName?.[productName] || lastOption
            break
          case 'color':
            lastOption = state.userBehavior.colorByProductName?.[productName] || lastOption
            break
        }
        return lastOption
      }
    },
    countryCode: (state) => {
      return state.userInfo.country || state.visitInfo.country || state.clientInfo.country
    },
    currentCurrencySymbol(): string {
      const { $getCurrencySymbol } = useNuxtApp()
      return $getCurrencySymbol(this.currentCurrency?.locale === 'en' ? undefined : this.currentCurrency?.locale, this.currentCurrency?.code)
    }
  },
  actions: {
    update(key: keyof UserSession, data: object) {
      Object.assign(this[key] as object, data)
    },
    setLastOrderDetail(order: LastOrder) {
      this.lastOrder!.order_number = order.order_number
      this.lastOrder!.order_token = order.order_token
    },
    async initClientInfo() {
      const clientInfo: { [key: string]: string } = {}
      try {
        let response = []
        const result = await $fetch('https://speed.cloudflare.com/meta')
        if (result) {
          // @ts-ignore
          result.ip = result?.clientIp
          response = Object.entries(result)
          response.forEach((row) => {
            if (row.length >= 2 && !['colo', 'fl', 'h', 'uag', 'sni', 'visit_scheme', 'hostname', 'asOrganization', 'httpProtocol', 'asn'].includes(row[0])) {
              clientInfo[row[0]] = row[1]
            }
          })
        }
      }
      catch (err) {
        let response = ''
        try {
          response = await $fetch(`${document?.location?.origin || ''}/cdn-cgi/trace`)
        }
        catch (err) {
          response = await $fetch(useRuntimeConfig().public.traceUrl)
        }
        if (response && response.includes('visit_scheme')) {
          response.split('\n').filter(res => res !== '' && res !== null).forEach((line) => {
            const [key, value] = line.split('=')
            if (!['colo', 'fl', 'h', 'uag', 'sni', 'visit_scheme', 'hostname', 'asOrganization', 'httpProtocol', 'asn'].includes(key)) {
              clientInfo[key] = value
            }
          })
        }
      }
      if (clientInfo.loc) {
        clientInfo.country = clientInfo.loc.toUpperCase()
        delete clientInfo.loc
      }
      Object.assign(this.clientInfo, clientInfo)
      this.attachMoreClientInfo()
    },
    attachMoreClientInfo() {
      const browserInfo = new UAParser().getResult()
      const { clientInfo, visitInfo, userBehavior, currentSellerId } = this
      Object.assign(clientInfo, {
        url: window.top?.location.href,
        queries: Object.assign(clientInfo.queries || {}, getQueriesString()),
        browser: browserInfo.browser,
        device: browserInfo.device,
        os: browserInfo.os,
        useragent: browserInfo.ua,
        seller_id: currentSellerId
      })

      if (clientInfo.device.type === undefined) {
        clientInfo.device.type = 'desktop'
      }

      if (!visitInfo.visitTime) {
        visitInfo.visitTime = new Date().getTime()
      }

      if (clientInfo.ip) {
        visitInfo.clientIp = clientInfo.ip
      }

      if (!userBehavior) {
        const userCountry = generalSettings().getCountryByCode(clientInfo.country)
        this.userBehavior.code = storeInfo().default_currency || userCountry?.default_currency_code || USD_CODE
        this.visitInfo.currency_code = this.userBehavior.code
      }
      const { query } = useRoute()
      const { utm_source, source, utm_campaign, utm_medium, seid, fbclid, gclid, user_id, msclkid } = query
      let { spsid } = query
      if (utm_source || source) {
        visitInfo.ad_source = (utm_source || source) as string
      }
      if (utm_campaign || msclkid) {
        visitInfo.ad_campaign = (utm_campaign || msclkid) as string
      }
      if (utm_medium) {
        visitInfo.ad_medium = utm_medium as string
      }
      if (fbclid) {
        visitInfo.ad_id = fbclid as string
        visitInfo.ad_campaign = visitInfo.ad_campaign || 'facebook_ad'
      }
      if (gclid) {
        visitInfo.ad_id = gclid as string
        visitInfo.ad_campaign = visitInfo.ad_campaign || 'google_ad'
      }
      if (seid) {
        visitInfo.ad_id = seid as string
      }
      if (user_id) {
        visitInfo.user_id = user_id as string
      }

      // // set custom store seller_id
      if (storeInfo().id !== 1) {
        visitInfo.seller_id = storeInfo().seller_id
      }
      else if (spsid) {
        // overwrite current seller id if visit come from an affiliate
        if (Array.isArray(spsid)) {
          spsid = spsid[spsid.length - 1]
        }
        visitInfo.seller_id = spsid as string
        visitInfo.spsid = spsid as string
        useCookie('spsid', { maxAge: 604800 }).value = spsid
      }

      if (clientInfo.seller_id !== visitInfo.seller_id) {
        clientInfo.seller_id = visitInfo.seller_id
      }

      if (clientInfo.city) {
        visitInfo.city = clientInfo.city
      }

      if (clientInfo.region) {
        visitInfo.region = clientInfo.region
      }

      if (clientInfo.latitude) {
        visitInfo.latitude = clientInfo.latitude
      }

      if (clientInfo.longitude) {
        visitInfo.longitude = clientInfo.longitude
      }

      visitInfo.country = clientInfo.country
      visitInfo.device = visitInfo.dd ? 'desktop' : (clientInfo.device && clientInfo.device.type)
      visitInfo.device_detail = `${clientInfo.os.name}/${clientInfo.browser.name}`
    },
    updateAdsSource() {
      const { utm_source, source, utm_campaign, utm_medium, seid, msclkid } = useRoute().query
      if (utm_source || source) {
        this.visitInfo.ad_source = (utm_source || source) as string
      }
      if (utm_campaign || msclkid) {
        this.visitInfo.ad_campaign = (utm_campaign || msclkid) as string
      }
      if (utm_medium) {
        this.visitInfo.ad_medium = utm_medium as string
      }
      if (seid) {
        this.visitInfo.ad_id = seid as string
      }
    },
    async initSessionId() {
      const newTrackingUrl = useRuntimeConfig().public.newTrackingUrl
      if (!this.visitInfo?.session_id || Date.now() > this.sessionExpireTime) {
        useTestPrice().initTestPrice()
        this.visitInfo.session_id = uuidV4()

        const fpPromise = FingerprintJS.load()
        // Get the visitor identifier when you need it.
        const fp = await fpPromise
        const result = await fp.get()
        // This is the visitor identifier:
        this.visitInfo.device_id = result.visitorId
        try {
          $fetch(newTrackingUrl, {
            signal: AbortSignal.timeout(15000),
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'fingerprint',
              id: this.visitInfo.device_id,
              ...this.clientInfo
            })
          })
        }
        catch (_e) {}
        const sessionLocation = null

        // try {
        //   if (!location) {
        //     sessionLocation = await this.getLocation()
        //   }
        // } catch (e) { }

        try {
          $fetch<ResponseData<any>>(newTrackingUrl, {
            signal: AbortSignal.timeout(15000),
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              type: 'session',
              id: this.visitInfo.session_id,
              fingerprintId: this.visitInfo.device_id,
              location: sessionLocation,
              referrer: document.referrer,
              ...this.clientInfo
            })
          }).then(() => {
            this.sessionExpireTime = Date.now() + 86400000
          })
        }
        catch (_e) {}
      }
    },
    async getCSRFToken() {
      const { $fetchWrite } = useNuxtApp()
      const { success, data } = await $fetchWrite<ResponseData<{ token: string }>>($api.API_CSRF, {
        method: $method.post,
        headers: {
          'x-session-id': this.visitInfo.session_id
        } as { 'x-session-id': string }
      })

      if (success) {
        this.visitInfo.csrfToken = data.token
      }
    },
    setOrderKey(orderToken: string, cartKey: string) {
      this.orderKey.order_token = orderToken
      this.orderKey.cart_key = cartKey
    },

    async getLocation() {
      const res = await $fetch('https://api.geoapify.com/v1/ipinfo?&apiKey=********************************', {
        method: 'GET', // or 'PUT'
        headers: {
          'Content-Type': 'application/json'
        }
      }) as any

      const resultLocation = res.location
      const result = {
        lat: resultLocation.latitude,
        lon: resultLocation.longitude
      }
      return result
    },
    updateUserInfo(userInfo: object) {
      Object.assign(this.userInfo, userInfo)
    },
    setBehavior(key: string, value: string, productName?: string) {
      if ((key === 'size' || key === 'color') && productName) {
        const behaviorKey = key === 'size' ? 'sizeByProductName' : 'colorByProductName'
        this.update('userBehavior', {
          [key]: value,
          [behaviorKey]: {
            ...this.userBehavior[behaviorKey],
            [productName]: value
          }
        })
      }
      else {
        this.update('userBehavior', {
          [key]: value
        })
      }
    }
  },
  persist: {
    storage: persistedState.localStorage
  },
  share: {
    enable: true,
    initialize: true
  }
})

function getQueriesString() {
  if (!window) {
    return {}
  }

  const qs = new URLSearchParams(window.top?.location.search)

  if (qs.toString() === '') {
    return {}
  }

  try {
    // convert to object
    return Object.fromEntries(qs)
  }
  catch (e) {
    return {}
  }
}
