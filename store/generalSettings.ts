import { defineStore } from 'pinia'

export const useGeneralSettings = defineStore('generalSettings', {
  state: (): GeneralSettings => ({
    categories: [],
    colors: [],
    countries: [],
    currencies: [],
    languages: [],
    size_guides: [],
    templates: [],
    paypal_discount: '',
    country_disabled_checkout: [],
    checkout_form_config: null
  }),
  getters: {
    getCurrencyByCode() {
      return (code?: CurrencyCode) => {
        return this.currencies.find(item => item.code === code)
      }
    },
    getCountryByCode() {
      return (code?: string) => {
        return this.countries.find(item => item.code === code)
      }
    },
    getCountryDisabledCheckout(state): string[] {
      if (state.country_disabled_checkout && state.country_disabled_checkout.length > 0) {
        return state.country_disabled_checkout
      }
      return ['MX', 'RU', 'UA', 'BR', 'CU', 'FK', 'IR', 'KP', 'SO', 'SD']
    },
    hasSizeGuide() {
      return (templateId?: number) => {
        return this.size_guides.some(item => item.product_id === templateId)
      }
    },
    getCheckoutFormConfig(state) {
      const DEFAULT = {
        AU: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address, P.O. Box, Company name, c/o',
            required: true
          },
          address_2: {
            label: 'Apt, Suite, Unit, Building, Floor',
            required: true
          },
          postCode: {
            label: 'Postcode',
            required: true
          },
          city: {
            label: 'City/Suburb',
            required: true
          },
          state: {
            label: 'State/Territory',
            required: true
          },
          position: ['address', 'postCode', 'city', 'state']
        },
        IT: {
          fullName: {
            label: 'Name, surname, company name, c/o',
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address',
            required: true
          },
          address_2: {
            label: 'Staircase, floor, interior, company',
            required: false
          },
          postCode: {
            label: 'Zip code',
            required: true,
            width: 30
          },
          city: {
            required: true,
            width: 70
          },
          state: {
            label: 'Province',
            required: true
          },
          deliveryNote: {
            required: false
          },
          position: ['address', 'postCode', 'city', 'state']
        },
        UK: {
          fullName: {
            required: true
          },
          phoneNumber: {
            label: '10 or 9 digit phone number',
            required: true
          },
          address_1: {
            label: 'Address line 1 (or Company Name)',
            required: true
          },
          address_2: {
            required: false
          },
          postCode: {
            label: 'Postcode',
            required: true
          },
          city: {
            label: 'Town/City',
            required: true
          },
          state: {
            label: 'County',
            required: false
          },
          deliveryNote: {
            required: false
          },
          position: ['postCode', 'address', 'city', 'state']
        },
        DE: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street name and number, pickup location',
            required: true
          },
          address_2: {
            label: 'Company name, P.O Box, c/o, Pakadoo PAK-ID, etc.',
            required: true
          },
          postCode: {
            label: 'PLZ',
            required: true,
            width: 30
          },
          city: {
            label: 'Town/City',
            required: true,
            width: 70
          },
          deliveryNote: {
            required: false
          },
          position: ['address', 'postCode', 'city']
        },
        BE: {
          fullName: {
            required: true
          },
          phoneNumber: {
            label: 'Enter mobile number for SMS notifications',
            required: true
          },
          address_1: {
            label: 'Street Avenue',
            required: true
          },
          address_2: {
            label: 'Apartment, suite, unit, building, floor, etc.',
            required: true
          },
          postCode: {
            label: 'Postcode (4 digits)',
            required: true
          },
          city: {
            required: true
          },
          houseNumber: {
            required: true,
            width: 50
          },
          mailboxNumber: {
            required: false,
            width: 50
          },
          deliveryNote: {
            required: false
          },
          position: ['address', 'houseNumber', 'mailboxNumber', 'postCode', 'city']
        },
        NL: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address, P.O box, company name, c/o',
            required: true
          },
          address_2: {
            label: 'Apartment, suite, unit, building, floor, etc.',
            required: true
          },
          postCode: {
            label: 'Zip code',
            required: true
          },
          city: {
            required: true
          },
          state: {
            label: 'State/Province/Region',
            required: true
          },
          position: ['address', 'city', 'state', 'postCode']
        },
        ES: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street name and number',
            required: true
          },
          address_2: {
            label: 'Apartment, suite, unit, building, floor, etc.',
            required: true
          },
          postCode: {
            label: 'Postal code',
            required: true,
            width: 30
          },
          city: {
            required: true,
            width: 70
          },
          state: {
            label: 'Province',
            required: true
          },
          deliveryNote: {
            required: false
          },
          position: ['address', 'postCode', 'city', 'state']
        },
        FR: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address',
            required: true
          },
          address_2: {
            label: 'Apt, suite, unit, company name',
            required: false
          },
          postCode: {
            label: 'Zip code',
            required: true,
            width: 30
          },
          city: {
            required: true,
            width: 70
          },
          deliveryNote: {
            required: false
          },
          position: ['fullName', 'address', 'postCode', 'city']
        },
        AT: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street name and number / stairwell (optional) / door number (optional)',
            required: true
          },
          address_2: {
            required: false
          },
          postCode: {
            label: 'PLZ',
            required: true,
            width: 30
          },
          city: {
            label: 'Town/City',
            required: true,
            width: 70
          },
          position: ['address', 'postCode', 'city']
        },
        NZ: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address (Ex: 18-22 Swanson St)',
            required: true
          },
          address_2: {
            label: 'Unit 904',
            required: true
          },
          postCode: {
            label: 'Postcode',
            required: true
          },
          city: {
            label: 'Town or City (Ex: Auckland)',
            required: true
          },
          state: {
            label: 'Suburb (Ex: Auckland CBD)',
            required: true
          },
          position: ['address', 'postCode', 'state', 'city']
        },
        CA: {
          fullName: {
            required: true
          },
          phoneNumber: {
            required: true
          },
          address_1: {
            label: 'Street address or P.O. Box',
            required: true
          },
          address_2: {
            label: 'Apt, suite, unit, building',
            required: true
          },
          postCode: {
            label: 'Postal code',
            required: true
          },
          city: {
            required: true
          },
          state: {
            label: 'Province/Territory',
            required: true
          },
          deliveryNote: {
            required: false
          },
          position: ['address', 'city', 'state', 'postCode']
        }
      }
      if (!state.checkout_form_config) {
        console.log('Using default checkout form config')
      }
      return state.checkout_form_config || DEFAULT
    }
  },
  actions: {
    update(data: object) {
      Object.assign(this, data)
    }
  }
})
