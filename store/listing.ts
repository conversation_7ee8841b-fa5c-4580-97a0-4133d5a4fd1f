import { defineStore } from 'pinia'
import { stringify } from 'qs'
import { nextTick } from 'vue'

export const useListingStore = defineStore('listingStore', {
  state: (): ListingStore => ({
    listProductByUrl: {},
    filterProductByUrl: {},
    relatedProducts: []
  }),

  actions: {
    async getListProductByUrl(apiQuery: string): Promise<ListingProduct> {
      const { $fetchDefault } = useNuxtApp()

      if (!this.listProductByUrl[apiQuery]) {
        const { data, banner_url, links, current_page: currentPage, per_page: perPage, last_page: lastPage, total, from, to } = await $fetchDefault<Omit<PaginatorResponse<Product[]>, 'banner_url'> & { banner_url: string }>(`${$api.API_GET_LISTING_PRODUCT}?${apiQuery}`)

        if (data) {
          this.listProductByUrl[apiQuery] = {
            products: data,
            bannerUrl: banner_url,
            links,
            currentPage,
            perPage,
            lastPage,
            total,
            from,
            to
          }
        }
      }
      return this.listProductByUrl[apiQuery]
    },

    async getFilterProductByUrl(apiQuery: string): Promise<FilterProduct> {
      const { $fetchDefault } = useNuxtApp()

      if (!this.filterProductByUrl[apiQuery]) {
        const data = await $fetchDefault<ResponseData<FilterProduct>>(`${$api.API_GET_LISTING_FILTER}?${apiQuery}`)

        if (data) {
          this.filterProductByUrl[apiQuery] = data
        }
      }
      return this.filterProductByUrl[apiQuery]
    },

    async getRelatedProduct(params: any) {
      await nextTick()
      const { $fetchDefault } = useNuxtApp()
      const { success, data } = await $fetchDefault<ResponseData<Product[]>>(`${$api.API_GET_UPSELL}?${stringify(params)}`)
      if (!success || !data.length) {
        // this.relatedProducts = []
        return []
      }
      else {
        // this.relatedProducts = data
        return data
      }
    },

    async postRelatedProduct(body: RelatedProductPostData) {
      await nextTick()
      const { $fetchDefault } = useNuxtApp()

      const { success, data } = await $fetchDefault<ResponseData<RelatedProduct[]>>(`${$api.API_GET_UPSELL}`, {
        method: $method.post,
        body
      })
      if (!success || !data.length) {
        this.relatedProducts = []
        return []
      }
      else {
        this.relatedProducts = data
        return data
      }
    }
  }
})
