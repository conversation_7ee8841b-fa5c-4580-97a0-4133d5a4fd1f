import { defineStore } from 'pinia'

interface PopupType {
  message: string
  type?: 'success' | 'error'
}

interface SizeGuideData {
  isShowModal: boolean
  name?: string
  size_chart?: string
  sizeGuideList?: SizeGuide[]
}
interface UiManager {
  showHeaderMenu: boolean
  isLoading: string | boolean
  isUploadFile?: string | boolean
  viewImageUrl?: string[]
  viewImageIndex: number
  popup: Array<PopupType>
  sizeGuide?: SizeGuideData
  createOrderNeedRecaptcha: boolean
  isSearching: boolean
  isShowCartDrawler: boolean
}

export const useUiManager = defineStore('uiManager', {
  state: (): UiManager => ({
    showHeaderMenu: false,
    isLoading: '',
    isUploadFile: '',
    popup: [],
    sizeGuide: {
      isShowModal: false,
      name: undefined,
      size_chart: undefined,
      sizeGuideList: undefined
    },
    viewImageUrl: undefined,
    viewImageIndex: 0,
    createOrderNeedRecaptcha: false,
    isSearching: false,
    isShowCartDrawler: false
  }),
  actions: {
    createPopup(message: string, type?: 'success' | 'error') {
      this.popup.push({ message, type })
      setTimeout(() => {
        this.popup.shift()
      }, 4000)
    },
    toggleModalSizeGuide(value: boolean) {
      if (value) {
        useTracking().customTracking({
          event: 'interact',
          data: {
            action: 'open_size_guide'
          }
        })
      }
      this.sizeGuide!.isShowModal = value
    },
    toggleCartDrawler(value: boolean) {
      this.isShowCartDrawler = value
    },
    toggleHeaderMenu() {
      this.showHeaderMenu = !this.showHeaderMenu
    },
    updateSizeGuideData(product?: Product, isShowModal = false) {
      if (!product) {
        this.sizeGuide = undefined
        return
      }

      const sizeGuideList = generalSettings().size_guides.filter(item => item.product_id === product?.template_id)

      Object.assign(this.sizeGuide as SizeGuideData, {
        isShowModal,
        name: product.name,
        size_chart: product && product.attributes && JSON.parse(product.attributes).size_chart,
        sizeGuideList
      })
    },
    viewImage(value?: string | string[], index?: number) {
      this.viewImageUrl = value ? Array.isArray(value) ? value : [value] : undefined
      this.viewImageIndex = index || 0
      if (value && (index || index === 0)) {
        useTracking().customTracking({
          event: 'interact',
          data: {
            action: 'zoom_product'
          }
        })
      }
    },
    viewModalCreateOrderRecaptcha(value: boolean) {
      this.createOrderNeedRecaptcha = value
    }
  }
})
