import { defineStore } from 'pinia'

export const useStoreInfo = defineStore('storeInfo', {
  // a function that returns a fresh state
  state: (): StoreInfo => ({
    id: undefined,
    name: '',
    sub_domain: '',
    domain: '',
    logo_url: '',
    theme: 'default',
    theme_options: undefined,
    foot_line: '',
    promotion_title: '',
    discount_code: '',
    description: '',
    auto_apply_coupon: '',
    seller_id: undefined,
    company_id: undefined,
    checkout_phone: 0,
    tracking_code: {
      google_tag: undefined,
      google_merchant_verification: undefined,
      google_analytics: undefined,
      google_adwords_id: undefined,
      google_ads_gtag: undefined,
      google_ads_gtag_thank_you: undefined,
      facebook_pixel: undefined,
      facebook_meta_tag: undefined,
      facebook_conversion_token: undefined,
      pinterest_meta_tag: undefined,
      pinterest_tag_id: undefined,
      tiktok_pixel: undefined,
      klaviyo_public_key: undefined
    },
    favicon: '',
    default_currency: 'USD',
    default_language: '',
    default_color: '',
    feature_collection_id: 0,
    featured_collection_ids: '',
    feature_text: '',
    list_all_my_campaigns: 0,
    random_popular: 0,
    market_place_listing: 0,
    market_place_upsell: 0,
    status: '',
    store_type: 'normal',
    show_payment_button: 0,
    show_tipping: 0,
    product_select_type: 'image',
    show_checkout_shipping_info: 0,
    sitewide_banner: '',
    sitewide_banner_enable: 0,
    option_label_enable: 0,
    is_proxy: 0,
    disable_promotion: 0,
    disable_pre_discount: 0,
    disable_related_product: 0,
    disable_related_collection: 0,
    product_review_display: 'disable',
    product_review_coupon: '',
    product_review_thank_you_message: '',
    order_prefix: undefined,
    tags: undefined,
    smart_remarketing: 0,
    currentDomain: '',
    storeContact: {
      address: '',
      phone: '',
      email: '',
      email_info: ''
    },
    banners: [],
    collection_banners: [],
    headerMenu: [],
    footerMenu: [],
    socialsLink: {
      facebook: '',
      instagram: '',
      twitter: '',
      google: '',
      pinterest: '',
      youtube: ''
    },
    collections: [],
    languages: [],
    primaryColor: '#ef4444',
    trackingCode: '',
    test_price_templates: [],
    head_tags: [],
    style: null,
    enable_search: 1,
    enable_add_to_cart: 1,
    enable_contract_form: 1,
    enable_product_name_after: 0,
    enable_custom_phone: 1,
    payment_gateway_type: '',
    always_show_order_summary: 0,
    enable_dynamic_base_cost: 0,
    order_summary_position: 'top',
    enable_distributed_checkout: 0,
    enable_payment_ssl_norton: 1,
    enable_insurance_fee: 1,
    enable_deliver_to: 1,
    enable_crisp_support: 0,
    paypal_enable_card: 0
  }),

  getters: {
    getStoreHeadTags(state) {
      return state.head_tags.reduce((returnObj: { [key in HeadTag as string]: HeadTag[] }, tag) => {
        if (['script_src', 'script', 'style'].includes(tag.tag)) {
          return returnObj // skip if tag matches type
        }

        if (!Object.hasOwnProperty.call(returnObj, tag.position)) {
          returnObj[tag.position] = []
        }

        returnObj[tag.position].push(tag)

        return returnObj
      }, ({}))
    },
    isShowFilterBox1(state) {
      return state.enable_search && state.store_type !== 'express_listing'
    }
  },

  actions: {
    update(data: object) {
      if (data.theme === 'vintage') {
        data.default_color = '#f1641e' // overrides default color
      }
      if (data.theme === 'basic') {
        data.default_color = '#ff0000' // overrides default color
      }
      Object.assign(this, data)

      // TODO: COMMENT THIS ON PRODUCTION
      // Object.assign(this, {
      //   ...data,
      //   theme: 'givehug'
      // })
    }
  }
})
