import process from 'node:process'
import { defineStore } from 'pinia'
import { DOMAIN_MARKET_PLACE } from '~/utils/constant'

// Helper function to check if we're in development/non-production environment
function isNonProductionEnv(): boolean {
  // Check if we're on client side and can access window
  if (typeof window !== 'undefined') {
    const domain = window?.location?.hostname || ''
    // Check for common development domains
    return domain === 'localhost' || domain.includes('.dev.') || domain.includes('.test.')
  }

  if (typeof process !== 'undefined' && process.env) {
    return process.env.NUXT_PUBLIC_APP_ENV !== 'production'
  }

  // For server-side, we'll default to false (production-like behavior)
  // since we can't safely access process.env in this context
  return false
}

export const useCampaignStore = defineStore('campaignStore', {
  state: (): {
    campaignList: { [index: string]: Promise<Campaign | undefined> | Campaign | undefined }
    productStatsList: { [index: string]: Promise<ProductStats | undefined> | ProductStats | undefined }
    campaignPromotionList: { [index: string]: Promise<Promotion[] | undefined> | Promotion[] | undefined }
    bundleProductList: { [index: string]: Promise<BundleProductResponse | undefined> | BundleProductResponse | undefined }
    campaignPersonalizeList: { [index: string]: Promise<PersonalizeResponse | undefined> | PersonalizeResponse | undefined }
    modalCampaignUrl: string
    productsSimilar: { [key: string]: Promise<SimilarProducts[] | undefined> | SimilarProducts[] | undefined }
    productVariants: { [index: number]: Variant[] }
  } => ({
    campaignList: {},
    productStatsList: {},
    campaignPromotionList: {},
    bundleProductList: {},
    campaignPersonalizeList: {},
    modalCampaignUrl: '',
    productsSimilar: {},
    productVariants: {}
  }),

  actions: {
    async getCampaignBySlug(campaignSlug: Slug): Promise<Campaign | undefined> {
      if (!this.campaignList[campaignSlug]) {
        // this.campaignList[campaignSlug] = $fetchDefault<ResponseData<Campaign>>(`${$api.API_GET_CAMPAIGN}/${campaignSlug}`).then((result) => {
        //   if (result.data?.id) {
        //     return result.data
        //   }
        //   if (result.message === 'blocked') {
        //     return { status: 'blocked' }
        //   }
        //   return {}
        // })

        // this.campaignList[campaignSlug] = await this.campaignList[campaignSlug]
        const { $fetchDefault } = useNuxtApp()

        const campaignInfo = await $fetchDefault<ResponseData<Campaign>>(`${$api.API_GET_CAMPAIGN}/${campaignSlug}`).then((result) => {
          if (result.data?.id) {
            return result.data
          } if (result.message === 'blocked') {
            return { status: 'blocked' }
          }
          return {}
        })

        this.campaignList[campaignSlug] = campaignInfo
        if (campaignInfo.current_product_id && campaignInfo.variants && !this.productVariants[campaignInfo.current_product_id]) {
          const productId = campaignInfo.current_product_id
          const variants = campaignInfo.variants
          this.productVariants[productId] = variants
        }
      }

      return await this.campaignList[campaignSlug]
    },

    async getProductStats(campaignId: number): Promise<ProductStats | undefined> {
      if (!this.productStatsList[campaignId]) {
        const { $fetchDefault } = useNuxtApp()
        this.productStatsList[campaignId] = $fetchDefault(`${$api.API_GET_PRODUCT_STATS}`, {
          method: $method.post,
          body: {
            campaign_id: campaignId
          }
        }).then((result) => {
          const { success, data } = result as ResponseData<Array<{ type: 'add_to_cart' | 'visit', count: number }>>
          if (success && data) {
            const object: ProductStats = {
              add_to_cart: 0,
              visit: 0
            }

            data.forEach((item) => {
              object[item.type] = item.count
            })

            return object
          }
          return {}
        })
        this.productStatsList[campaignId] = await this.productStatsList[campaignId]
      }
      return await this.productStatsList[campaignId]
    },

    async getPromotion(campaignIds: number | Array<number>): Promise<Promotion[] | undefined> {
      const { $fetchDefault } = useNuxtApp()
      const ids = campaignIds.toString()
      if (!this.campaignPromotionList[ids] || Object.keys(this.campaignPromotionList[ids]).length === 0) {
        this.campaignPromotionList[ids] = $fetchDefault<ResponseData<Promotion[]>>(`${$api.API_GET_PROMOTION}?campaign_ids[]=${ids}`)
          .then((result) => {
            if (result.data) {
              return result.data
            }
          })
      }
      return await this.campaignPromotionList[ids]
    },

    async getBundleProduct(campaignIds: number | Array<number>, productIds?: Array<number>, viewPlace?: number, allowReFetchBd = false, bundleIds = []): Promise<BundleProductResponse | undefined> {
      const { $fetchDefault } = useNuxtApp()
      const ids = campaignIds.toString()
      if (!this.bundleProductList[ids] || allowReFetchBd) {
        let getBundleDiscountAPI = `${$api.API_GET_BUNDLE_DISCOUNT}?campaign_ids[]=${ids}`
        if (viewPlace) {
          getBundleDiscountAPI = `${getBundleDiscountAPI}&position=${viewPlace}`
        }
        if (productIds && productIds.length > 0) {
          productIds = Array.from(new Set(productIds))
          getBundleDiscountAPI = `${getBundleDiscountAPI}&product_ids[]=${productIds.toString()}`
        }

        if (bundleIds && bundleIds.length > 0) {
          bundleIds = Array.from(new Set(bundleIds))
          getBundleDiscountAPI = `${getBundleDiscountAPI}&bundle_ids[]=${bundleIds.toString()}`
        }
        this.bundleProductList[ids] = $fetchDefault<ResponseData<BundleProductResponse>>(`${getBundleDiscountAPI}`).then((result) => {
          if (result.data) {
            return result.data
          }
        })
        this.bundleProductList[ids] = await this.bundleProductList[ids]
      }
      return await this.bundleProductList[ids]
    },

    async getCustomDesign(campaignSlug: Slug): Promise<PersonalizeResponse | undefined> {
      const { $fetchDefault } = useNuxtApp()
      if (!this.campaignPersonalizeList[campaignSlug]) {
        this.campaignPersonalizeList[campaignSlug] = $fetchDefault<ResponseData<PersonalizeResponse>>(`${$api.API_GET_CAMPAIGN}/${campaignSlug}/custom-designs`).then((result) => {
          if (result.data) {
            return result.data
          }
        })

        this.campaignPersonalizeList[campaignSlug] = await this.campaignPersonalizeList[campaignSlug]
      }
      return await this.campaignPersonalizeList[campaignSlug]
    },

    async getProductsSimilar(campaignId: number): Promise<SimilarProducts[] | undefined> {
      const { $fetchDefault } = useNuxtApp()
      if (!this.productsSimilar[campaignId]) {
        this.productsSimilar[campaignId] = $fetchDefault<ResponseData<SimilarProducts[]>>(`${$api.API_PRODUCTS_SIMILAR}?id=${campaignId}`).then((result) => {
          if (result.data) {
            return result.data
          }
        })

        this.productsSimilar[campaignId] = await this.productsSimilar[campaignId]
      }
      return await this.productsSimilar[campaignId]
    },

    async getVariantsByProductId(productId: number, sellerId: number): Promise<Variant[]> {
      if (!this.productVariants[productId]) {
        let url = `${$api.API_GET_PRODUCT_VARIANTS}/${productId}`
        const domain = window?.location?.hostname || ''
        if (isNonProductionEnv() || DOMAIN_MARKET_PLACE.includes(domain)) {
          url += `?seller_id=${sellerId}`
        }
        const { $fetchDefault } = useNuxtApp()
        const response = await $fetchDefault<ResponseData<Variant[]>>(url)

        if (response.success && response.data) {
          this.productVariants[productId] = response.data
        }
        else {
          this.productVariants[productId] = []
        }
      }
      return this.productVariants[productId]
    },

    resetProductVariants() {
      this.productVariants = {}
    }

  },
  persist: {
    storage: persistedState.localStorage,
    paths: ['productVariants']
  }
})
