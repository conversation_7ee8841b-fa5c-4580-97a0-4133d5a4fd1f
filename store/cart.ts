import {defineStore} from 'pinia'
import {useComboCartStore} from "~/store/comboCart";
import { useUserSession } from "~/store/userSession";

interface CartStore {
  listCartItem: Array<CartItem>,
  token: string,
  discountCode: string
}

export const useCartStore = defineStore('cartStore', {
  state: (): CartStore => ({
    listCartItem: [],
    token: '',
    discountCode: ''
  }),
  getters: {
    getTotalQuantity (): number {
      return this.listCartItem.reduce((previousValue, currentCartItem) => previousValue + currentCartItem.quantity, 0)
    },
    getTotalPrice (): number {
      const { $formatPriceNoUnit } = useNuxtApp()
      const campInComboIds = useComboCartStore().list.map(combo => combo.product_ids).flat()
      const normalCampaignTotalPrice = this.listCartItem
        .filter(item => !campInComboIds.includes(item.product_id))
        .reduce((previousValue, currentCartItem) => {
        let price = (currentCartItem.variantPrice || currentCartItem.price || 0) + (currentCartItem.extra_custom_fee || 0)
        if (currentCartItem.campaignBundleId && this.listCartItem.find(cartItem => currentCartItem.campaignBundleId === cartItem.campaign_id && currentCartItem.productBundleId === cartItem.product_id)) {
          price = price * (100 - (currentCartItem?.promotion?.discount_percentage || 0)) / 100
        }
        price = parseFloat(String($formatPriceNoUnit(price, currentCartItem.currency_code, USD_CODE, false)))
        return previousValue + price * currentCartItem.quantity
      }
      , 0)

      const { $getDynamicBaseCostIndex } = useNuxtApp()
      let totalDynamicBaseCost = 0
      const user = useUserSession()
      useComboCartStore().list.forEach((combo) => {
        const productIdInCombo = combo.items[0].campaign_product_ids
        const isEnoughCombo = productIdInCombo.length === combo.items.length
        if (isEnoughCombo) {
          const needRecalculateDynamicBaseCost = user.countryCode === combo.country
          const dynamicBaseCost = needRecalculateDynamicBaseCost ? combo.products.reduce((acc, product) => {
            return acc + $getDynamicBaseCostIndex(product)
          }
          , 0) : 0

          totalDynamicBaseCost += dynamicBaseCost * combo.quantity
        }
      })

      const comboCampaignTotalPrice = useComboCartStore().list.reduce((total, combo) => {
        return total + $comboPrice(combo) * combo.quantity + totalDynamicBaseCost
      }, 0)

      return normalCampaignTotalPrice + comboCampaignTotalPrice
    },
    totalCustomOptionFee (): number {
      return this.listCartItem.reduce((previousValue, currentCartItem) => previousValue + (currentCartItem.extra_custom_fee || 0), 0)
    },
  },
  actions: {
    addCartItem (campaign: Campaign, {
      currentOptions,
      currentProduct,
      currentVariant,
      quantity,
      imagesList,
      optionListFull
    }: UserCampaignOption, {
      comboId,
      isCombo,
      campaignBundleId,
      promotion,
      totalCustomOptionFee = 0,
      userCustomOptions = [],
      groupCustomOptionsQuantity = 1,
      productBundleId = undefined
    }: {
      comboId?: string,
      isCombo?: boolean,
      campaignBundleId?: number,
      promotion?: BundlePromotion,
      totalCustomOptionFee?: number,
      userCustomOptions?: Array<Array<PersonalizeCustomOptionsItem>>,
      groupCustomOptionsQuantity?: number,
      productBundleId?: number|undefined
    } = {}, isCheckQuantity: boolean) {
      const { $getRawPrice } = useNuxtApp()
      let cartItemId = ''
      let cartItem: CartItem | undefined
      if (!campaign.personalized && !isCombo) {
        cartItem = this.listCartItem.find(cartItem => {
          const options = {...cartItem.options}
          Object.keys(options).forEach((key) => {
            if (options[key]?.startsWith('__')) {
              delete options[key]
            }
          })
          return cartItem.campaign_id === campaign.id && cartItem.product_id === currentProduct?.id && shallowEqual(options, currentOptions)
        })
      }
      if (currentProduct && currentProduct.full_printed === 5) {
        cartItem = undefined
      }
      if (cartItem) {
        cartItemId = cartItem.id
        cartItem.quantity += quantity || 0
        cartItem.isCheckQuantity = isCheckQuantity
      } else {
        cartItemId = `${Date.now()}_${this.listCartItem.length}`

        const params = new URLSearchParams()
        const formatOptions: { [key: string]: string } = {}
        if (currentOptions) {
          let defaultOptionList: OptionsList = {}

          if (typeof currentProduct?.options === 'object') {
            defaultOptionList = currentProduct?.options
          }

          if (typeof currentProduct?.options === 'string') {
            defaultOptionList = JSON.parse(currentProduct?.options as string)
          }

          Object.keys(defaultOptionList).forEach((item) => {
            if (defaultOptionList[item].length >= 2) {
              params.set(item, currentOptions[item])
              formatOptions[item] = currentOptions[item]
            } else {
              formatOptions[item] = `__${defaultOptionList[item][0]}`
            }
          })
        }
        cartItem = {
          id: cartItemId,
          combo_id: comboId,
          campaign_id: campaign.id as number,
          campaign_title: campaign.name as string,
          campaign_slug: campaign.slug as string,
          campaign_system_type: campaign.system_type as string,
          combo_price: campaign.combo_price as number,
          campaign_thumb_url: campaign.thumb_url as string,
          campaign_product_ids: campaign.products?.map(product => product.id) as number[] ?? [],
          product_id: currentProduct?.id as number,
          product_name: currentProduct?.name as string,
          thumb_url: currentProduct?.thumb_url || (imagesList && imagesList[0]?.file_url),
          currency_code: currentProduct?.currency_code,
          optionList: optionListFull,
          options: formatOptions,
          price: useTestPrice().getPrice(currentProduct),
          variantPrice: useTestPrice().getPrice(currentProduct, currentVariant),
          quantity,
          personalized: campaign.personalized,
          isCheckQuantity,
          full_printed: currentProduct?.full_printed,
          template_id: currentProduct?.template_id as number,

          campaignBundleId,
          promotion,
          via: campaign.seller?.via,
          seller_id: campaign.seller_id,

          extra_custom_fee: totalCustomOptionFee,

          customer_custom_options: (userCustomOptions.filter((item, index) => index < groupCustomOptionsQuantity) as any),
          productBundleId
        }

        if (campaign.personalized === 1) {
          cartItem.designs = {}
          const customOptions: { [key: string]: string } = {}
          if (currentProduct?.personalizeList?.length === 1) {
            const currentDesign = currentProduct.personalizeList[0]
            cartItem.designs[currentDesign.customDesign!.print_space] = JSON.stringify(currentDesign.designCanvas.exportJson())
          } else {
            currentProduct?.personalizeList?.forEach((personalize) => {
              const size = (cartItem as CartItem).options.size || 'default' // resolve edge case: ornaments can't add to cart
              const printSpace = personalize.customDesign?.print_space
              // printSpace && size.startsWith(printSpace): chỉ lấy design của size đang được chọn
              if (printSpace && (size.startsWith(printSpace) || size === 'default') && cartItem?.designs) {
                cartItem.designs[personalize.customDesign?.print_space as string] = JSON.stringify(personalize.designCanvas.exportJson())
              }
            })
          }
          currentProduct?.customItemList?.forEach((object) => {
            if (object.data.type === 'i-text') {
              const customText = object as CustomTextItem
              customOptions[customText.data.name || 'custom text'] = customText.data.text as string
              params.set(customText.data.name?.replaceAll(' ', '-') || 'custom text', customText.data.text as string)
            }
            if (object.data.type === 'image' && object.data.isCustom) {
              const customImage = object as CustomImageItem
              customOptions.customImage = customImage.currentFileUploadUrl as string
              params.set('custom-image', customImage.currentFileUploadUrl as string)
            }
            if (currentProduct.customDesignType) {
              customOptions.design = currentProduct.selectedCustomDesign as string
            }
          })
          cartItem.custom_options = customOptions
        }

        if (campaign.personalized === 2 && currentProduct?.personalizeList?.length) {
          const { pbCustomInfo = {}, pbPrintUrl } = currentProduct.personalizeList[0]
          Object.entries(pbCustomInfo as Object).forEach(([key1, value1]) => {
            Object.entries(value1 as Object).forEach(([key2, value2]) => {
              if (value2.base64 !== undefined) {
                value2.base64 = ''
              }
              value1[key2] = value2
            })
            pbCustomInfo[key1] = value1
          })

          cartItem!.pbCustomInfo = pbCustomInfo
          cartItem!.pbPrintUrl = pbPrintUrl
        }

        if ((campaign.personalized === 3  || currentProduct?.full_printed === 5) && userCustomOptions?.length) {
          params.set('groups', userCustomOptions?.length.toString())
          userCustomOptions.forEach((groupOptions, groupNumber) => {
            groupOptions.forEach((customOption, optionIndex) => {
              const key = `${customOption.type}_${groupNumber + 1}_${optionIndex}`
              params.set(key, customOption.value as string)
            })
          })
        }
        cartItem!.product_url = `/${campaign.slug}/${stringHelperToSlug(currentProduct?.name)}?${params.toString()}`
        if (cartItem?.promotion?.discount_code) {
          this.setDiscountCode(cartItem.promotion.discount_code)
        }
        this.listCartItem.push(cartItem)
      }

      const totalPrice = $getRawPrice(((cartItem.variantPrice || cartItem.price) + (cartItem.extra_custom_fee || 0)) * (Number(quantity) || 1), cartItem.currency_code, 'USD')
      let trackData: {
        content_ids: number[],
        content_name: string,
        content_category: string,
        content_value: number[],
        content_quantity: number[],
        content_type: string,
        num_items: number,
        currency: string,
        value: number,
        campaignSlug: string | undefined,
        campaignId: number | undefined,
        klaviyoData?: any
      } = {
        content_ids: [cartItem.product_id],
        content_name: cartItem.campaign_title,
        content_category: cartItem.product_name,
        content_value: [totalPrice],
        content_quantity: [Number(quantity) || 1],
        content_type: 'product',
        num_items: Number(quantity) || 1,
        currency: 'USD',
        value: totalPrice,
        campaignSlug: campaign.slug,
        campaignId: campaign.id,
      }

      const klaviyoData = buildKlaviyoData(cartItem, this.listCartItem)
      if (klaviyoData) {
        trackData = {
          ...trackData,
          klaviyoData
        }
      }

      useTracking().trackEvent({
        event: 'add_to_cart',
        data: trackData
      })
      return cartItem
    },
    updateCartItem (cartItem: CartItem, newData: object) {
      if (cartItem && newData) {
        Object.assign(cartItem, newData)
      }
    },
    updateCartItemByID (cartItemID: string, newData: object) {
      const cartItem = this.listCartItem.find(item => item.id === cartItemID)
      if (cartItem && newData) {
        Object.assign(cartItem, newData)
      }
    },
    duplicateCartItem (index: number, comboId = undefined as string | undefined) {
      const newCartItem = _cloneDeep(this.listCartItem[index]) as CartItem
      newCartItem.id = `${Date.now()}_${this.listCartItem.length}`
      newCartItem.quantity = 1
      newCartItem.combo_id = comboId
      this.listCartItem.splice((index + 1), 0, newCartItem)
      return newCartItem
    },
    removeCartItem (index: number) {
      this.listCartItem.splice(index, 1)
    },
    setDiscountCode (code: string) {
      this.discountCode = code
    },
    resetCart () {
      this.listCartItem = []
    }
  },
  persist: {
    storage: persistedState.localStorage
  },
  share: {
    enable: true,
    initialize: true,
  }
})

function shallowEqual (object1: any, object2: any) {
  const keys1 = Object.keys(object1)
  const keys2 = Object.keys(object2)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (object1[key] !== object2[key]) {
      return false
    }
  }

  return true
}


// Build Klaviyo Data
function buildKlaviyoData (cartItem: CartItem, listCartItem: any) {
  try {
    const itemId = cartItem.id || cartItem.product_id;
    const itemName = cartItem.campaign_title || cartItem.product_name;
    const itemURL = cartItem.product_url || '';
    const itemImageURL = cartItem.thumb_url || '';
    const itemPrice = cartItem.price || 0;
    const itemQuantity = cartItem.quantity || 0;
    const checkoutURL = '/checkout';
    const categories = cartItem.product_name ? [cartItem.product_name] : [];

    const items = listCartItem.map((item: any) => {
      return {
        item_id: item.id || item.product_id,
        item_name: item.campaign_title || item.product_name,
        item_url: item.product_url || '',
        item_image_url: item.thumb_url || '',
        item_price: item.price || 0,
        item_quantity: item.quantity || 0,
        categories: item.product_name ? [item.product_name] : [],
      }
    });
    let totalPrice = 0;
    for (const item of items) {
      totalPrice += item.item_price * item.item_quantity;
    }
    return {
      item_id: itemId,
      item_name: itemName,
      item_url: itemURL,
      item_image_url: itemImageURL,
      item_price: itemPrice,
      item_quantity: itemQuantity,
      categories: categories,
      checkout_url: checkoutURL,
      items: items,
      $value: totalPrice,
    }
  } catch (error) {
    console.error('Error in buildKlaviyoData', error)
    return {}
  }
}
