<script lang="ts" setup>
import { useCartStore } from '~~/store/cart'
// import { useStoreInfo } from '~/store/storeInfo'
const { $i18n, $fetchWrite } = useNuxtApp()
const route = useRoute()
const localePath = useLocalePath()

useHead({ title: $i18n.t('Verifying payment status...') })
definePageMeta({
  layout: 'empty'
})

onMounted(async () => {
  const { orderToken, token, PayerID, paymentId, region } = route.query
  let redirectUrl = `/order/thank-you/${orderToken}`

  try {
    if (!orderToken) {
      throw new Error('orderToken not found')
    }
    if (!token || !PayerID || !paymentId) {
      throw new Error('token or PayerID or paymentId not found')
    }

    const endpoint = `/public/order/callback/paypal/${orderToken}?token=${token}&payerId=${PayerID}&transactionReference=${paymentId}&region=${region}`
    const { success, message } = await $fetchWrite<ResponseData<any>>(endpoint)

    if (!success) {
      throw new Error(message)
    }
  }
  catch (err) {
    if ((err as Error).message.includes('orderToken')) {
      const { token } = useCartStore()
      if (token && token.length) {
        // const {enable_distributed_checkout} = useStoreInfo()
        // const apiExceptionLog = useRuntimeConfig()?.public?.appEnv === 'dev' ? (enable_distributed_checkout === 1 ? $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION : $api.API_CHECKOUT_LOG_EXCEPTION) : $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION
        const apiExceptionLog = $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION

        await $fetchWrite(apiExceptionLog, {
          method: $method.post,
          body: {
            message: 'Log:: Order token is missing',
            token
          }
        }).then(() => {
          redirectUrl = `/checkout/${token}`
        })
      }
    }
    else {
      redirectUrl = '/cart'
    }
  }

  if (redirectUrl.startsWith('/order/thank-you')) {
    useCartStore().resetCart()
  }
  window.location.href = localePath(redirectUrl)
})
</script>

<template>
  <div class="mt-12" align="center">
    <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
    <h1 class="font-medium text-2xl">
      {{ $t('Verifying payment status...') }}
    </h1>
  </div>
</template>
