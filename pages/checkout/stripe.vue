<script lang="ts" setup>
import { useCartStore } from '~~/store/cart'

const { $i18n } = useNuxtApp()
const route = useRoute()
const localePath = useLocalePath()
const { $fetchWrite } = useNuxtApp()

useHead({
  title: $i18n.t('Verifying payment status...'),
  script: [{
    hid: 'stripe-js',
    src: 'https://js.stripe.com/v3/',
    defer: true
  }]
})
definePageMeta({
  layout: 'empty'
})

onMounted(async () => {
  const {
    order_token: orderToken,
    payment_intent_client_secret: clientSecret,
    publishable_key: publishableKey
  } = route.query
  let redirectUrl = `/order/thank-you/${orderToken}`

  try {
    if (!orderToken) {
      throw new Error('orderToken not found')
    }
    if (!clientSecret || !publishableKey) {
      throw new Error('clientSecret or publlishableKey not found')
    }

    const stripe = window.Stripe(publishableKey)

    await stripe.retrievePaymentIntent(clientSecret).then(async (response: any) => {
      const intent = response.paymentIntent
      if (!intent) {
        throw new Error('No intent data found')
      }
      if (intent.status !== 'succeeded' && intent.status !== 'processing') {
        throw new Error('intent status is not "succeeded" or "processing"')
      }

      // if processing will update pending order
      if (intent.status === 'processing') {
        const endpoint = `/public/order/callback/stripe/${orderToken}?token=${orderToken}&transactionReference=${intent.id}`
        const { success, message } = await $fetchWrite<ResponseData<{}>>(endpoint)

        if (!success) {
          throw new Error(message)
        }
      }
    })
  }
  catch (err) {
    redirectUrl = ((err as Error).message.includes('orderToken')) ? '/' : `/checkout/${orderToken}`
  }

  if (redirectUrl.startsWith('/order/thank-you')) {
    useCartStore().resetCart()
  }
  window.location.href = localePath(redirectUrl)
})
</script>

<template>
  <div class="mt-12" align="center">
    <common-loading-dot variant="bg-black !w-[20px] !h-[20px] !m-[9px]" />
    <h1 class="font-medium text-2xl">
      {{ $t('Verifying payment status...') }}
    </h1>
  </div>
</template>
