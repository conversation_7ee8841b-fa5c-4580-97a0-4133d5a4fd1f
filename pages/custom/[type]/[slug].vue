<script setup lang="ts">
const name = storeInfo().name
createSEOMeta({
  title: name
})

const { customPageData } = await usePageCustom()
</script>

<template>
  <main id="custom-page" class="container py-4">
    <div class="content-page content-text mt-6 mb-6">
      <div class="row">
        <div class="col-12">
          <h1 class="h3 page-title mb-3">
            {{ customPageData.title }}
          </h1>
          <div class="mb-3" v-html="customPageData.description" />
          <div class="mb-3" v-html="customPageData.content" />
        </div>
      </div>
      <div
        v-if="customPageData.link_json && customPageData.link_json.length"
        class="flex flex-wrap gap-3"
      >
        <common-collection-item
          v-for="(collection, index) in customPageData.link_json"
          :key="index"
          :value="collection.name"
          :slug="collection.slug"
        />
      </div>
      <div
        v-if="customPageData.data_json && customPageData.data_json.length"
        class="grid gird-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4 mt-5"
      >
        <default-common-product-item
          v-for="(product, pIdx) in customPageData.data_json"
          :key="pIdx"
          :product="product"
          class="col-6 col-lg-3 mb-3"
        />
      </div>
    </div>
  </main>
</template>
