export function $getPromotionGivehugStringRule(promotion: Promotion) {
  const { $formatPrice } = useNuxtApp()
  const rules = JSON.parse(promotion.rules || '{}')
  let stringRule = ''
  if (promotion.type === 'PD' && (rules.minimum_amount || rules.minimum_quantity)) {
    if (rules.minimum_amount) {
      stringRule += `Spend ${$formatPrice(rules.minimum_amount)}`
    }
    else if (rules.minimum_quantity) {
      stringRule += `Buy ${rules.minimum_quantity}`
    }

    stringRule += ' get '

    if (rules.discount_percentage) {
      stringRule += ` ${rules.discount_percentage}%`
    }
    if (rules.discount_amount) {
      stringRule += ` ${$formatPrice(rules.discount_amount)}`
    }

    return `${stringRule} Off`
  }

  return null
}

export function $getFixedCollectionData() {
  return [
    {
      color: '216, 76, 111'
    },
    {
      color: '40, 86, 153'
    },
    {
      color: '232, 126, 28'
    },
    {
      color: '27, 156, 155'
    },
    {
      color: '126, 87, 194'
    },
    {
      color: '76, 175, 80'
    }
  ]
}

export function $slugToName(slug: string) {
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
