export function stringHelperToKey (name?:string) {
  name = stringHelperToSlug(name)
  name = name.replace(/[^a-zA-Z0-9]/g, '')
  return name
}

export function stringHelperToSlug (title:any) {
  if (!title) {
    return ''
  }
  let slug = ''
  // Change to lower case
  const titleLower = title.toLowerCase()
  // Letter "e"
  slug = titleLower.replace(/e|é|è|ẽ|ẻ|ẹ|ê|ế|ề|ễ|ể|ệ/gi, 'e')
  // Letter "a"
  slug = slug.replace(/a|á|à|ã|ả|ạ|ă|ắ|ằ|ẵ|ẳ|ặ|â|ấ|ầ|ẫ|ẩ|ậ/gi, 'a')
  // Letter "o"
  slug = slug.replace(/o|ó|ò|õ|ỏ|ọ|ô|ố|ồ|ỗ|ổ|ộ|ơ|ớ|ờ|ỡ|ở|ợ/gi, 'o')
  // Letter "u"
  slug = slug.replace(/u|ú|ù|ũ|ủ|ụ|ư|ứ|ừ|ữ|ử|ự/gi, 'u')
  // Letter "d"
  slug = slug.replace(/đ/gi, 'd')
  // Trim the last whitespace
  slug = slug.replace(/\s*$/g, '')
  // Change whitespace to "-"
  slug = slug.replace(/\s+/g, '-')

  return slug
}

export function capitalize (string:string) {
  return string[0].toUpperCase() + string.substr(1)
}

export function capitalizeWords (str:string) {
  if (!str || typeof str !== 'string') {
    return ''
  }
  const arr = str.split(' ')
  return arr.map((element) => {
    return element.charAt(0).toUpperCase() + element.slice(1).toLowerCase()
  }).join(' ')
}

function hex (buffer:ArrayBufferLike) {
  let digest = ''
  const view = new DataView(buffer)
  for (let i = 0; i < view.byteLength; i += 4) {
    // We use getUint32 to reduce the number of iterations (notice the `i += 4`)
    const value = view.getUint32(i)
    // toString(16) will transform the integer into the corresponding hex string
    // but will remove any initial "0"
    const stringValue = value.toString(16)
    // One Uint32 element is 4 bytes or 8 hex chars (it would also work with 4
    // chars for Uint16 and 2 chars for Uint8)
    const padding = '00000000'
    const paddedValue = (padding + stringValue).slice(-padding.length)
    digest += paddedValue
  }

  return digest
}

export function sha256 (str:string) {
  str = str.toLowerCase()
  // Get the string as arraybuffer.
  const buffer = new TextEncoder().encode(str)
  return crypto.subtle.digest('SHA-256', buffer).then(function (hash) {
    return hex(hash)
  })
}

export function flatWord (word = '') {
  // eslint-disable-next-line no-useless-escape
  return word.replace(/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~\W]/g, '').toLowerCase()
}

export function splitName (name = '') {
  const [firstName, ...lastName] = name.split(' ').filter(Boolean)
  return {
    firstName,
    lastName: lastName.join(' ')
  }
}

export function capitalizeFirstLetter (string:string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

export function isValidEmail (addr: string) {
  // src: https://github.com/vuelidate/vuelidate/blob/next/packages/validators/src/raw/email.js
  // eslint-disable-next-line
  return /^(?:[A-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[A-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9]{2,}(?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])$/i
    .test(addr)
}

export function getCookie (cookieName?:string, stringCookie?:string) {
  if (!stringCookie) { return null }
  const strCookie = new RegExp('' + cookieName + '[^;]+').exec(stringCookie)
  return strCookie ? strCookie.toString().replace(/^[^=]+./, '') : null
}
