import { useGeneralSettings } from '~/store/generalSettings'
import { useUserSession } from '~/store/userSession'
import { PRICING_MODE_TYPE, USD_CODE } from '~/utils/constant'

export function $comboPrice(combo: ComboItem, countQuantity = false): number {
  const productIdInCombo = combo.items[0].campaign_product_ids
  const isEnoughCombo = productIdInCombo.length === combo.items.length
  if (!isEnoughCombo) {
    return combo.items.reduce((acc, item) => {
      if (!item.variantPrice) {
        return acc
      }
      return acc + item.variantPrice
    }, 0)
  }

  const diff = combo.items.reduce((acc, item) => {
    if (!item.variantPrice) {
      return acc
    }
    return (acc + item.variantPrice - item.price) * (countQuantity ? item.quantity : 1)
  }, 0)

  return combo.combo_price * (countQuantity ? combo.quantity : 1) + diff
}

/**
 * Rounds a number to the nearest half
 */
export function roundToHalf(number: number): number {
  const response = Math.ceil(Math.abs(number) * 2) / 2
  if (number < 0) {
    return -1 * response
  }
  return response
}

/**
 * Gets the base cost of a template depending on user country
 */
export function getBaseCostOfTemplateDependOnUserCountry(
  generalCountries: Array<Country>,
  templateBaseCosts: Array<Variant>,
  countryCodeGetting?: string
): Variant | null {
  if (countryCodeGetting === null || generalCountries.length === 0 || templateBaseCosts.length === 0) {
    return null
  }

  let baseCostOfTemplate = templateBaseCosts.find((variant) => {
    return variant.location_code === countryCodeGetting
  })

  if (baseCostOfTemplate) {
    return baseCostOfTemplate
  }

  const generalCountryInfo = generalCountries.find((country) => {
    return country.code === countryCodeGetting
  })

  if (generalCountryInfo === undefined) {
    return null
  }

  baseCostOfTemplate = templateBaseCosts.find((variant) => {
    return variant.location_code === generalCountryInfo.region_code
  })

  if (baseCostOfTemplate) {
    return baseCostOfTemplate
  }

  baseCostOfTemplate = templateBaseCosts.find((variant) => {
    return variant.location_code === '*'
  })

  if (baseCostOfTemplate) {
    return baseCostOfTemplate
  }

  return null
}

/**
 * Formats currency number without unit
 */
function formatCurrency(number: number | string, fix = 2): number {
  return number ? Number(Number.parseFloat(number as string).toFixed(fix)) : 0
}

/**
 * Converts price between currencies
 */
function convertPrice(
  value: number | string,
  getCurrencyByCode: (code: CurrencyCode) => Currency | undefined,
  userCurrencyCode: string,
  productCurrencyCode?: CurrencyCode,
  convertCode?: CurrencyCode,
  allowNoConvert: boolean = true
) {
  if (typeof value === 'string') {
    value = value.replace(/,/g, '.')
  }
  value = Number.parseFloat(value.toString())

  if (productCurrencyCode && allowNoConvert === true && userCurrencyCode && userCurrencyCode === productCurrencyCode) {
    const currency = getCurrencyByCode(userCurrencyCode)
    return { value, currency }
  }

  // convert other currency to default(USD)
  if (productCurrencyCode) {
    const productRate = getCurrencyByCode(productCurrencyCode)?.rate || 1
    value = value / productRate
  }

  // display store currency
  let currency: Currency | undefined

  if (convertCode) {
    currency = getCurrencyByCode(convertCode)
  }
  else {
    currency = getCurrencyByCode(userCurrencyCode)
  }

  value = value * (currency?.rate || 1)

  const VND_CODE = 'VND' // Import this if needed from constants
  if (currency?.code === VND_CODE) {
    value = Math.round(value / 1000) * 1000
  }

  return { value, currency }
}

/**
 * Formats price without currency unit
 */
function formatPriceNoUnit(
  value: string | number,
  getCurrencyByCode: (code: string) => Currency | undefined,
  userCurrencyCode: string,
  productCurrencyCode: CurrencyCode,
  convertCode: CurrencyCode,
  allowNoConvert: boolean = true
): number {
  const convertedValue = convertPrice(value, getCurrencyByCode, userCurrencyCode, productCurrencyCode, convertCode, allowNoConvert).value
  return formatCurrency(convertedValue)
}

/**
 * Calculates the dynamic base cost index for a product
 * This function was moved from plugins/3.commonFunction.ts
 */
export function getDynamicBaseCostIndex(
  product: Product
): number {
  const { clientInfo, userInfo } = useUserSession()
  const userLocation = userInfo && userInfo.country !== '' ? userInfo.country as string : clientInfo.country as string
  const { getCurrencyByCode, countries } = useGeneralSettings()
  if (storeInfo().enable_dynamic_base_cost) {
    return 0
  }

  let response = 0
  const productInfo = product

  try {
    const systemLocations = countries
    const campaignMarketLocation = productInfo.market_location
    const currentOption = productInfo.currentOptions ?? {}
    const pricingMode = productInfo.pricing_mode ?? null

    if (pricingMode && pricingMode !== PRICING_MODE_TYPE.ADJUST_PRICE) {
      return 0
    }

    const variants = productInfo.variantsList ?? []

    if (productInfo && variants) {
      const variantBaseSize = productInfo.options
        && typeof productInfo.options === 'object'
        && productInfo.options.size
        && productInfo.options.size.length > 0
        ? productInfo.options.size[0]
        : 's'

      const variantKey = `${
        currentOption.color !== undefined
          ? currentOption.color.replace(/_/g, '').replace(/ /g, '_')
          : productInfo.default_option?.replace(/_/g, '').replace(/ /g, '_')
      }-${variantBaseSize}`

      const variantsSelected = variants.filter((variant) => {
        return variant.variant_key === variantKey
      })

      const baseCostOnUserLocation = getBaseCostOfTemplateDependOnUserCountry(
        systemLocations,
        variantsSelected,
        userLocation
      )
      const baseCostOnCampLocation = getBaseCostOfTemplateDependOnUserCountry(
        systemLocations,
        variantsSelected,
        campaignMarketLocation
      )

      if (
        baseCostOnUserLocation === null
        || baseCostOnCampLocation === null
        || !baseCostOnUserLocation.base_cost
        || !baseCostOnCampLocation.base_cost
      ) {
        return response
      }

      response = roundToHalf(baseCostOnUserLocation.base_cost - baseCostOnCampLocation.base_cost)

      response = roundToHalf(
        Number(
          String(
            formatPriceNoUnit(
              response,
              getCurrencyByCode,
              userCurrencyCode,
              USD_CODE,
              productInfo.currency_code ?? USD_CODE,
              false
            )
          ).replace(/,/g, '.')
        )
      )
    }
  }
  catch (e) {
    // Handle error silently as in original implementation
  }

  return response
}
