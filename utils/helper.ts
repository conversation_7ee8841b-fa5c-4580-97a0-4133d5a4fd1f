globalThis!.global ||= window
export function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return `rgba(${Number.parseInt(result?.[1] || '0', 16)}, ${Number.parseInt(result?.[1] || '0', 16) || 0}, ${Number.parseInt(result?.[1] || '0', 16) || 0} , --tw-bg-opacity)`
}

export function copyToClipBoard(value: string) {
  const permissionName = 'clipboard-write' as PermissionName
  navigator.permissions.query({ name: permissionName })
    .then((result) => {
      if (result.state === 'granted' || result.state === 'prompt') {
        navigator.clipboard.writeText(value).then(() => {
          /* Resolved - text copied to clipboard */
        }, () => {
          /* Rejected - clipboard failed */
          oldCopyMethod(value)
        })
        return
      }

      // fallback method
      oldCopyMethod(value)
    })
}

function oldCopyMethod(text: string) {
  const el = document.createElement('textarea')
  el.value = text
  el.setAttribute('readonly', '')
  el.style.position = 'absolute'
  el.style.left = '-9999px'
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)
}

export function isLightColor(backgroundColor: string) {
  try {
    const bgColor = Number.parseInt(backgroundColor.slice(1), 16)
    const contrastWithBlack = (
      (bgColor >> 16) * 0.299
      + ((bgColor >> 8) & 0xFF) * 0.587
      + (bgColor & 0xFF) * 0.114
    )

    return contrastWithBlack > 178
  }
  catch (e) {
    console.error(e)
    return false
  }
}

export function adjustHexColor(hex: string, percent?: number) {
  try {
    const color = Number.parseInt(hex.slice(1), 16)
    if (!percent) {
      return hex
    }
    if (percent < 0) {
      const red = Math.round((color >> 16) * ((100 + percent) / 100))
      const green = Math.round(((color >> 8) & 255) * ((100 + percent) / 100))
      const blue = Math.round((color & 255) * ((100 + percent) / 100))
      return `#${(blue | (green << 8) | (red << 16)).toString(16).padStart(6, '0')}`
    }
    if (percent > 0) {
      const red = Math.round((color >> 16) * ((100 - percent) / 100) + 255 * percent / 100)
      const green = Math.round(((color >> 8) & 255) * ((100 - percent) / 100) + 255 * percent / 100)
      const blue = Math.round((color & 255) * ((100 - percent) / 100) + 255 * percent / 100)
      return `#${(blue | (green << 8) | (red << 16)).toString(16).padStart(6, '0')}`
    }
  }
  catch (e) {
    console.error(e)
    return '#333333'
  }
}

export function _cloneDeep(obj: any) {
  if (typeof obj !== 'object' || obj === null) {
    return obj
  }
  const clone: any = Array.isArray(obj) ? [] : {}

  for (const key in obj) {
    // eslint-disable-next-line no-prototype-builtins
    if (obj.hasOwnProperty(key)) {
      clone[key] = _cloneDeep(obj[key])
    }
  }

  return clone
}

export function _union(...args: any[]) {
  if (!args.length) {
    return []
  }
  const filter = typeof args[args.length - 1] === 'function' ? args.pop() as Function : undefined
  const array: any[] = []
  args.forEach((item: any) => {
    if (Array.isArray(item)) {
      array.push(...item)
    }
    else {
      array.push(item)
    }
  })
  return array.filter((item: any, index: any) => {
    return array.findIndex((item2: any) => {
      return filter ? filter(item, item2) : item === item2
    }) === index
  })
}

export function $getProductUrl(url?: string) {
  if (!url) {
    return {}
  }
  const parseUrl = url.split('?')
  const query: { [index: string]: string } = {}
  parseUrl[1].split('&').forEach((queryItem) => {
    const parseItem = queryItem.split('=')
    query[parseItem[0]] = parseItem[1]
  })
  return {
    path: parseUrl[0],
    query
  }
}

export function $shallowEqual(object1: any, object2: any) {
  const keys1 = Object.keys(object1)
  const keys2 = Object.keys(object2)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (object1[key] !== object2[key]) {
      return false
    }
  }

  return true
}

export function $getCampaignSubTitle(productName: string, options: {
  optionList: OptionsList
  currentOptions: {
    [key: string]: string
  }
}) {
  let title = productName

  if (options && typeof options === 'object') {
    const optionKeys = Object.keys(options.optionList)

    optionKeys.forEach((key) => {
      const optionValue = options.currentOptions?.[key]
      if (optionValue) {
        title += ` - ${optionValue.split('-').join(' ').toUpperCase()}`
      }
    })
  }

  return title
}

export function $paymentMethodToImageLink(paymentMethod: string) {
  switch (paymentMethod) {
    case 'stripe':
      return '/images/givehug/campaign/4.svg'
    case 'paypal':
      return '/images/givehug/campaign/2.svg'
    case 'stripe-card':
      return '/images/givehug/campaign/4.svg'
    case 'stripe-ewallet':
      return '/images/givehug/campaign/5.svg'
    case 'stripe-bank':
      return '/images/givehug/campaign/5.svg'
    default:
      return '/images/givehug/campaign/4.svg'
  }
}

export function $getSocialLink(link: string | null) {
  if (link?.toString().startsWith('http')) {
    return link
  }
  return `https://${link}`
}

/**
 * Convert base64 string to File object
 * @param base64String - Base64 string to convert
 * @param fileName - Name of the file
 * @returns File object
 */
export function base64ToFile(base64String: string, fileName: string = 'image.png'): File {
  // Extract the base64 data part if it includes the data URL prefix
  const base64Data = base64String.includes('base64,')
    ? base64String.split('base64,')[1]
    : base64String

  // Determine the MIME type from the data URL or default to image/png
  let mimeType = 'image/png'
  if (base64String.includes('data:')) {
    const matches = base64String.match(/data:([^;]+);base64,/)
    if (matches && matches.length > 1) {
      mimeType = matches[1]
    }
  }

  // Convert base64 to binary
  const binaryString = atob(base64Data)
  const bytes = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  // Create Blob and File
  const blob = new Blob([bytes], { type: mimeType })
  return new File([blob], fileName, { type: mimeType })
}

export function $openCrispChat() {
  if (typeof window !== 'undefined') {
    const crispElement = document.querySelector('.crisp-client')
    if ((window as any).$crisp && crispElement) {
      crispElement.classList.add('opened');
      (window as any).$crisp.push(['do', 'chat:open'])
    }
  }
}

export async function $initDesignCanvas() {
  if (typeof window === 'undefined')
    return null

  return new Promise((resolve, reject) => {
    const startedAt = Date.now()
    const timer = setInterval(() => {
      if (window.DesignCanvas) {
        clearInterval(timer)
        try {
          const instance = new window.DesignCanvas.DesignCanvas(null)
          return resolve(instance)
        }
        catch (err) {
          return reject(err)
        }
      }

      if (Date.now() - startedAt >= 5000) {
        clearInterval(timer)
        return reject(
          new Error('Something went wrong, please reload the page')
        )
      }
    }, 100)
  })
}
