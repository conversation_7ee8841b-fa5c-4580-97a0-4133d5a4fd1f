export const $method: Record<string, Method> = {
  get: 'GET',
  post: 'POST',
  put: 'PUT',
  del: 'DELETE',
  patch: 'PATCH',
  options: 'OPTIONS'
}

export const USD_CODE = 'USD'
export const EUR_CODE = 'EUR'
export const VND_CODE = 'VND'

export const VIEWPORT = {
  mobile: 'mobile',
  tablet: 'tablet',
  desktop: 'desktop'
}

export const $api = {
  API_STRIPE_GET_INTENT_ORDER_ADDITION: '/public/order/stripe/get-intent-order-addition',
  API_CHECKOUT_LOG_EXCEPTION: '/public/order/log-checkout-exception',
  API_UPDATE_ORDER: '/public/order/update',

  API_GET_STORE_INFO: '/public/storeinfo',
  API_HOME_PAGE: '/public/home',
  API_GET_CAMPAIGN: '/public/campaigns',
  API_GET_PRODUCT_STATS: '/public/products/stats',
  API_GET_PROMOTION: '/public/promotions',
  API_GET_BUNDLE_DISCOUNT: '/public/bundle-discount',
  API_GET_LISTING_PRODUCT: '/public/products',
  API_GET_LISTING_FILTER: '/public/products/get-filter',
  API_CREATE_ORDER: '/public/order/create',
  API_GET_ORDER: '/public/order',
  API_GET_PAGE: '/public/storefront/pages',
  API_FAQ: '/public/faq',
  API_CART_KEY: '/public/order/callback/abandoned',
  API_GENERATE_SEO: '/public/page/custom',
  API_GET_UPSELL: '/public/upsell',
  API_CONFIRM_ADDRESS: '/public/customers/confirm-address',
  API_CUSTOMER_INFO: '/public/customers/update',
  API_CONFIRM_CANCEL_ORDER: '/public/order/cancel',
  API_REPORT_FORM: '/public/report',
  API_CSRF: '/public/csrf-token',
  API_GET_SUBSCRIBE: '/public/subscribe',
  API_ORDER_CREATE_PAYPAL: '/public/order/create/paypal',
  API_ORDER_PAYPAL_CALLBACK: '/public/order/callback/paypal',
  API_CONTACT_FORM: '/public/storefront/contact',
  API_PRODUCTS_SIMILAR: '/public/products/similar',
  API_PRODUCT_REVIEW: '/public/product-reviews',
  API_CREATE_PRODUCT_REVIEW: '/public/product-reviews/create',
  API_EMAIL_VALIDATION: '/public/email-validation',
  API_UNSUBSCRIBE_EMAIL: '/public/order/unsubscribe',
  API_PRE_CHECK_REVIEW_ORDER: '/public/order/pre-check-review-order',
  API_AI_MOCKUP_PREVIEW: '/public/ai-mockup/preview',
  // Distributed checkout
  API_GET_PRODUCT_VARIANTS: '/public/product/variants',
  API_CREATE_DISTRIBUTED_ORDER: '/v2/public/order/create',
  API_UPDATE_DISTRIBUTED_ORDER: '/v2/public/order/update',
  API_GET_DISTRIBUTED_ORDER: '/v2/public/order',
  API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION: '/v2/public/order/log-checkout-exception',

  API_GET_GLOBAL_ENABLE_CRISP: '/public/is-enable-live-chat',

  API_BLOG: '/public/blogs'
}

export const $arrCurrenciesMustHavePrefix = [
  'ARS',
  'AUD',
  'BBD',
  'BSD',
  'CAD',
  'COP',
  'DOP',
  'GYD',
  'HKD',
  'JMD',
  'LRD',
  'MXN',
  'NAD',
  'NIO',
  'NZD',
  'TTD',
  'TWD',
  'XCD'
]

export const $menuFooter = {
  menu1: {
    title: 'Info & support',
    menuData: [{
      name: 'About us',
      url: '/page/about'
    }, {
      name: 'Track order',
      url: '/order/track'
    }, {
      name: 'FAQs',
      url: '/page/faq'
    }, {
      name: 'Contact support',
      url: '/page/contact-us'
    }]
  },
  menu2: {
    title: 'Policies',
    menuData: [{
      name: 'Return policy',
      url: '/page/return-policy'
    }, {
      name: 'Shipping policy',
      url: '/page/shipping-policy'
    }, {
      name: 'Terms & conditions',
      url: '/page/terms-of-service'
    }, {
      name: 'Privacy policy',
      url: '/page/privacy'
    }, {
      name: 'DMCA',
      url: '/page/dmca'
    }]
  }
}

export const $menuFooterUrls = {
  '/page/about': ['menu1', 0],
  '/order/track': ['menu1', 1],
  '/page/faq': ['menu1', 2],
  '/page/contact-us': ['menu1', 3],
  '/page/return-policy': ['menu2', 0],
  '/page/shipping-policy': ['menu2', 1],
  '/page/terms-of-service': ['menu2', 2],
  '/page/privacy': ['menu2', 3],
  '/page/dmca': ['menu2', 4]
}

export const $menuFooterUrlsGiveHug = {
  '/page/about': ['menu1', 0],
  '/order/track': ['menu1', 1],
  '/page/faq': ['menu1', 2],
  '/page/contact-us': ['menu1', 3],
  '/page/return-refund-policy': ['menu2', 0],
  '/page/shipping-policy': ['menu2', 1],
  '/page/terms-of-service': ['menu2', 2],
  '/page/privacy': ['menu2', 3],
  '/page/dmca': ['menu2', 4]
}

export const $footerContactPhoneData = {
  contactPhoneData: [{
    countryCode: ['UK'],
    phone: '+44 (20) 376-937-62',
    name: 'United Kingdom',
    flags: ['images/country-flag/gb.svg']
  }, {
    countryCode: ['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE'],
    phone: '+33 (9) 730-368-78',
    name: 'EU',
    flags: ['images/country-flag/eu.svg']
  }, {
    countryCode: ['US', 'CA'],
    phone: '+****************',
    name: 'American and Canada',
    flags: ['images/country-flag/us.svg', 'images/country-flag/ca.svg']
  }, {
    countryCode: ['AU', 'NZ'],
    phone: '+61 (2) 8317-3292',
    name: 'Australia and New Zealand',
    flags: ['images/country-flag/au.svg', 'images/country-flag/nz.svg']
  }, {
    countryCode: [],
    phone: '+****************',
    name: 'Other country',
    flags: ['images/country-flag/world.svg']
  }],
  contactPhoneData2: [{
    countryCode: ['UK'],
    phone: '+44 (20) 8089-3193',
    name: 'United Kingdom',
    flags: ['images/country-flag/gb.svg']
  }, {
    countryCode: ['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE'],
    phone: '+33 (9) 7303-8418',
    name: 'EU',
    flags: ['images/country-flag/eu.svg']
  }, {
    countryCode: ['US', 'CA'],
    phone: '+****************',
    name: 'American and Canada',
    flags: ['images/country-flag/us.svg', 'images/country-flag/ca.svg']
  }, {
    countryCode: ['AU', 'NZ'],
    phone: '+61 (2) 8372-0168',
    name: 'Australia and New Zealand',
    flags: ['images/country-flag/au.svg', 'images/country-flag/nz.svg']
  }, {
    countryCode: [],
    phone: '+****************',
    name: 'Other country',
    flags: ['images/country-flag/world.svg']
  }]
}

export const COUNTRY_STATE_LIST = {
  US: [{ text: 'Alabama', value: 'AL' }, { text: 'Alaska', value: 'AK' }, { text: 'American Samoa', value: 'AS' }, { text: 'Arizona', value: 'AZ' }, { text: 'Arkansas', value: 'AR' }, { text: 'Baker Island', value: 'UM-81' }, { text: 'California', value: 'CA' }, { text: 'Colorado', value: 'CO' }, { text: 'Connecticut', value: 'CT' }, { text: 'Delaware', value: 'DE' }, { text: 'District of Columbia', value: 'DC' }, { text: 'Florida', value: 'FL' }, { text: 'Georgia', value: 'GA' }, { text: 'Guam', value: 'GU' }, { text: 'Hawaii', value: 'HI' }, { text: 'Howland Island', value: 'UM-84' }, { text: 'Idaho', value: 'ID' }, { text: 'Illinois', value: 'IL' }, { text: 'Indiana', value: 'IN' }, { text: 'Iowa', value: 'IA' }, { text: 'Jarvis Island', value: 'UM-86' }, { text: 'Johnston Atoll', value: 'UM-67' }, { text: 'Kansas', value: 'KS' }, { text: 'Kentucky', value: 'KY' }, { text: 'Kingman Reef', value: 'UM-89' }, { text: 'Louisiana', value: 'LA' }, { text: 'Maine', value: 'ME' }, { text: 'Maryland', value: 'MD' }, { text: 'Massachusetts', value: 'MA' }, { text: 'Michigan', value: 'MI' }, { text: 'Midway Atoll', value: 'UM-71' }, { text: 'Minnesota', value: 'MN' }, { text: 'Mississippi', value: 'MS' }, { text: 'Missouri', value: 'MO' }, { text: 'Montana', value: 'MT' }, { text: 'Navassa Island', value: 'UM-76' }, { text: 'Nebraska', value: 'NE' }, { text: 'Nevada', value: 'NV' }, { text: 'New Hampshire', value: 'NH' }, { text: 'New Jersey', value: 'NJ' }, { text: 'New Mexico', value: 'NM' }, { text: 'New York', value: 'NY' }, { text: 'North Carolina', value: 'NC' }, { text: 'North Dakota', value: 'ND' }, { text: 'Northern Mariana Islands', value: 'MP' }, { text: 'Ohio', value: 'OH' }, { text: 'Oklahoma', value: 'OK' }, { text: 'Oregon', value: 'OR' }, { text: 'Palmyra Atoll', value: 'UM-95' }, { text: 'Pennsylvania', value: 'PA' }, { text: 'Puerto Rico', value: 'PR' }, { text: 'Rhode Island', value: 'RI' }, { text: 'South Carolina', value: 'SC' }, { text: 'South Dakota', value: 'SD' }, { text: 'Tennessee', value: 'TN' }, { text: 'Texas', value: 'TX' }, { text: 'United States Minor Outlying Islands', value: 'UM' }, { text: 'United States Virgin Islands', value: 'VI' }, { text: 'Utah', value: 'UT' }, { text: 'Vermont', value: 'VT' }, { text: 'Virginia', value: 'VA' }, { text: 'Wake Island', value: 'UM-79' }, { text: 'Washington', value: 'WA' }, { text: 'West Virginia', value: 'WV' }, { text: 'Wisconsin', value: 'WI' }, { text: 'Wyoming', value: 'WY' }],
  AU: [{ text: 'Australian Capital Territory', value: 'ACT' }, { text: 'New South Wales', value: 'NSW' }, { text: 'Northern Territory', value: 'NT' }, { text: 'Queensland', value: 'QLD' }, { text: 'South Australia', value: 'SA' }, { text: 'Tasmania', value: 'TAS' }, { text: 'Victoria', value: 'VIC' }, { text: 'Western Australia', value: 'WA' }],
  NZ: [{ text: 'Auckland Region', value: 'AUK' }, { text: 'Bay of Plenty Region', value: 'BOP' }, { text: 'Canterbury Region', value: 'CAN' }, { text: 'Chatham Islands', value: 'CIT' }, { text: 'Gisborne District', value: 'GIS' }, { text: 'Hawke\'s Bay Region', value: 'HKB' }, { text: 'Manawatu-Wanganui Region', value: 'MWT' }, { text: 'Marlborough Region', value: 'MBH' }, { text: 'Nelson Region', value: 'NSN' }, { text: 'Northland Region', value: 'NTL' }, { text: 'Otago Region', value: 'OTA' }, { text: 'Southland Region', value: 'STL' }, { text: 'Taranaki Region', value: 'TKI' }, { text: 'Tasman District', value: 'TAS' }, { text: 'Waikato Region', value: 'WKO' }, { text: 'Wellington Region', value: 'WGN' }, { text: 'West Coast Region', value: 'WTC' }],
  IT: [{ text: 'Abruzzo', value: '65' }, { text: 'Aosta Valley', value: '23' }, { text: 'Apulia', value: '75' }, { text: 'Basilicata', value: '77' }, { text: 'Benevento Province', value: 'BN' }, { text: 'Calabria', value: '78' }, { text: 'Campania', value: '72' }, { text: 'Emilia-Romagna', value: '45' }, { text: 'Friuli\u2013Venezia Giulia', value: '36' }, { text: 'Lazio', value: '62' }, { text: 'Agrigento', value: 'AG' }, { text: 'Caltanissetta', value: 'CL' }, { text: 'Enna', value: 'EN' }, { text: 'Ragusa', value: 'RG' }, { text: 'Siracusa', value: 'SR' }, { text: 'Trapani', value: 'TP' }, { text: 'Liguria', value: '42' }, { text: 'Lombardy', value: '25' }, { text: 'Marche', value: '57' }, { text: 'Bari', value: 'BA' }, { text: 'Bologna', value: 'BO' }, { text: 'Cagliari', value: 'CA' }, { text: 'Catania', value: 'CT' }, { text: 'Florence', value: 'FI' }, { text: 'Genoa', value: 'GE' }, { text: 'Messina', value: 'ME' }, { text: 'Milan', value: 'MI' }, { text: 'Naples', value: 'NA' }, { text: 'Palermo', value: 'PA' }, { text: 'Reggio Calabria', value: 'RC' }, { text: 'Rome', value: 'RM' }, { text: 'Turin', value: 'TO' }, { text: 'Venice', value: 'VE' }, { text: 'Molise', value: '67' }, { text: 'Pesaro and Urbino Province', value: 'PU' }, { text: 'Piedmont', value: '21' }, { text: 'Alessandria', value: 'AL' }, { text: 'Ancona', value: 'AN' }, { text: 'Ascoli Piceno', value: 'AP' }, { text: 'Asti', value: 'AT' }, { text: 'Avellino', value: 'AV' }, { text: 'Barletta-Andria-Trani', value: 'BT' }, { text: 'Belluno', value: 'BL' }, { text: 'Bergamo', value: 'BG' }, { text: 'Biella', value: 'BI' }, { text: 'Brescia', value: 'BS' }, { text: 'Brindisi', value: 'BR' }, { text: 'Campobasso', value: 'CB' }, { text: 'Carbonia-Iglesias', value: 'CI' }, { text: 'Caserta', value: 'CE' }, { text: 'Catanzaro', value: 'CZ' }, { text: 'Chieti', value: 'CH' }, { text: 'Como', value: 'CO' }, { text: 'Cosenza', value: 'CS' }, { text: 'Cremona', value: 'CR' }, { text: 'Crotone', value: 'KR' }, { text: 'Cuneo', value: 'CN' }, { text: 'Fermo', value: 'FM' }, { text: 'Ferrara', value: 'FE' }, { text: 'Foggia', value: 'FG' }, { text: 'Forl\u00EC-Cesena', value: 'FC' }, { text: 'Frosinone', value: 'FR' }, { text: 'Gorizia', value: 'GO' }, { text: 'Grosseto', value: 'GR' }, { text: 'Imperia', value: 'IM' }, { text: 'Isernia', value: 'IS' }, { text: 'L\'Aquila', value: 'AQ' }, { text: 'La Spezia', value: 'SP' }, { text: 'Latina', value: 'LT' }, { text: 'Lecce', value: 'LE' }, { text: 'Lecco', value: 'LC' }, { text: 'Livorno', value: 'LI' }, { text: 'Lodi', value: 'LO' }, { text: 'Lucca', value: 'LU' }, { text: 'Macerata', value: 'MC' }, { text: 'Mantua', value: 'MN' }, { text: 'Massa and Carrara', value: 'MS' }, { text: 'Matera', value: 'MT' }, { text: 'Medio Campidano', value: 'VS' }, { text: 'Modena', value: 'MO' }, { text: 'Monza and Brianza', value: 'MB' }, { text: 'Novara', value: 'NO' }, { text: 'Nuoro', value: 'NU' }, { text: 'Ogliastra', value: 'OG' }, { text: 'Olbia-Tempio', value: 'OT' }, { text: 'Oristano', value: 'OR' }, { text: 'Padua', value: 'PD' }, { text: 'Parma', value: 'PR' }, { text: 'Pavia', value: 'PV' }, { text: 'Perugia', value: 'PG' }, { text: 'Pescara', value: 'PE' }, { text: 'Piacenza', value: 'PC' }, { text: 'Pisa', value: 'PI' }, { text: 'Pistoia', value: 'PT' }, { text: 'Pordenone', value: 'PN' }, { text: 'Potenza', value: 'PZ' }, { text: 'Prato', value: 'PO' }, { text: 'Ravenna', value: 'RA' }, { text: 'Reggio Emilia', value: 'RE' }, { text: 'Rieti', value: 'RI' }, { text: 'Rimini', value: 'RN' }, { text: 'Rovigo', value: 'RO' }, { text: 'Salerno', value: 'SA' }, { text: 'Sassari', value: 'SS' }, { text: 'Savona', value: 'SV' }, { text: 'Siena', value: 'SI' }, { text: 'Sondrio', value: 'SO' }, { text: 'Taranto', value: 'TA' }, { text: 'Teramo', value: 'TE' }, { text: 'Terni', value: 'TR' }, { text: 'Treviso', value: 'TV' }, { text: 'Trieste', value: 'TS' }, { text: 'Udine', value: 'UD' }, { text: 'Varese', value: 'VA' }, { text: 'Verbano-Cusio-Ossola', value: 'VB' }, { text: 'Vercelli', value: 'VC' }, { text: 'Verona', value: 'VR' }, { text: 'Vibo Valentia', value: 'VV' }, { text: 'Vicenza', value: 'VI' }, { text: 'Viterbo', value: 'VT' }, { text: 'Sardinia', value: '88' }, { text: 'Sicily', value: '82' }, { text: 'South Tyrol', value: 'BZ' }, { text: 'Trentino', value: 'TN' }, { text: 'Trentino-South Tyrol', value: '32' }, { text: 'Tuscany', value: '52' }, { text: 'Umbria', value: '55' }, { text: 'Veneto', value: '34' }],
  CA: [{ text: 'Alberta', value: 'AB' }, { text: 'British Columbia', value: 'BC' }, { text: 'Manitoba', value: 'MB' }, { text: 'New Brunswick', value: 'NB' }, { text: 'Newfoundland and Labrador', value: 'NL' }, { text: 'Northwest Territories', value: 'NT' }, { text: 'Nova Scotia', value: 'NS' }, { text: 'Nunavut', value: 'NU' }, { text: 'Ontario', value: 'ON' }, { text: 'Prince Edward Island', value: 'PE' }, { text: 'Quebec', value: 'QC' }, { text: 'Saskatchewan', value: 'SK' }, { text: 'Yukon', value: 'YT' }],
  BR: [{ text: 'Acre', value: 'AC' }, { text: 'Alagoas', value: 'AL' }, { text: 'Amap\u00E1', value: 'AP' }, { text: 'Amazonas', value: 'AM' }, { text: 'Bahia', value: 'BA' }, { text: 'Cear\u00E1', value: 'CE' }, { text: 'Distrito Federal', value: 'DF' }, { text: 'Esp\u00EDrito Santo', value: 'ES' }, { text: 'Goi\u00E1s', value: 'GO' }, { text: 'Maranh\u00E3o', value: 'MA' }, { text: 'Mato Grosso', value: 'MT' }, { text: 'Mato Grosso do Sul', value: 'MS' }, { text: 'Minas Gerais', value: 'MG' }, { text: 'Par\u00E1', value: 'PA' }, { text: 'Para\u00EDba', value: 'PB' }, { text: 'Paran\u00E1', value: 'PR' }, { text: 'Pernambuco', value: 'PE' }, { text: 'Piau\u00ED', value: 'PI' }, { text: 'Rio de Janeiro', value: 'RJ' }, { text: 'Rio Grande do Norte', value: 'RN' }, { text: 'Rio Grande do Sul', value: 'RS' }, { text: 'Rond\u00F4nia', value: 'RO' }, { text: 'Roraima', value: 'RR' }, { text: 'Santa Catarina', value: 'SC' }, { text: 'S\u00E3o Paulo', value: 'SP' }, { text: 'Sergipe', value: 'SE' }, { text: 'Tocantins', value: 'TO' }]
}

export const $stripeBanks = [{
  name: 'iDEAL',
  payment_method: 'ideal',
  currency: 'EUR'
}, {
  name: 'Bancontact',
  payment_method: 'bancontact',
  currency: 'EUR'
}, {
  name: 'Sofort',
  payment_method: 'sofort',
  currency: 'EUR',
  // required: ['country'],
  countries: { EUR: ['AT', 'BE', 'DE', 'IT', 'NL', 'ES'] } as any
}, {
  name: 'Klarna',
  payment_method: 'klarna',
  currency: 'EUR',
  required: ['country'],
  countries: {
    EUR: ['AT', 'BE', 'FI', 'FR', 'DE', 'IE', 'IT', 'NL', 'ES'],
    USD: ['US']
  } as any
}]

export const SHIPPING_METHOD = {
  standard: 'standard',
  express: 'express'
}

export const PAYMENT_METHOD = {
  stripe: 'stripe',
  paypal: 'paypal',
  stripeCard: 'stripe-card',
  stripeEwallet: 'stripe-ewallet',
  stripeBank: 'stripe-bank'
}

export const PAYMENT_DISPLAY_TYPE = {
  SMARTCHECKOUT: 'smartcheckout',
  SMARTCHECKOUT_IFRAME: 'smartcheckout_iframe',
  LIST: 'list',
  NULL: null,
  UNINITIALIZED: ''
}

export const CAMPAIGN_OPTION = {
  color: 'color',
  size: 'size'
}

export const imageType: {
  [key in SImageType]: {
    express: string
    s3: string
  } } = {
  'share': {
    express: '&expectedWidth=1080',
    s3: '/rx/1080x1080'
  },
  'full': {
    express: '',
    s3: '/rx/-'
  },
  'logo': {
    express: '&expectedWidth=256',
    s3: '/rx/256x256'
  },
  'banner_mobile': {
    express: '&expectedWidth=1080',
    s3: '/rx/1080x608'
  },
  'collection_banner': {
    express: '&expectedWidth=600',
    s3: '/rx/600x600,c_1'
  },
  'list': {
    express: '&expectedWidth=600',
    s3: '/rx/600x750,c_2'
  },
  'full_hd': {
    express: '&expectedWidth=1280',
    s3: '/rx/1000x1250'
  },
  'icon': {
    express: '&expectedWidth=32',
    s3: '/rx/32x32'
  },
  'icon-xl': {
    express: '&expectedWidth=512',
    s3: '/rx/512x512'
  },
  'icon-lg': {
    express: '&expectedWidth=64',
    s3: '/rx/64x64'
  },
  'product-review-thumb': {
    express: '&expectedWidth=256',
    s3: '/rx/256x256,c_1'
  },
  'product-review-image': {
    express: '&expectedWidth=720',
    s3: '/rx/720'
  },
  'product-review-video': {
    express: '&expectedWidth=160',
    s3: '/rx/-'
  },
  'thumb': {
    express: '&expectedWidth=160',
    s3: '/rx/160x200,c_2'
  }
}

export const $customDesignArray: Record<string, string[]> = {
  by_alphabet: ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],
  by_month: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
  by_year: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99'],
  custom: []
}

export const CUSTOM_OPTION_TYPE: { [key: string]: 'text' | 'dropdown' | 'image' } = {
  text: 'text',
  dropdown: 'dropdown',
  image: 'image'
}

export const TRACKING_EVENT_LIST: {
  [key: string]: Record<string, string>
} = {
  page_view: {
    custom: 'pageview',
    facebook: 'PageView',
    pintrk: 'PageVisit',
    ttq: 'page_view',
    gtag: 'page_view',
    snap: 'PAGE_VIEW',
    quora: 'ViewContent',
    bing: 'page_view',
    klaviyo: 'Active on Site',
    reddit: 'PageVisit'
  },
  view_content: {
    custom: 'viewcontent',
    facebook: 'ViewContent',
    pintrk: 'PageVisit',
    ttq: 'ViewContent',
    gtag: 'view_item',
    snap: 'VIEW_CONTENT',
    quora: 'ViewContent',
    bing: 'view_item',
    klaviyo: 'Viewed Product',
    reddit: 'ViewContent'
  },
  contact: {
    custom: 'contact',
    facebook: 'Contact',
    pintrk: 'Contact',
    ttq: 'Contact',
    snap: 'CONTACT',
    quora: 'Contact',
    bing: 'contact',
    klaviyo: 'Contact',
    reddit: 'Contact'
  },
  add_to_cart: {
    custom: 'addtocart',
    facebook: 'AddToCart',
    pintrk: 'AddToCart',
    ttq: 'AddToCart',
    gtag: 'add_to_cart',
    snap: 'ADD_CART',
    quora: 'AddToCart',
    bing: 'add_to_cart',
    klaviyo: 'Added to Cart',
    reddit: 'AddToCart'
  },
  initiate_checkout: {
    custom: 'initiatecheckout',
    facebook: 'InitiateCheckout',
    pintrk: 'Checkout',
    ttq: 'InitiateCheckout',
    gtag: 'begin_checkout',
    snap: 'START_CHECKOUT',
    quora: 'InitiateCheckout',
    bing: 'begin_checkout',
    klaviyo: 'Started Checkout',
    reddit: 'InitiateCheckout'
  },
  purchase: {
    custom: 'purchase',
    facebook: 'Purchase',
    pintrk: 'Purchase',
    ttq: 'CompletePayment',
    gtag: 'purchase',
    snap: 'PURCHASE',
    quora: 'Purchase',
    bing: 'purchase',
    klaviyo: 'Placed Order',
    reddit: 'Purchase'
  },
  view_item_list: {
    gtag: 'view_item_list',
    klaviyo: 'Viewed Collection'
  },
  select_item: {
    gtag: 'select_item'
  },
  search: {
    klaviyo: 'Submitted Search'
  }
}

export const PAYMENT_GATEWAY_TYPE = {
  BOTH: 'both',
  STRIPE: 'stripe',
  PAYPAL: 'paypal'
}

export const PRICING_MODE_TYPE = {
  FIXED_PRICE: 'fixed_price',
  ADJUST_PRICE: 'adjust_price',
  CUSTOM_PRICE: 'custom_price'
}

export const ORDER_SUMMARY_POSITION = {
  TOP: 'top',
  BOTTOM: 'bottom'
}

export const BUNDLE_DISCOUNT_VIEW_PLACE = {
  CAMPAIGN_DETAIL: 1,
  CART: 2
}

export const DOMAIN_MARKET_PLACE = ['senprints.com', 'senstores.com']

export const PERSONALIZED_TYPE = {
  NONE: 0,
  CUSTOM: 1,
  PB: 2,
  CUSTOM_OPTION: 3
}

export const SYSTEM_CAMPAIGN_TYPE = {
  REGULAR: 'regular',
  CUSTOM: 'custom',
  EXPRESS: 'campaign_express',
  MOCKUP: 'mockup',
  AOP: 'aop',
  COMBO: 'combo',
  EXPRESS_2: 'express_2',
  AI_MOCKUP: 'ai_mockup'
}
