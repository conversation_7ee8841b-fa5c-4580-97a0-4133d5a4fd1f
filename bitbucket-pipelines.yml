image: atlassian/default-image:2
options:
  max-time: 30
  size: 2x
definitions:
  services:
    docker:
      #image: docker:dind
      image: docker:20.10.12-dind
      memory: 6000

run-test-production: &run-test-production
        - step:
            name: "Post Test Production: senprints.com"
            image: itlboy/docker:dind-buildx
            clone:
             enabled: false
            runs-on: 
              - 'self.hosted'  
            script:
                - ls -la
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/browser-test-v4
                - export TEST_DOMAIN=senprints.com
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker run -i $DOCKER_HUB_USER/browser-test-v4 -d $TEST_DOMAIN -b $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH -m $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH:#$BITBUCKET_BUILD_NUMBER -i $BITBUCKET_REPO_SLUG/$BITBUCKET_BUILD_NUMBER:prod
            services:
             - docker

run-test-staging: &run-test-staging
        - step:
            name: "Pre Test (Commit [skip test] to disable) Staging: staging2-v4.senprints.net"
            # image: itlboy/docker:dind-buildx
            image: senasia/base:docker-kubectl-alpine
            clone:
             depth: 1
            runs-on: 
              - 'self.hosted'  
            script:
                - COMMIT_MESSAGE=`git log --format=%B -n 1 $BITBUCKET_COMMIT`
                - echo $COMMIT_MESSAGE
                - word="\[skip test\]"
                - | 
                  case "$COMMIT_MESSAGE" in
                    *$word*) echo "SKIP TEST!"; exit 0 ;;
                    *      ) echo "CONTINUE TEST" ;;
                  esac
                - ls -la
                - export DOCKER_IMAGE=$DOCKER_HUB_USER/browser-test-v4
                - export TEST_DOMAIN=staging2-v4.senprints.net
                - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                - docker run -i $DOCKER_HUB_USER/browser-test-v4 -d $TEST_DOMAIN -b $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH -m $BITBUCKET_REPO_SLUG/$BITBUCKET_BRANCH:#$BITBUCKET_BUILD_NUMBER -i $BITBUCKET_REPO_SLUG/$BITBUCKET_BUILD_NUMBER
            services:
             - docker


pipelines:
  branches:
    master:
        - parallel:
            - step:
                name: Build Production  Image
                image: itlboy/docker:dind-buildx
                runs-on: 
                  - 'self.hosted'
                script:
                    - ls -la
                    - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                    - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-prod
                    - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-v4-prod"
                    - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                    - docker buildx create --use
                    -  >
                     docker buildx build
                     --platform=linux/amd64
                     --cache-from $DOCKER_CACHE_IMAGE
                     --cache-to $DOCKER_CACHE_IMAGE
                     -o type=docker,name=$DOCKER_IMAGE
                     -f Dockerfile-alpine
                     -t $DOCKER_IMAGE .
                    - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                    - docker push $DOCKER_IMAGE:$DOCKER_TAG
                services:
                 - docker
        - step:
            name: Deploy to Stagging
            image: itlboy/kubectl
            runs-on: 
              - 'self.hosted'
            clone: 
              enabled: false 
            script:
              - ls -la
              # Deploy US GOOGLE
              # - export KUBECONFIG=./kubeconfig
              # - echo "$KUBECONFIG_GG_US" | base64 -d > ./kubeconfig
              # - kubectl  get node
              # - kubectl rollout restart deployment/frontend-storefront-v4-staging2	 -n production
              # Deploy US CONTABO
              - export KUBECONFIG=./kubeconfig
              - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
              - kubectl  get node
              - kubectl set image deployment/frontend-storefront-v4-staging2 nodejs=senasia/storefront-v4-prod:$BITBUCKET_BUILD_NUMBER  -n production
              - kubectl rollout status deployment/frontend-storefront-v4-staging2	 -n production
              - echo "Deploy staging successfuly"
        - parallel:
          - <<: *run-test-staging
          - <<: *run-test-staging
          - <<: *run-test-staging

        - step:
            name: Deploy to Production
            image: senasia/base:docker-kubectl-alpine
            runs-on: 
              - 'self.hosted'
              - 'us'
            services:
              - docker
            clone:
              enabled: false
            deployment: Production
            script:
              - export SOURCE_DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-prod:$BITBUCKET_BUILD_NUMBER
              - export TARGET_DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-prod
              - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
              - docker manifest create $TARGET_DOCKER_IMAGE $SOURCE_DOCKER_IMAGE
              - docker manifest push $TARGET_DOCKER_IMAGE
              - ls -la
              # Deploy US


              # Deploy US BOSTON
              - alias kubectl="kubectl --insecure-skip-tls-verify"
              - export KUBECONFIG=./kubeconfig
              - echo "$KUBECONFIG_BOSTON_1" | base64 -d > ./kubeconfig
              - kubectl  get node
              - kubectl rollout restart deployment/frontend-storefront-v4-cdnjs -n production
              - kubectl rollout status deployment/frontend-storefront-v4-cdnjs -n production
              - kubectl rollout restart deployment/frontend-storefront-v4	 -n production

              ####### Run deploy k8s-config
              - docker run -e SOURCE_DOCKER_IMAGE=$SOURCE_DOCKER_IMAGE  senasia/k8s-config senprints-store-v4

              # Delete cache    
              - |
               curl --location --request POST 'https://cache-manager.senprints.net/delete' \
               --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
               --header 'Content-Type: application/json' \
               --data '{
                      "data": [
                          {
                              "type": "all",
                              "storeVersion" : "4"
                          }
                      ]
                  }'
              # Delete cache 2nd    
              -  |
               curl --location --request POST 'https://cache-manager.senprints.net/delete' \
               --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
               --header 'Content-Type: application/json' \
               --data '{
                      "data": [
                          {
                              "type": "all",
                              "storeVersion": "4"
                          }
                      ],
                      "delay": 30
                  }'
              # Delete cache 3rd 
              -  |
               curl --location --request POST 'https://cache-manager.senprints.net/delete' \
               --header 'Authorization: Bearer VSQQa7dT94EkkmDR' \
               --header 'Content-Type: application/json' \
               --data '{
                      "data": [
                          {
                              "type": "all",
                              "storeVersion": "4"
                          }
                      ],
                      "delay": 180
                  }'

              - echo 'Deploy successful!'
        - parallel:
            fail-fast: true
            steps:
                - <<: *run-test-production
                - <<: *run-test-production

#        - step:
#            name: Deploy to DEV server
#            image: itlboy/kubectl
#            deployment: Dev
#            runs-on: 
#              - 'self.hosted'
#            clone:
#             enabled: false
#            script:
#              - ls -la
#              - echo "$KUBECONFIG_DEV_1" | base64 -d > ./kubeconfig
#              - export KUBECONFIG=./kubeconfig
#              - kubectl get node
#              - kubectl set image deployment/storefront-v4-build nodejs=senasia/storefront-v4-build:$BITBUCKET_BUILD_NUMBER
#    #              - kubectl rollout status deployment storefront-v4-build -n development
#              - kubectl set image deployment/storefront-v4-dev nodejs=senasia/storefront-v4-dev:$BITBUCKET_BUILD_NUMBER
#              - kubectl rollout status deployment storefront-v4-dev -n development
      
    develop:
        - parallel:
            - step:
                name: Build Image - Built mode
                image: itlboy/docker:dind-buildx
                runs-on: 
                  - 'self.hosted'
                script:
                    - ls -la
                    - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                    - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-build
                    - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-v4-build"
                    - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                    - docker buildx create --use
                    -  >
                     docker buildx build
                     --platform=linux/amd64
                     --cache-from $DOCKER_CACHE_IMAGE
                     --cache-to $DOCKER_CACHE_IMAGE
                     -o type=docker,name=$DOCKER_IMAGE
                     -f Dockerfile-alpine
                     -t $DOCKER_IMAGE .
                    - docker push $DOCKER_IMAGE
                    - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                    - docker push $DOCKER_IMAGE:$DOCKER_TAG
                services:
                 - docker
            - step:
                name: Build Image - Dev mode
                image: itlboy/docker:dind-buildx
                runs-on: 
                  - 'self.hosted'
                script:
                    - ls -la
                    - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                    - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-dev
                    - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-v4-dev"
                    - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                    - docker buildx create --use
                    -  >
                     docker buildx build
                     --platform=linux/amd64
                     --cache-from $DOCKER_CACHE_IMAGE
                     --cache-to $DOCKER_CACHE_IMAGE
                     -o type=docker,name=$DOCKER_IMAGE
                     -f Dockerfile-alpine.dev
                     -t $DOCKER_IMAGE .
                    - docker push $DOCKER_IMAGE
                    - docker tag $DOCKER_IMAGE $DOCKER_IMAGE:$DOCKER_TAG
                    - docker push $DOCKER_IMAGE:$DOCKER_TAG
                services:
                 - docker
        - step:
            name: Deploy to DEV server
            image: senasia/base:docker-kubectl-alpine
            deployment: Dev
            runs-on: 
              - 'self.hosted'
            clone:
             enabled: false
            services:
              - docker
            script:
              - ls -la

              - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
              - docker run -e SOURCE_DOCKER_IMAGE=senasia/storefront-v4-build:$BITBUCKET_BUILD_NUMBER senasia/k8s-config senprints-store-v4-dev

              - echo "$KUBECONFIG_SG_HOME" | base64 -d > ./kubeconfig
              - export KUBECONFIG=./kubeconfig
              - alias kubectl="kubectl --insecure-skip-tls-verify"
              - kubectl get node
              - kubectl set image deployment/storefront-v4-build nodejs=senasia/storefront-v4-build:$BITBUCKET_BUILD_NUMBER -n senprints-dev
    #              - kubectl rollout status deployment storefront-v4-build -n development
             # - kubectl set image deployment/storefront-v4-dev nodejs=senasia/storefront-v4-dev:$BITBUCKET_BUILD_NUMBER
              - kubectl rollout status deployment storefront-v4-build -n senprints-dev
              # Deploy US GOOGLE
              # - export KUBECONFIG=./kubeconfig
              # - echo "$KUBECONFIG_GG_US" | base64 -d > ./kubeconfig
              # - kubectl  get node
              # - kubectl rollout restart deployment/frontend-storefront-v4-staging	 -n production
              # Deploy US CONTABO
              - export KUBECONFIG=./kubeconfig
              - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
              - kubectl  get node
              - kubectl rollout restart deployment/frontend-storefront-v4-staging	 -n production
        # - parallel:
        #   - <<: *run-test-staging
        #   - <<: *run-test-staging
        #   - <<: *run-test-staging

    test/browser_test_prod:
      # - step:
      #     name: Deploy to Stagging
      #     image: itlboy/kubectl
      #     runs-on: 
      #       - 'self.hosted'
      #     clone:
      #     enabled: false 
      #     script:
      #       - ls -la
      #       # Deploy US GOOGLE
      #       - export KUBECONFIG=./kubeconfig
      #       - echo "$KUBECONFIG_GG_US" | base64 -d > ./kubeconfig
      #       - kubectl  get node
      #       - kubectl rollout restart deployment/frontend-storefront-v4-staging2	 -n production
      #       # Deploy US CONTABO
      #       - export KUBECONFIG=./kubeconfig
      #       - echo "$KUBECONFIG_PRODUCTION_NY" | base64 -d > ./kubeconfig
      #       - kubectl  get node
      #       - kubectl rollout restart deployment/frontend-storefront-v4-staging2	 -n production
      - parallel:
          fail-fast: true
          steps:
              - <<: *run-test-production
              - <<: *run-test-production
    beta:
      - parallel:
        - step:
            name: Build Image
            image: itlboy/docker:dind-buildx
            runs-on: 
              - 'self.hosted'
            services:
              - docker  
            script:
                  - ls -la
                  - export DOCKER_TAG=$BITBUCKET_BUILD_NUMBER
                  - export DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-beta
                  - export DOCKER_CACHE_IMAGE="$DOCKER_HUB_USER/cache:storefront-v4-beta"
                  - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
                  - docker buildx create --use
                  -  >
                    docker buildx build
                    --platform=linux/amd64
                    --cache-from $DOCKER_CACHE_IMAGE
                    --cache-to $DOCKER_CACHE_IMAGE
                    -o type=registry,name=$DOCKER_IMAGE
                    -f Dockerfile-alpine
                    -t $DOCKER_IMAGE .

                  -  >
                    docker buildx build
                    --platform=linux/amd64
                    --cache-from $DOCKER_CACHE_IMAGE
                    --cache-to $DOCKER_CACHE_IMAGE
                    -o type=registry,name=$DOCKER_IMAGE:$BITBUCKET_BUILD_NUMBER
                    -f Dockerfile-alpine
                    -t $DOCKER_IMAGE:$BITBUCKET_BUILD_NUMBER .


      - step:
          name: Deploy
          image: itlboy/kubectl
          runs-on: 
            - 'self.hosted'
          services:
            - docker  
          clone:
           enabled: false
          script:
            - export SOURCE_DOCKER_IMAGE=$DOCKER_HUB_USER/storefront-v4-beta:$BITBUCKET_BUILD_NUMBER
            - docker login  --username $DOCKER_HUB_USER --password $DOCKER_HUB_PASSWORD
            - docker run -e SOURCE_DOCKER_IMAGE=$SOURCE_DOCKER_IMAGE  senasia/k8s-config senprints-store-v4-beta