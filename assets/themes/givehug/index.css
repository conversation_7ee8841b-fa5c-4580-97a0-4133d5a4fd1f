@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLDz8Z1JlFc-K.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiEyp8kv8JHgFVrJJnecmNE.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLGT9Z1JlFc-K.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLEj6Z1JlFc-K.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLEj6Z1xlFQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLCz7Z1JlFc-K.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/poppins/v22/pxiByp8kv8JHgFVrLCz7Z1xlFQ.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

html,
body {
  -webkit-font-smoothing: antialiased;
}

body.givehug {
  font-family: "Poppins",-apple-system,"Helvetica Neue","Droid Sans",Arial,sans-serif;
  --sp-border-radius-1: 9999px;
  --sp-border-radius-2: 10px;
}

.givehug .font-secondary {
  font-family: "Poppins",-apple-system,"Helvetica Neue","Droid Sans",Arial,sans-serif;
}

:root {
  --color-primary-rgb: 241, 100, 30;
  --color-primary: rgba(var(--color-primary-rgb), 1);
  --color-primary-focus: rgba(var(--color-primary-rgb), 0.8);
  --color-primary-active: rgba(var(--color-primary-rgb), 0.8);
  --color-primary-hover: rgba(var(--color-primary-rgb), 0.8);

  --color-secondary: #0F743B;
  --color-error: #D00739;
  --color-muted: #595959;
  --color-social: #CACACA;

  --text-2xl: 24px;
  --text-xs: 12px;
  --font-weight-medium: 500;
  --font-weight-heavy: 600;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-primary);
}

@media (min-width: 1023px) {
  .shop-info {
    width: fit-content !important;
    /* margin-left: auto; */
  }
}

/* Product Listing */
.collection__banner {
  background: linear-gradient(
    to bottom,
    rgba(var(--color-primary-rgb), 0.15)   0%,
    rgba(var(--color-primary-rgb), 0) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 180px;
  min-height: 229px;
}

.collection__banner h1 {
  text-align: center;
  color: rgba(var(--color-primary-rgb), 1);
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-heavy);
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.personalize__tag {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: rgba(238, 238, 238, 1);
  color: rgba(135, 135, 135, 1);
  padding: 0.1rem 0.5rem;
  border-radius: 9999px;
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  text-wrap: nowrap;

  @media (max-width: 768px) {
    font-size: 10px;
    padding: 1px 0.25rem;
  }

  @media (max-width: 425px) {
    font-size: 9px;
    margin-bottom: 2px;
  }
}

.viewbox-image img {
  border-radius: 10px !important;
}

.text-social {
  color: var(--color-social);
}


.rippe-effect {
  position: relative;
  color: #000;
  transition: color 0.2s ease-in-out;
}

.rippe-effect::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: var(--color-primary);
  transform: translate(-50%, -50%) scale(0.9);
  transition: all 0.2s ease-out;
  z-index: -1;
  opacity: 0;
  border-radius: 9999px;
}

.rippe-effect:hover::after,
.rippe-effect:focus::after {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}

.rippe-effect:hover,
.rippe-effect:focus {
  color: #fff;
}

/* Personalize PB */
.cropper-wrapper {
  max-height: 80vh;
  overflow-y: auto !important;
}

.text-option {
  color: #575757;
}

.pb-tooltip-title {
  display: none;
}

.pb-tooltip {
  transition: all 0.2s ease-in-out;
}

.pb-tooltip:hover {
  opacity: 0.8;
}

/* Why You'll Love Us */
.why__love__us svg {
  color: var(--color-primary);
}

.why__love__us h2 {
  text-align: center;
  font-size: 2.5rem;
  line-height: 3.75rem;
  font-weight: 500;
  margin-bottom: 2.25rem;

  @media (max-width: 450px) {
    font-size: 2rem;
  }
}

.why__love__us .feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 0 1rem; /* Add internal padding */
}

.why__love__us .feature-item h5 {
  text-wrap: nowrap;
  font-size: 1.8rem;
  font-weight: 500;
  text-transform: capitalize;
  line-height: 2.25rem;
  margin-bottom: 0.75rem; /* Increase spacing between title and text */

  @media (max-width: 450px) {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }
}

.why__love__us .feature-item p {
  margin: 0;
  opacity: 0.8;
  line-height: 1.5;
  color: var(--color-muted);
  max-width: 300px; /* Slightly reduce max-width for better fit */
}

/* Custom Splide pagination styling */
.why__love__us .splide__pagination {
  display: none;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}
