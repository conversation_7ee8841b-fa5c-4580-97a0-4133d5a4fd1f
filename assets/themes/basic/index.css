@font-face {
	font-family: "BasicPro Regular";
	font-stretch: normal;
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src:
    url(/fonts/BasicPro-Regular.woff2) format("woff2"),
		url(/fonts/BasicPro-Regular.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Italic";
	font-stretch: normal;
	font-style: italic;
	font-weight: 400;
	src:
    url(/fonts/BasicPro-Italic.woff2) format("woff2"),
		url(/fonts/BasicPro-Italic.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Bold";
	font-stretch: normal;
	font-style: normal;
	font-weight: 700;
	font-display: swap;
	src:
    url(/fonts/BasicPro-Bold.woff2) format("woff2"),
		url(/fonts/BasicPro-Bold.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Bold Italic";
	font-stretch: normal;
	font-style: italic;
	font-weight: 700;
	src:
    url(/fonts/BasicPro-BoldItalic.woff2) format("woff2"),
		url(/fonts/BasicPro-BoldItalic.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Bold Rounded";
	font-stretch: normal;
	font-style: normal;
	font-weight: 700;
	src:
    url(/fonts/BasicPro-BoldRounded.woff2) format("woff2"),
		url(/fonts/BasicPro-BoldRounded.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Light";
	font-stretch: normal;
	font-style: normal;
	font-weight: 300;
	font-display: swap;
	src:
    url(/fonts/BasicPro-Light.woff2) format("woff2"),
		url(/fonts/BasicPro-Light.woff) format("woff");
}
@font-face {
	font-family: "BasicPro Light Italic";
	font-stretch: normal;
	font-style: italic;
	font-weight: 300;
	src:
    url(/fonts/BasicPro-LightItalic.woff2) format("woff2"),
		url(/fonts/BasicPro-LightItalic.woff) format("woff");
}
@font-face {
	font-family: "BasicPro UltraLight";
	font-stretch: normal;
	font-style: normal;
	font-weight: 200;
	src:
    url(/fonts/BasicPro-UltraLight.woff2) format("woff2"),
		url(/fonts/BasicPro-UltraLight.woff) format("woff");
}
@font-face {
	font-family: "BasicPro UltraLight Italic";
	font-stretch: normal;
	font-style: italic;
	font-weight: 200;
	src:
    url(/fonts/BasicPro-UltraLightItalic.woff2) format("woff2"),
		url(/fonts/BasicPro-UltraLightItalic.woff) format("woff");
}

html,
body {
  -webkit-font-smoothing: antialiased;
}

body.basic {
	--color-gray-1: #f4f4f4;
	--color-gray-2: #7d7d7d;
	--color-gray-3: #757575;
	--color-gray-4: #ababab;
	--color-gray-5: #DADADA;
	--color-gray-6: #6a6a6a;
	--color-gray-7: #929292;

	--color-black-1: #1b1b1b;

	--color-blue-1: #005db5;

	--slide-arrow-background: #f6f6f6bf;
	--slide-arrow-background-hover: #929191bf;

	--input-bg: #f7f7f7;
	--input-focus-color: #378694;

  font-family: 'Helvetica Neue', HelveticaNeue, Helvetica, 'Noto Sans', Roboto, Arial, 'Hiragino Sans', 'Hiragino Kaku Gothic Pro', '\30D2\30E9\30AE\30CE\89D2\30B4   Pro W3', 'Noto Sans CJK JP', Osaka, Meiryo, '\30E1\30A4\30EA\30AA', 'MS PGothic', '\FF2D\FF33   \FF30\30B4\30B7\30C3\30AF', 'Hiragino Sans GB', 'Arial Unicode MS', sans-serif;
}
body.basic[lang="vi"] .font-bold-2,
body.basic[lang="vi"] {
	font-family: Arial, 'Helvetica Neue', HelveticaNeue, Helvetica, 'Noto Sans', Roboto, 'Hiragino Sans', 'Hiragino Kaku Gothic Pro', '\30D2\30E9\30AE\30CE\89D2\30B4 Pro W3', 'Noto Sans CJK JP', Osaka, Meiryo, '\30E1\30A4\30EA\30AA', 'MS PGothic', '\FF2D\FF33 \FF30\30B4\30B7\30C3\30AF', 'Hiragino Sans GB', 'Arial Unicode MS', sans-serif;
}

body.basic .font-regular {
	font-family: "BasicPro Regular", "Helvetica Neue", HelveticaNeue, Helvetica, "Noto Sans", Roboto, Arial, "Hiragino Sans", "Hiragino Kaku Gothic Pro", "\30D2\30E9\30AE\30CE\89D2\30B4   Pro W3", "Noto Sans CJK JP", Osaka, Meiryo, "\30E1\30A4\30EA\30AA", "MS PGothic", "MS P\30B4\30B7\30C3\30AF", "Hiragino Sans GB", "Arial Unicode MS", sans-serif;
	letter-spacing: 0.4px;
}
body.basic .font-italic-2 {
	font-family: "BasicPro Italic";
	letter-spacing: 0.4px;
}
body.basic .font-bold-2 {
	/**
	* An exception for classes here
	*
	* Sometimes we need the font bold, but not using the "BasicPro Bold"
	*/
	font-family: "BasicPro Bold", "Helvetica Neue", HelveticaNeue, Helvetica, "Noto Sans", Roboto, Arial, "Hiragino Sans", "Hiragino Kaku Gothic Pro", "\30D2\30E9\30AE\30CE\89D2\30B4   Pro W3", "Noto Sans CJK JP", Osaka, Meiryo, "\30E1\30A4\30EA\30AA", "MS PGothic", "MS P\30B4\30B7\30C3\30AF", "Hiragino Sans GB", "Arial Unicode MS", sans-serif;
	letter-spacing: 0.4px;
}
body.basic .font-bold-2.font-italic {
	font-family: "BasicPro Bold Italic";
	letter-spacing: 0.4px;
}
body.basic .font-bold-2.font-rounded {
	font-family: "BasicPro Bold Rounded";
	letter-spacing: 0.4px;
}
body.basic .font-light-2 {
	font-family: "BasicPro Light", "Helvetica Neue", HelveticaNeue, Helvetica, "Noto Sans", Roboto, Arial, "Hiragino Sans", "Hiragino Kaku Gothic Pro", "\30D2\30E9\30AE\30CE\89D2\30B4   Pro W3", "Noto Sans CJK JP", Osaka, Meiryo, "\30E1\30A4\30EA\30AA", "MS PGothic", "MS P\30B4\30B7\30C3\30AF", "Hiragino Sans GB", "Arial Unicode MS", sans-serif;
}
body.basic .font-light-2.font-italic {
	font-family: "BasicPro Light Italic";
	letter-spacing: 0.4px;
}
body.basic .font-extralight-2 {
	font-family: "BasicPro UltraLight";
	letter-spacing: 0.4px;
}
body.basic .font-extralight-2.font-italic {
	font-family: "BasicPro UltraLight Italic";
	letter-spacing: 0.4px;
}

.opt-item-hover:hover {
  box-shadow: 0 0 0 3px #fff, 0 0 0 4px var(--color-gray-7);
}
.opt-item-hover-2:hover {
  box-shadow: 0 0 0 1px #fff, 0 0 0 2px var(--color-gray-7);
}