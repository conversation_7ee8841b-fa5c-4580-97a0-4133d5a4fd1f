@font-face {
  font-family: Guardian-EgypTT;
  src: url(/fonts/Guardian-EgypTT-Light.woff2) format("woff2"),
    url(/fonts/Guardian-EgypTT-Light.woff) format("woff");
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 96%;
  descent-override: 23%;
}

@font-face {
  font-family: Guardian-EgypTT;
  src: url(/fonts/StagCyr-Light-Web.woff2) format("woff2"),
    url(/fonts/StagCyr-Light-Web.woff) format("woff");
  font-style: normal;
  font-weight: 300;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 96%;
  descent-override: 23%;
  unicode-range: U+4??;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/Graphik-Medium-Web.woff2) format("woff2"),
    url(/fonts/Graphik-Medium-Web.woff) format("woff");
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/GraphikCyr-Medium-Web.woff2) format("woff2"),
    url(/fonts/GraphikCyr-Medium-Web.woff) format("woff");
  font-style: normal;
  font-weight: 500;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
  unicode-range: U+4??;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/Graphik-Regular-Web.woff2) format("woff2"),
    url(/fonts/Graphik-Regular-Web.woff) format("woff");
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/GraphikCyr-Regular-Web.woff2) format("woff2"),
    url(/fonts/GraphikCyr-Regular-Web.woff) format("woff");
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
  unicode-range: U+4??;
}

@font-face {
  font-family: Guardian-EgypTT;
  src: url(/fonts/Guardian-EgypTT-Thin-It.woff2) format("woff2"),
    url(/fonts/Guardian-EgypTT-Thin-It.woff) format("woff");
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 96%;
  descent-override: 23%;
}

@font-face {
  font-family: Guardian-EgypTT;
  src: url(/fonts/StagCyr-LightItalic-Web.woff2) format("woff2"),
    url(/fonts/StagCyr-LightItalic-Web.woff) format("woff");
  font-style: normal;
  font-weight: 200;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 96%;
  descent-override: 23%;
  unicode-range: U+4??;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/Graphik-Medium-Web.woff2) format("woff2"),
    url(/fonts/Graphik-Medium-Web.woff) format("woff");
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
}

@font-face {
  font-family: Graphik Webfont;
  src: url(/fonts/GraphikCyr-Medium-Web.woff2) format("woff2"),
    url(/fonts/GraphikCyr-Medium-Web.woff) format("woff");
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  ascent-override: 92%;
  descent-override: 22%;
  unicode-range: U+4??;
}

html,
body {
  -webkit-font-smoothing: antialiased;
}

body.vintage {
  --sp-border-radius-1: 9999px;
  --sp-border-radius-2: 0.6rem;
  font-family: "Graphik Webfont",-apple-system,"Helvetica Neue","Droid Sans",Arial,sans-serif;
}

.vintage .font-secondary {
  font-family: "Guardian-EgypTT",-apple-system,"Helvetica Neue","Droid Sans",Arial,sans-serif
}

.btn-vintage-lighter, .btn-vintage {
  --hover-scale: 1.1;
  --bg: none;

  background: var(--bg);
  color: var(--color-highlight);
}

.btn-vintage-lighter::before, .btn-vintage::before {
  transform: scale(0);
  position: absolute;
  z-index: -1;
  top: 0;
  left: 0;
  content: '';
  display: block;
  height: 100%;
  width: 100%;
  border-radius: 999px;
  background: transparent;
  transition: transform 300ms cubic-bezier(0.345, 0.115, 0.135, 1.42),background 150ms ease-out;
}

.btn-vintage:hover::before {
  background: rgba(14,14,14,.0901960784);
  transform: scale(var(--hover-scale));
}

.btn-vintage-lighter:hover::before {
  background: rgba(255, 255, 255, 0.105);
  transform: scale(var(--hover-scale));
}

.btn-vintage-shadow:before, .btn-vintage-shadow:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
  border-radius: inherit;
}

.btn-vintage-shadow:before {
  box-shadow: var(--clg-effect-sem-shadow-elevation-3, 0px 1px 3px 0px #0000004d, 0px 4px 8px 3px #00000026);
  opacity: 0;
  border: 2px solid #0000;
  transition: transform .2s cubic-bezier(.345,.115,.135,1.42), opacity .15s ease-out;
}

.btn-vintage-shadow:after {
  background-color: var(--sp-btn-background);
  border: #222 solid 2px;
  transition: transform .2s cubic-bezier(.345,.115,.135,1.42), background .15s ease-out, box-shadow .2s ease-out;
}

.btn-vintage-shadow:hover:before {
  opacity: 1;
}