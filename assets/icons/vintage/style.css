@font-face {
  font-family: 'icomoon-vintage';
  src:  url('fonts/icomoon.eot?r9ogj1');
  src:  url('fonts/icomoon.eot?r9ogj1#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?r9ogj1') format('truetype'),
    url('fonts/icomoon.woff?r9ogj1') format('woff'),
    url('fonts/icomoon.svg?r9ogj1#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-vintage-"], [class*=" icon-vintage-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon-vintage' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-vintage-check:before {
  content: "\e90a";
}
.icon-vintage-plus:before {
  content: "\e909";
}
.icon-vintage-close:before {
  content: "\e908";
}
.icon-vintage-arrow-right:before {
  content: "\e907";
}
.icon-vintage-filters:before {
  content: "\e905";
}
.icon-vintage-menu:before {
  content: "\e904";
}
.icon-vintage-chevron-left:before {
  content: "\e900";
}
.icon-vintage-heart-outline:before {
  content: "\e902";
}
.icon-vintage-heart:before {
  content: "\e903";
}
.icon-vintage-cart:before {
  content: "\e942";
}
.icon-vintage-arrow-left:before {
  content: "\e906";
}
.icon-vintage-chevron-right:before {
  content: "\e901";
}
