@font-face {
  font-family: 'icomoon-basic';
  src:  url('fonts/icomoon.eot?7ge145');
  src:  url('fonts/icomoon.eot?7ge145#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?7ge145') format('truetype'),
    url('fonts/icomoon.woff?7ge145') format('woff'),
    url('fonts/icomoon.svg?7ge145#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-basic-"], [class*=" icon-basic-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon-basic' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-basic-checked:before {
  content: "\e919";
}
.icon-basic-sort:before {
  content: "\e918";
}
.icon-basic-minus:before {
  content: "\e917";
}
.icon-basic-filter:before {
  content: "\e916";
}
.icon-basic-chevron-up:before {
  content: "\e90e";
}
.icon-basic-coupon:before {
  content: "\e90f";
}
.icon-basic-gift:before {
  content: "\e910";
}
.icon-basic-location:before {
  content: "\e911";
}
.icon-basic-menu:before {
  content: "\e912";
}
.icon-basic-pencil:before {
  content: "\e913";
}
.icon-basic-plus:before {
  content: "\e914";
}
.icon-basic-search:before {
  content: "\e915";
}
.icon-basic-close:before {
  content: "\e900";
}
.icon-basic-cart:before {
  content: "\e901";
}
.icon-basic-chevron-down:before {
  content: "\e902";
}
.icon-basic-chevron-left:before {
  content: "\e903";
}
.icon-basic-chevron-right:before {
  content: "\e904";
}
.icon-basic-globe:before {
  content: "\e905";
}
.icon-basic-heart:before {
  content: "\e906";
}
.icon-basic-home:before {
  content: "\e907";
}
.icon-basic-size-chart:before {
  content: "\e908";
}
.icon-basic-star-empty:before {
  content: "\e909";
  color: #dadada;
}
.icon-basic-star-half .path1:before {
  content: "\e90a";
  color: rgb(235, 190, 0);
}
.icon-basic-star-half .path2:before {
  content: "\e90b";
  margin-left: -1em;
  color: rgb(218, 218, 218);
}
.icon-basic-start-full:before {
  content: "\e90c";
  color: #ebbe00;
}
.icon-basic-user:before {
  content: "\e90d";
}
