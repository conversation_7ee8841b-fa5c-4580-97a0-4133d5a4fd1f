// @import url("https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,600;1,700;1,800;1,900&display=swap");

// vars
@import "../icons/sen/style.css";
@import "normalize";
@import "variables";
@import "animations";
@import "splide";
// @import "vuetelinput";
@import "@splidejs/vue-splide/css/core";

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.no-scrollbar::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  display: none !important;
}

.no-scrollbar {
  -ms-overflow-style: none !important; /* IE and Edge */
  scrollbar-width: none !important; /* Firefox */
  margin: 0 -1.25rem;
  padding: 0 1.25rem !important;
}

.small-scroll::-webkit-scrollbar {
  width: 1px;
  height: 1px;
}

.medium-scroll::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  border-radius: 8px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--color-primary-lighter);
  border-radius: 8px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-light);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

div {
  scrollbar-width: thin;
  scrollbar-color: var(--color-primary);
}

html.dark {
  ::-webkit-scrollbar-track {
    background: theme("colors.slate.800");
    border-left: 1px solid theme("colors.slate.700");
  }
  ::-webkit-scrollbar-thumb {
    border-color: theme("colors.slate.800");
    background-color: theme("colors.slate.500");
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: theme("colors.slate.400");
  }
}

@media (max-width: 1023px) {
  .container {
    max-width: unset !important;
  }
}

@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 1px;
    height: 1px;
  }
}

.input-error {
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.input-success {
  padding-right: calc(1.5em + 0.75rem) !important;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
// animations

.border-radius-override,
[class*="btn-border-fill"]:not([class*="rounded"]),
[class*="btn-fill"]:not([class*="rounded"]) {
  border-radius: var(--sp-border-radius-1); /* Means default border radius */
}

.border-radius-override-p2,
.btn-border:not([class*="rounded"]),
.border:not([class*="rounded"]),
.product-item {
  border-radius: var(--sp-border-radius-2);
}

/* Hacky way to adjust the personalize option position on mobile */
@media (max-width: 767px) {
  body:has(.bottom-fixed > button[data-test-id="campaign-add-to-cart"]) .custom-opt-vari-position {
    bottom: 3.5rem; /* windicss: bottom-14 */
  }
}

.truncate-lines {
  --line: 3;

  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: var(--line);
  line-clamp: var(--line);
  word-break: break-word;
}

/* #### COMPONENTS #### */
// Accept Payment Gateways
.payment-gateway-accept {
  .icon-card-container {
    display: flex;
    justify-content: center;
    gap: 5px;
    flex-wrap: wrap;

    @media (min-width: 1024px) {
      justify-content: start;
    }
  }

  .icon-card {
    --base-height: 25;
    height: calc(var(--base-height) * 1px);
    background: #fff;
  }

  .payments-sprite {
    --scale-fac: calc(85.5 / var(--base-height)); /* scaling factor for height */
    display: block;
    border: 1px solid #d3d3d3;
    height: inherit;
    width: calc(150px / var(--scale-fac) + 4px);
    background: var(--sprite-url) no-repeat;
    background-size: calc(150px / var(--scale-fac));
    background-position: center calc((var(--icon-position) * (var(--base-height) * 1px) + 0.5px) * -1);
  }

  .payments-sprite-amex {
    --icon-position: 0;
  }
  .payments-sprite-paypal {
    --icon-position: 1;
  }
  .payments-sprite-visa {
    --icon-position: 2;
  }
  .payments-sprite-master {
    --icon-position: 3;
  }
  .payments-sprite-sofort {
    --icon-position: 4;
  }
  .payments-sprite-giropay {
    --icon-position: 5;
  }
}

// Deliver to [location]
@media (max-width: 767px) {
  .scroll-down {
    #PageHeader:not(.show-menu) {
      transform: translateY(-100%);
    }
  }
}

@media (min-width: 768px) {
  .scroll-down {
    #PageHeader {
      transform: translateY(-100%);
    }
  }
}

.deliver-to-location {
  grid-template-columns: 1fr 1fr 1fr;

  & span {
    font-size: 0.85rem;
    line-height: 1.2;
  }
}

// Loading dots
.loading-dots {
  &--dot {
    animation: dot-keyframes 1.2s infinite ease-in-out;
    border-radius: 100%;
    display: inline-block;
    height: 7px;
    width: 7px;
    margin: 3px;

    &:nth-child(2) {
      animation-delay: 0.3s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }

    &:nth-child(4) {
      animation-delay: 0.9s;
    }
  }
}

@keyframes dot-keyframes {
  0% {
    opacity: 0.4;
    transform: scale(1, 1);
  }

  50% {
    opacity: 1;
    transform: scale(1.2, 1.2);
  }

  100% {
    opacity: 0.4;
    transform: scale(1, 1);
  }
}

// Common productItem
.default .product-list-item:hover img {
  transform: scale(1.05);
}

.product-list-item button:hover .loading-dots--dot {
  background-color: white;
}
//  Common productCarousel
.arrow-on-ends button.splide__arrow[disabled] {
  display: none;
}

// popup.client.vue & modal.client.vue
.fade-enter-active,
.fade-leave-active {
  z-index: 3;
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  z-index: 3;
  opacity: 0;
}

// menuItem
// applies to themes: default, vintage
// and a variant of theme "default": classic
.default,
.vintage,
.classic {
  #headerMenu {
    .sub-menu {
      display: none;
      min-width: max-content;

      .menu-item {
        padding: 0.15rem 1rem;
        max-width: 250px;
        min-width: 150px;
      }

      &:not(:has(> * > .sub-menu)) {
        overflow-y: auto;
      }
    }

    .menu-item {
      &:hover {
        & > .sub-menu {
          display: inherit;
        }

        & > div * {
          color: var(--color-primary-hover);
        }
      }
    }
  }
}

.givehug {
  .viewbox-image img {
    border-radius: 10px !important;
  }

  #headerMenu {
    .sub-menu {
      min-width: max-content;
      box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;

      .menu-item {
        padding: 0.15rem 1rem;
        max-width: 250px;
        min-width: 150px;
      }

      &:not(:has(> * > .sub-menu)) {
        overflow-y: auto;
      }
    }

    .menu-item {
      & > .sub-menu-wrapper {
        transition: all 0.2s ease-in-out;
        visibility: hidden;
        pointer-events: none;
        top: 5.2rem;
        opacity: 0;
        display: block !important;
      }

      &:hover {
        & > .sub-menu-wrapper {
          color: #000000 !important;
          visibility: visible;
          pointer-events: auto;
          top: 2.2rem;
          opacity: 1;
        }
      }
    }

    .sub-menu li:hover a {
      color: var(--color-primary) !important;
    }
  }

  .vue-slider-rail {
    height: 1px !important;
  }

  .vue-slider-dot-handle {
    border-color: #000 !important;
  }

  .vue-slider-dot-handle:hover {
    border-color: var(--color-primary) !important;
  }

  .vue-slider-dot-handle:focus {
    border-color: var(--color-primary) !important;
    outline: none !important;
  }

  .vue-slider-process {
    background-color: #000 !important;
  }

  .product-list-item {
    border: 1px solid #f6f6f6;
    user-select: none;
    width: 100%;
    overflow: hidden;
    transition: all 0.2s ease-in-out;

    img {
      transition: all 0.2s ease-in-out;
    }
  }

  .product-list-item:hover {
    border-color: var(--color-primary);
  }

  .product-list-item img:hover,
  .cart-item img:hover {
    transform: scale(1.05);
  }

  .cart-item img,
  .cart-item a h5 {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  .cart-item a:hover h5 {
    color: var(--color-primary);
  }

  .border-givehug {
    border: 1px solid #eaeaea;
    border-radius: 10px;
  }

  .bg__foreground {
    background-color: rgb(14, 14, 14, 0.03);
  }

  .square-image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }

  .btn-givehug {
    border-radius: 9999px;
    background-color: var(--color-primary);
    color: #ffffff;
    transition: all 0.2s ease-in-out;
  }

  .btn-givehug:hover {
    opacity: 0.95;
  }

  .givehug__background__description {
    background: linear-gradient(180deg, rgba(241, 241, 241, 0.5) 0%, rgba(255, 255, 255, 0.5) 27.4%);
  }

  .givehug__shadow {
    box-shadow: rgba(100, 100, 111, 0.1) 0px 7px 29px 0px;
  }

  .popover {
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
    border-radius: 10px;
    border: none !important;
  }

  .givehug__description ul {
    padding-left: 1.2rem !important;
    list-style-type: disc;
    margin-bottom: 0.5rem !important;
    font-size: 0.8rem !important;
  }

  .givehug__description p + ul {
    margin-top: -0.5rem !important;
  }

  .givehug__description p {
    margin-bottom: 0.5rem !important;
  }

  /* Personalize PB */
  .personalized-form {
    .ant-col.ant-form-item-label {
      font-family: Poppins;
      font-weight: 600;
      font-size: 14px;
      line-height: 100%;
      letter-spacing: 0%;
    }

    .image-item {
      background-color: transparent;
      aspect-ratio: 1/1;
      width: auto !important;
      border-radius: 10px !important;
      height: auto !important;
      border: 1px solid #eaeaea !important;
      box-shadow: none !important;
    }

    .image-item.active {
      border: 1px solid var(--color-primary) !important;
    }

    .ant-col.ant-form-item-control-wrapper {
      margin-top: 12px;
    }

    .ant-form-item-control-wrapper input {
      border-radius: 10px !important;
      background-color: #f3f3f3 !important;
      border: 1px solid transparent !important;
      padding: 12px 20px !important;
      outline: none !important;
      box-shadow: none !important;
      margin-top: -11px !important;
    }

    .ant-form-item-control-wrapper input:focus {
      outline: none !important;
    }

    .btn.btn-upload {
      border-radius: 10px !important;
      border: 1px dashed var(--color-primary) !important;
      font-size: 0.85rem !important;
      width: 100% !important;
      height: 108px !important;
      background-color: #fff2ec !important;
      padding-top: 35px !important;
    }

    .btn-upload::before {
      content: "";
      position: absolute;
      left: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
      width: 30px;
      height: 30px;
      background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'><path d='M16.0625 25.3125V19.6875H19.8125L15.125 14.0625L10.4375 19.6875H14.1875V25.3125H9.5V25.2656C9.3425 25.275 9.1925 25.3125 9.03125 25.3125C7.16645 25.3125 5.37802 24.5717 4.05941 23.2531C2.74079 21.9345 2 20.1461 2 18.2812C2 14.6737 4.72813 11.7338 8.22875 11.3306C8.53568 9.72618 9.3921 8.27883 10.6507 7.23751C11.9093 6.1962 13.4915 5.626 15.125 5.625C16.7588 5.6259 18.3412 6.19602 19.6001 7.23732C20.859 8.27861 21.7158 9.72602 22.0231 11.3306C25.5238 11.7338 28.2481 14.6737 28.2481 18.2812C28.2481 20.1461 27.5073 21.9345 26.1887 23.2531C24.8701 24.5717 23.0817 25.3125 21.2169 25.3125C21.0594 25.3125 20.9075 25.275 20.7481 25.2656V25.3125H16.0625Z' fill='%23F1641E'/></svg>")
        center / contain no-repeat;
    }

    .input-count {
      color: black !important;
      font-weight: 500 !important;
    }

    .niftyjs-grid > div {
      margin-bottom: 0 !important;
    }

    .niftyjs-grid {
      gap: 16px 22px !important;
      grid-template-columns: repeat(auto-fill, minmax(76px, 1fr)) !important;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(65px, 1fr)) !important;
        gap: 15px 9px !important;
      }
    }

    .niftyjs-grid div:empty[style*="margin-bottom: 0px;"] {
      display: none !important;
    }
  }
  .personalized-form > div {
    margin-bottom: 24px !important;
  }
  .personalized-form > div:last-child {
    margin-bottom: 0 !important;
  }
  .add-to-cart-group {
    display: none !important;
  }

  /* vue-slider */
  .vue-slider-dot {
    width: 11px !important;
    height: 11px !important;
  }

  .vue-slider-dot-handle {
    border: 1px solid #000000 !important;
  }

  /* Order Status */
  .orderstatus {
    .orderstatus-item {
      border: 1px solid #eaeaea;
      border-radius: 10px;
    }
  }

  .splide__track {
    position: relative;
    margin: 0 -1.25rem !important;
    padding: 0 1.25rem !important;
  }

  .splide__track::before,
  .splide__track::after {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 1.2rem;
    height: 100%;
    display: block;
    z-index: 1;
    content: "";
    pointer-events: none;
  }

  .splide__track::before {
    right: auto;
    left: 0;
    background: linear-gradient(90deg, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 100%);
  }

  .splide__track::after {
    left: auto;
    right: 0;
    background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
  }

  img:not([alt*=flag]):not([class*='rounded']) {
    border-radius: 0;
  }

  /* Crisp Chat */
  .crisp-client {
    display: none !important;
  }

  .crisp-client.opened {
    display: block !important;
  }

  .livechatbtn {
    display: none !important;
  }

  .banner {
    cursor: pointer;
  }
  .banner div {
    transition: all 0.2s ease-in-out;
  }

  .banner-item-img {
    transform: scale(1);
    transition: all 0.2s ease-in-out;
  }

  .banner:hover .banner-item-img {
    transform: scale(1.05);
  }

  .trans__time {
    transition: all 0.2s ease-in-out;
  }

  .hover__scale,
  .hover__scale2 {
    transition: all 0.2s ease-in-out;
  }

  .hover__scale:hover {
    transform: scale(1.01);
  }

  .hover__scale2:hover {
    transform: scale(1.1);
  }
}

.givehug.scroll-down #PageHeader {
  transform: translateY(0) !important;
}

@media (max-width: 768px) {
  .crisp-client {
    display: none !important;
  }

  .crisp-client.opened {
    display: block !important;
  }

  .livechatbtn {
    display: block !important;
    position: fixed;
    right: 0px;
    top: 0px;
    height: 100%;
    transform: translateY(50%);
    z-index: 9;

    & span {
      white-space: nowrap;
      position: absolute;
      transform: rotate(-90deg) translateY(-200%);
      padding: 0.3rem 1rem;
      border: 0;
      border-radius: 0.3rem 0.3rem 0 0;
      border: 1px solid var(--color-primary);
      background: white;
    }
  }
}

.vue-slider-disabled .vue-slider-process {
  background-color: #a7a7a7;
}
.vue-slider-disabled .vue-slider-dot-handle {
  border-color: #a7a7a7;
}
.vue-slider-disabled .vue-slider-mark-step-active {
  box-shadow: 0 0 0 2px #a7a7a7;
}

/* rail style */
.vue-slider-rail {
  background-color: whitesmoke;
  border-radius: 15px;
  transition: background-color 0.3s;
}
.vue-slider:hover .vue-slider-rail {
  background-color: #e1e1e1;
}

/* process style */
.vue-slider-process {
  background-color: var(--color-primary-light);
  border-radius: 15px;
  transition: background-color 0.3s;
}
.vue-slider:hover .vue-slider-process {
  background-color: var(--color-primary);
}

/* mark style */
.vue-slider-mark-step {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  box-shadow: 0 0 0 2px #e8e8e8;
  background-color: #fff;
}
.vue-slider-mark-step-active {
  box-shadow: 0 0 0 2px var(--color-primary-light);
}
.vue-slider:hover .vue-slider-mark-step-active {
  box-shadow: 0 0 0 2px var(--color-primary);
}

.vue-slider-mark-label {
  font-size: 12px;
  white-space: nowrap;
}
/* dot style */
.vue-slider-dot-handle {
  cursor: pointer;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid var(--color-primary);
  box-sizing: border-box;
  transition:
    box-shadow 0.3s,
    border-color 0.3s;
}
.vue-slider:hover .vue-slider-dot-handle {
  border-color: var(--color-primary-hover);
}

.vue-slider-dot-handle-focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 5px rgba(54, 171, 255, 0.2);
}
.vue-slider:hover .vue-slider-dot-handle-focus {
  border-color: var(--color-primary);
}

.vue-slider-dot-handle:hover {
  border-color: var(--color-primary);
}
.vue-slider:hover .vue-slider-dot-handle:hover {
  border-color: var(--color-primary);
}

.vue-slider-dot-handle-disabled {
  cursor: not-allowed;
  border-color: #ddd !important;
}

.vue-slider-dot-tooltip {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}
.vue-slider-dot-tooltip-inner {
  font-size: 14px;
  white-space: nowrap;
  padding: 6px 8px;
  color: #fff;
  border-radius: 5px;
  border-color: rgba(0, 0, 0, 0.75);
  background-color: rgba(0, 0, 0, 0.75);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: scale(0.9);
  transition: transform 0.3s;
}
.vue-slider-dot-tooltip-inner::after {
  content: "";
  position: absolute;
}
.vue-slider-dot-tooltip-inner-top::after {
  top: 100%;
  left: 50%;
  transform: translate(-50%, 0);
  height: 0;
  width: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px;
  border-top-color: inherit;
}
.vue-slider-dot-tooltip-inner-bottom::after {
  bottom: 100%;
  left: 50%;
  transform: translate(-50%, 0);
  height: 0;
  width: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px;
  border-bottom-color: inherit;
}
.vue-slider-dot-tooltip-inner-left::after {
  left: 100%;
  top: 50%;
  transform: translate(0, -50%);
  height: 0;
  width: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px;
  border-left-color: inherit;
}
.vue-slider-dot-tooltip-inner-right::after {
  right: 100%;
  top: 50%;
  transform: translate(0, -50%);
  height: 0;
  width: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px;
  border-right-color: inherit;
}
.vue-slider-dot-tooltip-inner-top {
  transform-origin: 50% 100%;
}
.vue-slider-dot-tooltip-inner-bottom {
  transform-origin: 50% 0;
}
.vue-slider-dot-tooltip-inner-left {
  transform-origin: 100% 50%;
}
.vue-slider-dot-tooltip-inner-right {
  transform-origin: 0% 50%;
}

.vue-slider-dot:hover .vue-slider-dot-tooltip,
.vue-slider-dot-tooltip-show {
  opacity: 1;
  visibility: visible;
}
.vue-slider-dot:hover .vue-slider-dot-tooltip .vue-slider-dot-tooltip-inner,
.vue-slider-dot-tooltip-show .vue-slider-dot-tooltip-inner {
  transform: scale(1);
}
