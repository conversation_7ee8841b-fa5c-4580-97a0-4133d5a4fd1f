declare global {
  interface TrackingCode {
    google_tag?: string
    google_merchant_verification?: string
    google_analytics?: string
    google_adwords_id?: string
    google_ads_gtag?: string
    google_ads_gtag_thank_you?: string
    facebook_pixel?: string
    facebook_meta_tag?: string
    facebook_conversion_token?: string
    pinterest_meta_tag?: string
    pinterest_tag_id?: string
    tiktok_pixel?: string
    facebook_pixel_version?: string
    snapchat_pixel?: string
    quora_pixel?: string
    bing_pixel?: string
    klaviyo_public_key?: string
    reddit_pixel?: string
  }

  interface StoreContact {
    address?: string
    phone?: string
    email?: string
    email_info?: string
  }

  interface Banner {
    banner_url?: string
    banner_link?: string
    banner_text?: string
  }

  interface Menu {
    name?: string
    title?: string
    url?: string
    submenu?: Menu
  }

  interface SocialsLink {
    facebook?: string
    instagram?: string
    twitter?: string
    google?: string
    skype?: string
    pinterest?: string
    youtube?: string
  }

  interface Collection {
    id: number
    name: string
    slug: string
  }

  interface TestPriceTemplate {
    id: number
    seller_id: number
    product_id: number
    variant_key: any
    price: number
    test_price_percent: number
    test_price: number
    test_price_applied_at: string
    compare_price: number
    currency_code: string
    location_code: string
    status: number
  }

  interface HeadTag {
    store_id: number
    tag: 'script' | 'script_src' | 'html' | 'style'
    code: string
    position: 'head' | 'body' | 'footer_start' | 'footer_middle' | 'footer_end'
    priority: number
    name: string
    additional_properties: string | null
    path: string | null
  }

  interface UserInfo {
    name: string
    email: string
    address: string
    address_2: string
    city: string
    state: string
    zipcode: string
    postcode?: string
    phone: string
    country: string
    subscribed: 0 | 1
    house_number?: string
    mailbox_number?: string
    note?: string
  }

  interface VisitInfo {
    clientIp?: string
    country?: string
    currency_code?: string
    device?: string
    device_detail?: string
    device_id?: string | number
    email?: string
    seller_id?: string | number
    session_id?: string | number
    spsid?: string | number
    visitTime?: number
    csrfToken?: string
    dd?: boolean

    ad_source?: string
    ad_campaign?: string
    ad_medium?: string
    ad_id?: string
    user_id?: number | string
    city?: string
    region?: string
    latitude?: string
    longitude?: string
  }

  interface ClientInfo {
    ip: string
    ts: string
    sliver: string
    city: string
    region: string
    latitude: string
    longitude: string
    http: string
    tls: string
    warp: string
    gateway: string
    rbi: string
    kex: string
    country: string
    url: string
    queries: object
    browser: IResult
    device: Device
    os: IOS
    useragent: string
    seller_id: string | number
  }

  type ListingSoftType = 'popular' | 'newest' | 'highest_price' | 'lowest_price'

  interface UserBehavior {
    currencyCode?: CurrencyCode
    sizeByProductName?: { [key: string]: string }
    colorByProductName?: { [key: string]: string }
    [key: string]: string | boolean | number | undefined | { [key: string]: string }
  }

  interface StoreInfo {
    id?: number
    name: string
    sub_domain: string
    domain: string
    logo_url: string
    theme: string
    theme_options: any
    foot_line?: string
    promotion_title?: string
    discount_code?: string
    auto_apply_coupon: string
    seller_id?: number | string
    company_id: any
    checkout_phone: number
    tracking_code: TrackingCode
    favicon: string
    default_currency: CurrencyCode
    default_language: string
    default_color: string
    feature_collection_id: number
    featured_collection_ids: string
    feature_text: string
    list_all_my_campaigns: 0 | 1
    random_popular: 0 | 1
    market_place_listing: 0 | 1
    market_place_upsell: 0 | 1
    status: string
    store_type: 'normal' | 'google_ads' | 'express_listing'
    show_payment_button: 0 | 1
    show_tipping: 0 | 1
    product_select_type: string
    show_checkout_shipping_info: 0 | 1
    sitewide_banner: string
    sitewide_banner_enable: 0 | 1
    option_label_enable: 0 | 1
    is_proxy: 0 | 1
    product_review_display: 'disable' | 'enable-share-review'
    product_review_coupon: string
    product_review_thank_you_message: string
    order_prefix: any
    tags: any
    smart_remarketing: 0 | 1
    currentDomain: string
    description: string
    storeContact: StoreContact
    banners: Array<Banner>
    collection_banners: Array<Banner>
    headerMenu: Array<Menu>
    footerMenu: Array<Menu>
    socialsLink: SocialsLink
    collections: Array<Collection>
    languages: Array<Language>
    primaryColor: string
    disable_promotion: 0 | 1
    disable_related_product: 0 | 1
    disable_related_collection: 0 | 1
    disable_pre_discount: 0 | 1
    trackingCode: string
    test_price_templates: Array<TestPriceTemplate>
    head_tags: Array<HeadTag>
    style: string | null
    enable_search: 0 | 1 | null
    enable_add_to_cart: 0 | 1
    enable_contract_form: 0 | 1
    enable_custom_phone: 0 | 1
    enable_product_name_after: 0 | 1
    payment_gateway_type: string
    always_show_order_summary: 0 | 1
    enable_dynamic_base_cost: 0 | 1
    order_summary_position: 'top' | 'bottom'
    enable_distributed_checkout: 0 | 1
    enable_payment_ssl_norton: 0 | 1
    enable_insurance_fee: 0 | 1
    enable_deliver_to: 0 | 1
    enable_crisp_support: 0 | 1
    seller_id: number
  }

  interface LastOrder {
    order_number: string
    order_token: string
  }

  interface AbandonedOrderKey {
    order_token: string
    cart_key: string
  }

  interface UserSession {
    userInfo: UserInfo
    clientInfo: Partial<ClientInfo>
    userBehavior: UserBehavior
    visitInfo: VisitInfo
    lastOrder: LastOrder
    orderKey: AbandonedOrderKey
    isTestPriceApply: boolean
    currentSellerId?: number | string
    sessionExpireTime: number
    nocacheExpireTime: number
  }

  interface CartItem {
    id: string
    campaign_id: number
    campaign_slug: string
    campaign_title: string
    campaign_system_type: string
    campaign_thumb_url: string
    campaign_product_ids: number[]
    combo_price: number
    combo_id?: string
    currency_code: CurrencyCode
    isCheckQuantity: boolean
    optionList: { [key: string]: Array<string> }
    options: { [key: string]: string }
    price: number
    variantPrice?: number
    product_id: number
    product_name: string
    product_url?: string
    quantity: number
    thumb_url: string
    template_id: number
    full_printed?: 0 | 1 | 2 | 3 | 4 | 5

    personalized?: 0 | 1 | 2 | 3
    designs?: { [key: string]: string }
    custom_options?: { [key: string]: string }

    // For AI campaign design images
    design_image_base64?: string
    original_thumb_url?: string
    aws_uploaded?: boolean

    pbCustomInfo?: object
    pbPrintUrl?: string
    campaignBundleId?: number
    promotion?: BundlePromotion
    seller_id: number | undefined
    via: string | undefined
    extra_custom_fee: number
    customer_custom_options: Array<Array<PersonalizeCustomOptionsItem>>
    productBundleId?: number
  }

  interface ComboItem {
    id: string
    campaign_id: number
    quantity: number
    thumb_url: string
    title: string
    product_ids: number[]
    items: CartItem[]
    combo_price: number
    products: Product[]
    country: string // country when add to cart
  }
}

export {}
