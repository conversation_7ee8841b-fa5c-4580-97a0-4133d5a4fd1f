declare global {
  type CurrencyCode = 'USD' | 'EUR' | 'VND' | 'MXN' | 'AUD' | 'CAD' | 'GBP' | 'BRL' | 'TRY' | 'INR' | 'JPY' | 'BDT'

  interface Category {
    id: number
    name: string
    slug: string
    parent_id: number | null
    child_menu?: Array<Category> | null
  }

  interface Color {
    id: number
    name: string
    hex_code: string
    is_heather?: number
    multi_color_mode?: number
  }

  interface Language {
    code: string
    name: string
  }

  interface Country {
    code: string
    name: string
    default_currency_code: string
    region_code: string | null
  }

  interface SizeGuide {
    id: number
    product_id: number
    sku: string
    size: string
    length: number | null
    width: number | null
    sleeve: number | null
    hip: number | null
    waist: number | null
    height: number | null
    weight: number | null
    unit: 'in' | 'cm'
    default_currency_code: string
    note: string | null
  }

  interface Template {
    id: number
    name: string
  }

  interface Currency {
    code: CurrencyCode
    locale: string
    name: string
    rate: number
    settable: number
  }

  interface CheckoutFormConfigItem {
    label?: string
    width?: number
    required: boolean
  }

  type CheckoutFormConfigItemKey = 'fullName' | 'phoneNumber' | 'address_1' | 'address_2' | 'postCode' | 'city' | 'state' | 'houseNumber' | 'mailboxNumber' | 'deliveryNote'
  type CheckoutFormConfigPosition = 'address' | 'city' | 'state' | 'postCode'

  interface CheckoutFormConfigsObjects {
    fullName: CheckoutFormConfigItem
    phoneNumber: CheckoutFormConfigItem
    address_1: CheckoutFormConfigItem
    address_2: CheckoutFormConfigItem
    postCode: CheckoutFormConfigItem
    city: CheckoutFormConfigItem
    state?: CheckoutFormConfigItem
    houseNumber?: CheckoutFormConfigItem
    mailboxNumber?: CheckoutFormConfigItem
    position: Array<CheckoutFormConfigPosition>
    deliveryNote?: CheckoutFormConfigItem
  }

  interface CheckoutFormConfig {
    [key: string]: CheckoutFormConfigsObjects
  }

  interface GeneralSettings {
    categories: Array<Category>
    colors: Array<Color>
    languages: Array<Language>
    countries: Array<Country>
    size_guides: Array<SizeGuide>
    templates: Array<Template>
    currencies: Array<Currency>
    paypal_discount: any
    country_disabled_checkout: Array<string>
    checkout_form_config: CheckoutFormConfig | null
  }

  interface CurrentOptions {
    [key: string]: any
  }
}

export {}
