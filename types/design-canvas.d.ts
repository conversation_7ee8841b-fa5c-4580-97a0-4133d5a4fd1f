 
import type { JsonCanvasExport} from 'fabric';
import { fabric } from 'fabric'
export declare namespace DesignCanvas {
    class BaseMockup extends fabric.Canvas {
      strokeWidth?: number
      mockupOverlay?: boolean
      strokeColor?: string
      printSpace?: fabric.Rect
      locked: any
      designGuide: any
      constructor(canvasEl: string | HTMLCanvasElement | null, options?: fabric.ICanvasOptions);
      setDisplaySize(displayWidth: number): void;
      setMockup(imgUrl: string): Promise<unknown>;
      setMockupOverlay(overlay?: boolean): void;
      addPrintSpace(width: number, height: number): void;
      viewDesign(canvas: string | HTMLImageElement | HTMLVideoElement): void;
      setMockupColor(hex: string): void;
      getObjectByName(name: string): fabric.Object;
      loadFromJSONPromise(json: any): Promise<fabric.Canvas>;
      setDesignGuide(imgUrl?: string): Promise<unknown>;
      lock(locked?: boolean): void;
      enableToughScroll(): void;
      toJSON(): string;
      toBlob(): Promise<unknown>;
      _loadImg(fileUrl: string): Promise<fabric.Image>;
    }
    class DesignManager {
      container: HTMLElement | null
      designs: {
            [key: string]: DesignCanvas;
        }

      applySameDesign: boolean
      restricted: any
      activeDesign?: DesignCanvas
      activeDesignId?: string
      constructor(containerID: string);
      loadDesign(params: any): DesignCanvas;
      getDesign(id: string): false | DesignCanvas;
      copyDesign(newDesign: DesignCanvas): Promise<unknown>;
      getActiveDesign(): DesignCanvas;
      setActiveDesign(id?: string): false | DesignCanvas;
      setRestricted(restricted?: boolean): void;
      _applySameDesign(design: DesignCanvas): void;
      _designUpdated(design: DesignCanvas): void;
      onupdate(design: DesignCanvas): void;
      copyObject(design: DesignCanvas, obj: fabric.IObjectOptions): void;
      _copyCustomImage(design: DesignCanvas, image: fabric.Image): void;
      _copyCustomText(design: DesignCanvas, objText: fabric.IText): void;
      _removeObject(design: DesignCanvas, obj: fabric.IObjectOptions): void;
    }
    class DesignCanvas extends fabric.Canvas {
      locked: any
      isMain: any
      texts?: fabric.IText[]
      designUrl?: string
      manager?: DesignManager
      restricted: any
      key: any
      printSpace: any
      canvasEl: any
      designs: fabric.Object[]
      onUpdates: Function[]
      textLimit: number
      DPI: number
      minDPI: number
      unique: boolean
      bgColor?: string | fabric.Pattern | fabric.Gradient
      printWidth?: number
      printHeight?: number
      displayWidth?: number
      printRect?: fabric.Rect
      strokeWidth?: number
      strokeColor?: string
      printStroke?: fabric.Rect
      viewStrokeColor?: string
      viewRect?: fabric.Rect
      json?: JsonCanvasExport
      loadingJson?: boolean
      loadFrontPromises?: Promise<boolean | undefined>[]
      isLockEdit?: boolean
      oldImage?: fabric.Image
      printCanvas?: fabric.StaticCanvas | HTMLCanvasElement
      onupdate?: Function
      _currentTransform: any
      contextContainer: any
      toCanvasElement: any
      constructor(params: any);
      load(json: JsonCanvasExport | string, displayWidth?: number, correctData?: boolean): Promise<unknown>;
      setDisplaySize(displayWidth: number): void;
      lockEdit(): void;
      setRestricted(restricted?: boolean): void;
      cloneActiveObject(): void;
      addDesign(designUrl?: string, adjustedScale?: number): Promise<unknown>;
      addCustomImage(imageUrl: string): Promise<fabric.Image>;
      changeCustomImage(imageUrl: string): Promise<unknown>;
      getCustomImage(): fabric.Object;
      getRequiredSize(image: fabric.Image): {
            width: number;
            height: number;
        };

      hasCustomText(): boolean;
      addText(message: string, name: string, fontFamily?: string, fontUrl?: string): fabric.IText;
      _resetTextPosition(objText: fabric.IText): void;
      loadFont(fontFamily?: string, fontUrl?: string): Promise<boolean>;
      canAddText(): boolean;
      updateText(obj: fabric.IText, message: string): void;
      convertText(obj: fabric.IText, message: string): string;
      updateTextName(obj: fabric.IText, name: string): void;
      _saveOldPosition(obj: fabric.IObjectOptions, reset?: boolean): void;
      _correctTextPosition(obj: fabric.IText): void;
      updateFont(obj: fabric.IText, fontFamily: string, fontUrl?: string): void;
      setColor(obj: fabric.IText, color: string): void;
      getObjectByName(name: string): fabric.Object;
      changeDesignByCharacter(character: string): Promise<unknown>;
      replaceDesign(designUrl: string, adjustedScale?: number): Promise<unknown>;
      _copyDesignConfig(newImg: fabric.Image, img: fabric.Image): void;
      _addDesign(design: fabric.Object): void;
      setBackgroundColor(color: string | fabric.Pattern | fabric.Gradient, callback: Function): fabric.Canvas;
      reset(): void;
      removeDesign(): void;
      removeObject(obj: fabric.Object, systemUpdate?: boolean): void;
      removeActiveObject(): void;
      hasDesign(): boolean;
      hasArtwork(): boolean;
      _correctPosition(obj: fabric.Object): void;
      _correctScale(obj: fabric.Object): void;
      align(val: string, obj?: fabric.Object): void;
      getMinDPI(): number;
      isGoodDPI(): boolean;
      isNormalDPI(): boolean;
      isLowDPI(): boolean;
      checkDPI(): 'normal' | 'good' | 'low';
      _loadImg(fileUrl?: string): Promise<fabric.Image>;
      exportCanvas(printSize?: number): fabric.StaticCanvas | HTMLCanvasElement;
      exportPrint(): this;
      exportPNG(toPrint?: boolean): string;
      exportJson(): string;
      backup(json: JsonCanvasExport): void;
      show(): void;
      hide(): void;
      _fitImageToRect(img: fabric.Image, rect: fabric.Rect, fit?: boolean): void;
      _grayoutNonPrintArea(): void;
      _hexToRgbA(hex: string, opacity?: string): string;
      addUpdateCallback(callback: Function): void;
      _onupdate(system?: boolean): void;
    }
}
