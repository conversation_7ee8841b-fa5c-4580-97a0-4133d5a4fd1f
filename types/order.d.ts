declare global {

  interface ProductReview {
    average_rating: number
  }
  interface OrderProduct extends Product {
    product_review: any
    id: number
    campaign_id: number
    campaign_title: string
    product_name: string
    product_url: string
    options: string
    custom_options: null
    order_id: number
    price: number
    product_id: number
    template_id: number
    base_cost: number
    base_shipping_cost: number
    shipping_cost: number
    quantity: number
    thumb_url: string
    total_amount: number
    discount_amount: number
    personalized: number
    full_printed: number
    fulfill_status: string
    shipping_rule_id: number
    supplier_id: null
    shipping_rule: ShippingRule
    tracking_url: string
    shipping_carrier: string
    combo_id?: string
    campaign?: Campaign
  }

  interface Gateway {
    id: number
    name: string
    checkout_domain: any
    clientId: string
    gateway: string
    location: any
    options: {
      klarna_currency: CurrencyCode
      other_payments: boolean
    }
    rate_limit: number
    seller_id: any
    seller_status: any
    store_id: any
    paypal_merchant_id: string | null
  }

  interface Order {
    id: number
    order_token: string
    access_token: string
    order_number: string
    discount_code: string
    products: Array<OrderProduct>
    store: {
      base_url: string
      default_tipping: number
      id: number
      show_tipping: number
    }
    address_verified: string
    address: string
    address_2?: string
    city: string
    mailbox_number: string
    house_number: string
    country: string
    currency_code: CurrencyCode
    currency_rate: number
    customer_email: string
    customer_name: string
    customer_phone: string
    fraud_status: string
    fulfill_status: string
    delivery_insurance: boolean
    insurance_fee: number
    insurance_fee_2: number
    paid_at?: string
    payment_discount: number
    payment_gateway_id: number
    payment_domain?: string
    postcode: string
    processing_fee: number
    promotion_rule_id: number
    request_cancel: any
    seller_id: number
    session_id: string
    shipping_method: string
    state: string
    status: string
    store_id: number
    tip_amount: number
    total_amount: number
    total_discount: number
    total_product_amount: number
    total_product_base_cost: number
    total_quantity: number
    total_shipping_amount: number
    total_tax_amount: number
    type: string
    updated_at: string
    order_note: string
    is_notify_china_delivery_late?: boolean
    payment_method: string
  }

  interface ShippingMethod {
    description: string
    name: string
    printing_time: number
    shipping_cost: number
    shipping_time: Array<number>
  }

  interface ListGateway {
    stripeGateway?: Gateway
    paypalGateway?: Gateway
  }

  type FulfillStatus = 'unfulfilled' | 'cancelled' | 'processing' | 'on_delivery' | 'fulfilled'

  interface Timeframe {
    received: string
    validate: string
    print: string
    package: string
    ship: string
    delivered: null
  }

  interface RequestCancel {
    order_id: number
    status: string // need explicit type
    sent_email: number
  }

  // Actual use
  type Fulfillments = Record<FulfillStatus, OrderProduct[]>
}

export {}
