declare global {
  interface RelatedProductPostFilter {
    product_id: number,
    campaign_id: number,
    template_id?: number,
  }

  interface RelatedProductPostData {
    type: string | 'post_sale' | 'cart',
    filter: RelatedProductPostFilter[],
    source?: 'thank_you' | 'order_status',
    order_id?: number,
  }

  interface RelatedProductOptions {
    color: string[],
    size: string[],
  }

  interface RelatedProduct extends Product {
    r_price?: number;
    r_old_price?: number;
  }
}

export {}
