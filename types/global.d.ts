declare global {

  interface Window {
    MSStream: any
    google: any
    opera: any
    paypal: any
    Stripe: any
    gtag: any
    dataLayer: any
    snaptr: any
    fbq: any
    pintrk: any
    ttq: any
    qp: any
    uetq: any
    klaviyo: any
    rdt: any
    trackingInit: boolean
    eventQueueList: Array<TrackEventData>
    userActivity: number
    isSupportWebp: boolean
    loadingUploadImage: Promise<any>
    pbsdk: {
      form: object
      getCustomData: () => any
      getPreviewImage: () => any
      init: (initOptions: {
        key?: string
        artworkId?: string
        $formEl: string
        $canvasEl: string
        onChange: (pbCustomInfo: any) => any
      }) => any
      validate: (key?: string) => any
    }
    searchInput: HTMLElement | null

    DesignCanvas: DesignCanvas
  }

  interface HTMLImageElement {
    complete: boolean
    onload: () => void
  }
  interface EventTarget {
    value: string
  }
  type RequireAllExcept<T, K extends keyof T> = Required<Omit<T, K>> & Pick<T, K>
}
export { }
