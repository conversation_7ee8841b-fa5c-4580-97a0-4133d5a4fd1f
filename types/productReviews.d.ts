declare global {
  type ProductReviewStars = 'five' | 'four' | 'three' | 'two' | 'one'
  type ProductReviewFilter = 'helpful' | 'newest' | `${ReviewStars}_star`

  interface ProductReviewMedia {
    src: string
    thumb: string
    type: 'image' | 'video'
  }

  interface ProductReview {
    id: string
    customer_name: string
    avatar_url?: string
    customer_location: string
    average_rating: string
    product_name: string
    product_url: string
    product_size: string
    product_color: string
    comment: string
    files: ProductReviewMedia[]
    allow_sharing: 'share_campaign' | string
    score: string
    created_at: string
  }

  interface ProductReviewSummary {
    files: ProductReviewMedia[]
    summary: {
      average_rating: number | string
      review_count: string
      five_star_count: number | string
      four_star_count: number | string
      three_star_count: number | string
      two_star_count: number | string
      one_star_count: number | string
    }
  }

  interface ProductReviewRatingStats {
    average_rating: number
    review_count: number
    best_rating: number | null
    worst_rating: number | null
  }

  interface ProductReviewStore {
    reviewSummary: {
      [key: string]: ProductReviewSummary
    }
    reviews: {
      [key: string]: PaginatorResponse<ProductReview[]>
    }
  }

  interface ProductReviewComposable {
    reviewSummary: ProductReviewSummary
    reviews: PaginatorResponse<ProductReview[]>
    page: number | string
    filter: ProductReviewFilter
  }
}

export {}
