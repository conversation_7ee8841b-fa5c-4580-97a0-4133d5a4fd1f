declare global {
  interface BlogCollection {
    id: number
    name: string
    slug: string
  }

  interface BlogSeller {
    id: number
    name: string
  }

  interface BlogCategory {
    id: number
    name: string
  }

  interface Blog {
    id: number
    store_id: number
    seller_id: number
    title: string
    category_id: number
    collections: BlogCollection[]
    sub_description: string
    html: string
    main_image: string
    status: number
    slug: string
    created_at: string
    updated_at: string
    seller: BlogSeller
    category: BlogCategory
  }

  type BlogResponse = PaginatorResponse<Blog[]>
}

export {}
