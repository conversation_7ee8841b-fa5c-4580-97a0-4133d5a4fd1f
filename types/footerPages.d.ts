declare global {
  interface FAQ {
    id: number;
    category_id: number;
    question: string;
    answer: string;
    language: string;
    position: number;
  }

  interface FAQCategory {
    id: number;
    title: string;
    language: string;
    position: number;
  }

  interface ProcessedFAQResponse extends FAQCategory {
    faqList: FAQ[];
  }

  interface FAQResponse {
    faq_category: FAQCategory[];
    faq: FAQ[];
  }

  interface PageData {
    id: number;
    title: string;
    slug: string;
    content: string;
  }
}

export {}
