declare global {

  type Slug = string
  interface OptionsList {
    [key: string]: Array<string>
  }

  interface Seller {
    id: number
    name: string
    nickname: string
    status: string
    custom_payment: 0 | 1
    slug: string
    sharding_status: number
    via: string
  }

  interface ImageData {
    id: number
    token: string
    campaign_id: number
    product_id: number
    file_url: string
    option: any
    print_space: string
    type: string
    type_detail: any
    mockup?: Mockup
  }

  interface Variant {
    variant_key: string
    product_id: number
    adjust_price: number
    price: number
    old_price: number
    r_price: number
    r_old_price: number
    out_of_stock: number
    location_code: string
    base_cost: number
  }

  interface DesignItem {
    id: number
    campaign_id: number
    product_id: number
    design_json: string
    option: string
    print_space: string
    token: string
    type: string
  }

  interface BaseMockup extends DesignItem {
    file_url_2: string
  }

  interface CustomDesign extends DesignItem {
    file_url: string
  }

  interface PbArtwork extends DesignItem {
    pb_artwork_id: string
  }

  interface Personalize {
    personalized?: 0 | 1 | 2 | 3
    imagePreloaded?: boolean
    loaded?: boolean
    baseMockup?: BaseMockup
    customDesign?: CustomDesign
    pbArtwork?: PbArtwork
    mockupCanvas?: DesignCanvas.MockupCanvas
    designCanvas?: DesignCanvas.DesignCanvas

    personalizeKey?: string

    customTextList?: Array<CustomTextItem>
    customImageList?: Array<CustomImageItem>
    customItemList?: Array<CustomItem>

    isChangeCustomDesign?: boolean
    customDesignType?: 'by_alphabet' | 'by_month' | 'by_year' | 'custom'
    customDesignList?: Array<string>
    selectedCustomDesign?: string

    pbCustomInfo?: any
    pbPrintUrl?: string
  }
  interface CustomItem {
    data: fabric.IObjectOptions
    personalize: Personalize
    subPersonalize?: Personalize
    product: Product
    error?: string | boolean
  }
  interface CustomTextItem extends CustomItem {
    data: fabric.ITextOptions
  }
  interface CustomImageItem extends CustomItem {
    data: fabric.Image
    canvasObject?: fabric.Image
    currentFileUploadUrl?: string
    isImageLowQuality?: boolean
    isConfirmQuantityImage?: boolean
  }

  interface PersonalizeCustomOptionsItem {
    type: 'text' | 'dropdown' | 'image'
    label: string
    placeholder?: string | Array<any> | null
    imagePath?: string | null
    g_type?: string | null
    g_index?: number | null
    unrequired?: boolean | null | number
    price?: number | 0
    max_length?: number | null
    value: string | Array<any> | null
  }
  interface PersonalizeCustomOptions {
    group: {
      extra_custom_fee: string | number
      limit: number
      name: string | null
    }
    options: Array<PersonalizeCustomOptionsItem>
  }
  interface PersonalizeCommonOptions {
    extra_custom_fee: string
    options: Array<PersonalizeCustomOptionsItem>
  }

  interface Product {
    id?: number
    name?: string
    campaign_id?: number
    template_id?: number
    slug?: Slug
    thumb_url?: string
    attributes?: string
    campaign_name?: string
    currency_code?: CurrencyCoder
    default_option?: string
    default_product_id?: number
    description?: string
    full_printed?: 0 | 1 | 2 | 3 | 4 | 5
    price?: number
    old_price?: number
    r_price?: number
    r_old_price?: number
    options?: string | OptionsList
    personalized?: 0 | 1 | 2 | 3 | 4 | 5
    product_type?: 'product'
    seller_id?: number
    start_time?: any
    end_time?: any
    show_countdown?: boolean
    supplier_id?: any
    system_product_type?: any
    tracking_code?: TrackingCode
    full_path?: string
    custom_options?: PersonalizeCustomOptions
    common_options?: PersonalizeCommonOptions
    // bundle
    isBundle?: boolean
    currentOptions?: { [key: string]: string }
    isSelected?: boolean
    optionsList?: OptionsList
    variantsList?: Variant[]
    existVariantList?: { key: string, value: string }[]
    currentVariantKey?: string
    currentVariant?: Variant
    customFeePrice?: number
    customOptionGroupNumbers?: number
    optionError?: string | boolean

    optionsListFull?: optionsList

    personalizeList?: Array<Personalize>
    customTextList?: Array<CustomTextItem>
    customImageList?: Array<CustomImageItem>
    customItemList?: Array<CustomItem>

    personalizeCustomDesign?: Personalize
    isChangeCustomDesign?: boolean
    customDesignType?: 'by_alphabet' | 'by_month' | 'by_year' | 'custom'
    customDesignList?: Array<string>
    selectedCustomDesign?: string

    personalizeManager?: ReturnType<typeof useCampaignPersonalize>
    customOptions?: PersonalizeCustomOptions
    commonOptions?: PersonalizeCommonOptions
    personalizeCustomManager?: ReturnType<typeof useCampaignPersonalizeCustomOptions>
    market_location?: string
    variant_options?: { [key: string]: string }
    default_variants?: Variant[]
    pricing_mode?: string
    system_type?: string
    template_custom_options?: PersonalizeCustomOptions
  }
  interface Campaign {
    id?: number
    name?: string
    slug?: string
    description?: string
    end_time?: number
    show_countdown?: number
    seller_id?: number
    default_product_id?: number
    type?: string
    tracking_code?: string
    mockup_type?: string
    personalized?: 0 | 1 | 2 | 3
    market_location?: string
    currency_code?: string
    status?: string
    product_type?: string
    template_id?: number
    options?: PersonalizeCustomOptions
    common_options?: PersonalizeCommonOptions
    default_option?: object
    system_type?: string
    thumb_url?: string
    variants?: Array<Variant>
    back?: Array<ImageData>
    products?: Array<Product>
    collections?: Array<Collection>
    seller?: Seller
    attributes?: string
    reviews?: ProductReviewRatingStats
    store_name?: string
    current_product_id?: number
    pricing_mode?: string
    combo_price?: number
    images?: ImageData[]
  }

  export interface Mockup {
    id: number
    campaign_id: number | null
    file_url: string
    file_url_2: string | null
    product_id: number
    type: string
    type_detail: string
    print_space: string
    allow_additional_print_space: number
  }

  interface ProductStats {
    add_to_cart?: number
    visit?: number
  }

  interface Promotion {
    campaign_id?: number | null
    collection_id?: number | null
    discount_code?: string | null
    end_time?: string | null
    id?: number | null
    name?: string | null
    public_status?: number | null
    rules?: string | null
    seller_id?: number | null
    start_time?: string | null
    status?: number | null
    store_id?: number | null
    type?: string | null
    used_count?: number | null
    created_at?: string | null
    deleted_at?: string | null
    updated_at?: string | null
  }

  interface FilterProduct {
    category_id_group: Array[number]
    template_id_group: Array[number]
    collection_group: Array[any]
    color_group: Array[string]
    min_price: number
    max_price: number
  }

  interface SimilarProducts {
    id: number
    name: string
    slug: Slug
  }

  interface ListingProduct {
    products: Array<Product>
    links: Array<{
      url: string | null
      label: string | null
    }>
    currentPage: number
    perPage: number
    lastPage: number
    total: number
    from: number
    to: number
  }
  interface ListingStore {
    listProductByUrl: { [key: string]: Omit<ListingProduct, 'bannerUrl'> & { bannerUrl: string } }
    filterProductByUrl: { [key: string]: Filter }
    relatedProducts: RelatedProduct[]
  }

  interface UserCampaignOption {
    currentProduct?: Product
    currentOptions: { [key: string]: string }
    optionListFull: OptionsList
    optionList: OptionsList
    imagesList: Array<ImageData>
    currentVariant?: Variant
    currentPrice?: number
    currentOldPrice?: number
    quantity: number
    optionError?: string | boolean
    isAddToCart?: boolean
  }

  interface CheckProductResult {
    success: boolean
    designDataUrl?: string[]
    product?: Product
    customItem?: CustomItem
    errorElementKey?: string | null
    errorType?: 'text' | 'dropdown' | 'image' | null
  }

}

export {}
