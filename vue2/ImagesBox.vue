<template>
  <div class="campaign-images-box d-flex">
    <ImagesEasyLightBox
      :images="compoundFilterImages"
      :color="color"
      :image-clicked="imageClicked"
      @close-box="imageClicked = null"
    />
    <div v-if="!isModal" class="list-images pr-1 d-none d-lg-block">
      <template v-if="hasVideo">
        <video-player-trigger v-for="(video, index) in videos" :key="video.url" :thumb-url="video.thumb" :is-active="currentImg === index" @clicked="$nextTick(()=> $refs.carousel.goTo(index))" />
      </template>
      <template v-if="(filterImages && (filterImages.length + modifyIndex) > 1)">
        <div
          v-for="( image, index ) in filterImages"
          :key="index"
          class="campaign-thumb-image cursor-pointer m-1"
          :class="{active: currentImg === index + modifyIndex}"
          @click="$emit('updateShowDesign', false); $nextTick(()=> $refs.carousel.goTo(index + modifyIndex))"
        >
          <product-img
            :path="image.file_url"
            :color="color"
            :alt="title"
            class="shadow-hover-1"
          />
        </div>
      </template>
      <div
        v-else-if="filterImages && filterImages[0]"
        class="campaign-thumb-image cursor-pointer m-1 active"
      >
        <product-img
          :path="filterImages[0].file_url"
          :color="color"
          :alt="title"
          class="shadow-hover-1"
        />
      </div>
      <div
        v-else
        class="campaign-thumb-image cursor-pointer m-1 active"
      >
        <product-img
          :path="currentProduct.thumb_url"
          :color="color"
          :alt="title"
          class="shadow-hover-1"
        />
      </div>
    </div>
    <div id="viewBox" ref="viewBox" class="w-100">
      <div
        v-if="currentDesign"
        v-show="isShowDesign"
        class="thumb-canvas"
        :class="{'three-d-thumb-canvas': isPersonalize3DCustomFullPrint}"
      >
        <template
          v-for="(design, index) in filterDesigns"
        >
          <three-d-mockup-canvas
            v-if="isPersonalize3DCustomFullPrint"
            v-show="design === currentDesign"
            :key="index + 'threeD' + currentThreeDMockupData?.id"
            :is-modal="isModal"
            :mockup-data="currentThreeDMockupData"
            :design-data="design.designData"
            :design="design.canvas"
            :mockup-width="imageBoxWidth"
            :type="design.personalized"
            :show-navigation="hasMultipleViews"
            @updateMockup="mockup => design.mockup = mockup"
            @finishLoadingDesign="design.loaded = true"
            @nextMockup="goToNextMockup"
            @prevMockup="goToPrevMockup"
          />
          <mockup-canvas
            v-else
            v-show="design === currentDesign"
            :key="index"
            :is-modal="isModal"
            :design="design.canvas"
            :mockup-data="design.mockupData"
            :design-data="design.designData"
            :color="color"
            :mockup-width="imageBoxWidth"
            :custom-image="customImage"
            :type="design.personalized"
            @updateMockup="mockup => design.mockup = mockup"
            @finishLoadingDesign="design.loaded = true"
          />
        </template>
      </div>
      <expand-image v-if="currentImg > modifyIndex || !hasVideo" @click.native="expand" />
      <custom-carousel
        v-if="(filterImages && ((filterImages.length + modifyIndex) > 1))"
        v-show="!(isShowDesign && currentDesign)"
        ref="carousel"
        carousel-classes="campaign-carousel-images"
        @beforeChange="(value)=>{
          currentImg = value
        }"
      >
        <template v-if="hasVideo">
          <cs-video-player
            v-for="(video, videoIndex) in videos"
            :key="video.url"
            ref="videoPlayerEl"
            :video-url="video.url"
            :thumbnail-url="video.thumb"
            :autoplay="currentImg === videoIndex"
            class="thumb"
          />
        </template>
        <div v-for="( image, index ) in filterImages" :key="index" class="thumb" @click="onImageThumbClick">
          <zoom-on-hover
            v-if="!isModal"
            class="d-none d-md-block"
            :img-normal="image.file_url"
            :color="color"
            type="full_hd"
            :scale="1.3"
            :alt-text="title"
          />
          <product-img
            class="w-100"
            :class="{'d-md-none': !isModal}"
            :path="image.file_url"
            :color="color"
            type="full_hd"
            :alt="title"
          />
        </div>
      </custom-carousel>
      <div
        v-else-if="filterImages && filterImages[0]"
        v-show="!(isShowDesign && currentDesign)"
        class="thumb"
      >
        <zoom-on-hover
          v-if="!isModal"
          class="d-none d-md-block"
          :img-normal="filterImages[0].file_url"
          type="full_hd"
          :scale="1.3"
          :color="color"
          :alt-text="title"
        />
        <product-img
          class="w-100"
          :class="{'d-md-none ': !isModal}"
          :path="filterImages[0].file_url"
          :color="color"
          type="full_hd"
          alt="title"
        />
      </div>
      <div
        v-else-if="currentProduct.thumb_url"
        v-show="!(isShowDesign && currentDesign)"
        class="thumb"
      >
        <zoom-on-hover
          v-if="!isModal"
          class="d-none d-md-block"
          :img-normal="currentProduct.thumb_url"
          type="full_hd"
          :scale="1.3"
          :color="color"
          :alt-text="title"
        />
        <product-img
          class="w-100"
          :path="currentProduct.thumb_url"
          :class="{'d-md-none ': !isModal}"
          :color="color"
          type="full_hd"
          :alt="title"
        />
      </div>
      <personalize-tag v-if="currentProduct && currentProduct.personalized" class="personalize" :click-focus-pesronalize-input="true" />
    </div>
  </div>
</template>
<script>

import CustomCarousel from '../common/CustomCarousel'
import ExpandImage from '~/components/ExpandImage'
import ImagesEasyLightBox from '~/themes/default/components/campaign/ImagesEasyLightBox.vue'
export default {
  name: 'CampaignImagesBox',
  components: {
    ImagesEasyLightBox,
    CustomCarousel,
    ExpandImage
  },
  /* eslint-disable vue/require-prop-types */
  props: ['isModal',
    'filterDesigns', 'currentDesign', 'isShowDesign',
    'filterImages', 'campaign', 'currentProduct', 'currentOptions', 'optionList', 'customImage', 'currentFileUploadUrl'],
  data () {
    return {
      currentImg: 0,
      imageBoxWidth: 0,
      imageClicked: null,
      colorOption: null,
      extendedFilterImages: [],
    }
  },
  computed: {
    color,
    title,
    videos () {
      return this.currentProduct?.videos
    },
    hasVideo () {
      return this.videos && this.videos.length
    },
    modifyIndex () {
      return this.hasVideo ? this.videos.length : 0
    },
    compoundFilterImages () {
      return [
        ...(this.filterImages ?? []),
        ...(this.currentProduct.thumb_url && this.isModal ? [{
          file_url: this.currentProduct.thumb_url
        }] : []),
        ...(this.extendedFilterImages ?? []),
      ]
    },
    currentImgObject () {
      return this.compoundFilterImages[this.currentImg]
    },
    currentThreeDMockupData () {
      try {
        const mockupId = this.currentImgObject?.mockup_id
        if (mockupId) {
          return this.currentDesign?.threeDMockupData?.find(item => item.id === mockupId)
        }
      } catch (error) {
        console.error('Error getting currentThreeDMockupData', error)
        return null
      }
      return null
    },
    isPersonalize3DCustomFullPrint () {
      return this.campaign.personalized === 1 && this.currentProduct.full_printed === 2 && this.currentThreeDMockupData
    },
    hasMultipleViews () {
      return this.filterImages && this.filterImages.length > 1
    }
  },
  watch: {
    filterImages: filterImagesChange,
    imageClicked (val) {
      if (val !== null) {
        this.$tracking.customTracking({ event: 'interact', action: 'zoom_product' })
      }
      if (val === null) {
        this.extendedFilterImages = []
      }
    },
    currentImg (val) {
      if (this.isPersonalize3DCustomFullPrint) {
        this.$emit('updateShowDesign', true)
      }
    }
  },
  mounted,
  methods: {
    expand () {
      const mockupId = this.currentThreeDMockupData?.id || this.currentDesign?.mockupData?.id
      const canvas = document.querySelector(`canvas[id*='design_${mockupId}']`)
      if (this.isShowDesign && canvas) {
        this.extendedFilterImages.push({ file_url: canvas.toDataURL() })
        this.imageClicked = this.compoundFilterImages.length
        return
      }
      this.imageClicked = this.currentImg
    },
    onImageThumbClick,
    goToNextMockup () {
      if (this.currentImg < this.compoundFilterImages.length - 1) {
        this.currentImg++
      }
    },
    goToPrevMockup () {
      if (this.currentImg > 0) {
        this.currentImg--
      }
    }
  }
}

function color () {
  if (this.currentOptions && this.currentOptions.color) {
    return this.$colorVal(this.currentOptions.color.replace(/-/g, ' '))
  }
  return ''
}

function title () {
  return `${this.campaign.name} ${this.currentProduct.name} ${Object.keys(this.optionList).map(item => this.currentOptions[item]).join(' ')}`
}

function mounted () {
  this.imageBoxWidth = this.$refs.viewBox?.clientWidth ? this.$refs.viewBox.clientWidth : 368
  const defaultImage = this.$store.state.storeInfo.store_type === 'express_listing' ? this.campaign.thumb_url : this.currentProduct.thumb_url || this.currentProduct.full_path
  if (this.filterImages && this.filterImages.length) {
    this.currentImg = this.hasVideo ? 0 : this.filterImages.findIndex(item => item.file_url === defaultImage)
    if (!this.currentImg || this.currentImg < 0) {
      this.currentImg = 0
    } else {
      this.currentImg = this.currentImg + this.modifyIndex
    }
    if (this.filterImages.length > 1) {
      this.$nextTick(() => this.$refs.carousel.goTo(this.currentImg))
    }
  }
}

function filterImagesChange (oldVal, newVal) {
  if (oldVal.length === newVal.length && oldVal[0].file_url === newVal[0].file_url) { // fix change color reset index
    return this.$nextTick(() => this.$refs.carousel.goTo(this.currentImg))
  }

  const imageIndex = this.filterImages.findIndex(item => item.file_url === this.currentProduct.thumb_url)
  if (imageIndex > 0) {
    this.$nextTick(() => this.$refs.carousel && this.$refs.carousel.goTo(imageIndex + this.modifyIndex))
  } else {
    this.$nextTick(() => this.$refs.carousel && this.$refs.carousel.goTo(0))
  }
}

function onImageThumbClick () {
  console.log('onImageThumbClick')
  this.imageClicked = this.currentImg + this.modifyIndex
  console.log('this.imageClicked', this.imageClicked)
}
</script>
<style lang="scss" scoped>
.three-d-thumb-canvas {
  aspect-ratio: 3/4;
}

.personalize {
  @media (max-width: 768px) {
    top: 10px;
  }
}
</style>
