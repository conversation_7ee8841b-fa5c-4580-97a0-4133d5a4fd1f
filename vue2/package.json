{"name": "storefront", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt dev", "build": "nuxt build", "build:theme": "rimraf static/css static/fonts && node ./tools/compile-theme-scss.js && node ./tools/copy-theme-fonts.js", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext .js,.vue --ignore-path .gitignore .", "lint:style": "stylelint **/*.{vue,css,scss} --ignore-path .gitignore", "lint": "yarn lint:js && yarn lint:style"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^3.3.3", "@nuxtjs/axios": "^5.12.2", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/proxy": "^2.0.1", "@nuxtjs/recaptcha": "^1.0.4", "@nuxtjs/sentry": "^5.1.7", "@nuxtjs/toast": "^3.3.1", "@sentry/node": "^6.18.2", "@sentry/tracing": "^6.18.2", "aws-sdk": "^2.937.0", "bootstrap-vue": "^2.21.2", "cookie-universal-nuxt": "^2.1.5", "devtools-detector": "^2.0.22", "fabric": "4.5.1", "lscache": "^1.3.0", "nuxt": "^2.15.8", "nuxt-i18n": "^6.15.3", "qs": "^6.10.1", "sanitize-html": "2.3.0", "spark-md5": "^3.0.1", "ua-parser-js": "^0.7.27", "uuid": "^8.3.2", "video.js": "^8.17.4", "vue-cool-lightbox": "^2.7.4", "vue-easy-lightbox": "^0.23.0", "vue-select": "^3.11.2", "vue-slick-carousel": "^1.0.6", "vue-slider-component": "^3.2.11", "vue-tel-input": "^5.10.1", "vue-video-player": "^5.0.2", "vuelidate": "^0.7.6", "vuex-persistedstate": "^4.0.0-beta.3", "vuex-shared-mutations": "^1.0.2", "three": "^0.126.1", "three-gltf-loader": "^1.111.0"}, "overrides": {"node-sass": "7.0.3"}, "resolutions": {"node-sass": "7.0.3"}, "devDependencies": {"@nuxtjs/eslint-config": "^3.1.0", "@nuxtjs/eslint-module": "^2.0.0", "@nuxtjs/stylelint-module": "^4.0.0", "babel-eslint": "^10.1.0", "eslint": "^7.10.0", "eslint-plugin-nuxt": "^1.0.0", "eslint-plugin-vue": "^7.5.0", "sass": "^1.49.9", "sass-loader": "^10.0.3", "stylelint": "^13.7.2", "stylelint-config-standard": "^20.0.0"}}