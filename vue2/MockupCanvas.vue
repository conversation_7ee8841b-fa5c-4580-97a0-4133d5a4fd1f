<template>
  <div :key="forceRender" class="mockup-canvas-item position-relative">
    <canvas :id="`design_${mockupData?.id}_${designData&&designData.print_space}`" ref="mockup" />
    <div v-if="isLoading" class="absolute-center">
      <b-spinner variant="gray" />
    </div>
    <div v-if="customImage" class="control-box">
      <div class=" d-flex justify-content-center">
        <button
          v-for="control in imageControlList"
          :key="control.key"
          type="button"
          class="btn border control-custom-image-btn m-1 btn-outline-custom"
          oncontextmenu="return false;"
          @click="controlCustomImage(control.key)"
          @mousedown="startIntevalControl(control.key)"
          @touchstart="startIntevalControl(control.key)"
          @mouseleave="removeIntervalControl"
          @touchend="removeIntervalControl"
          @mouseup="removeIntervalControl"
        >
          <i :class="control.icon" />
        </button>
      </div>
      <p v-if="isImageLowQuanlity" class="text-danger text-center my-3">
        <span>
          {{ $t('Image resolution is low. Please upload higher resolution image, minimum size is heightxwidth px', {height: Math.round(customImage && customImage.height * customImage.scaleY / 2) , width: Math.round(customImage && customImage.width * customImage.scaleX / 2)}) }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import controlCustomImage from '~/mixins/controlCustomImage'
export default {
  name: 'MockupCanvas',
  mixins: [controlCustomImage],
  // eslint-disable-next-line vue/require-prop-types
  props: ['isModal', 'mockupData', 'design', 'color', 'designData', 'mockupWidth', 'type', 'customImage'],
  data () {
    return {
      forceRender: 1,
      mockup: false,
      canvas: false,
      isLoading: true,
      imageControlList: [{
        key: 'zoomOut',
        icon: 'icon-sen-magnify-minus-outline'
      }, {
        key: 'zoomIn',
        icon: 'icon-sen-magnify-plus-outline'
      }, {
        key: 'rollLeft',
        icon: 'icon-sen-refresh'
      }, {
        key: 'moveRight',
        icon: 'icon-sen-arrow-right-thin'
      }, {
        key: 'moveLeft',
        icon: 'icon-sen-arrow-left-thin'
      }, {
        key: 'moveDown',
        icon: 'icon-sen-arrow-down-thin'
      }, {
        key: 'moveUp',
        icon: 'icon-sen-arrow-up-thin'
      }],
      timeoutControl: null,
      intervalControll: null
    }
  },
  watch: {
    color: changeColor,
    designData: loadMockup
  },
  mounted: loadMockup,
  beforeDestroy,
  methods: {
    resetMockupSize,
    loadMockup,
    loadDesign,
    changeColor,
    reRender
  }
}

function beforeDestroy () {
  window.removeEventListener('resize', this.resetMockupSize)
}

function resetMockupSize () {
  const width = this.mockupWidth || this.$el.clientWidth || 500
  this.mockup.setWidth(width)
  this.mockup.setHeight(width * 1.25)
  this.mockup.renderAll()
}

function changeColor () {
  if (this.mockup) {
    this.mockup.setMockupColor(this.color)
  }
}

function loadDesign () {
  if (this.type === 1) {
    if (this.design) {
      if (this.design.hasDesign()) {
        this.mockup.viewDesign(this.design.exportCanvas())
      }
      this.design.onupdate = (design) => {
        this.mockup.viewDesign(design.exportCanvas())
      }
    }
  } else if (this.type === 2) {
    this.mockup.viewDesign(this.design)
  }
  this.$emit('finishLoadingDesign')
}

function loadMockup () {
  this.forceRender++
  this.$nextTick(async () => {
    this.isLoading = true
    // if (!this.mockup) {
    this.mockup = new this.$BaseMockup(this.$refs.mockup, {
      selection: false,
      controlsAboveOverlay: true,
      centeredScaling: true,
      allowTouchScrolling: true
      // enableRetinaScaling: false
    })
    this.mockup.locked = true
    this.$emit('updateMockup', this.mockup)
    this.resetMockupSize()
    if (this.mockupData && this.mockupData.design_json) {
      await this.mockup.loadFromJSON(this.mockupData.design_json)
    }
    this.changeColor()
    this.loadDesign()
    this.isLoading = false
  })
}

function reRender () {
  this.design.renderAll()
  this.design._onupdate()
}
</script>

<style lang="scss">
.control-custom-image-btn {
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  i {
    font-size: 1.5rem;
  }

  @media (min-width: 768px) {
    height: 40px;
    width: 40px;

    i {
      font-size: 2rem;
    }
  }
}
</style>
