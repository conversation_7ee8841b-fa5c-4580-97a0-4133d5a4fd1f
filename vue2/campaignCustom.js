export default {
  data () {
    return {
      personalized: 0,
      isShowDesign: false,
      customDesigns: false,
      uploadFileDesignUrl: false,
      filterDesigns: [],
      filterMockups: [],

      currentDesign: false,
      customTextList: [],
      customImageList: [],
      customItemList: [],
      selectedCustom: false,

      loadingChangeCustomDesign: false,
      isChangeCustomDesign: false,
      customDesignArray: {
        by_alphabet: ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'],
        by_month: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
        by_year: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95', '96', '97', '98', '99'],
        custom: []
      },
      designHasCustomDesign: false,
      customDesign: false,
      customDesignType: false,
      isSelectedCustomDesignType: false,

      // customImage
      customImage: null,
      fileUpload: null,
      isUploadFile: false,
      currentFileUploadUrl: null,
      isImageLowQuanlity: false,
      isConfirmQuantityImage: false,
      isShowModalConfirmQuantityImage: false,

      // customOption
      tempCustomOption: false,
      tempCommonOption: false,
      customFeePrice: 0,
      customOption: false,
      customerCustomOptions: [],
      customOptionGroupNumbers: 1
    }
  },
  methods: {
    getCampaignCustom,
    resetCanvasData,
    // customImage
    triggerUploadImage,
    updateCustomDesign,
    changeCurrentDesign,
    updateShowDesign,
    updateCanvasData,
    updateDesignQuery,
    getConfirmDesignImage,
    confirmQuantityImage,
    getMockupUpload,

    scrollToImage
  },
  computed: {
    extraCustomFee
  },
  head
}

function head (app) {
  if (this.campaignData && this.campaignData.personalized === 2) {
    return {
      link: [{
        rel: 'stylesheet',
        href: 'https://apis.personalbridge.com/style.css'
      }],
      script: [{
        src: app.$config.personalBridgeUrl,
        body: true
      }]
    }
  }
  return {}
}

function getCampaignCustom (campaignSlug) {
  this.customDesigns = false
  if (this.campaignData.personalized === 1 || this.campaignData.personalized === 2) {
    this.$store.dispatch('campaign/getProductCustom', { campaignSlug }).then(({ success, data }) => {
      if (success && data) {
        if (this.campaignData.personalized === 2 && data.pb_artworks && data.pb_artworks.length) {
          this.customDesigns = Object.freeze(data.pb_artworks.map((design) => {
            const product = this.productList ? this.productList.find(product => product.id === design.product_id) : this.currentProduct

            const mockupData = data.base_mockup.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
            return {
              designData: design,
              mockupData
            }
          }))
        } else if (this.campaignData.personalized === 1 && data.custom_designs && data.custom_designs.length) {
          // check if any product has full_printed
          const hasFullPrinted = this.productList.some(product => product.full_printed === 2)

          this.customDesigns = Object.freeze(data.custom_designs.map((design) => {
            const product = this.productList ? this.productList.find(product => product.id === design.product_id) : this.currentProduct

            if (hasFullPrinted) {
              const mockupData = data.base_mockup.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
              return {
                designData: design,
                mockupData,
                threeDMockupData: data.three_d_mockups
              }
            } else {
              const mockupData = data.base_mockup?.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
              return {
                designData: design,
                mockupData,
                threeDMockupData: false
              }
            }
          }))
        }
        this.resetCanvasData(true)
      }
    })
  } else if (this.campaignData.personalized === 3) {
    this.customOption = this.campaignData.options
  }
}

function resetCanvasData (isFirstInit) {
  this.isShowDesign = false
  this.customTextList = false
  this.customImageList = false
  this.customItemList = false
  this.currentDesign = false
  this.filterDesigns = false
  this.selectedCustom = false
  if (this.campaignData.personalized === 1 && this.currentProduct.filterDesigns && this.currentProduct.filterDesigns.length && this.isModal) {
    this.filterDesigns = this.currentProduct.filterDesigns
    this.customTextList = this.currentProduct.customTextList
    this.customImageList = this.currentProduct.customImageList
    this.customItemList = this.currentProduct.customItemList
    this.selectedCustom = this.currentProduct.customItemList[0]
    if (this.customImageList && this.customImageList.length && this.customImageList[0].design.currentFileUploadUrl) {
      this.currentFileUploadUrl = this.customImageList[0].design.currentFileUploadUrl
      this.fileUpload = { name: this.customImageList[0].design.currentFileUploadUrl }
    }
    setTimeout(() => {
      this.changeCurrentDesign(this.filterDesigns[0])
    }, 200)
  } else if (this.customDesigns && this.customDesigns.length) {
    this.filterDesigns = this.customDesigns && this.customDesigns.filter(design => design.designData.product_id === (this.currentProduct && this.currentProduct.id))
    if (this.currentProduct.full_printed) {
      const size = this.currentOptions.size || this.currentOptions?.print_side || this.optionList.size?.[0] || this.optionList.print_side?.[0]
      const filterDesigns2 = this.filterDesigns.filter(design => size?.startsWith(design.designData.print_space))
      if (filterDesigns2 && filterDesigns2.length) {
        this.filterDesigns = filterDesigns2
      }
    } else {
      this.currentProduct.filterDesigns = Object.freeze(this.filterDesigns)
    }
    // clear unsed canvas
    if (!this.isModal) {
      this.customDesigns.forEach((item) => {
        if (!this.filterDesigns.includes(item)) {
          item.canvas = null
          item.mockup = null
          item.loaded = false
        }
      })
    }

    const customTextList = []
    const customImageList = []
    const isSupportWebp = this.$supportWebp()
    this.filterDesigns.forEach(async (designItem, index) => {
      designItem.personalized = this.campaignData.personalized
      if (designItem.mockupData && !designItem.imagePreloaded) {
        const mockupData = JSON.parse(designItem.mockupData.design_json)
        if (mockupData.backgroundImage && mockupData.backgroundImage.src) {
          if (isSupportWebp) {
            mockupData.backgroundImage.src = mockupData.backgroundImage.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900,q_90,ofmt_webp/')
          } else {
            mockupData.backgroundImage.src = mockupData.backgroundImage.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900/')
          }
          designItem.mockupData.design_json = JSON.stringify(mockupData)
          if (mockupData && mockupData.objects) {
            mockupData.objects.forEach((item) => {
              if (item.type === 'image') {
                this.$preloadImg(item.src)
              }
            })
          }
          this.$preloadImg(mockupData.backgroundImage.src)
        }
      }

      if (this.campaignData.personalized === 1) {
        const queryParrams = this.$route.query
        let checkHasData = false
        let textCustomDesign = false
        const designData = JSON.parse(designItem.designData.design_json)
        if (!designItem.imagePreloaded) {
          designData.objects.forEach((item) => {
            if (item.type === 'image') {
              if (isSupportWebp) {
                item.src = item.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900,q_90,ofmt_webp/')
              } else {
                item.src = item.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900/')
              }
              // this.$preloadImg(item.src)
            }
          })
        }
        designItem.imagePreloaded = true

        designItem.designData.design_json = JSON.stringify(designData)

        designData.objects.forEach((item) => {
          if (item.type === 'i-text') {
            item.placeholder = item.placeholder || item.text || this.$t('Enter text')
            if (!this.isModal) {
              item.text = queryParrams[item.name && item.name.replaceAll(' ', '-')] || ''
            } else {
              item.text = ''
            }
            if (item.text) {
              checkHasData = true
              textCustomDesign = item.text && item.text.toLowerCase().charAt(0)
            }
            const checkHasTextItem = customTextList.find(customTextItem => customTextItem.data.name === item.name)
            if (checkHasTextItem) {
              checkHasTextItem.subDesign = designItem
            } else {
              customTextList.push({
                data: item,
                design: designItem
              })
            }
          }
          if (item.type === 'image' && item.isCustom) {
            if (queryParrams['custom-image'] && !this.isModal) {
              const parsedFileURL = (() => {
                try {
                  return atob(queryParrams['custom-image'])
                } catch (err) {
                  return queryParrams['custom-image']
                }
              })()

              this.currentFileUploadUrl = parsedFileURL
              designItem.currentFileUploadUrl = parsedFileURL
              this.fileUpload = { name: parsedFileURL }
            }
            customImageList.push({
              data: item,
              design: designItem
            })
          }
        })

        if (!designItem.canvas) {
          designItem.canvas = new this.$DesignCanvas()
          designItem.canvas.set({ enableRetinaScaling: false })
          await designItem.canvas.load(designItem.designData.design_json, 900)
          designItem.canvas.lockEdit()
          designItem.canvas.getObjects().forEach((item) => {
            if (item.type === 'i-text') {
              if (!this.isModal) {
                designItem.canvas.updateText(item, queryParrams[item.name && item.name.replaceAll(' ', '-')] || item.text)
              } else {
                designItem.canvas.updateText(item, item.text)
              }
            }
          })
        }
        if (designItem.canvas && designItem.canvas.customDesign && this.customDesignArray[designItem.canvas.customDesign]) {
          this.customDesignType = designItem.canvas.customDesign
          if (this.customDesignType === 'custom') {
            this.customDesignArray.custom = designItem.canvas.customDesignList
          }
          if (queryParrams.customdesign && this.customDesignArray[this.customDesignType].includes(queryParrams.customdesign)) {
            this.customDesign = queryParrams.customdesign
            checkHasData = true
          } else if (checkHasData && this.customDesignArray[this.customDesignType].includes(textCustomDesign)) {
            this.customDesign = textCustomDesign
          } else {
            this.customDesign = this.customDesignArray[this.customDesignType][0]
          }
          designItem.customDesign = this.customDesign
          this.designHasCustomDesign = designItem
          designItem.canvas.changeDesignByCharacter(this.customDesign)
        }

        this.finishLoading = true
        if ((index === 0 && checkHasData && isFirstInit) || this.currentFileUploadUrl || this.isModal) {
          setTimeout(async () => {
            this.changeCurrentDesign(designItem)
            if (this.currentFileUploadUrl) {
              await designItem.canvas.changeCustomImage(this.$imgUrl(this.currentFileUploadUrl, 'full'))
              this.customImage = designItem.canvas.getCustomImage()
              this.isConfirmQuantityImage = false
            }
          }, 200)
        } else {
          setTimeout(() => {
            this.scrollToImage()
          }, 100)
        }
      } else if (this.campaignData.personalized === 2 && designItem.designData && designItem.designData.pb_artwork_id) {
        await initPersonalize(designItem, this.isModal)
        this.currentDesign = designItem
      }
    })

    if (this.campaignData.personalized === 1) {
      this.customTextList = customTextList
      this.customImageList = customImageList
      this.customItemList = [...this.customTextList, ...this.customImageList]
      this.selectedCustom = this.customItemList[0]

      this.currentProduct.customTextList = Object.freeze(customTextList)
      this.currentProduct.customImageList = Object.freeze(customImageList)
      this.currentProduct.customItemList = Object.freeze(this.customItemList)
    }
  }
}

function triggerUploadImage () {
  if (window.innerWidth < 768 || (window.innerWidth < 992 && this.isModal)) {
    this.selectedCustom = this.customImageList[0]
    this.$nextTick(() => {
      document.getElementById('fileInputCustomImage').click()
    })
  } else {
    this.$nextTick(() => {
      document.getElementById('fileInput-campaign-0').click()
    })
  }
}

async function updateCustomDesign (value) {
  if (value === this.customDesign) {
    return
  }
  this.isChangeCustomDesign = true
  this.loadingChangeCustomDesign = true
  if (this.selectedCustom?.design?.canvas) {
    await this.selectedCustom.design.canvas.changeDesignByCharacter(value)
    this.customDesign = value
    this.currentDesign.customDesign = value
    this.selectedCustom.customDesign = value
    if (!this.isModal) {
      const query = { ...this.$route.query }
      query.customdesign = value
      this.$router.push(this.localePath({ query }))
    }
  }
  this.loadingChangeCustomDesign = false
  this.changeCurrentDesign(this.selectedCustom.design)
}

function changeCurrentDesign (design) {
  if (!this.isShowDesign) {
    this.updateShowDesign(true)
  }
  if (design && design !== this.currentDesign) {
    this.currentDesign = design
    const queryParrams = this.$route.query
    if (this.campaignData.personalized === 1) {
      design.canvas.getObjects().forEach((item) => {
        if (item.type === 'i-text') {
          if (!this.isModal) {
            design.canvas.updateText(item, queryParrams[item.name && item.name.replaceAll(' ', '-')] || item.text)
          } else {
            design.canvas.updateText(item, item.text)
          }
        }
      })
      this.customImage = design.canvas.getCustomImage()
    }
    this.scrollToImage()
  }
}

function updateShowDesign (value) {
  this.isShowDesign = value
}

async function updateCanvasData ({ design, data, subDesign }, value) {
  if (design && design.canvas && data) {
    if (data.type === 'i-text') {
      data.text = this.$removeEmoji(value)
      const canvasDesign = design.canvas
      const currentObjects = canvasDesign.getAllObjectsByName(data.name)
      currentObjects.forEach((currentObject) => {
        canvasDesign.updateText(currentObject, data.text)
      })
      if (subDesign) {
        const subCanvasDesign = subDesign.canvas
        const subObjects = subCanvasDesign.getAllObjectsByName(data.name)
        subObjects.forEach((subObject) => {
          subCanvasDesign.updateText(subObject, data.text)
        })
      }
      if (data.text && !this.isChangeCustomDesign && this.customDesignType === 'by_alphabet') {
        const firstLetter = data.text.trim().charAt(0).toLowerCase()
        if (firstLetter && this.customDesign !== firstLetter && this.customDesignArray.by_alphabet.includes(firstLetter)) {
          this.loadingChangeCustomDesign = true
          await design.canvas.changeDesignByCharacter(firstLetter)
          this.loadingChangeCustomDesign = false
          this.customDesign = firstLetter
          design.customDesign = firstLetter
          if (!this.isModal) {
            const query = { ...this.$route.query }
            query.customdesign = firstLetter
            this.$router.push(this.localePath({ query }))
          }
        }
      }
    } else if (data.type === 'image') {
      // this.$inputFileToBase64(value).then(
      this.isUploadFile = true
      this.isLoadingAddToCart = true
      this.fileUpload = value
      const upload = this.$preSignedUploader(value)
      upload.then(async (result) => {
        await design.canvas.changeCustomImage(this.$imgUrl(result.Key, 'full'))
        this.currentFileUploadUrl = result.Key
        data.currentFileUploadUrl = this.currentFileUploadUrl
        design.currentFileUploadUrl = this.currentFileUploadUrl

        this.customImage = design.canvas.getCustomImage()
        this.isConfirmQuantityImage = false
      }).then(() => {
        if (!this.isModal) {
          const query = { ...this.$route.query }
          query['custom-image'] = btoa(this.currentFileUploadUrl)
          this.$router.replace(this.localePath({ query }))
        }
        this.$refs.modalEditCustomImage.isShowModal = true
        this.isUploadFile = false
        this.isLoadingAddToCart = false
      }).catch((error) => {
        this.isUploadFile = false
        this.isLoadingAddToCart = false
        this.$toast.error(`Error: ${error.message}`)
      })
      this.scrollToImage()
    }
  }

  if (this.currentProduct.full_printed && !(this.currentOptions.size || this.currentOptions.print_side)) {
    this.selectSizeSubmit = false
    this.$refs.modalSelectSize.isShowModalSelectSize = true
  }
}

function updateDesignQuery ({ design, data }) {
  if (design && !this.isModal) {
    const query = { ...this.$route.query }
    query[data.name && data.name.replaceAll(' ', '-')] = data.text
    this.$router.push(this.localePath({ query }))
  }
}

function getConfirmDesignImage (isAddAllToCart) {
  this.uploadFileDesignUrl = false
  if ((
    this.campaignData.personalized === 1 ||
    this.campaignData.personalized === 2) &&
    this.filterDesigns &&
    this.filterDesigns.length &&
    this.filterDesigns.every(design => design.mockup && design.canvas)) {
    this.uploadFileDesignUrl = this.filterDesigns.map(design => design.mockup.toDataURL('png'))
  }
  if (isAddAllToCart) {
    this.bundleDiscount.products.forEach((product) => {
      if (product.personalized === 1 || product.personalized === 2) {
        if (this.uploadFileDesignUrl) {
          this.uploadFileDesignUrl = [...this.uploadFileDesignUrl, ...product.filterDesigns.map(design => design.mockup.toDataURL('png'))]
        } else {
          this.uploadFileDesignUrl = product.filterDesigns.map(design => design.mockup.toDataURL('png'))
        }
      }
    })
  }
  if (this.uploadFileDesignUrl) {
    this.$refs.modalConfirmDesign.isShowModalConfirmDesign = true
  } else {
    this.addToCart(this.typeSubmit, true)
  }
}

function confirmQuantityImage (value) {
  this.isShowModalConfirmQuantityImage = false
  this.isConfirmQuantityImage = true
  if (value) {
    this.triggerUploadImage()
  } else if (this.isAddToCartProcess) {
    this.addToCart()
  }
  this.isAddToCartProcess = false
}

function getMockupUpload (filterDesigns) {
  const AWSUpload = filterDesigns.map((design) => {
    if (!design || !design.designData) {
      return false
    }

    const designData = design.designData
    const designID = `design_${designData.product_id}_${designData.print_space}.png`
    if (design.mockup) {
      const canvasDom = design.mockup.getElement()
      if (canvasDom) {
        return this.$getImageFileFromDom(canvasDom).then((blob) => {
          const file = new File([blob], designID, { type: 'image/png' })
          return this.$preSignedUploader(file)
        })
      }
    }
  })
  return Promise.all(AWSUpload)
}

function scrollToImage () {
  const el = this.$refs && this.$refs.imageBox && this.$refs.imageBox.$el
  if (el && !this.$checkViewPort(el)) {
    el.scrollIntoView()
  }
}

function extraCustomFee () {
  return parseFloat(this.customFeePrice)
}

async function initPersonalize (designItem, isModal) {
  if (window.pbsdk) {
    const result = await window.pbsdk.init({
      key: `design_${designItem.designData && designItem.designData.campaign_id}_${designItem.designData && designItem.designData.print_space}`,
      artworkId: designItem.designData.pb_artwork_id,
      $formEl: isModal ? '#artwork_form_selector_modal' : '#artwork_form_selector',
      $canvasEl: '',
      onChange: (customInfo) => {
        designItem.customInfo = customInfo
        if (designItem.mockup) {
          designItem.mockup.viewDesign(designItem.canvas)
        }
      }
    })
    designItem.canvas = result[0]
    designItem.printUrl = result[1]
  } else {
    setTimeout(() => {
      initPersonalize(designItem, isModal)
    }, 500)
  }
}
