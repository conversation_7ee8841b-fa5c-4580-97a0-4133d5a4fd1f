# Commit Diff Analysis: Baseball Cap Custom Text Feature

**Commit:** `1a2c55d3` - `feat: baseball cap custom text`  
**Author:** thiennt  
**Date:** Wed Jul 23 21:59:55 2025 +0700  
**Impact:** 907 insertions, 14 deletions across 7 files

## Summary

This commit implements 3D interactive mockup visualization for baseball cap customization, introducing Three.js-based rendering capabilities that allow users to preview custom text on caps from multiple angles.

## Detailed File Changes

### 1. `package.json` - Dependencies Addition
```diff
   "vuex-persistedstate": "^4.0.0-beta.3",
-  "vuex-shared-mutations": "^1.0.2"
+  "vuex-shared-mutations": "^1.0.2",
+  "three": "^0.126.1",
+  "three-gltf-loader": "^1.111.0"
```
**Impact:** Adds Three.js ecosystem for 3D rendering and GLTF model loading

### 2. `nuxt.config.js` - Plugin Registration
```diff
   { src: '~/plugins/base-mockup', ssr: false },
+  { src: '~/plugins/t-mockup', ssr: false },
   { src: '~/plugins/design-canvas', ssr: false },
```
```diff
-  appRegion: process.env.APP_REGION || 'us'
+  appRegion: process.env.APP_REGION || 'us',
+  cdnUrl: process.env.CDN_URL || 'https://cdn.cloudimgs.net'
```
**Impact:** Registers new 3D mockup plugin and adds CDN URL configuration

### 3. `components/MockupCanvas.vue` - ID Fix
```diff
- <canvas :id="`design_${designData&&designData.product_id}_${designData&&designData.print_space}`" ref="mockup" />
+ <canvas :id="`design_${mockupData?.id}_${designData&&designData.print_space}`" ref="mockup" />
```
**Impact:** Fixes canvas ID generation to use mockup data instead of design data

### 4. `components/ThreeDMockupCanvas.vue` - New Component (239 lines)

**Template Structure:**
```vue
<template>
  <div class="mockup-canvas-item position-relative">
    <div id="preview_mockup" ref="preview_mockup" class="three-d-canvas-container" />
    
    <!-- Navigation Arrows -->
    <div v-if="showNavigation" class="mockup-navigation-arrows">
      <div class="mockup-arrow prev-arrow" @click="goToPrev">
        <i class="icon-sen-chevron-left absolute-center" />
      </div>
      <div class="mockup-arrow next-arrow" @click="goToNext">
        <i class="icon-sen-chevron-right absolute-center" />
      </div>
    </div>

    <div v-if="isLoading" class="absolute-center">
      <b-spinner variant="gray" />
    </div>
  </div>
</template>
```

**Key Methods:**
- `loadMockup()`: Initializes 3D mockup manager and loads GLTF models
- `loadDesign()`: Handles design canvas updates for personalized/full-print products
- `changeColor()`: Updates mockup color through 3D rendering
- `goToNext/goToPrev()`: Navigation between different mockup angles

**3D Integration Logic:**
```javascript
// Initialize 3D mockup manager
this.threeDMockupCanvas = new this.$MockupManager('preview_mockup')

// Load mockup parameters with 3D data
const mockupParams = {
  id: this.mockupData.id,
  name: 'default',
  maxSize: el?.clientWidth * 1.25 || 500,
  designImg: this.$imgUrl(this.designData.file_url, 'full_hd'),
  background: this.$imgUrl(this.mockupData.file_url, 'full_hd'),
  color: this.color ?? '#ffffff',
  mockupType: this.designData.type_detail ?? 'flat',
}

// Parse 3D model data from mockup JSON
if (this.mockupData && this.mockupData.design_json) {
  const designJson = JSON.parse(this.mockupData.design_json)
  mockupParams.glb = designJson.glb ? this.$config.cdnUrl + '/' + designJson.glb : ''
  await this.threeDMockupCanvas.loadMockup(mockupParams)
}
```

### 5. `mixins/campaignCustom.js` - Enhanced Logic

**3D Mockup Data Support:**
```diff
  } else if (this.campaignData.personalized === 1 && data.custom_designs && data.custom_designs.length) {
+   // check if any product has full_printed
+   const hasFullPrinted = this.productList.some(product => product.full_printed === 2)
+
    this.customDesigns = Object.freeze(data.custom_designs.map((design) => {
      const product = this.productList ? this.productList.find(product => product.id === design.product_id) : this.currentProduct

-     const mockupData = data.base_mockup.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
-     return {
-       designData: design,
-       mockupData
+     if (hasFullPrinted) {
+       const mockupData = data.base_mockup.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
+       return {
+         designData: design,
+         mockupData,
+         threeDMockupData: data.three_d_mockups
+       }
+     } else {
+       const mockupData = data.base_mockup?.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === design.print_space)
+       return {
+         designData: design,
+         mockupData,
+         threeDMockupData: false
+       }
      }
    }))
```

**Multi-Object Text Updates:**
```diff
  if (data.type === 'i-text') {
    data.text = this.$removeEmoji(value)
    const canvasDesign = design.canvas
-   const currentObject = canvasDesign.getObjectByName(data.name)
-   canvasDesign.updateText(currentObject, data.text)
+   const currentObjects = canvasDesign.getAllObjectsByName(data.name)
+   currentObjects.forEach((currentObject) => {
+     canvasDesign.updateText(currentObject, data.text)
+   })
    if (subDesign) {
      const subCanvasDesign = subDesign.canvas
-     const subObject = subCanvasDesign.getObjectByName(data.name)
-     subCanvasDesign.updateText(subObject, data.text)
+     const subObjects = subCanvasDesign.getAllObjectsByName(data.name)
+     subObjects.forEach((subObject) => {
+       subCanvasDesign.updateText(subObject, data.text)
+     })
    }
```

### 6. `themes/default/components/campaign/ImagesBox.vue` - UI Integration

**Conditional Component Rendering:**
```diff
  <div class="thumb-canvas"
+      :class="{'three-d-thumb-canvas': isPersonalize3DCustomFullPrint}">
    <template v-for="(design, index) in filterDesigns">
+     <three-d-mockup-canvas
+       v-if="isPersonalize3DCustomFullPrint"
+       v-show="design === currentDesign"
+       :key="index + 'threeD' + currentThreeDMockupData?.id"
+       :is-modal="isModal"
+       :mockup-data="currentThreeDMockupData"
+       :design-data="design.designData"
+       :design="design.canvas"
+       :mockup-width="imageBoxWidth"
+       :type="design.personalized"
+       :show-navigation="hasMultipleViews"
+       @updateMockup="mockup => design.mockup = mockup"
+       @finishLoadingDesign="design.loaded = true"
+       @nextMockup="goToNextMockup"
+       @prevMockup="goToPrevMockup"
+     />
      <mockup-canvas
+       v-else
        v-show="design === currentDesign"
```

**New Computed Properties:**
```diff
+ currentImgObject () {
+   return this.compoundFilterImages[this.currentImg]
+ },
+ currentThreeDMockupData () {
+   try {
+     const mockupId = this.currentImgObject?.mockup_id
+     if (mockupId) {
+       return this.currentDesign?.threeDMockupData?.find(item => item.id === mockupId)
+     }
+   } catch (error) {
+     console.error('Error getting currentThreeDMockupData', error)
+     return null
+   }
+   return null
+ },
+ isPersonalize3DCustomFullPrint () {
+   return this.campaign.personalized === 1 && this.currentProduct.full_printed === 2 && this.currentThreeDMockupData
+ },
+ hasMultipleViews () {
+   return this.filterImages && this.filterImages.length > 1
+ }
```

### 7. `plugins/t-mockup.js` - 3D Rendering Engine (574 lines)

**Core Classes:**
- `TMockupRender`: Extends base MockupRender with Three.js integration
- `TMockupLoader`: Handles GLTF model loading and texture management
- `MockupManager`: Main orchestration class for multiple 3D mockups

**Key Features:**
- Three.js scene setup with cameras, lighting, and rendering
- GLTF model loading and manipulation
- Design canvas texture mapping to 3D models
- Real-time design updates and color changes
- Export capabilities for 3D rendered designs

**Architecture Integration:**
```javascript
export default (context, inject) => {
  class MockupManager {
    constructor (containerID) {
      this.container = containerID ? document.getElementById(containerID) : document.createElement('div')
      this.mockupLoader = new TMockupLoader()
      this.mockups = {}
    }

    async loadMockup (params) {
      const mockup = await parent.mockupLoader.loadMockup(params)
      const mockupRender = new TMockupRender(mockup)
      await mockupRender.render()
      parent.mockups[params.id] = mockupRender
      return mockupRender
    }
  }

  inject('MockupManager', MockupManager)
}
```

## Technical Architecture Changes

### Data Flow Enhancement
1. **API Integration**: Supports `three_d_mockups` data structure from backend
2. **Product Detection**: Uses `full_printed === 2` to identify 3D-capable products
3. **Conditional Rendering**: Switches between 2D/3D components based on product type
4. **State Management**: Maintains compatibility with existing Vuex store patterns

### 3D Rendering Pipeline
1. **Model Loading**: GLTF models loaded via Three.js with texture mapping
2. **Design Integration**: Canvas designs mapped as textures to 3D models
3. **Real-time Updates**: Design changes reflected immediately in 3D preview
4. **Multi-angle Support**: Navigation between different viewing angles

### Backward Compatibility
- Existing 2D mockup system remains unchanged
- Conditional activation only for supported products
- Fallback mechanisms for non-3D products
- Maintains existing API contracts and data structures

## Business Impact

This enhancement significantly modernizes the baseball cap customization experience by providing:
- **Interactive 3D Previews**: Users can rotate and view caps from multiple angles
- **Better Design Accuracy**: More realistic preview of text placement and appearance  
- **Enhanced User Engagement**: Modern 3D visualization increases user confidence
- **Scalable Architecture**: Foundation for extending 3D capabilities to other products

The implementation follows established codebase patterns and maintains full backward compatibility while introducing cutting-edge 3D visualization technology.