FROM node:22.0.0-alpine as builder

WORKDIR /app

COPY ./package.json .
COPY ./yarn.lock .
COPY ./.yarnrc.yml .

RUN corepack enable

RUN yarn set version 4.3.1

RUN yarn add --cached

ENV CI=true

RUN yarn install --immutable

COPY . .

ENV NUXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/2
ENV NUXT_DISCORD_WEBHOOK=https://discord.com/api/webhooks/1202199141560889354/bALKdcKqKkzDxbf0mQGCs6ZrlihPio0w_1fRbHPNX3Qm6y4uJsPnWk4QU5fMCrhFTO8T

RUN yarn build

RUN rm -rf node_modules

FROM node:22.0.0-alpine

WORKDIR /app

COPY --from=builder /app  .

ENV HOST=0.0.0.0
EXPOSE 3000
ENV NUXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/2
ENV NUXT_DISCORD_WEBHOOK=https://discord.com/api/webhooks/1202199141560889354/bALKdcKqKkzDxbf0mQGCs6ZrlihPio0w_1fRbHPNX3Qm6y4uJsPnWk4QU5fMCrhFTO8T

CMD [ "yarn", "start" ]