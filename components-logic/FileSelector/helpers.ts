export async function getVideoCover(file: File, seekTo = 0.0) {
  return await new Promise((resolve) => {
    const video = document.createElement('video')
    video.setAttribute('src', URL.createObjectURL(file))
    video.load()
    video.addEventListener('loadedmetadata', () => {
      setTimeout(() => {
        video.currentTime = seekTo
      }, 200)
      video.addEventListener('seeked', () => {
        const canvas = document.createElement('canvas')
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext('2d')
        ctx!.drawImage(video, 0, 0, canvas.width, canvas.height)
        ctx?.canvas.toBlob((blob) => {
          return resolve(blob)
        }, 'image/jpeg', 1)
      })
    })
  })
}

export function parseFileName(fileName: string) {
  const els = fileName.split('.')
  return {
    name: els.slice(0, -1).join('.'),
    ext: els.pop()
  }
}
