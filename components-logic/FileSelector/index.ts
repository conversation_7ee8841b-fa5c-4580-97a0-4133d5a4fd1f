import type { FileSelectorEmits, FileSelectorProps, InputFileEvent } from './type'
import { getVideoCover, parseFileName } from './helpers'

type OverloadToRecord<T> =
  T extends { (e: infer E, ...args: infer A): any }
    ? { [K in E & string]: (...args: A) => any }
    : never

export function getDefaultsPropsValue() {
  return {
    modelValue: () => [],
    inputType: 1,
    accept: 'image/*',
    limit: 1,
    browseText: 'Choose File',
    placeholder: 'Choose a image or drop it here...',
    dropMsg: 'Drop files here...',
    sizeLimit: 0,
    acceptFileExtensions: () => [],
    disabled: false,
    autoUpload: true,
    state: undefined,
    message: undefined,
    label: undefined,
    inputClass: undefined,
    id: `__FileSelector__${Date.now()}`,
    labelClass: 'label mb-1 mt-2 transition-all duration-150 text-overflow-hidden w-full capitalize z-1 text-sm',
    selectorClass: '',
    returnMediaType: false,
    isPushToValue: false,
    fileUpload: undefined
  }
}

export function useFileSelector(props: RequireAllExcept<FileSelectorProps, 'fileUpload' | 'state' | 'message' | 'label' | 'inputClass'>, emit: FileSelectorEmits) {
  const { AWSUpload } = useAWSUpload()
  const fileInput = ref()

  function resetFilesInput() {
    if (fileInput.value) {
      fileInput.value.value = null
      fileInput.value.dispatchEvent(new Event('change'))
    }
  }

  const files = ref({} as Partial<FileList>)
  const filesName = computed((): string => {
    if (!files.value.length) {
      return ''
    }
    return Object.entries(files.value).map(([, file]) => (file as File).name).join(', ')
  })

  const multiple = computed((): boolean => Boolean(props.limit && props.limit > 1))

  const regexMatchAcceptType = computed(() => {
    const str = props.accept.replace(/\s*/g, '').replace(/,/g, '|').replace(/\*/g, '.*')
    return new RegExp(`^(${str})$`, 'i')
  })

  const internalDisable = computed(() => {
    if (props.limit && props.modelValue.length && props.isPushToValue) {
      return props.modelValue.length >= props.limit
    }
    return props.disabled
  })

  function uploadFilesAWS(files: FileList) {
    emit('onStateChange', { isUploading: true })
    try {
      loading(true)
      const promisedArr = Array.from(files).map(async (file) => {
        if (props.returnMediaType) {
          if (/video/.test(file.type)) {
            const thumbnail: {
              name?: string
              size: number
              type: string
            } = (await getVideoCover(file)) as Blob
            thumbnail.name = `${parseFileName(file.name).name}.jpeg`

            return {
              type: 'video',
              thumb: await AWSUpload(thumbnail as File),
              url: await AWSUpload(file)
            }
          }

          return {
            type: 'image',
            url: await AWSUpload(file)
          }
        }

        return await AWSUpload(file)
      })
      Promise.allSettled(promisedArr).then((settled) => {
        const urls = (settled as unknown as { status: string, value: string }[]).map(uploaded => uploaded.value)

        if (props.isPushToValue) {
          emit('update:modelValue', props.modelValue.concat(urls))
          resetFilesInput()
        }
        else {
          emit('update:modelValue', urls)
        }
        emit('onChange', urls)
        emit('onStateChange', { isUploading: false })
        loading(false)
      })
    }
    catch (err) {
      emit('onError', err)
      loading(false)
    }
  }

  function processFiles(lfiles: FileList) {
    emit('onFileInputChange', lfiles.length)
    let invalidFileInput = false

    if (lfiles.length + props.modelValue.length > props.limit) {
      invalidFileInput = true
      return emit('onValidatorError', 'exceed_limit')
    }
    if (lfiles.length === 0) {
      files.value = {}
      return emit('onChange', [])
    }

    for (let itera = 0; itera < lfiles.length; itera++) {
      const file = lfiles[itera]

      if (!regexMatchAcceptType.value.test(file.type) || (props.acceptFileExtensions.length && !props.acceptFileExtensions.includes(parseFileName(file.name)?.ext ?? ''))) {
        invalidFileInput = true
        emit('onValidatorError', 'invalid_extension')
      }
      if (props.sizeLimit && file.size > props.sizeLimit) {
        invalidFileInput = true
        emit('onValidatorError', 'exceed_file_size_limit')
      }
    }

    if (invalidFileInput) {
      return resetFilesInput()
    }

    files.value = lfiles
    if (props.autoUpload) {
      uploadFilesAWS(lfiles)
    }
    else {
      emit('onChange', lfiles)
    }
  }

  const isDroping = ref(false)

  function onDragging() {
    isDroping.value = true
  }

  function onDrop(ev: DragEvent) {
    processFiles(ev.dataTransfer!.files)
    isDroping.value = false
  }

  function onInputChange(i: Event) {
    const lfiles = (i as InputFileEvent).target.files as FileList
    processFiles(lfiles)
  }

  return {
    fileInput,
    files,
    filesName,
    multiple,
    internalDisable,
    // Helpers
    resetFilesInput,
    isDroping,
    onDragging,
    onDrop,
    onInputChange
  }
}
