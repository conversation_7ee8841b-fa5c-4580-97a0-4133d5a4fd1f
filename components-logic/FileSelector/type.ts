export interface FileSelectorProps {
  modelValue?: Array<any>
  inputType?: number
  accept?: string
  limit?: number
  browseText?: string
  placeholder?: string
  dropMsg?: string
  sizeLimit?: number
  acceptFileExtensions?: string[]
  disabled?: boolean
  autoUpload?: boolean
  state?: string | boolean
  message?: string
  label?: string
  inputClass?: string
  id?: string
  labelClass?: string
  selectorClass?: string
  returnMediaType?: boolean
  isPushToValue?: boolean
  fileUpload?: string
}

export interface FileSelectorEmits {
  (e: 'onChange', value: FileList): void
  (e: 'onValidatorError', value: string): void
  (e: 'onStateChange', value: { isUploading: boolean }): void
  (e: 'onError', value: Error): void
  (e: 'onFileInputChange', value: number): void
  (e: 'update:modelValue', value: string[]): void
}

export interface InputFileEvent extends Event {
  target: HTMLInputElement
}
