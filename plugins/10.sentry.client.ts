import * as Sentry from '@sentry/vue'
import type { Router } from 'vue-router'

export default defineNuxtPlugin((nuxtApp) => {
  const { $config, vueApp, $router } = nuxtApp

  const environment = `v4-${$config.public.appEnv}`
  const dsn = $config.public.sentryDSN
  const sampleRate = parseFloat($config.public.sentrySampleRate) || 1

  if (dsn) {
    Sentry.init({
      // @ts-ignore
      app: vueApp,
      dsn,
      environment,
      integrations: [
        new Sentry.BrowserTracing({
          routingInstrumentation: Sentry.vueRouterInstrumentation($router as Router),
        }),
      ],
      sampleRate,
      tracesSampleRate: 1,
      trackComponents: true,
      hooks: ['activate', 'create', 'destroy', 'mount', 'update'],

      // https://docs.sentry.io/platforms/javascript/configuration/filtering/#decluttering-sentry
      denyUrls: [
        // Facebook flakiness
        /graph\.facebook\.com/i,
        // Facebook blocked
        /connect\.facebook\.net\/en_US\/all\.js/i,
        // Woopra flakiness
        /eatdifferent\.com\.woopra-ns\.com/i,
        /static\.woopra\.com\/js\/woopra\.js/i,
        // Chrome extensions
        /extensions\//i,
        /^chrome:\/\//i,
        /^chrome-extension:\/\//i,
        // Other plugins
        /127\.0\.0\.1:4001\/isrunning/i, // Cacaoweb
        /webappstoolbarba\.texthelp\.com\//i,
        /metrics\.itunes\.apple\.com\.edgesuite\.net\//i,
      ],
    })
  }

  return {
    provide: {
      sentrySetContext: Sentry.setContext,
      sentrySetUser: Sentry.setUser,
      sentrySetTag: Sentry.setTag,
      sentryAddBreadcrumb: Sentry.addBreadcrumb,
      sentryCaptureException: Sentry.captureException,
    },
  }
})
