import { useUserSession } from '~/store/userSession'

// https://github.com/unjs/ofetch
export default defineNuxtPlugin(({ ssrContext, $config, _route, $isBot, $userAgent }) => {
  const userSession = useUserSession()
  const {
    proxyApiUrl,
    proxyWriteApiUrl,
    public: publicConfig
  } = $config

  const { req, res } = ssrContext?.event.node || {}
  let cacheTags: Array<string> = []

  const host = req?.headers.host as string
  const baseURL = proxyApiUrl
  const baseURLWrite = proxyWriteApiUrl || proxyApiUrl
  const target = new URL(baseURL)

  const headers: HeadersInit = {
    'X-DOMAIN': publicConfig.xDomain || host,
    'host': target.host,
    'sp-request-source': 'ssr',
    'sp-is-bot': $isBot ? 'true' : 'false',
    'sp-user-agent': $userAgent as string,
    'sp-referer': req?.headers.referer || '',
    'X-VERSION': '4',
    'FORCE-DISTRIBUTED-CHECKOUT': useRuntimeConfig()?.public?.appEnv === 'dev' ? '0' : '1'
  }
  const spsid = userSession.visitInfo.spsid || _route.query.spsid || getCookie('spsid', req?.headers.cookie)
  if (spsid) {
    headers.spsid = spsid as string
  }

  if (_route.query.fcache) {
    headers.fcache = _route.query.fcache as string
  }

  if (_route.query.nocache) {
    userSession.$patch({
      nocacheExpireTime: Date.now() + 360000
    })
  }

  if (userSession.nocacheExpireTime >= Date.now()) {
    headers['No-Cache'] = 'true'
  }

  const fetchDefault = $fetch.create({
    retry: 3,
    // signal: AbortSignal.timeout(15000),
    baseURL,
    headers,
    method: $method.get,
    parseResponse: JSON.parse,
    onRequest({ options }) {
    },
    onResponse({ response }) {
      if (response.headers.get('cache-tags')) {
        cacheTags = _union(response.headers.get('cache-tags')?.split(','))
      }

      try {
        if (res && cacheTags.length) {
          res.setHeader('Cache-Tags', cacheTags.toString())
        }
        const resCacheExpireTime = response.headers.get('cache-expire-time')
        if (res && resCacheExpireTime) {
          res.setHeader('Cache-Expire-Time', resCacheExpireTime)
        }
      }
      catch (error) {
        console.error('errorSendHeader')
      }
    }

  })

  const fetchWrite = fetchDefault.create({
    baseURL: baseURLWrite
  })

  return {
    provide: {
      host,
      fetchDefault,
      fetchWrite
    }
  }
})
