import { useCampaignStore } from '~/store/campaign'
export default defineNuxtPlugin(() => {
  const router = useRouter()

  router.afterEach(() => {
    reloadProductVariantsSize()
  })

  function reloadProductVariantsSize () {
    const campaignStore = useCampaignStore()
    if (!campaignStore.$state.productVariants) { return }

    if (Object.keys(campaignStore.$state.productVariants).length >= 10) {
      campaignStore.resetProductVariants()
    }
  }
})
