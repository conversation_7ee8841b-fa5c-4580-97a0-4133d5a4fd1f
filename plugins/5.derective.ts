export default defineNuxtPlugin(({ vueApp }) => {
  vueApp.directive('click-outside', {
    mounted (el, binding) {
      el.clickOutsideEvent = function (event: Event) {
        if (!(el === event.target || el.contains(event.target))) {
          binding.value(event)
        }
      }
      document.body.addEventListener('click', el.clickOutsideEvent)
      document.body.addEventListener('contextmenu', el.clickOutsideEvent)
    },
    unmounted (el) {
      document.body.removeEventListener('click', el.clickOutsideEvent)
      document.body.removeEventListener('contextmenu', el.clickOutsideEvent)
    }
  })

  vueApp.directive('click-not-drag', {
    mounted (el, binding) {
      el.currentClientX = false
      el.currentClientY = false
      el.mousedownEvent = function (event: MouseEvent) {
        if (event && (event.which === 1 || event.buttons === 1)) {
          el.currentClientX = event.clientX
          el.currentClientY = event.clientY
          el.firstChild.classList.add('pointer-events-none')
        }
      }
      el.mouseupEvent = function (event: MouseEvent) {
        if (
          event &&
          (event.which === 1 || event.buttons === 1) &&
          el.currentClientX <= (event.clientX + 10) &&
          el.currentClientX >= (event.clientX - 10) &&
          el.currentClientY <= (event.clientY + 10) &&
          el.currentClientY >= (event.clientY - 10)
        ) {
          binding.value(event)
        }
        el.firstChild.classList.remove('pointer-events-none')
      }

      el.mouseoutEvent = function () {
        el.firstChild.classList.remove('pointer-events-none')
      }

      el.addEventListener('mousedown', el.mousedownEvent)
      el.addEventListener('mouseup', el.mouseupEvent)
      el.addEventListener('onmouseout', el.mouseoutEvent)
    },
    unmounted (el) {
      el.removeEventListener('mousedown', el.mousedownEvent)
      el.removeEventListener('mouseup', el.mouseupEvent)
      el.removeEventListener('onmouseout', el.mouseoutEvent)
      el.firstChild?.classList?.remove('pointer-events-none')
    }
  })
})
