import type { Pinia } from 'pinia'
import { useCampaignStore } from '~/store/campaign'
import { useGeneralSettings } from '~/store/generalSettings'
import { useStoreInfo } from '~/store/storeInfo'

export default defineNuxtPlugin(async ({ $pinia }) => {
  const storeInfo = useStoreInfo($pinia as Pinia)
  const generalSettings = useGeneralSettings($pinia as Pinia)
  const campaignStore = useCampaignStore($pinia as Pinia)
  const { $fetchDefault } = useNuxtApp()

  const route = useRoute()

  if (!storeInfo.id) {
    try {
      const nuxtSeverInitDataAsync: Promise<any>[] = [
        $fetchDefault<ResponseData<StoreInfoResponse>>($api.API_GET_STORE_INFO),
        $fetchDefault<ResponseData<boolean>>($api.API_GET_GLOBAL_ENABLE_CRISP)
      ]

      if ((route.name as string)?.startsWith('productSlug__')) {
        nuxtSeverInitDataAsync.push(campaignStore.getCampaignBySlug(route.params.campaignSlug as string))
      }

      const nuxtSeverInitData = await Promise.all(nuxtSeverInitDataAsync)
      const { data, success } = nuxtSeverInitData[0]
      const { data: isGlobalEnableCrisp, success: successCrisp } = nuxtSeverInitData[1]

      if (success && data) {
        data.storeInfo.enable_crisp_support = data.storeInfo.enable_crisp_support && successCrisp && isGlobalEnableCrisp
        storeInfo.update(data.storeInfo)
        generalSettings.update(data.generalSettings)
      }
      else {
        throw new Error('Store Not Found')
      }
    }
    catch {
      showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
    }
  }
})
