import type { Pinia } from 'pinia'
import { useGeneralSettings } from '~/store/generalSettings'
import { useStoreInfo } from '~/store/storeInfo'
import { useUserSession } from '~/store/userSession'
import { getDynamicBaseCostIndex as $getDynamicBaseCostIndex, getBaseCostOfTemplateDependOnUserCountry, roundToHalf } from '~/utils/price'

export default defineNuxtPlugin(({ $pinia, ssrContext, _route, $config }) => {
  supportWebp()

  const { userBehavior, clientInfo, userInfo } = useUserSession($pinia as Pinia)
  const { countries } = useGeneralSettings($pinia as Pinia)
  const { getCurrencyByCode } = useGeneralSettings($pinia as Pinia)
  const storeInfo = useStoreInfo($pinia as Pinia)

  const loadedImgs: { [key: string]: string } = {}

  function imgUrl({ path, color, type, format }: Partial<SImage>): string {
    if (!path) { return '' }
    if (path.startsWith('data:image')) { return path }
    // return static image
    if (path.startsWith('images/')) {
      return `${cdnURL.value}${path}`
    }

    if (path.startsWith('http')) {
      if (path.includes('cloudmockups.com')) {
        if (color) {
          const colorValue = getColorForImageTransform(color)
          if (colorValue) {
            path = path.replace(/(c=\w)\w+/g, `c=${colorValue?.replace('#', '')}`)
          }
        }
        if (imageType[type as SImageType]) {
          path += imageType[type as SImageType].express
        }
        else {
          path += '&expectedWidth=160'
        }
        return path
      }

      if (color) {
        const colorValue = getColorForImageTransform(color)
        if (colorValue) {
          path = path.replace(/co_rgb:.{6}/, `co_rgb:${colorValue.replace('#', '')}`)
        }
      }

      return path
    }

    let imgUrl = $config?.public.baseImageUrl || ''

    if (storeInfo.is_proxy && $config?.public.appEnv === 'product') {
      imgUrl = `https://images.${getHost()}`
    }

    if (imageType[type as SImageType]) {
      imgUrl += imageType[type as SImageType].s3
    }
    else {
      imgUrl += '/rx/160x200,c_2'
    }

    const fm = format === 'webp' ? ',q_90,ofmt_webp' : ''
    imgUrl += fm

    if (path.includes('cloudinary.com') || path.startsWith('/s')) {
      if (color) {
        const colorValue = getColorForImageTransform(color)
        if (colorValue) {
          path = path.replace(/co_rgb:.{6}/, `co_rgb:${colorValue?.replace('#', '')}`)
        }
      }
      path = path.replace(/https:\/\/res\.cloudinary\.com\/\w+\/image\/upload/, '/s1')
      imgUrl += path
    }
    else {
      imgUrl += `/s2/${path}`
    }
    return imgUrl
  }

  async function preloadImg({ path, color, type }: Partial<SImage>) {
    if (import.meta.server) {
      return imgUrl({ path, type, color, format: 'webp' })
    }
    const key = `${type}-${path}`
    let img, url
    // preload webp image if browser support webp
    if (supportWebp()) {
      url = imgUrl({ path, type, color, format: 'webp' })
      img = await imgReady(url, key)
    }
    if (!img) {
      url = imgUrl({ path, type, color, format: null })
      await imgReady(url, key)
    }
    return url
  }

  function getHost(): any {
    if (import.meta.client) {
      return window.top?.location.hostname
    }
    return ssrContext?.event.node.req.headers.host
  }

  function supportWebp(): boolean {
    let webpSupported
    try {
      if (typeof document === 'undefined') {
        return false
      }

      if (typeof webpSupported !== 'undefined') {
        return webpSupported
      }

      const elem = document.createElement('canvas')

      if (elem.getContext && elem.getContext('2d')) {
        // was able or not to get WebP representation
        webpSupported = elem.toDataURL('image/webp').indexOf('data:image/webp') === 0
      }
      else {
        // very old browser like IE 8, canvas not supported
        webpSupported = false
      }
      if (import.meta.client) {
        window.isSupportWebp = webpSupported
      }
      return webpSupported
    }
    catch (error) {
      window.isSupportWebp = false
      return false
    }
  }

  function imgReady(url: string, key: string) {
    return new Promise((resolve) => {
      if (!(import.meta.client)) {
        resolve(null)
      }
      if (loadedImgs[key]) {
        return resolve(loadedImgs[key])
      }

      const img = new Image(80, 100)
      img.onload = function () {
        if (key) {
          loadedImgs[key] = url
        }
        resolve(url)
      }
      img.onerror = function () {
        resolve(null)
      }
      img.src = url
    })
  }

  function getCurrencySymbol(locale = 'en-US', currency = 'USD') {
    return (0).toLocaleString(locale, { style: 'currency', currency, minimumFractionDigits: 0, maximumFractionDigits: 0 }).replace(/\d/g, '').trim()
  }

  function convertPrice(value: number | string, productCurrencyCode?: CurrencyCode, convertCode?: CurrencyCode, allowNoConvert: boolean = true) {
    if (typeof value === 'string') {
      value = value.replace(/,/g, '.')
    }
    value = Number.parseFloat(value.toString())
    if (productCurrencyCode && allowNoConvert === true && userBehavior.currencyCode && userBehavior.currencyCode === productCurrencyCode) {
      const currency = getCurrencyByCode(userBehavior.currencyCode)
      return { value, currency }
    }
    // convert other currency to default(USD)
    if (productCurrencyCode) {
      const productRate = getCurrencyByCode(productCurrencyCode)?.rate || 1
      value = value / productRate
    }

    // display store currency
    let currency: Currency | undefined

    if (convertCode) {
      currency = getCurrencyByCode(convertCode)
    }
    else {
      currency = getCurrencyByCode(userBehavior.currencyCode)
    }

    value = value * (currency?.rate || 1)

    if (currency?.code === VND_CODE) {
      value = Math.round(value / 1000) * 1000
    }

    return { value, currency }
  }

  function formatPriceNoUnit(value: string | number, productCurrencyCode: CurrencyCode, convertCode: CurrencyCode, allowNoConvert: boolean = true) {
    value = convertPrice(value, productCurrencyCode, convertCode, allowNoConvert).value
    return formatCurrency(value)
  }

  function formatCurrency(number: number | string, fix = 2) {
    return number ? Number(Number.parseFloat(number as string).toFixed(fix)).toLocaleString() : 0
  }

  function getRawPrice(value: string | number, productCurrencyCode: CurrencyCode, convertCode: CurrencyCode) {
    const output = convertPrice(value, productCurrencyCode, convertCode).value
    return output ? Number(Number.parseFloat(String(output)).toFixed(2)) : 0
  }

  function formatPrice(value: number = 0, productCurrencyCode?: CurrencyCode, convertCode?: CurrencyCode) {
    const data = convertPrice(value, productCurrencyCode, convertCode)

    return toLocalePrice(data.value, data.currency)
  }

  function formatPriceByCurrency(value: number = 0, currencyRate: number = 1, currencyCode?: CurrencyCode) {
    const currency = getCurrencyByCode(currencyCode || userBehavior.currencyCode)
    value = value * (currencyRate || currency?.rate || 1)

    if (currency?.code === VND_CODE) {
      value = Math.round(value / 1000) * 1000
    }

    return toLocalePrice(value, currency)
  }

  function toLocalePrice(value: number, currency?: Currency) {
    if (!currency) {
      currency = getCurrencyByCode(userBehavior.currencyCode)
    }
    let string = value.toLocaleString(currency?.locale, {
      style: 'currency',
      currency: currency?.code
    }).replace(/\s/, '') // remove space in string

    if ($arrCurrenciesMustHavePrefix.includes(currency?.code as any)) {
      string = `${currency?.code} ${string}`
    }

    return string
  }

  function getMobileOperatingSystem() {
    const userAgent = getUserAgent()
    // Windows Phone must come first because its UA also contains "Android"
    if (/windows phone/i.test(userAgent)) {
      return 'Windows Phone'
    }
    if (/android/i.test(userAgent)) {
      return 'Android'
    }
    // iOS detection from: http://stackoverflow.com/a/9039885/177710
    if (/iPad|iPhone|iPod|Macintosh/.test(userAgent) && !window?.MSStream) {
      return 'iOS'
    }
    return 'unknown'
  }

  function isBot(): boolean {
    if (import.meta.client) {
      return getUserAgent().match(/abacho|accona|AddThis|AdsBot|ahoy|AhrefsBot|AISearchBot|alexa|altavista|anthill|appie|applebot|arale|araneo|AraybOt|ariadne|arks|aspseek|ATN_Worldwide|Atomz|baiduspider|baidu|bbot|bingbot|bing|Bjaaland|BlackWidow|BotLink|bot|boxseabot|bspider|calif|CCBot|ChinaClaw|christcrawler|CMC\/0\.01|combine|confuzzledbot|contaxe|CoolBot|cosmos|crawler|crawlpaper|crawl|curl|cusco|cyberspyder|cydralspider|dataprovider|digger|DIIbot|DotBot|downloadexpress|DragonBot|DuckDuckBot|dwcp|EasouSpider|ebiness|ecollector|elfinbot|esculapio|ESI|esther|eStyle|Ezooms|facebookexternalhit|facebook|facebot|fastcrawler|FatBot|FDSE|FELIX IDE|fetch|fido|find|Firefly|fouineur|Freecrawl|froogle|gammaSpider|gazz|gcreep|geona|Getterrobo-Plus|get|girafabot|golem|googlebot|-google|grabber|GrabNet|griffon|Gromit|gulliver|gulper|hambot|havIndex|hotwired|htdig|HTTrack|ia_archiver|iajabot|IDBot|Informant|InfoSeek|InfoSpiders|INGRID\/0\.1|inktomi|inspectorwww|Internet Cruiser Robot|irobot|Iron33|JBot|jcrawler|Jeeves|jobo|KDD-Explorer|KIT-Fireball|ko_yappo_robot|label-grabber|larbin|legs|libwww-perl|linkedin|Linkidator|linkwalker|Lockon|logo_gif_crawler|Lycos|m2e|majesticsEO|marvin|mattie|mediafox|mediapartners|MerzScope|MindCrawler|MJ12bot|mod_pagespeed|moget|Motor|msnbot|muncher|muninn|MuscatFerret|MwdSearch|NationalDirectory|naverbot|NEC-MeshExplorer|NetcraftSurveyAgent|NetScoop|NetSeer|newscan-online|nil|none|Nutch|ObjectsSearch|Occam|openstat.ru\/Bot|packrat|pageboy|ParaSite|patric|pegasus|perlcrawler|phpdig|piltdownman|Pimptrain|pingdom|pinterest|pjspider|PlumtreeWebAccessor|PortalBSpider|psbot|rambler|Raven|RHCS|RixBot|roadrunner|Robbie|robi|RoboCrawl|robofox|Scooter|Scrubby|Search-AU|searchprocess|search|SemrushBot|Senrigan|seznambot|Shagseeker|sharp-info-agent|sift|SimBot|Site Valet|SiteSucker|skymob|SLCrawler\/2\.0|slurp|snooper|solbot|speedy|spider_monkey|SpiderBot\/1\.0|spiderline|spider|suke|tach_bw|TechBOT|TechnoratiSnoop|templeton|teoma|titin|topiclink|twitterbot|twitter|UdmSearch|Ukonline|UnwindFetchor|URL_Spider_SQL|urlck|urlresolver|Valkyrie libwww-perl|verticrawl|Victoria|void-bot|Voyager|VWbot_K|wapspider|WebBandit\/1\.0|webcatcher|WebCopier|WebFindBot|WebLeacher|WebMechanic|WebMoose|webquest|webreaper|webspider|webs|WebWalker|WebZip|wget|whowhere|winona|wlm|WOLP|woriobot|WWWC|XGET|xing|yahoo|YandexBot|YandexMobileBot|yandex|yeti|Zeus/i)
    }
    return false
  }

  function getUserAgent() {
    return import.meta.client ? navigator.userAgent || navigator.vendor || window?.opera : ssrContext?.event.node.req.headers['user-agent']
  }

  // function createSEOMeta ({ title = '', description = '', image, keywords, price, currency = 'USD', SKU = '', name = '', markupScript = {} }:any) {
  //   const host = getHost()
  //   const url = `https://${host}${_route?.fullPath || ''}`

  //   if (!title) {
  //     title = storeInfo.name
  //   }
  //   if (!description) {
  //     description = storeInfo.description || storeInfo.name || ''
  //   }
  //   const meta = [
  //     { hid: 'description', name: 'description', content: description },
  //     { hid: 'title', name: 'title', content: title },
  //     // google
  //     { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: title },

  //     // facebook
  //     { hid: 'og:title', property: 'og:title', content: title },
  //     { hid: 'og:description', property: 'og:description', content: description },
  //     { hid: 'og:url', property: 'og:url', content: url },
  //     { hid: 'og:type', property: 'og:type', content: 'website' },
  //     { hid: 'og:site_name', property: 'og:site_name', content: storeInfo.name || '' },

  //     // twitter
  //     { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
  //     { hid: 'twitter:site', name: 'twitter:site', content: storeInfo.name || '' },
  //     { hid: 'twitter:title', name: 'twitter:title', content: title },
  //     { hid: 'twitter:description', name: 'twitter:description', content: description },
  //     { hid: 'twitter:creator', name: 'twitter:creator', content: storeInfo.name },
  //     { hid: 'twitter:url', name: 'twitter:url', content: url }
  //   ]

  //   if (storeInfo.store_type === 'google_ads') {
  //     meta.push(
  //       { hid: 'description', property: 'description', content: description },
  //       { hid: 'title', name: 'title', content: title }
  //     )

  //     if (keywords) {
  //       meta.push(
  //         { hid: 'keywords', property: 'keywords', content: keywords }
  //       )
  //     }
  //   }

  //   if (image) {
  //     meta.push(
  //       { hid: 'og:image:alt', property: 'og:image:alt', content: description },
  //       { hid: 'og:image', property: 'og:image', content: image },
  //       { hid: 'twitter:image:src', name: 'twitter:image:src', content: image }
  //     )
  //   }

  //   if (price) {
  //     meta.push(
  //       { hid: 'product:price:amount', property: 'product:price:amount', content: price },
  //       { hid: 'product:brand', property: 'product:brand', content: storeInfo.name || '' },
  //       { hid: 'product:price:currency', name: 'product:price:currency', content: currency },
  //       { hid: 'product:availability', name: 'product:availability', content: 'in stock' },
  //       { hid: 'product:retailer_item_id', name: 'product:retailer_item_id', content: SKU },
  //       { hid: 'product:item_group_id', name: 'product:item_group_id', content: name },
  //       { hid: 'product:condition', name: 'product:condition', content: 'new' }
  //     )
  //   }

  //   if (storeInfo.tracking_code && storeInfo.tracking_code.google_merchant_verification) {
  //     meta.push({ hid: 'google-site-verification', name: 'google-site-verification', content: storeInfo.tracking_code.google_merchant_verification })
  //   }
  //   if (storeInfo.tracking_code && storeInfo.tracking_code.facebook_meta_tag) {
  //     meta.push({ hid: 'facebook-domain-verification', name: 'facebook-domain-verification', content: storeInfo.tracking_code.facebook_meta_tag })
  //   }
  //   if (storeInfo.tracking_code && storeInfo.tracking_code.pinterest_meta_tag) {
  //     meta.push({ hid: 'p:domain_verify', name: 'p:domain_verify', content: storeInfo.tracking_code.pinterest_meta_tag })
  //   }

  //   const script = [
  //     markupScript,
  //   ]
  //   // vueApp.runWithContext(() => {
  //   //   useHead({
  //   //     title,
  //   //     meta,
  //   //     script
  //   //   })
  //   // })
  // }

  function getDynamicBaseCostIndex(product: Product) {
    return $getDynamicBaseCostIndex(product)
  }

  function getProductVariantsBySelectedKeyAndCountry(variants: Array<Variant>, variantKey?: string, productId?: number, countryCodeGetting?: string): Variant | undefined {
    const generalCountries = countries
    if (countryCodeGetting === null || variants.length === 0 || variantKey === null || productId === null) {
      return undefined
    }
    let variantFound = variants.find((variant) => {
      return variant.location_code === countryCodeGetting && variant.variant_key === variantKey && variant.product_id === productId
    })
    if (variantFound) {
      return variantFound
    }
    const generalCountryInfo = generalCountries.find((country) => {
      return country.code === countryCodeGetting
    })
    if (generalCountryInfo === undefined) {
      return undefined
    }
    variantFound = variants.find((variant) => {
      return variant.location_code === generalCountryInfo.region_code && variant.variant_key === variantKey && variant.product_id === productId
    })
    if (variantFound) {
      return variantFound
    }
    variantFound = variants.find((variant) => {
      return variant.location_code === '*' && variant.variant_key === variantKey && variant.product_id === productId
    })
    if (variantFound) {
      return variantFound
    }
    return undefined
  }

  function userCountryForPricing() {
    const userCountry = userInfo && userInfo.country !== '' ? userInfo.country : clientInfo.country
    return userCountry
  }

  return {
    provide: {
      imgUrl,
      preloadImg,
      getHost,
      getCurrencySymbol,
      formatPrice,
      convertPrice,
      formatPriceNoUnit,
      formatPriceByCurrency,
      toLocalePrice,
      getMobileOperatingSystem,
      isBot,
      getRawPrice,
      // createSEOMeta
      getBaseCostOfTemplateDependOnUserCountry,
      roundToHalf,
      getDynamicBaseCostIndex,
      getProductVariantsBySelectedKeyAndCountry,
      userCountryForPricing
    }
  }
})
