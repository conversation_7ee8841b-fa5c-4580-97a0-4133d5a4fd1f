/* eslint-disable no-console */
import { useUserSession } from '~/store/userSession'

// https://github.com/unjs/ofetch
export default defineNuxtPlugin(({ _route, $isDebug }) => {
  const userSession = useUserSession()

  const host = window.top?.location.hostname as string
  const protocol = window.top?.location.protocol as string || 'https:'
  const port = window.top?.location.port !== '' ? `:${window.top?.location.port}` : ''
  const baseURL = `${protocol}//${host}${port}/api`
  const baseURLWrite = `${protocol}//${host}${port}/api2`

  const headers: HeadersInit = {}

  headers['X-VERSION'] = '4'
  headers['FORCE-DISTRIBUTED-CHECKOUT'] = useRuntimeConfig()?.public?.appEnv === 'dev' ? '0' : '1'
  const spsid = userSession.visitInfo.spsid || _route.query.spsid
  if (spsid) {
    headers.spsid = spsid as string
  }

  if (_route.query.fcache) {
    headers.fcache = _route.query.fcache as string
  }

  if (_route.query.nocache) {
    userSession.$patch({
      nocacheExpireTime: Date.now() + 360000
    })
  }

  if (userSession.nocacheExpireTime >= Date.now()) {
    headers['No-Cache'] = 'true'
  }

  const fetchDefault = $fetch.create({
    retry: 3,
    // signal: AbortSignal.timeout(15000),
    baseURL,
    headers,
    method: $method.get,
    parseResponse: JSON.parse,
    onRequest({ request, options }) {
      if ($isDebug && !(request as string).includes('nlogs')) {
        console.groupCollapsed(`%c${options.method}`, 'color:blue', request)
        console.log(options.body)
        console.groupEnd()
      }
    },

    onResponse({ request, response, options }) {
      if ($isDebug && response.ok && !(request as string).includes('nlogs')) {
        console.groupCollapsed(`%c${options.method}`, 'color:green', request)
        console.log(response._data)
        console.groupEnd()
      }
    },
    onResponseError({ request, response }) {
      console.log('httpError')

      if ($isDebug && !(request as string).includes('nlogs')) {
        // usePopup().createPopup(
        //   (response._data.message) ? response._data.message : `${response.status} ${response.statusText}`
        // )
        console.groupCollapsed(`%cresponse-error ${response.status}`, 'color:red', request)
        console.log(response.status, response.statusText)
        console.groupEnd()
      }
    }
  })

  const fetchWrite = fetchDefault.create({
    baseURL: baseURLWrite
  })

  return {
    provide: {
      host,
      fetchDefault,
      fetchWrite
    }
  }
})
