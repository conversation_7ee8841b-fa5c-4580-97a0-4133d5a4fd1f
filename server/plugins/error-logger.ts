import { ErrorLoggers } from "~/composables/errorLogger"

export default defineNitroPlugin((nitroApp) => {
  nitroApp.hooks.hook('error', (error, { event }) => {
    const { xDomain } = useRuntimeConfig().public
    const domain = xDomain || event?.node?.req?.headers['x-domain'] || event?.node?.req?.headers['x-forwarded-host'] || event?.node?.req?.headers.host

    if (/page\snot\sfound/i.test(error?.message)) {
      /**
       * Becase there's a lot of "Page Not Found"
       * and in progress to reduce runtime CPU/Mem
       * I'll skip it to see does it make a difference
       */
      return
    }

    ErrorLoggers().logAllThrottled(error, {
      'x-domain': domain,
      url: event?.node?.req?.url,
    })
  })
})
