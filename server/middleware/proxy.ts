import { ErrorLoggers } from "~/composables/errorLogger"

const colorCodes: { [key: string]: number } = {
  POST: 42,
  post: 42,
  GET: 44,
  get: 44,
  DELETE: 41,
  delete: 41,
  PUT: 43,
  put: 43,
  PATCH: 46,
  patch: 46,

  RESET: 0,
}


export default defineEventHandler(async (event) => {
  const { xDomain } = useRuntimeConfig().public

  try {
    const { proxyApiUrl, proxyWriteApiUrl } = useRuntimeConfig()

    let target: URL | null = null

    if (event.node.req.url?.startsWith('/api/')) {
      target = new URL(event.node.req.url.replace('api/', ''), proxyApiUrl)
    } else if (event.node.req.url?.startsWith('/api2/')) {
      target = new URL(event.node.req.url.replace('api2/', ''), proxyWriteApiUrl)
    }

    if (target) {
      const domain = xDomain || event.node.req.headers['x-domain'] as string || event.node.req.headers['x-forwarded-host'] as string || event.node.req.headers.host
      // console.log(`\x1B[${colorCodes[event.node.req.method || 'RESET']}m ${event.node.req.method}: proxy \x1B[${colorCodes.RESET}m ${domain}${event.node.req.url} to ${target}`)
      console.log(`\x1B[${colorCodes[event.node.req.method || 'RESET']}m ${event.node.req.method}: proxy \x1B[${colorCodes.RESET}m ${event.node.req.url}`)

      const checkIp = event.node.req.headers['cf-connecting-ip']

      const headers:any = {
        host: target.host, // if you need to bypass host security
        'X-DOMAIN': domain,
        'X-VERSION': '4',
        'FORCE-DISTRIBUTED-CHECKOUT':  useRuntimeConfig()?.public?.appEnv === 'dev' ? '0' : '1'
      }

      if (checkIp) {
        headers['sp-connecting-ip'] = checkIp
      }

      return await proxyRequest(event, target.toString(), {
        headers,

        // Could cause error with request method === 'delete' && having body
        // TODO: double check can execute request with method === 'delete' and headers 'content length' !== 0
        ...(event.node.req.method === 'DELETE')
          ? ({
              fetchOptions: {
                headers: {}
              }
            })
          : ({})
      })
    }
  } catch (error: any) {
    // Check if headers are already sent
    if (!event.node.res.headersSent) {
      console.error('[ProxyError]', error)
      ErrorLoggers().logAllThrottled(error, { codeblock: '[ProxyError]' })
      return sendError(event, createError({ statusCode: 500, message: 'Proxy Error' }))
    } else {
      ErrorLoggers().logAllThrottled(error, { codeblock: '[ProxyError after headers sent]' })
      console.error('[ProxyError after headers sent]', error)
      // Can't send error response, maybe log to external system here
    }
  }
})
