import { createRouter, defineEventHandler, useBase, appendHeader } from 'h3'

const router = createRouter()

function generate (host: string) {
  const content = [
    `Sitemap: https://${host}/sitemap.xml`,
    'User-agent: *',
    'User-agent: FacebookBot',
    'user-agent: Pinterestbot',
    'User-agent: Googlebot',
    'Crawl-delay: 5',
    'Disallow:',
    '',
    'User-agent: Googlebot',
    'Disallow: /admin/',
    'Allow: /',
    '',
    'User-agent: Googlebot-image',
    'Disallow: /admin/',
    'Allow: /',
    '',
    'User-agent: *',
    'Disallow: /bot',
    'Allow: /',
  ]

  return content.join('\n')
}

router.get('/robots.txt', defineEventHandler((event) => {
  appendHeader(event, 'Content-Type', 'text/plain')
  return generate(event.node.req.headers.host as string)
}))

router.get('/bot', defineEventHandler((event) => {
  appendHeader(event, 'Content-Type', 'text/plain')
  return 'Hello World'
}))

export default useBase('/', router.handler)
