import { createRouter, defineEvent<PERSON><PERSON><PERSON>, useBase, appendHeader } from 'h3'
import type { NodeIncomingMessage } from 'h3'

const router = createRouter()

function getId (url: string) {
  if (!url.includes('?')) {
    return null
  }

  try {
    const searchParams = new URLSearchParams(`?${url.split('?')[1]}`)
    return searchParams.get('id')
  } catch (e) {
    return null
  }
}

async function fetchSitemap (req: NodeIncomingMessage) {
  const host = req.headers['x-domain'] || req.headers.host
  // fix: connect ECONNREFUSED ::1:80
  // ref: https://github.com/axios/axios/issues/3821#issuecomment-1074294296
  const protocol = host!.includes('localhost') ? 'http' : 'https'

  const id = getId(req.url || '')
  // const url = `${process.env.PROXY_API_URL || 'https://apis.dev.senprints.net'}/api/public/sitemap${id ? `/${id}` : ''}`
  const url = `${protocol}://${host}/api/public/sitemap${(id) ? `/${id}` : ''}`

  try {
    const response = await fetch(url, {
      headers: {
        'X-DOMAIN': host as string
      }
    }).then(res => res.text())

    return {
      success: true,
      data: response,
    }
  } catch (err) {
    // TODO: sentry capture exeption
    return {
      success: false,
      err
    }
  }
}

router.get('/sitemap.xml', defineEventHandler(async (event) => {
  try {
    const json = await fetchSitemap(event.node.req)

    if (json.success) {
      appendHeader(event, 'Content-Type', 'text/xml')
      return json.data
    } else {
      appendHeader(event, 'Content-Type', 'text/plain')
      return 'Error 1: Please Contact Support'
    }
  } catch (_e) {
    appendHeader(event, 'Content-Type', 'text/plain')
    return 'Error 2: Please Contact Support'
  }
}))

export default useBase('/', router.handler)
