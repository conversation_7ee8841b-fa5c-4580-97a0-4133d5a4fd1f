import { createRouter, defineEventHand<PERSON>, useBase, sendRedirect, sendError } from 'h3'
import type { H3Error } from 'h3'

const runtimeConfig = useRuntimeConfig()

const router = createRouter()

router.get('/api/public/email/click', defineEventHandler(async (event) => {
  const url = `${runtimeConfig.proxyApiUrl}${event.node.req.url?.slice(4)}`
  const res = await fetch(url, {
    redirect: 'follow'
  })
  return sendRedirect(event, res.url)
}))

router.get('/api/marketing/email/click', defineEventHandler(async (event) => {
  const url = `${runtimeConfig.proxyApiUrl}${event.node.req.url?.slice(4)}`
  const res = await fetch(url, {
    redirect: 'follow'
  })
  return sendRedirect(event, res.url)
}))

router.get('/api/public/email/confirm-address', defineEventHandler(async (event) => {
  const url = `${runtimeConfig.proxyApiUrl}${event.node.req.url?.slice(4)}`
  const res = await fetch(url, {
    redirect: 'follow'
  })
  return sendRedirect(event, res.url)
}))

router.get('/cdn-cgi/trace', defineEventHandler((event) => {
  return sendError(event, { statusCode: 404 } as H3Error)
}))

export default useBase('/', router.handler)
