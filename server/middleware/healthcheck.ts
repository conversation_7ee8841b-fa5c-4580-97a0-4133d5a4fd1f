import process from 'node:process'
import { appendHeader, createRouter, defineEventHand<PERSON>, useBase } from 'h3'

const router = createRouter()
const baseApiUrl = process.env.NUXT_PROXY_API_URL || null

router.get('/healthcheck', defineEventHandler((event) => {
  appendHeader(event, 'Content-Type', 'text/plain')
  return 'OK'
}))

async function checkApiHealth(): Promise<number> {
  if (!baseApiUrl)
    return 1
  const controller = new AbortController()
  const signal = controller.signal
  setTimeout(() => controller.abort(), 5000)
  let code = 0
  if (baseApiUrl) {
    try {
      const res = await fetch(`${baseApiUrl}/public/ping`, {
        method: 'HEAD',
        signal
      })
      code = res.status === 200 ? 1 : 0
    }
    catch {
      code = 2
    }
  }
  return code
}
function createPingHandler(routeVersion = 3) {
  return defineEventHandler(async (event) => {
    const healthy = await checkApiHealth()
    if (healthy === 2) {
      setResponseStatus(event, 503)
      return ''
    }
    appendHeader(event, 'Content-Type', 'application/json')
    return JSON.stringify({ result: 'pong', version: routeVersion, healthyCode: healthy })
  })
}

router.get('/ping', createPingHandler())
router.get('/ping-v4', createPingHandler(4))
export default useBase('/', router.handler)
