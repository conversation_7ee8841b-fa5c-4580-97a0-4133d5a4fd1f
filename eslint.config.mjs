// @ts-check
import antfu from '@antfu/eslint-config'

export default antfu(
  {
    formatters: true,
    vue: true
  },
  {
    rules: {
      'ts/no-unsafe-function-type': 'off',
      'regexp/no-unused-capturing-group': 'off',
      'unused-imports/no-unused-vars': 'off',
      'eslint-comments/no-unlimited-disable': 'off',
      'style/max-statements-per-line': 'off',
      'no-console': 'warn',
      'vue/no-v-for-template-key': 'off',
      'vue/no-v-html': 'off',
      'vue/custom-event-name-casing': 'off',
      'style/comma-dangle': [
        'error',
        'never'
      ],
      'ts/no-use-before-define': 'off',
      'vue/comma-dangle': [
        'error',
        'never'
      ],
      'vue/require-explicit-emits': 'off',
      'ts/ban-ts-comment': 'off',
      'vue/no-restricted-v-bind': 'off',
      'vars-on-top': 'off'
    }
  }
)
