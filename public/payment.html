<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">

  <title>Redirecting to PayPal...</title>
  <script>
    ((window) => {
      function redirect(url) {
        window.top.location.href = url
      }

      const url = new URL(window.top.location.href)
      const qs = url.searchParams
      const token = qs.get('token')
      let domain = qs.get('ref')

      if (!domain) {
        redirect('https://senprints.com/')
        return
      }

      domain = window.atob(domain)

      const cartUrl = `https://${domain}/cart`

      if (!token) {
        redirect(cartUrl)
        return
      }

      const data = new FormData;
      data.set('order_token', token)
      data.set('origin', window.top.location.hostname)
      data.set('safe_redirect', '1')

      // create order
      fetch('/api/public/order/create/paypal', {
        headers: {
          accept: 'application/json, text/plain, */*'
        },
        body: data,
        method: 'POST',
        mode: 'cors',
        credentials: 'omit'
      })
        .then(res => res.json())
        .then(json => {
          redirect(json.success ? json.data : cartUrl)
        })
    })(window)
  </script>
</head>
<body>
<h3>Redirecting to PayPal...</h3>
</body>
</html>
