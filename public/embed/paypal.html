<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="initial-scale=1.0, user-scalable=no, maximum-scale=1, width=device-width">
  <style>
    * {
      margin: 0;
      padding: 0;
    }
  </style>
  <style id="custom"></style>
</head>

<body>
  <div id="paypal-button-container"></div>
  <script>
    // https://developer.paypal.com/sdk/js/configuration/#link-datapartnerattributionid
    const PAYPAL_BN_CODE = 'SensePrints_Ecom';

    let createOrderData = {};
    let submittedCheckoutValid = null;

    function setButtonContainerState(isFullScreen = false) {
      isFullScreen = !!isFullScreen;
      sendMessage({ name: "paypal_set_frame_state", args: [isFullScreen] });
    }

    function cancelOrder() {
      setButtonContainerState(true);
      submittedCheckoutValid = null;
      createOrderData = {};
      sendMessage({ name: "paypal_transaction_cancelled" });
    }

    function sendMessage(objMessageDetail) {
      // property "name" & "args" is required. value of "args" must be an array
      // { name: "handle_error", args: ["No clientId was supplied"] }
      if (!objMessageDetail.args) { objMessageDetail.args = []; }
      if (!Array.isArray(objMessageDetail.args)) { objMessageDetail.args = [objMessageDetail.args]; }

      window.parent.postMessage(objMessageDetail, "*");
    }

    const evTable = {
      set_create_order_data: (orderData) => {
        createOrderData = orderData;
      },
      set_submitted_checkout: (isValid) => {
        submittedCheckoutValid = isValid;
      },
      set_button_container_state: (isFullScreen = false) => {
        document.getElementById("custom").innerHTML = (isFullScreen) ? "#paypal-button-container { display: none; }" : "";
      },

      init_paypal: (clientId, merchantId, retries = 0) => {
        if (!clientId) {
          sendMessage({ name: "handle_error", args: ["No clientId was supplied"] });
          return
        }
        if (retries > 7) {
          sendMessage({ name: "handle_error", args: ["Retries limit exceeded"] });
          return
        }

        if (!document.querySelector("[hid='paypal-sdk']")) {
          const paypalScript = document.createElement("script");

          let src = `https://www.paypal.com/sdk/js?client-id=${clientId}&currency=USD&enable-funding=venmo`;

          if (merchantId) {
            src += `&merchant-id=${merchantId}`;
          }

          paypalScript.src = src;
          paypalScript.setAttribute("hid", "paypal-sdk");
          paypalScript.setAttribute("data-partner-attribution-id", PAYPAL_BN_CODE);
          document.head.appendChild(paypalScript);
        }

        if (typeof window.paypal === "undefined") {
          setTimeout(() => {
            evTable.init_paypal(clientId, merchantId, ++retries);
          }, 1000)
          return
        }

        try {
          document.getElementById("paypal-button-container").innerHTML = ''
          window.paypal.Buttons({
            // Setup the transaction when a payment button is clicked
            createOrder: async () => {
              sendMessage({ name: "paypal_retrieve_create_order_data" });

              await new Promise((resolve) => {
                const itv = setInterval(() => {
                  if (!Object.keys(createOrderData).length) { return; }

                  clearInterval(itv);
                  resolve();
                }, 300);
              });

              setButtonContainerState();

              return createOrderData.orderId;
            },

            onClick: async (_data, actions) => {
              // isSubmittingSmartCheckout = true
              // const check = await submitCheckout()
              sendMessage({ name: "paypal_submit_checkout" });

              await new Promise((resolve) => {
                const itv = setInterval(() => {
                  if (typeof submittedCheckoutValid !== "boolean") { return; }

                  clearInterval(itv);
                  resolve();
                }, 300);
              });

              if (submittedCheckoutValid) {
                return actions.resolve()
              } else {
                cancelOrder();
                return actions.reject()
              }
            },

            // Finalize the transaction after payer approval
            onApprove: (data) => {
              sendMessage({ name: "paypal_verify_transaction", args: [data] });
              setButtonContainerState(true);
            },

            onCancel: () => { cancelOrder(); },
            onError: () => { cancelOrder(); }
          }).render("#paypal-button-container")
        } catch (err) {
          setTimeout(() => {
            evTable.init_paypal(clientId, merchantId, ++retries);
          }, 1000)
        }
      },
    }

    function postMessageHandler(messageEvent) {
      const { data: event } = messageEvent;
      if (
        typeof event !== "object" ||
        !event.name ||
        !event.args || !Array.isArray(event.args)
      ) { return; }

      if (Object.hasOwnProperty.call(evTable, event.name)) {
        evTable[event.name].apply(null, event.args)
      }
    }

    (() => {
      if (window.addEventListener) {
        window.addEventListener("message", postMessageHandler);
      } else {
        window.attachEvent("onmessage", postMessageHandler);
      }

      sendMessage({ name: "paypal_get_client_id" })
    })()
  </script>
</body>

</html>
