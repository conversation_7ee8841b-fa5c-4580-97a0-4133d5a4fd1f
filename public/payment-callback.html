<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">

  <title>Redirecting...</title>
  <script>
    ((window) => {
      function redirect(url) {
        window.top.location.href = url
      }

      const url = new URL(window.top.location.href)
      const qs = url.searchParams
      const token = qs.get('orderToken')
      let domain = qs.get('ref')
      const type = qs.get('type')

      if (!token || !domain || !type) {
        redirect('https://senprints.com/')
        return
      }

      domain = window.atob(domain)

      const baseUrl = `https://${domain}/checkout`;

      if (type === 'cancel') {
        redirect(`${baseUrl}/${token}`)
        return
      }

      if (type === 'return') {
        qs.delete('domain')
        qs.delete('type')

        // forward queries string
        redirect(`${baseUrl}/paypal${url.search}`)
        return
      }

      redirect('https://senprints.com/')
    })(window)
  </script>
</head>
<body>
<h3>Redirecting...</h3>
</body>
</html>
