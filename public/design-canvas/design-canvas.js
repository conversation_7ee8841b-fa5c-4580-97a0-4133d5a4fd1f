/*! For license information please see design-canvas.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.DesignCanvas=e():t.DesignCanvas=e()}(this,(()=>(()=>{var t={676:(t,e,i)=>{var n,r,s,o,a,h,l,c,u,f,d,g,p,v,m,y,_,x,b,C,S,w,T=T||{version:"4.5.1"};if(e.fabric=T,"undefined"!=typeof document&&"undefined"!=typeof window)document instanceof("undefined"!=typeof HTMLDocument?HTMLDocument:Document)?T.document=document:T.document=document.implementation.createHTMLDocument(""),T.window=window;else{var O=new(i(574).JSDOM)(decodeURIComponent("%3C!DOCTYPE%20html%3E%3Chtml%3E%3Chead%3E%3C%2Fhead%3E%3Cbody%3E%3C%2Fbody%3E%3C%2Fhtml%3E"),{features:{FetchExternalResources:["img"]},resources:"usable"}).window;T.document=O.document,T.jsdomImplForWrapper=i(748).implForWrapper,T.nodeCanvas=i(246).Canvas,T.window=O,DOMParser=T.window.DOMParser}function k(t,e){var i=t.canvas,n=e.targetCanvas,r=n.getContext("2d");r.translate(0,n.height),r.scale(1,-1);var s=i.height-n.height;r.drawImage(i,0,s,n.width,n.height,0,0,n.width,n.height)}function P(t,e){var i=e.targetCanvas.getContext("2d"),n=e.destinationWidth,r=e.destinationHeight,s=n*r*4,o=new Uint8Array(this.imageBuffer,0,s),a=new Uint8ClampedArray(this.imageBuffer,0,s);t.readPixels(0,0,n,r,t.RGBA,t.UNSIGNED_BYTE,o);var h=new ImageData(a,n,r);i.putImageData(h,0,0)}T.isTouchSupported="ontouchstart"in T.window||"ontouchstart"in T.document||T.window&&T.window.navigator&&T.window.navigator.maxTouchPoints>0,T.isLikelyNode="undefined"!=typeof Buffer&&"undefined"==typeof window,T.SHARED_ATTRIBUTES=["display","transform","fill","fill-opacity","fill-rule","opacity","stroke","stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"],T.DPI=96,T.reNum="(?:[-+]?(?:\\d+|\\d*\\.\\d+)(?:[eE][-+]?\\d+)?)",T.commaWsp="(?:\\s+,?\\s*|,\\s*)",T.rePathCommand=/([-+]?((\d+\.\d+)|((\d+)|(\.\d+)))(?:[eE][-+]?\d+)?)/gi,T.reNonWord=/[ \n\.,;!\?\-]/,T.fontPaths={},T.iMatrix=[1,0,0,1,0,0],T.svgNS="http://www.w3.org/2000/svg",T.perfLimitSizeTotal=2097152,T.maxCacheSideLimit=4096,T.minCacheSideLimit=256,T.charWidthsCache={},T.textureSize=2048,T.disableStyleCopyPaste=!1,T.enableGLFiltering=!0,T.devicePixelRatio=T.window.devicePixelRatio||T.window.webkitDevicePixelRatio||T.window.mozDevicePixelRatio||1,T.browserShadowBlurConstant=1,T.arcToSegmentsCache={},T.boundsOfCurveCache={},T.cachesBoundsOfCurve=!0,T.forceGLPutImageData=!1,T.initFilterBackend=function(){return T.enableGLFiltering&&T.isWebglSupported&&T.isWebglSupported(T.textureSize)?(console.log("max texture size: "+T.maxTextureSize),new T.WebglFilterBackend({tileSize:T.textureSize})):T.Canvas2dFilterBackend?new T.Canvas2dFilterBackend:void 0},"undefined"!=typeof document&&"undefined"!=typeof window&&(window.fabric=T),function(){function t(t,e){if(this.__eventListeners[t]){var i=this.__eventListeners[t];e?i[i.indexOf(e)]=!1:T.util.array.fill(i,!1)}}T.Observable={fire:function(t,e){if(!this.__eventListeners)return this;var i=this.__eventListeners[t];if(!i)return this;for(var n=0,r=i.length;n<r;n++)i[n]&&i[n].call(this,e||{});return this.__eventListeners[t]=i.filter((function(t){return!1!==t})),this},on:function(t,e){if(this.__eventListeners||(this.__eventListeners={}),1===arguments.length)for(var i in t)this.on(i,t[i]);else this.__eventListeners[t]||(this.__eventListeners[t]=[]),this.__eventListeners[t].push(e);return this},off:function(e,i){if(!this.__eventListeners)return this;if(0===arguments.length)for(e in this.__eventListeners)t.call(this,e);else if(1===arguments.length&&"object"==typeof arguments[0])for(var n in e)t.call(this,n,e[n]);else t.call(this,e,i);return this}}}(),T.Collection={_objects:[],add:function(){if(this._objects.push.apply(this._objects,arguments),this._onObjectAdded)for(var t=0,e=arguments.length;t<e;t++)this._onObjectAdded(arguments[t]);return this.renderOnAddRemove&&this.requestRenderAll(),this},insertAt:function(t,e,i){var n=this._objects;return i?n[e]=t:n.splice(e,0,t),this._onObjectAdded&&this._onObjectAdded(t),this.renderOnAddRemove&&this.requestRenderAll(),this},remove:function(){for(var t,e=this._objects,i=!1,n=0,r=arguments.length;n<r;n++)-1!==(t=e.indexOf(arguments[n]))&&(i=!0,e.splice(t,1),this._onObjectRemoved&&this._onObjectRemoved(arguments[n]));return this.renderOnAddRemove&&i&&this.requestRenderAll(),this},forEachObject:function(t,e){for(var i=this.getObjects(),n=0,r=i.length;n<r;n++)t.call(e,i[n],n,i);return this},getObjects:function(t){return void 0===t?this._objects.concat():this._objects.filter((function(e){return e.type===t}))},item:function(t){return this._objects[t]},isEmpty:function(){return 0===this._objects.length},size:function(){return this._objects.length},contains:function(t){return this._objects.indexOf(t)>-1},complexity:function(){return this._objects.reduce((function(t,e){return t+(e.complexity?e.complexity():0)}),0)}},T.CommonMethods={_setOptions:function(t){for(var e in t)this.set(e,t[e])},_initGradient:function(t,e){!t||!t.colorStops||t instanceof T.Gradient||this.set(e,new T.Gradient(t))},_initPattern:function(t,e,i){!t||!t.source||t instanceof T.Pattern?i&&i():this.set(e,new T.Pattern(t,i))},_setObject:function(t){for(var e in t)this._set(e,t[e])},set:function(t,e){return"object"==typeof t?this._setObject(t):this._set(t,e),this},_set:function(t,e){this[t]=e},toggle:function(t){var e=this.get(t);return"boolean"==typeof e&&this.set(t,!e),this},get:function(t){return this[t]}},n=e,r=Math.sqrt,s=Math.atan2,o=Math.pow,a=Math.PI/180,h=Math.PI/2,T.util={cos:function(t){if(0===t)return 1;switch(t<0&&(t=-t),t/h){case 1:case 3:return 0;case 2:return-1}return Math.cos(t)},sin:function(t){if(0===t)return 0;var e=1;switch(t<0&&(e=-1),t/h){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(t)},removeFromArray:function(t,e){var i=t.indexOf(e);return-1!==i&&t.splice(i,1),t},getRandomInt:function(t,e){return Math.floor(Math.random()*(e-t+1))+t},degreesToRadians:function(t){return t*a},radiansToDegrees:function(t){return t/a},rotatePoint:function(t,e,i){var n=new T.Point(t.x-e.x,t.y-e.y),r=T.util.rotateVector(n,i);return new T.Point(r.x,r.y).addEquals(e)},rotateVector:function(t,e){var i=T.util.sin(e),n=T.util.cos(e);return{x:t.x*n-t.y*i,y:t.x*i+t.y*n}},transformPoint:function(t,e,i){return i?new T.Point(e[0]*t.x+e[2]*t.y,e[1]*t.x+e[3]*t.y):new T.Point(e[0]*t.x+e[2]*t.y+e[4],e[1]*t.x+e[3]*t.y+e[5])},makeBoundingBoxFromPoints:function(t,e){if(e)for(var i=0;i<t.length;i++)t[i]=T.util.transformPoint(t[i],e);var n=[t[0].x,t[1].x,t[2].x,t[3].x],r=T.util.array.min(n),s=T.util.array.max(n)-r,o=[t[0].y,t[1].y,t[2].y,t[3].y],a=T.util.array.min(o);return{left:r,top:a,width:s,height:T.util.array.max(o)-a}},invertTransform:function(t){var e=1/(t[0]*t[3]-t[1]*t[2]),i=[e*t[3],-e*t[1],-e*t[2],e*t[0]],n=T.util.transformPoint({x:t[4],y:t[5]},i,!0);return i[4]=-n.x,i[5]=-n.y,i},toFixed:function(t,e){return parseFloat(Number(t).toFixed(e))},parseUnit:function(t,e){var i=/\D{0,2}$/.exec(t),n=parseFloat(t);switch(e||(e=T.Text.DEFAULT_SVG_FONT_SIZE),i[0]){case"mm":return n*T.DPI/25.4;case"cm":return n*T.DPI/2.54;case"in":return n*T.DPI;case"pt":return n*T.DPI/72;case"pc":return n*T.DPI/72*12;case"em":return n*e;default:return n}},falseFunction:function(){return!1},getKlass:function(t,e){return t=T.util.string.camelize(t.charAt(0).toUpperCase()+t.slice(1)),T.util.resolveNamespace(e)[t]},getSvgAttributes:function(t){var e=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":e=e.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);break;case"radialGradient":e=e.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);break;case"stop":e=e.concat(["offset","stop-color","stop-opacity"])}return e},resolveNamespace:function(t){if(!t)return T;var e,i=t.split("."),r=i.length,s=n||T.window;for(e=0;e<r;++e)s=s[i[e]];return s},loadImage:function(t,e,i,n){if(t){var r=T.util.createImage(),s=function(){e&&e.call(i,r,!1),r=r.onload=r.onerror=null};r.onload=s,r.onerror=function(){T.log("Error loading "+r.src),e&&e.call(i,null,!0),r=r.onload=r.onerror=null},0!==t.indexOf("data")&&null!=n&&(r.crossOrigin=n),"data:image/svg"===t.substring(0,14)&&(r.onload=null,T.util.loadImageInDom(r,s)),r.src=t}else e&&e.call(i,t)},loadImageInDom:function(t,e){var i=T.document.createElement("div");i.style.width=i.style.height="1px",i.style.left=i.style.top="-100%",i.style.position="absolute",i.appendChild(t),T.document.querySelector("body").appendChild(i),t.onload=function(){e(),i.parentNode.removeChild(i),i=null}},enlivenObjects:function(t,e,i,n){var r=[],s=0,o=(t=t||[]).length;function a(){++s===o&&e&&e(r.filter((function(t){return t})))}o?t.forEach((function(t,e){t&&t.type?T.util.getKlass(t.type,i).fromObject(t,(function(i,s){s||(r[e]=i),n&&n(t,i,s),a()})):a()})):e&&e(r)},enlivenPatterns:function(t,e){function i(){++r===s&&e&&e(n)}var n=[],r=0,s=(t=t||[]).length;s?t.forEach((function(t,e){t&&t.source?new T.Pattern(t,(function(t){n[e]=t,i()})):(n[e]=t,i())})):e&&e(n)},groupSVGElements:function(t,e,i){var n;return t&&1===t.length?t[0]:(e&&(e.width&&e.height?e.centerPoint={x:e.width/2,y:e.height/2}:(delete e.width,delete e.height)),n=new T.Group(t,e),void 0!==i&&(n.sourcePath=i),n)},populateWithProperties:function(t,e,i){if(i&&"[object Array]"===Object.prototype.toString.call(i))for(var n=0,r=i.length;n<r;n++)i[n]in t&&(e[i[n]]=t[i[n]])},drawDashedLine:function(t,e,i,n,o,a){var h=n-e,l=o-i,c=r(h*h+l*l),u=s(l,h),f=a.length,d=0,g=!0;for(t.save(),t.translate(e,i),t.moveTo(0,0),t.rotate(u),e=0;c>e;)(e+=a[d++%f])>c&&(e=c),t[g?"lineTo":"moveTo"](e,0),g=!g;t.restore()},createCanvasElement:function(){return T.document.createElement("canvas")},copyCanvasElement:function(t){var e=T.util.createCanvasElement();return e.width=t.width,e.height=t.height,e.getContext("2d").drawImage(t,0,0),e},toDataURL:function(t,e,i){return t.toDataURL("image/"+e,i)},createImage:function(){return T.document.createElement("img")},multiplyTransformMatrices:function(t,e,i){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],i?0:t[0]*e[4]+t[2]*e[5]+t[4],i?0:t[1]*e[4]+t[3]*e[5]+t[5]]},qrDecompose:function(t){var e=s(t[1],t[0]),i=o(t[0],2)+o(t[1],2),n=r(i),h=(t[0]*t[3]-t[2]*t[1])/n,l=s(t[0]*t[2]+t[1]*t[3],i);return{angle:e/a,scaleX:n,scaleY:h,skewX:l/a,skewY:0,translateX:t[4],translateY:t[5]}},calcRotateMatrix:function(t){if(!t.angle)return T.iMatrix.concat();var e=T.util.degreesToRadians(t.angle),i=T.util.cos(e),n=T.util.sin(e);return[i,n,-n,i,0,0]},calcDimensionsMatrix:function(t){var e=void 0===t.scaleX?1:t.scaleX,i=void 0===t.scaleY?1:t.scaleY,n=[t.flipX?-e:e,0,0,t.flipY?-i:i,0,0],r=T.util.multiplyTransformMatrices,s=T.util.degreesToRadians;return t.skewX&&(n=r(n,[1,0,Math.tan(s(t.skewX)),1],!0)),t.skewY&&(n=r(n,[1,Math.tan(s(t.skewY)),0,1],!0)),n},composeMatrix:function(t){var e=[1,0,0,1,t.translateX||0,t.translateY||0],i=T.util.multiplyTransformMatrices;return t.angle&&(e=i(e,T.util.calcRotateMatrix(t))),(1!==t.scaleX||1!==t.scaleY||t.skewX||t.skewY||t.flipX||t.flipY)&&(e=i(e,T.util.calcDimensionsMatrix(t))),e},resetObjectTransform:function(t){t.scaleX=1,t.scaleY=1,t.skewX=0,t.skewY=0,t.flipX=!1,t.flipY=!1,t.rotate(0)},saveObjectTransform:function(t){return{scaleX:t.scaleX,scaleY:t.scaleY,skewX:t.skewX,skewY:t.skewY,angle:t.angle,left:t.left,flipX:t.flipX,flipY:t.flipY,top:t.top}},isTransparent:function(t,e,i,n){n>0&&(e>n?e-=n:e=0,i>n?i-=n:i=0);var r,s=!0,o=t.getImageData(e,i,2*n||1,2*n||1),a=o.data.length;for(r=3;r<a&&0!=(s=o.data[r]<=0);r+=4);return o=null,s},parsePreserveAspectRatioAttribute:function(t){var e,i="meet",n=t.split(" ");return n&&n.length&&("meet"!==(i=n.pop())&&"slice"!==i?(e=i,i="meet"):n.length&&(e=n.pop())),{meetOrSlice:i,alignX:"none"!==e?e.slice(1,4):"none",alignY:"none"!==e?e.slice(5,8):"none"}},clearFabricFontCache:function(t){(t=(t||"").toLowerCase())?T.charWidthsCache[t]&&delete T.charWidthsCache[t]:T.charWidthsCache={}},limitDimsByArea:function(t,e){var i=Math.sqrt(e*t),n=Math.floor(e/i);return{x:Math.floor(i),y:n}},capValue:function(t,e,i){return Math.max(t,Math.min(e,i))},findScaleToFit:function(t,e){return Math.min(e.width/t.width,e.height/t.height)},findScaleToCover:function(t,e){return Math.max(e.width/t.width,e.height/t.height)},matrixToSVG:function(t){return"matrix("+t.map((function(t){return T.util.toFixed(t,T.Object.NUM_FRACTION_DIGITS)})).join(" ")+")"},removeTransformFromObject:function(t,e){var i=T.util.invertTransform(e),n=T.util.multiplyTransformMatrices(i,t.calcOwnMatrix());T.util.applyTransformToObject(t,n)},addTransformToObject:function(t,e){T.util.applyTransformToObject(t,T.util.multiplyTransformMatrices(e,t.calcOwnMatrix()))},applyTransformToObject:function(t,e){var i=T.util.qrDecompose(e),n=new T.Point(i.translateX,i.translateY);t.flipX=!1,t.flipY=!1,t.set("scaleX",i.scaleX),t.set("scaleY",i.scaleY),t.skewX=i.skewX,t.skewY=i.skewY,t.angle=i.angle,t.setPositionByOrigin(n,"center","center")},sizeAfterTransform:function(t,e,i){var n=t/2,r=e/2,s=[{x:-n,y:-r},{x:n,y:-r},{x:-n,y:r},{x:n,y:r}],o=T.util.calcDimensionsMatrix(i),a=T.util.makeBoundingBoxFromPoints(s,o);return{x:a.width,y:a.height}}},function(){var t=Array.prototype.join,e={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},i={m:"l",M:"L"};function n(t,e,i,n,r,s,o,a,h,l,c){var u=T.util.cos(t),f=T.util.sin(t),d=T.util.cos(e),g=T.util.sin(e),p=i*r*d-n*s*g+o,v=n*r*d+i*s*g+a;return["C",l+h*(-i*r*f-n*s*u),c+h*(-n*r*f+i*s*u),p+h*(i*r*g+n*s*d),v+h*(n*r*g-i*s*d),p,v]}function r(t,e,i,r,o,a,h){var l=Math.PI,c=h*l/180,u=T.util.sin(c),f=T.util.cos(c),d=0,g=0,p=-f*t*.5-u*e*.5,v=-f*e*.5+u*t*.5,m=(i=Math.abs(i))*i,y=(r=Math.abs(r))*r,_=v*v,x=p*p,b=m*y-m*_-y*x,C=0;if(b<0){var S=Math.sqrt(1-b/(m*y));i*=S,r*=S}else C=(o===a?-1:1)*Math.sqrt(b/(m*_+y*x));var w=C*i*v/r,O=-C*r*p/i,k=f*w-u*O+.5*t,P=u*w+f*O+.5*e,D=s(1,0,(p-w)/i,(v-O)/r),j=s((p-w)/i,(v-O)/r,(-p-w)/i,(-v-O)/r);0===a&&j>0?j-=2*l:1===a&&j<0&&(j+=2*l);for(var E=Math.ceil(Math.abs(j/l*2)),A=[],M=j/E,F=8/3*Math.sin(M/4)*Math.sin(M/4)/Math.sin(M/2),I=D+M,L=0;L<E;L++)A[L]=n(D,I,f,u,i,r,k,P,F,d,g),d=A[L][5],g=A[L][6],D=I,I+=M;return A}function s(t,e,i,n){var r=Math.atan2(e,t),s=Math.atan2(n,i);return s>=r?s-r:2*Math.PI-(r-s)}function o(e,i,n,r,s,o,a,h){var l;if(T.cachesBoundsOfCurve&&(l=t.call(arguments),T.boundsOfCurveCache[l]))return T.boundsOfCurveCache[l];var c,u,f,d,g,p,v,m,y=Math.sqrt,_=Math.min,x=Math.max,b=Math.abs,C=[],S=[[],[]];u=6*e-12*n+6*s,c=-3*e+9*n-9*s+3*a,f=3*n-3*e;for(var w=0;w<2;++w)if(w>0&&(u=6*i-12*r+6*o,c=-3*i+9*r-9*o+3*h,f=3*r-3*i),b(c)<1e-12){if(b(u)<1e-12)continue;0<(d=-f/u)&&d<1&&C.push(d)}else(v=u*u-4*f*c)<0||(0<(g=(-u+(m=y(v)))/(2*c))&&g<1&&C.push(g),0<(p=(-u-m)/(2*c))&&p<1&&C.push(p));for(var O,k,P,D=C.length,j=D;D--;)O=(P=1-(d=C[D]))*P*P*e+3*P*P*d*n+3*P*d*d*s+d*d*d*a,S[0][D]=O,k=P*P*P*i+3*P*P*d*r+3*P*d*d*o+d*d*d*h,S[1][D]=k;S[0][j]=e,S[1][j]=i,S[0][j+1]=a,S[1][j+1]=h;var E=[{x:_.apply(null,S[0]),y:_.apply(null,S[1])},{x:x.apply(null,S[0]),y:x.apply(null,S[1])}];return T.cachesBoundsOfCurve&&(T.boundsOfCurveCache[l]=E),E}function a(t,e,i){for(var n=i[1],s=i[2],o=i[3],a=i[4],h=i[5],l=r(i[6]-t,i[7]-e,n,s,a,h,o),c=0,u=l.length;c<u;c++)l[c][1]+=t,l[c][2]+=e,l[c][3]+=t,l[c][4]+=e,l[c][5]+=t,l[c][6]+=e;return l}function h(t,e,i,n){return Math.sqrt((i-t)*(i-t)+(n-e)*(n-e))}function l(t,e,i,n,r,s,o,a){return function(h){var l,c=(l=h)*l*l,u=function(t){return 3*t*t*(1-t)}(h),f=function(t){return 3*t*(1-t)*(1-t)}(h),d=function(t){return(1-t)*(1-t)*(1-t)}(h);return{x:o*c+r*u+i*f+t*d,y:a*c+s*u+n*f+e*d}}}function c(t,e,i,n,r,s,o,a){return function(h){var l=1-h,c=3*l*l*(i-t)+6*l*h*(r-i)+3*h*h*(o-r),u=3*l*l*(n-e)+6*l*h*(s-n)+3*h*h*(a-s);return Math.atan2(u,c)}}function u(t,e,i,n,r,s){return function(o){var a,h=(a=o)*a,l=function(t){return 2*t*(1-t)}(o),c=function(t){return(1-t)*(1-t)}(o);return{x:r*h+i*l+t*c,y:s*h+n*l+e*c}}}function f(t,e,i,n,r,s){return function(o){var a=1-o,h=2*a*(i-t)+2*o*(r-i),l=2*a*(n-e)+2*o*(s-n);return Math.atan2(l,h)}}function d(t,e,i){var n,r,s={x:e,y:i},o=0;for(r=.01;r<=1;r+=.01)n=t(r),o+=h(s.x,s.y,n.x,n.y),s=n;return o}function g(t){for(var e,i,n,r,s=0,o=t.length,a=0,g=0,p=0,v=0,m=[],y=0;y<o;y++){switch(n={x:a,y:g,command:(e=t[y])[0]},e[0]){case"M":n.length=0,p=a=e[1],v=g=e[2];break;case"L":n.length=h(a,g,e[1],e[2]),a=e[1],g=e[2];break;case"C":i=l(a,g,e[1],e[2],e[3],e[4],e[5],e[6]),r=c(a,g,e[1],e[2],e[3],e[4],e[5],e[6]),n.iterator=i,n.angleFinder=r,n.length=d(i,a,g),a=e[5],g=e[6];break;case"Q":i=u(a,g,e[1],e[2],e[3],e[4]),r=f(a,g,e[1],e[2],e[3],e[4]),n.iterator=i,n.angleFinder=r,n.length=d(i,a,g),a=e[3],g=e[4];break;case"Z":case"z":n.destX=p,n.destY=v,n.length=h(a,g,p,v),a=p,g=v}s+=n.length,m.push(n)}return m.push({length:s,x:a,y:g}),m}T.util.parsePath=function(t){var n,r,s,o,a,h=[],l=[],c=T.rePathCommand,u="[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?\\s*",f="("+u+")"+T.commaWsp,d="([01])"+T.commaWsp+"?",g=new RegExp(f+"?"+f+"?"+f+d+d+f+"?("+u+")","g");if(!t||!t.match)return h;for(var p,v=0,m=(a=t.match(/[mzlhvcsqta][^mzlhvcsqta]*/gi)).length;v<m;v++){o=(n=a[v]).slice(1).trim(),l.length=0;var y=n.charAt(0);if(p=[y],"a"===y.toLowerCase())for(var _;_=g.exec(o);)for(var x=1;x<_.length;x++)l.push(_[x]);else for(;s=c.exec(o);)l.push(s[0]);x=0;for(var b=l.length;x<b;x++)r=parseFloat(l[x]),isNaN(r)||p.push(r);var C=e[y.toLowerCase()],S=i[y]||y;if(p.length-1>C)for(var w=1,O=p.length;w<O;w+=C)h.push([y].concat(p.slice(w,w+C))),y=S;else h.push(p)}return h},T.util.makePathSimpler=function(t){var e,i,n,r,s,o,h=0,l=0,c=t.length,u=0,f=0,d=[];for(i=0;i<c;++i){switch(n=!1,(e=t[i].slice(0))[0]){case"l":e[0]="L",e[1]+=h,e[2]+=l;case"L":h=e[1],l=e[2];break;case"h":e[1]+=h;case"H":e[0]="L",e[2]=l,h=e[1];break;case"v":e[1]+=l;case"V":e[0]="L",l=e[1],e[1]=h,e[2]=l;break;case"m":e[0]="M",e[1]+=h,e[2]+=l;case"M":h=e[1],l=e[2],u=e[1],f=e[2];break;case"c":e[0]="C",e[1]+=h,e[2]+=l,e[3]+=h,e[4]+=l,e[5]+=h,e[6]+=l;case"C":s=e[3],o=e[4],h=e[5],l=e[6];break;case"s":e[0]="S",e[1]+=h,e[2]+=l,e[3]+=h,e[4]+=l;case"S":"C"===r?(s=2*h-s,o=2*l-o):(s=h,o=l),h=e[3],l=e[4],e[0]="C",e[5]=e[3],e[6]=e[4],e[3]=e[1],e[4]=e[2],e[1]=s,e[2]=o,s=e[3],o=e[4];break;case"q":e[0]="Q",e[1]+=h,e[2]+=l,e[3]+=h,e[4]+=l;case"Q":s=e[1],o=e[2],h=e[3],l=e[4];break;case"t":e[0]="T",e[1]+=h,e[2]+=l;case"T":"Q"===r?(s=2*h-s,o=2*l-o):(s=h,o=l),e[0]="Q",h=e[1],l=e[2],e[1]=s,e[2]=o,e[3]=h,e[4]=l;break;case"a":e[0]="A",e[6]+=h,e[7]+=l;case"A":n=!0,d=d.concat(a(h,l,e)),h=e[6],l=e[7];break;case"z":case"Z":h=u,l=f}n||d.push(e),r=e[0]}return d},T.util.getPathSegmentsInfo=g,T.util.fromArcToBeziers=a,T.util.fromArcToBeizers=a,T.util.getBoundsOfCurve=o,T.util.getPointOnPath=function(t,e,i){i||(i=g(t));for(var n=0;e-i[n].length>0&&n<i.length-2;)e-=i[n].length,n++;var r,s=i[n],o=e/s.length,a=s.command,l=t[n];switch(a){case"M":return{x:s.x,y:s.y,angle:0};case"Z":case"z":return(r=new T.Point(s.x,s.y).lerp(new T.Point(s.destX,s.destY),o)).angle=Math.atan2(s.destY-s.y,s.destX-s.x),r;case"L":return(r=new T.Point(s.x,s.y).lerp(new T.Point(l[1],l[2]),o)).angle=Math.atan2(l[2]-s.y,l[1]-s.x),r;case"C":case"Q":return function(t,e){for(var i,n,r,s=0,o=0,a=t.iterator,l={x:t.x,y:t.y},c=.01,u=t.angleFinder;o<e&&s<=1&&c>1e-4;)i=a(s),r=s,(n=h(l.x,l.y,i.x,i.y))+o>e?s-=c/=2:(l=i,s+=c,o+=n);return i.angle=u(r),i}(s,e)}},T.util.getBoundsOfArc=function(t,e,i,n,s,a,h,l,c){for(var u,f=0,d=0,g=[],p=r(l-t,c-e,i,n,a,h,s),v=0,m=p.length;v<m;v++)u=o(f,d,p[v][1],p[v][2],p[v][3],p[v][4],p[v][5],p[v][6]),g.push({x:u[0].x+t,y:u[0].y+e}),g.push({x:u[1].x+t,y:u[1].y+e}),f=p[v][5],d=p[v][6];return g},T.util.drawArc=function(t,e,i,n){a(e,i,n=n.slice(0).unshift("X")).forEach((function(e){t.bezierCurveTo.apply(t,e.slice(1))}))}}(),function(){var t=Array.prototype.slice;function e(t,e,i){if(t&&0!==t.length){var n=t.length-1,r=e?t[n][e]:t[n];if(e)for(;n--;)i(t[n][e],r)&&(r=t[n][e]);else for(;n--;)i(t[n],r)&&(r=t[n]);return r}}T.util.array={fill:function(t,e){for(var i=t.length;i--;)t[i]=e;return t},invoke:function(e,i){for(var n=t.call(arguments,2),r=[],s=0,o=e.length;s<o;s++)r[s]=n.length?e[s][i].apply(e[s],n):e[s][i].call(e[s]);return r},min:function(t,i){return e(t,i,(function(t,e){return t<e}))},max:function(t,i){return e(t,i,(function(t,e){return t>=e}))}}}(),function(){function t(e,i,n){if(n)if(!T.isLikelyNode&&i instanceof Element)e=i;else if(i instanceof Array){e=[];for(var r=0,s=i.length;r<s;r++)e[r]=t({},i[r],n)}else if(i&&"object"==typeof i)for(var o in i)"canvas"===o||"group"===o?e[o]=null:i.hasOwnProperty(o)&&(e[o]=t({},i[o],n));else e=i;else for(var o in i)e[o]=i[o];return e}T.util.object={extend:t,clone:function(e,i){return t({},e,i)}},T.util.object.extend(T.util,T.Observable)}(),function(){function t(t,e){var i=t.charCodeAt(e);if(isNaN(i))return"";if(i<55296||i>57343)return t.charAt(e);if(55296<=i&&i<=56319){if(t.length<=e+1)throw"High surrogate without following low surrogate";var n=t.charCodeAt(e+1);if(56320>n||n>57343)throw"High surrogate without following low surrogate";return t.charAt(e)+t.charAt(e+1)}if(0===e)throw"Low surrogate without preceding high surrogate";var r=t.charCodeAt(e-1);if(55296>r||r>56319)throw"Low surrogate without preceding high surrogate";return!1}T.util.string={camelize:function(t){return t.replace(/-+(.)?/g,(function(t,e){return e?e.toUpperCase():""}))},capitalize:function(t,e){return t.charAt(0).toUpperCase()+(e?t.slice(1):t.slice(1).toLowerCase())},escapeXml:function(t){return t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")},graphemeSplit:function(e){var i,n=0,r=[];for(n=0;n<e.length;n++)!1!==(i=t(e,n))&&r.push(i);return r}}}(),function(){var t=Array.prototype.slice,e=function(){},i=function(){for(var t in{toString:1})if("toString"===t)return!1;return!0}(),n=function(t,e,n){for(var r in e)r in t.prototype&&"function"==typeof t.prototype[r]&&(e[r]+"").indexOf("callSuper")>-1?t.prototype[r]=function(t){return function(){var i=this.constructor.superclass;this.constructor.superclass=n;var r=e[t].apply(this,arguments);if(this.constructor.superclass=i,"initialize"!==t)return r}}(r):t.prototype[r]=e[r],i&&(e.toString!==Object.prototype.toString&&(t.prototype.toString=e.toString),e.valueOf!==Object.prototype.valueOf&&(t.prototype.valueOf=e.valueOf))};function r(){}function s(e){for(var i=null,n=this;n.constructor.superclass;){var r=n.constructor.superclass.prototype[e];if(n[e]!==r){i=r;break}n=n.constructor.superclass.prototype}return i?arguments.length>1?i.apply(this,t.call(arguments,1)):i.call(this):console.log("tried to callSuper "+e+", method not found in prototype chain",this)}T.util.createClass=function(){var i=null,o=t.call(arguments,0);function a(){this.initialize.apply(this,arguments)}"function"==typeof o[0]&&(i=o.shift()),a.superclass=i,a.subclasses=[],i&&(r.prototype=i.prototype,a.prototype=new r,i.subclasses.push(a));for(var h=0,l=o.length;h<l;h++)n(a,o[h],i);return a.prototype.initialize||(a.prototype.initialize=e),a.prototype.constructor=a,a.prototype.callSuper=s,a}}(),l=!!T.document.createElement("div").attachEvent,c=["touchstart","touchmove","touchend"],T.util.addListener=function(t,e,i,n){t&&t.addEventListener(e,i,!l&&n)},T.util.removeListener=function(t,e,i,n){t&&t.removeEventListener(e,i,!l&&n)},T.util.getPointer=function(t){var e=t.target,i=T.util.getScrollLeftTop(e),n=function(t){var e=t.changedTouches;return e&&e[0]?e[0]:t}(t);return{x:n.clientX+i.left,y:n.clientY+i.top}},T.util.isTouchEvent=function(t){return c.indexOf(t.type)>-1||"touch"===t.pointerType},f="string"==typeof(u=T.document.createElement("div")).style.opacity,d="string"==typeof u.style.filter,g=/alpha\s*\(\s*opacity\s*=\s*([^\)]+)\)/,p=function(t){return t},f?p=function(t,e){return t.style.opacity=e,t}:d&&(p=function(t,e){var i=t.style;return t.currentStyle&&!t.currentStyle.hasLayout&&(i.zoom=1),g.test(i.filter)?(e=e>=.9999?"":"alpha(opacity="+100*e+")",i.filter=i.filter.replace(g,e)):i.filter+=" alpha(opacity="+100*e+")",t}),T.util.setStyle=function(t,e){var i=t.style;if(!i)return t;if("string"==typeof e)return t.style.cssText+=";"+e,e.indexOf("opacity")>-1?p(t,e.match(/opacity:\s*(\d?\.?\d*)/)[1]):t;for(var n in e)"opacity"===n?p(t,e[n]):i["float"===n||"cssFloat"===n?void 0===i.styleFloat?"cssFloat":"styleFloat":n]=e[n];return t},function(){var t,e,i,n,r=Array.prototype.slice,s=function(t){return r.call(t,0)};try{t=s(T.document.childNodes)instanceof Array}catch(t){}function o(t,e){var i=T.document.createElement(t);for(var n in e)"class"===n?i.className=e[n]:"for"===n?i.htmlFor=e[n]:i.setAttribute(n,e[n]);return i}function a(t){for(var e=0,i=0,n=T.document.documentElement,r=T.document.body||{scrollLeft:0,scrollTop:0};t&&(t.parentNode||t.host)&&((t=t.parentNode||t.host)===T.document?(e=r.scrollLeft||n.scrollLeft||0,i=r.scrollTop||n.scrollTop||0):(e+=t.scrollLeft||0,i+=t.scrollTop||0),1!==t.nodeType||"fixed"!==t.style.position););return{left:e,top:i}}t||(s=function(t){for(var e=new Array(t.length),i=t.length;i--;)e[i]=t[i];return e}),e=T.document.defaultView&&T.document.defaultView.getComputedStyle?function(t,e){var i=T.document.defaultView.getComputedStyle(t,null);return i?i[e]:void 0}:function(t,e){var i=t.style[e];return!i&&t.currentStyle&&(i=t.currentStyle[e]),i},i=T.document.documentElement.style,n="userSelect"in i?"userSelect":"MozUserSelect"in i?"MozUserSelect":"WebkitUserSelect"in i?"WebkitUserSelect":"KhtmlUserSelect"in i?"KhtmlUserSelect":"",T.util.makeElementUnselectable=function(t){return void 0!==t.onselectstart&&(t.onselectstart=T.util.falseFunction),n?t.style[n]="none":"string"==typeof t.unselectable&&(t.unselectable="on"),t},T.util.makeElementSelectable=function(t){return void 0!==t.onselectstart&&(t.onselectstart=null),n?t.style[n]="":"string"==typeof t.unselectable&&(t.unselectable=""),t},T.util.setImageSmoothing=function(t,e){t.imageSmoothingEnabled=t.imageSmoothingEnabled||t.webkitImageSmoothingEnabled||t.mozImageSmoothingEnabled||t.msImageSmoothingEnabled||t.oImageSmoothingEnabled,t.imageSmoothingEnabled=e},T.util.getById=function(t){return"string"==typeof t?T.document.getElementById(t):t},T.util.toArray=s,T.util.addClass=function(t,e){t&&-1===(" "+t.className+" ").indexOf(" "+e+" ")&&(t.className+=(t.className?" ":"")+e)},T.util.makeElement=o,T.util.wrapElement=function(t,e,i){return"string"==typeof e&&(e=o(e,i)),t.parentNode&&t.parentNode.replaceChild(e,t),e.appendChild(t),e},T.util.getScrollLeftTop=a,T.util.getElementOffset=function(t){var i,n,r=t&&t.ownerDocument,s={left:0,top:0},o={left:0,top:0},h={borderLeftWidth:"left",borderTopWidth:"top",paddingLeft:"left",paddingTop:"top"};if(!r)return o;for(var l in h)o[h[l]]+=parseInt(e(t,l),10)||0;return i=r.documentElement,void 0!==t.getBoundingClientRect&&(s=t.getBoundingClientRect()),n=a(t),{left:s.left+n.left-(i.clientLeft||0)+o.left,top:s.top+n.top-(i.clientTop||0)+o.top}},T.util.getNodeCanvas=function(t){var e=T.jsdomImplForWrapper(t);return e._canvas||e._image},T.util.cleanUpJsdomNode=function(t){if(T.isLikelyNode){var e=T.jsdomImplForWrapper(t);e&&(e._image=null,e._canvas=null,e._currentSrc=null,e._attributes=null,e._classList=null)}}}(),function(){function t(){}T.util.request=function(e,i){i||(i={});var n=i.method?i.method.toUpperCase():"GET",r=i.onComplete||function(){},s=new T.window.XMLHttpRequest,o=i.body||i.parameters;return s.onreadystatechange=function(){4===s.readyState&&(r(s),s.onreadystatechange=t)},"GET"===n&&(o=null,"string"==typeof i.parameters&&(e=function(t,e){return t+(/\?/.test(t)?"&":"?")+e}(e,i.parameters))),s.open(n,e,!0),"POST"!==n&&"PUT"!==n||s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.send(o),s}}(),T.log=console.log,T.warn=console.warn,function(){function t(){return!1}function e(t,e,i,n){return-i*Math.cos(t/n*(Math.PI/2))+i+e}var i=T.window.requestAnimationFrame||T.window.webkitRequestAnimationFrame||T.window.mozRequestAnimationFrame||T.window.oRequestAnimationFrame||T.window.msRequestAnimationFrame||function(t){return T.window.setTimeout(t,1e3/60)},n=T.window.cancelAnimationFrame||T.window.clearTimeout;function r(){return i.apply(T.window,arguments)}T.util.animate=function(i){r((function(n){i||(i={});var s,o=n||+new Date,a=i.duration||500,h=o+a,l=i.onChange||t,c=i.abort||t,u=i.onComplete||t,f=i.easing||e,d="startValue"in i?i.startValue:0,g="endValue"in i?i.endValue:100,p=i.byValue||g-d;i.onStart&&i.onStart(),function t(e){var i=(s=e||+new Date)>h?a:s-o,n=i/a,v=f(i,d,p,a),m=Math.abs((v-d)/p);if(!c())return s>h?(l(g,1,1),void u(g,1,1)):(l(v,m,n),void r(t));u(g,1,1)}(o)}))},T.util.requestAnimFrame=r,T.util.cancelAnimFrame=function(){return n.apply(T.window,arguments)}}(),function(){function t(t,e,i){var n="rgba("+parseInt(t[0]+i*(e[0]-t[0]),10)+","+parseInt(t[1]+i*(e[1]-t[1]),10)+","+parseInt(t[2]+i*(e[2]-t[2]),10);return(n+=","+(t&&e?parseFloat(t[3]+i*(e[3]-t[3])):1))+")"}T.util.animateColor=function(e,i,n,r){var s=new T.Color(e).getSource(),o=new T.Color(i).getSource(),a=r.onComplete,h=r.onChange;r=r||{},T.util.animate(T.util.object.extend(r,{duration:n||500,startValue:s,endValue:o,byValue:o,easing:function(e,i,n,s){return t(i,n,r.colorEasing?r.colorEasing(e,s):1-Math.cos(e/s*(Math.PI/2)))},onComplete:function(e,i,n){if(a)return a(t(o,o,0),i,n)},onChange:function(e,i,n){if(h){if(Array.isArray(e))return h(t(e,e,0),i,n);h(e,i,n)}}}))}}(),function(){function t(t,e,i,n){return t<Math.abs(e)?(t=e,n=i/4):n=0===e&&0===t?i/(2*Math.PI)*Math.asin(1):i/(2*Math.PI)*Math.asin(e/t),{a:t,c:e,p:i,s:n}}function e(t,e,i){return t.a*Math.pow(2,10*(e-=1))*Math.sin((e*i-t.s)*(2*Math.PI)/t.p)}function i(t,e,i,r){return i-n(r-t,0,i,r)+e}function n(t,e,i,n){return(t/=n)<1/2.75?i*(7.5625*t*t)+e:t<2/2.75?i*(7.5625*(t-=1.5/2.75)*t+.75)+e:t<2.5/2.75?i*(7.5625*(t-=2.25/2.75)*t+.9375)+e:i*(7.5625*(t-=2.625/2.75)*t+.984375)+e}T.util.ease={easeInQuad:function(t,e,i,n){return i*(t/=n)*t+e},easeOutQuad:function(t,e,i,n){return-i*(t/=n)*(t-2)+e},easeInOutQuad:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t+e:-i/2*(--t*(t-2)-1)+e},easeInCubic:function(t,e,i,n){return i*(t/=n)*t*t+e},easeOutCubic:function(t,e,i,n){return i*((t=t/n-1)*t*t+1)+e},easeInOutCubic:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t+e:i/2*((t-=2)*t*t+2)+e},easeInQuart:function(t,e,i,n){return i*(t/=n)*t*t*t+e},easeOutQuart:function(t,e,i,n){return-i*((t=t/n-1)*t*t*t-1)+e},easeInOutQuart:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t*t+e:-i/2*((t-=2)*t*t*t-2)+e},easeInQuint:function(t,e,i,n){return i*(t/=n)*t*t*t*t+e},easeOutQuint:function(t,e,i,n){return i*((t=t/n-1)*t*t*t*t+1)+e},easeInOutQuint:function(t,e,i,n){return(t/=n/2)<1?i/2*t*t*t*t*t+e:i/2*((t-=2)*t*t*t*t+2)+e},easeInSine:function(t,e,i,n){return-i*Math.cos(t/n*(Math.PI/2))+i+e},easeOutSine:function(t,e,i,n){return i*Math.sin(t/n*(Math.PI/2))+e},easeInOutSine:function(t,e,i,n){return-i/2*(Math.cos(Math.PI*t/n)-1)+e},easeInExpo:function(t,e,i,n){return 0===t?e:i*Math.pow(2,10*(t/n-1))+e},easeOutExpo:function(t,e,i,n){return t===n?e+i:i*(1-Math.pow(2,-10*t/n))+e},easeInOutExpo:function(t,e,i,n){return 0===t?e:t===n?e+i:(t/=n/2)<1?i/2*Math.pow(2,10*(t-1))+e:i/2*(2-Math.pow(2,-10*--t))+e},easeInCirc:function(t,e,i,n){return-i*(Math.sqrt(1-(t/=n)*t)-1)+e},easeOutCirc:function(t,e,i,n){return i*Math.sqrt(1-(t=t/n-1)*t)+e},easeInOutCirc:function(t,e,i,n){return(t/=n/2)<1?-i/2*(Math.sqrt(1-t*t)-1)+e:i/2*(Math.sqrt(1-(t-=2)*t)+1)+e},easeInElastic:function(i,n,r,s){var o=0;return 0===i?n:1==(i/=s)?n+r:(o||(o=.3*s),-e(t(r,r,o,1.70158),i,s)+n)},easeOutElastic:function(e,i,n,r){var s=0;if(0===e)return i;if(1==(e/=r))return i+n;s||(s=.3*r);var o=t(n,n,s,1.70158);return o.a*Math.pow(2,-10*e)*Math.sin((e*r-o.s)*(2*Math.PI)/o.p)+o.c+i},easeInOutElastic:function(i,n,r,s){var o=0;if(0===i)return n;if(2==(i/=s/2))return n+r;o||(o=s*(.3*1.5));var a=t(r,r,o,1.70158);return i<1?-.5*e(a,i,s)+n:a.a*Math.pow(2,-10*(i-=1))*Math.sin((i*s-a.s)*(2*Math.PI)/a.p)*.5+a.c+n},easeInBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),i*(t/=n)*t*((r+1)*t-r)+e},easeOutBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),i*((t=t/n-1)*t*((r+1)*t+r)+1)+e},easeInOutBack:function(t,e,i,n,r){return void 0===r&&(r=1.70158),(t/=n/2)<1?i/2*(t*t*((1+(r*=1.525))*t-r))+e:i/2*((t-=2)*t*((1+(r*=1.525))*t+r)+2)+e},easeInBounce:i,easeOutBounce:n,easeInOutBounce:function(t,e,r,s){return t<s/2?.5*i(2*t,0,r,s)+e:.5*n(2*t-s,0,r,s)+.5*r+e}}}(),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r=e.util.toFixed,s=e.util.parseUnit,o=e.util.multiplyTransformMatrices,a={cx:"left",x:"left",r:"radius",cy:"top",y:"top",display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing"},h={stroke:"strokeOpacity",fill:"fillOpacity"},l="font-size",c="clip-path";function u(t){return t in a?a[t]:t}function f(t,i,n,r){var a,h="[object Array]"===Object.prototype.toString.call(i);if("fill"!==t&&"stroke"!==t||"none"!==i){if("strokeUniform"===t)return"non-scaling-stroke"===i;if("strokeDashArray"===t)i="none"===i?null:i.replace(/,/g," ").split(/\s+/).map(parseFloat);else if("transformMatrix"===t)i=n&&n.transformMatrix?o(n.transformMatrix,e.parseTransformAttribute(i)):e.parseTransformAttribute(i);else if("visible"===t)i="none"!==i&&"hidden"!==i,n&&!1===n.visible&&(i=!1);else if("opacity"===t)i=parseFloat(i),n&&void 0!==n.opacity&&(i*=n.opacity);else if("textAnchor"===t)i="start"===i?"left":"end"===i?"right":"center";else if("charSpacing"===t)a=s(i,r)/r*1e3;else if("paintFirst"===t){var l=i.indexOf("fill"),c=i.indexOf("stroke");i="fill",(l>-1&&c>-1&&c<l||-1===l&&c>-1)&&(i="stroke")}else{if("href"===t||"xlink:href"===t||"font"===t)return i;if("imageSmoothing"===t)return"optimizeQuality"===i;a=h?i.map(s):s(i,r)}}else i="";return!h&&isNaN(a)?i:a}function d(t){return new RegExp("^("+t.join("|")+")\\b","i")}function g(t,e){var i,n,r,s,o=[];for(r=0,s=e.length;r<s;r++)i=e[r],n=t.getElementsByTagName(i),o=o.concat(Array.prototype.slice.call(n));return o}function p(t,e){var i,n=!0;return(i=v(t,e.pop()))&&e.length&&(n=function(t,e){for(var i,n=!0;t.parentNode&&1===t.parentNode.nodeType&&e.length;)n&&(i=e.pop()),n=v(t=t.parentNode,i);return 0===e.length}(t,e)),i&&n&&0===e.length}function v(t,e){var i,n,r=t.nodeName,s=t.getAttribute("class"),o=t.getAttribute("id");if(i=new RegExp("^"+r,"i"),e=e.replace(i,""),o&&e.length&&(i=new RegExp("#"+o+"(?![a-zA-Z\\-]+)","i"),e=e.replace(i,"")),s&&e.length)for(n=(s=s.split(" ")).length;n--;)i=new RegExp("\\."+s[n]+"(?![a-zA-Z\\-]+)","i"),e=e.replace(i,"");return 0===e.length}function m(t,e){var i;if(t.getElementById&&(i=t.getElementById(e)),i)return i;var n,r,s,o=t.getElementsByTagName("*");for(r=0,s=o.length;r<s;r++)if(e===(n=o[r]).getAttribute("id"))return n}e.svgValidTagNamesRegEx=d(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]),e.svgViewBoxElementsRegEx=d(["symbol","image","marker","pattern","view","svg"]),e.svgInvalidAncestorsRegEx=d(["pattern","defs","symbol","metadata","clipPath","mask","desc"]),e.svgValidParentsRegEx=d(["symbol","g","a","svg","clipPath","defs"]),e.cssRules={},e.gradientDefs={},e.clipPaths={},e.parseTransformAttribute=function(){function t(t,i,n){t[n]=Math.tan(e.util.degreesToRadians(i[0]))}var i=e.iMatrix,n=e.reNum,r=e.commaWsp,s="(?:(?:(matrix)\\s*\\(\\s*("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")"+r+"("+n+")\\s*\\))|(?:(translate)\\s*\\(\\s*("+n+")(?:"+r+"("+n+"))?\\s*\\))|(?:(scale)\\s*\\(\\s*("+n+")(?:"+r+"("+n+"))?\\s*\\))|(?:(rotate)\\s*\\(\\s*("+n+")(?:"+r+"("+n+")"+r+"("+n+"))?\\s*\\))|(?:(skewX)\\s*\\(\\s*("+n+")\\s*\\))|(?:(skewY)\\s*\\(\\s*("+n+")\\s*\\)))",o=new RegExp("^\\s*(?:(?:"+s+"(?:"+r+"*"+s+")*)?)\\s*$"),a=new RegExp(s,"g");return function(n){var r=i.concat(),h=[];if(!n||n&&!o.test(n))return r;n.replace(a,(function(n){var o=new RegExp(s).exec(n).filter((function(t){return!!t})),a=o[1],l=o.slice(2).map(parseFloat);switch(a){case"translate":!function(t,e){t[4]=e[0],2===e.length&&(t[5]=e[1])}(r,l);break;case"rotate":l[0]=e.util.degreesToRadians(l[0]),function(t,i){var n=e.util.cos(i[0]),r=e.util.sin(i[0]),s=0,o=0;3===i.length&&(s=i[1],o=i[2]),t[0]=n,t[1]=r,t[2]=-r,t[3]=n,t[4]=s-(n*s-r*o),t[5]=o-(r*s+n*o)}(r,l);break;case"scale":!function(t,e){var i=e[0],n=2===e.length?e[1]:e[0];t[0]=i,t[3]=n}(r,l);break;case"skewX":t(r,l,2);break;case"skewY":t(r,l,1);break;case"matrix":r=l}h.push(r.concat()),r=i.concat()}));for(var l=h[0];h.length>1;)h.shift(),l=e.util.multiplyTransformMatrices(l,h[0]);return l}}();var y=new RegExp("^\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*,?\\s*("+e.reNum+"+)\\s*$");function _(t){if(!e.svgViewBoxElementsRegEx.test(t.nodeName))return{};var i,n,r,o,a,h,l=t.getAttribute("viewBox"),c=1,u=1,f=t.getAttribute("width"),d=t.getAttribute("height"),g=t.getAttribute("x")||0,p=t.getAttribute("y")||0,v=t.getAttribute("preserveAspectRatio")||"",m=!l||!(l=l.match(y)),_=!f||!d||"100%"===f||"100%"===d,x=m&&_,b={},C="",S=0,w=0;if(b.width=0,b.height=0,b.toBeParsed=x,m&&(g||p)&&t.parentNode&&"#document"!==t.parentNode.nodeName&&(C=" translate("+s(g)+" "+s(p)+") ",a=(t.getAttribute("transform")||"")+C,t.setAttribute("transform",a),t.removeAttribute("x"),t.removeAttribute("y")),x)return b;if(m)return b.width=s(f),b.height=s(d),b;if(i=-parseFloat(l[1]),n=-parseFloat(l[2]),r=parseFloat(l[3]),o=parseFloat(l[4]),b.minX=i,b.minY=n,b.viewBoxWidth=r,b.viewBoxHeight=o,_?(b.width=r,b.height=o):(b.width=s(f),b.height=s(d),c=b.width/r,u=b.height/o),"none"!==(v=e.util.parsePreserveAspectRatioAttribute(v)).alignX&&("meet"===v.meetOrSlice&&(u=c=c>u?u:c),"slice"===v.meetOrSlice&&(u=c=c>u?c:u),S=b.width-r*c,w=b.height-o*c,"Mid"===v.alignX&&(S/=2),"Mid"===v.alignY&&(w/=2),"Min"===v.alignX&&(S=0),"Min"===v.alignY&&(w=0)),1===c&&1===u&&0===i&&0===n&&0===g&&0===p)return b;if((g||p)&&"#document"!==t.parentNode.nodeName&&(C=" translate("+s(g)+" "+s(p)+") "),a=C+" matrix("+c+" 0 0 "+u+" "+(i*c+S)+" "+(n*u+w)+") ","svg"===t.nodeName){for(h=t.ownerDocument.createElementNS(e.svgNS,"g");t.firstChild;)h.appendChild(t.firstChild);t.appendChild(h)}else(h=t).removeAttribute("x"),h.removeAttribute("y"),a=h.getAttribute("transform")+a;return h.setAttribute("transform",a),b}function x(t,e){var i="xlink:href",n=m(t,e.getAttribute(i).substr(1));if(n&&n.getAttribute(i)&&x(t,n),["gradientTransform","x1","x2","y1","y2","gradientUnits","cx","cy","r","fx","fy"].forEach((function(t){n&&!e.hasAttribute(t)&&n.hasAttribute(t)&&e.setAttribute(t,n.getAttribute(t))})),!e.children.length)for(var r=n.cloneNode(!0);r.firstChild;)e.appendChild(r.firstChild);e.removeAttribute(i)}e.parseSVGDocument=function(t,i,r,s){if(t){!function(t){for(var i=g(t,["use","svg:use"]),n=0;i.length&&n<i.length;){var r=i[n],s=r.getAttribute("xlink:href")||r.getAttribute("href");if(null===s)return;var o,a,h,l,c=s.substr(1),u=r.getAttribute("x")||0,f=r.getAttribute("y")||0,d=m(t,c).cloneNode(!0),p=(d.getAttribute("transform")||"")+" translate("+u+", "+f+")",v=i.length,y=e.svgNS;if(_(d),/^svg$/i.test(d.nodeName)){var x=d.ownerDocument.createElementNS(y,"g");for(a=0,l=(h=d.attributes).length;a<l;a++)o=h.item(a),x.setAttributeNS(y,o.nodeName,o.nodeValue);for(;d.firstChild;)x.appendChild(d.firstChild);d=x}for(a=0,l=(h=r.attributes).length;a<l;a++)"x"!==(o=h.item(a)).nodeName&&"y"!==o.nodeName&&"xlink:href"!==o.nodeName&&"href"!==o.nodeName&&("transform"===o.nodeName?p=o.nodeValue+" "+p:d.setAttribute(o.nodeName,o.nodeValue));d.setAttribute("transform",p),d.setAttribute("instantiated_by_use","1"),d.removeAttribute("id"),r.parentNode.replaceChild(d,r),i.length===v&&n++}}(t);var o,a,h=e.Object.__uid++,l=_(t),c=e.util.toArray(t.getElementsByTagName("*"));if(l.crossOrigin=s&&s.crossOrigin,l.svgUid=h,0===c.length&&e.isLikelyNode){var u=[];for(o=0,a=(c=t.selectNodes('//*[name(.)!="svg"]')).length;o<a;o++)u[o]=c[o];c=u}var f=c.filter((function(t){return _(t),e.svgValidTagNamesRegEx.test(t.nodeName.replace("svg:",""))&&!function(t,e){for(;t&&(t=t.parentNode);)if(t.nodeName&&e.test(t.nodeName.replace("svg:",""))&&!t.getAttribute("instantiated_by_use"))return!0;return!1}(t,e.svgInvalidAncestorsRegEx)}));if(!f||f&&!f.length)i&&i([],{});else{var d={};c.filter((function(t){return"clipPath"===t.nodeName.replace("svg:","")})).forEach((function(t){var i=t.getAttribute("id");d[i]=e.util.toArray(t.getElementsByTagName("*")).filter((function(t){return e.svgValidTagNamesRegEx.test(t.nodeName.replace("svg:",""))}))})),e.gradientDefs[h]=e.getGradientDefs(t),e.cssRules[h]=e.getCSSRules(t),e.clipPaths[h]=d,e.parseElements(f,(function(t,n){i&&(i(t,l,n,c),delete e.gradientDefs[h],delete e.cssRules[h],delete e.clipPaths[h])}),n(l),r,s)}}};var b=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+e.reNum+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+e.reNum+"))?\\s+(.*)");i(e,{parseFontDeclaration:function(t,e){var i=t.match(b);if(i){var n=i[1],r=i[3],o=i[4],a=i[5],h=i[6];n&&(e.fontStyle=n),r&&(e.fontWeight=isNaN(parseFloat(r))?r:parseFloat(r)),o&&(e.fontSize=s(o)),h&&(e.fontFamily=h),a&&(e.lineHeight="normal"===a?1:a)}},getGradientDefs:function(t){var e,i=g(t,["linearGradient","radialGradient","svg:linearGradient","svg:radialGradient"]),n=0,r={};for(n=i.length;n--;)(e=i[n]).getAttribute("xlink:href")&&x(t,e),r[e.getAttribute("id")]=e;return r},parseAttributes:function(t,n,o){if(t){var a,d,g,v={};void 0===o&&(o=t.getAttribute("svgUid")),t.parentNode&&e.svgValidParentsRegEx.test(t.parentNode.nodeName)&&(v=e.parseAttributes(t.parentNode,n,o));var m=n.reduce((function(e,i){return(a=t.getAttribute(i))&&(e[i]=a),e}),{}),y=i(function(t,i){var n={};for(var r in e.cssRules[i])if(p(t,r.split(" ")))for(var s in e.cssRules[i][r])n[s]=e.cssRules[i][r][s];return n}(t,o),e.parseStyleAttribute(t));m=i(m,y),y[c]&&t.setAttribute(c,y[c]),d=g=v.fontSize||e.Text.DEFAULT_SVG_FONT_SIZE,m[l]&&(m[l]=d=s(m[l],g));var _,x,b={};for(var C in m)x=f(_=u(C),m[C],v,d),b[_]=x;b&&b.font&&e.parseFontDeclaration(b.font,b);var S=i(v,b);return e.svgValidParentsRegEx.test(t.nodeName)?S:function(t){for(var i in h)if(void 0!==t[h[i]]&&""!==t[i]){if(void 0===t[i]){if(!e.Object.prototype[i])continue;t[i]=e.Object.prototype[i]}if(0!==t[i].indexOf("url(")){var n=new e.Color(t[i]);t[i]=n.setAlpha(r(n.getAlpha()*t[h[i]],2)).toRgba()}}return t}(S)}},parseElements:function(t,i,n,r,s){new e.ElementsParser(t,i,n,r,s).parse()},parseStyleAttribute:function(t){var e={},i=t.getAttribute("style");return i?("string"==typeof i?function(t,e){var i,n;t.replace(/;\s*$/,"").split(";").forEach((function(t){var r=t.split(":");i=r[0].trim().toLowerCase(),n=r[1].trim(),e[i]=n}))}(i,e):function(t,e){var i,n;for(var r in t)void 0!==t[r]&&(i=r.toLowerCase(),n=t[r],e[i]=n)}(i,e),e):e},parsePointsAttribute:function(t){if(!t)return null;var e,i,n=[];for(e=0,i=(t=(t=t.replace(/,/g," ").trim()).split(/\s+/)).length;e<i;e+=2)n.push({x:parseFloat(t[e]),y:parseFloat(t[e+1])});return n},getCSSRules:function(t){var i,n,r=t.getElementsByTagName("style"),s={};for(i=0,n=r.length;i<n;i++){var o=r[i].textContent;""!==(o=o.replace(/\/\*[\s\S]*?\*\//g,"")).trim()&&o.match(/[^{]*\{[\s\S]*?\}/g).map((function(t){return t.trim()})).forEach((function(t){var r=t.match(/([\s\S]*?)\s*\{([^}]*)\}/),o={},a=r[2].trim().replace(/;$/,"").split(/\s*;\s*/);for(i=0,n=a.length;i<n;i++){var h=a[i].split(/\s*:\s*/),l=h[0],c=h[1];o[l]=c}(t=r[1]).split(",").forEach((function(t){""!==(t=t.replace(/^svg/i,"").trim())&&(s[t]?e.util.object.extend(s[t],o):s[t]=e.util.object.clone(o))}))}))}return s},loadSVGFromURL:function(t,i,n,r){t=t.replace(/^\n\s*/,"").trim(),new e.util.request(t,{method:"get",onComplete:function(t){var s=t.responseXML;if(!s||!s.documentElement)return i&&i(null),!1;e.parseSVGDocument(s.documentElement,(function(t,e,n,r){i&&i(t,e,n,r)}),n,r)}})},loadSVGFromString:function(t,i,n,r){var s=(new e.window.DOMParser).parseFromString(t.trim(),"text/xml");e.parseSVGDocument(s.documentElement,(function(t,e,n,r){i(t,e,n,r)}),n,r)}})}(e),T.ElementsParser=function(t,e,i,n,r,s){this.elements=t,this.callback=e,this.options=i,this.reviver=n,this.svgUid=i&&i.svgUid||0,this.parsingOptions=r,this.regexUrl=/^url\(['"]?#([^'"]+)['"]?\)/g,this.doc=s},(v=T.ElementsParser.prototype).parse=function(){this.instances=new Array(this.elements.length),this.numElements=this.elements.length,this.createObjects()},v.createObjects=function(){var t=this;this.elements.forEach((function(e,i){e.setAttribute("svgUid",t.svgUid),t.createObject(e,i)}))},v.findTag=function(t){return T[T.util.string.capitalize(t.tagName.replace("svg:",""))]},v.createObject=function(t,e){var i=this.findTag(t);if(i&&i.fromElement)try{i.fromElement(t,this.createCallback(e,t),this.options)}catch(t){T.log(t)}else this.checkIfDone()},v.createCallback=function(t,e){var i=this;return function(n){var r;i.resolveGradient(n,e,"fill"),i.resolveGradient(n,e,"stroke"),n instanceof T.Image&&n._originalElement&&(r=n.parsePreserveAspectRatioAttribute(e)),n._removeTransformMatrix(r),i.resolveClipPath(n,e),i.reviver&&i.reviver(e,n),i.instances[t]=n,i.checkIfDone()}},v.extractPropertyDefinition=function(t,e,i){var n=t[e],r=this.regexUrl;if(r.test(n)){r.lastIndex=0;var s=r.exec(n)[1];return r.lastIndex=0,T[i][this.svgUid][s]}},v.resolveGradient=function(t,e,i){var n=this.extractPropertyDefinition(t,i,"gradientDefs");if(n){var r=e.getAttribute(i+"-opacity"),s=T.Gradient.fromElement(n,t,r,this.options);t.set(i,s)}},v.createClipPathCallback=function(t,e){return function(t){t._removeTransformMatrix(),t.fillRule=t.clipRule,e.push(t)}},v.resolveClipPath=function(t,e){var i,n,r,s,o=this.extractPropertyDefinition(t,"clipPath","clipPaths");if(o){r=[],n=T.util.invertTransform(t.calcTransformMatrix());for(var a=o[0].parentNode,h=e;h.parentNode&&h.getAttribute("clip-path")!==t.clipPath;)h=h.parentNode;h.parentNode.appendChild(a);for(var l=0;l<o.length;l++)i=o[l],this.findTag(i).fromElement(i,this.createClipPathCallback(t,r),this.options);o=1===r.length?r[0]:new T.Group(r),s=T.util.multiplyTransformMatrices(n,o.calcTransformMatrix()),o.clipPath&&this.resolveClipPath(o,h);var c=T.util.qrDecompose(s);o.flipX=!1,o.flipY=!1,o.set("scaleX",c.scaleX),o.set("scaleY",c.scaleY),o.angle=c.angle,o.skewX=c.skewX,o.skewY=0,o.setPositionByOrigin({x:c.translateX,y:c.translateY},"center","center"),t.clipPath=o}else delete t.clipPath},v.checkIfDone=function(){0==--this.numElements&&(this.instances=this.instances.filter((function(t){return null!=t})),this.callback(this.instances,this.elements))},function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t,e){this.x=t,this.y=e}e.Point?e.warn("fabric.Point is already defined"):(e.Point=i,i.prototype={type:"point",constructor:i,add:function(t){return new i(this.x+t.x,this.y+t.y)},addEquals:function(t){return this.x+=t.x,this.y+=t.y,this},scalarAdd:function(t){return new i(this.x+t,this.y+t)},scalarAddEquals:function(t){return this.x+=t,this.y+=t,this},subtract:function(t){return new i(this.x-t.x,this.y-t.y)},subtractEquals:function(t){return this.x-=t.x,this.y-=t.y,this},scalarSubtract:function(t){return new i(this.x-t,this.y-t)},scalarSubtractEquals:function(t){return this.x-=t,this.y-=t,this},multiply:function(t){return new i(this.x*t,this.y*t)},multiplyEquals:function(t){return this.x*=t,this.y*=t,this},divide:function(t){return new i(this.x/t,this.y/t)},divideEquals:function(t){return this.x/=t,this.y/=t,this},eq:function(t){return this.x===t.x&&this.y===t.y},lt:function(t){return this.x<t.x&&this.y<t.y},lte:function(t){return this.x<=t.x&&this.y<=t.y},gt:function(t){return this.x>t.x&&this.y>t.y},gte:function(t){return this.x>=t.x&&this.y>=t.y},lerp:function(t,e){return void 0===e&&(e=.5),e=Math.max(Math.min(1,e),0),new i(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)},distanceFrom:function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},midPointFrom:function(t){return this.lerp(t)},min:function(t){return new i(Math.min(this.x,t.x),Math.min(this.y,t.y))},max:function(t){return new i(Math.max(this.x,t.x),Math.max(this.y,t.y))},toString:function(){return this.x+","+this.y},setXY:function(t,e){return this.x=t,this.y=e,this},setX:function(t){return this.x=t,this},setY:function(t){return this.y=t,this},setFromPoint:function(t){return this.x=t.x,this.y=t.y,this},swap:function(t){var e=this.x,i=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=i},clone:function(){return new i(this.x,this.y)}})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t){this.status=t,this.points=[]}e.Intersection?e.warn("fabric.Intersection is already defined"):(e.Intersection=i,e.Intersection.prototype={constructor:i,appendPoint:function(t){return this.points.push(t),this},appendPoints:function(t){return this.points=this.points.concat(t),this}},e.Intersection.intersectLineLine=function(t,n,r,s){var o,a=(s.x-r.x)*(t.y-r.y)-(s.y-r.y)*(t.x-r.x),h=(n.x-t.x)*(t.y-r.y)-(n.y-t.y)*(t.x-r.x),l=(s.y-r.y)*(n.x-t.x)-(s.x-r.x)*(n.y-t.y);if(0!==l){var c=a/l,u=h/l;0<=c&&c<=1&&0<=u&&u<=1?(o=new i("Intersection")).appendPoint(new e.Point(t.x+c*(n.x-t.x),t.y+c*(n.y-t.y))):o=new i}else o=new i(0===a||0===h?"Coincident":"Parallel");return o},e.Intersection.intersectLinePolygon=function(t,e,n){var r,s,o,a,h=new i,l=n.length;for(a=0;a<l;a++)r=n[a],s=n[(a+1)%l],o=i.intersectLineLine(t,e,r,s),h.appendPoints(o.points);return h.points.length>0&&(h.status="Intersection"),h},e.Intersection.intersectPolygonPolygon=function(t,e){var n,r=new i,s=t.length;for(n=0;n<s;n++){var o=t[n],a=t[(n+1)%s],h=i.intersectLinePolygon(o,a,e);r.appendPoints(h.points)}return r.points.length>0&&(r.status="Intersection"),r},e.Intersection.intersectPolygonRectangle=function(t,n,r){var s=n.min(r),o=n.max(r),a=new e.Point(o.x,s.y),h=new e.Point(s.x,o.y),l=i.intersectLinePolygon(s,a,t),c=i.intersectLinePolygon(a,o,t),u=i.intersectLinePolygon(o,h,t),f=i.intersectLinePolygon(h,s,t),d=new i;return d.appendPoints(l.points),d.appendPoints(c.points),d.appendPoints(u.points),d.appendPoints(f.points),d.points.length>0&&(d.status="Intersection"),d})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});function i(t){t?this._tryParsingColor(t):this.setSource([0,0,0,1])}function n(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}e.Color?e.warn("fabric.Color is already defined."):(e.Color=i,e.Color.prototype={_tryParsingColor:function(t){var e;t in i.colorNameMap&&(t=i.colorNameMap[t]),"transparent"===t&&(e=[255,255,255,0]),e||(e=i.sourceFromHex(t)),e||(e=i.sourceFromRgb(t)),e||(e=i.sourceFromHsl(t)),e||(e=[0,0,0,1]),e&&this.setSource(e)},_rgbToHsl:function(t,i,n){t/=255,i/=255,n/=255;var r,s,o,a=e.util.array.max([t,i,n]),h=e.util.array.min([t,i,n]);if(o=(a+h)/2,a===h)r=s=0;else{var l=a-h;switch(s=o>.5?l/(2-a-h):l/(a+h),a){case t:r=(i-n)/l+(i<n?6:0);break;case i:r=(n-t)/l+2;break;case n:r=(t-i)/l+4}r/=6}return[Math.round(360*r),Math.round(100*s),Math.round(100*o)]},getSource:function(){return this._source},setSource:function(t){this._source=t},toRgb:function(){var t=this.getSource();return"rgb("+t[0]+","+t[1]+","+t[2]+")"},toRgba:function(){var t=this.getSource();return"rgba("+t[0]+","+t[1]+","+t[2]+","+t[3]+")"},toHsl:function(){var t=this.getSource(),e=this._rgbToHsl(t[0],t[1],t[2]);return"hsl("+e[0]+","+e[1]+"%,"+e[2]+"%)"},toHsla:function(){var t=this.getSource(),e=this._rgbToHsl(t[0],t[1],t[2]);return"hsla("+e[0]+","+e[1]+"%,"+e[2]+"%,"+t[3]+")"},toHex:function(){var t,e,i,n=this.getSource();return t=1===(t=n[0].toString(16)).length?"0"+t:t,e=1===(e=n[1].toString(16)).length?"0"+e:e,i=1===(i=n[2].toString(16)).length?"0"+i:i,t.toUpperCase()+e.toUpperCase()+i.toUpperCase()},toHexa:function(){var t,e=this.getSource();return t=1===(t=(t=Math.round(255*e[3])).toString(16)).length?"0"+t:t,this.toHex()+t.toUpperCase()},getAlpha:function(){return this.getSource()[3]},setAlpha:function(t){var e=this.getSource();return e[3]=t,this.setSource(e),this},toGrayscale:function(){var t=this.getSource(),e=parseInt((.3*t[0]+.59*t[1]+.11*t[2]).toFixed(0),10),i=t[3];return this.setSource([e,e,e,i]),this},toBlackWhite:function(t){var e=this.getSource(),i=(.3*e[0]+.59*e[1]+.11*e[2]).toFixed(0),n=e[3];return t=t||127,i=Number(i)<Number(t)?0:255,this.setSource([i,i,i,n]),this},overlayWith:function(t){t instanceof i||(t=new i(t));var e,n=[],r=this.getAlpha(),s=this.getSource(),o=t.getSource();for(e=0;e<3;e++)n.push(Math.round(.5*s[e]+.5*o[e]));return n[3]=r,this.setSource(n),this}},e.Color.reRGBa=/^rgba?\(\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*,\s*(\d{1,3}(?:\.\d+)?\%?)\s*(?:\s*,\s*((?:\d*\.?\d+)?)\s*)?\)$/i,e.Color.reHSLa=/^hsla?\(\s*(\d{1,3})\s*,\s*(\d{1,3}\%)\s*,\s*(\d{1,3}\%)\s*(?:\s*,\s*(\d+(?:\.\d+)?)\s*)?\)$/i,e.Color.reHex=/^#?([0-9a-f]{8}|[0-9a-f]{6}|[0-9a-f]{4}|[0-9a-f]{3})$/i,e.Color.colorNameMap={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#00FFFF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000000",blanchedalmond:"#FFEBCD",blue:"#0000FF",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#00FFFF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#FF00FF",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#00FF00",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#FF00FF",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#663399",red:"#FF0000",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFFFFF",whitesmoke:"#F5F5F5",yellow:"#FFFF00",yellowgreen:"#9ACD32"},e.Color.fromRgb=function(t){return i.fromSource(i.sourceFromRgb(t))},e.Color.sourceFromRgb=function(t){var e=t.match(i.reRGBa);if(e){var n=parseInt(e[1],10)/(/%$/.test(e[1])?100:1)*(/%$/.test(e[1])?255:1),r=parseInt(e[2],10)/(/%$/.test(e[2])?100:1)*(/%$/.test(e[2])?255:1),s=parseInt(e[3],10)/(/%$/.test(e[3])?100:1)*(/%$/.test(e[3])?255:1);return[parseInt(n,10),parseInt(r,10),parseInt(s,10),e[4]?parseFloat(e[4]):1]}},e.Color.fromRgba=i.fromRgb,e.Color.fromHsl=function(t){return i.fromSource(i.sourceFromHsl(t))},e.Color.sourceFromHsl=function(t){var e=t.match(i.reHSLa);if(e){var r,s,o,a=(parseFloat(e[1])%360+360)%360/360,h=parseFloat(e[2])/(/%$/.test(e[2])?100:1),l=parseFloat(e[3])/(/%$/.test(e[3])?100:1);if(0===h)r=s=o=l;else{var c=l<=.5?l*(h+1):l+h-l*h,u=2*l-c;r=n(u,c,a+1/3),s=n(u,c,a),o=n(u,c,a-1/3)}return[Math.round(255*r),Math.round(255*s),Math.round(255*o),e[4]?parseFloat(e[4]):1]}},e.Color.fromHsla=i.fromHsl,e.Color.fromHex=function(t){return i.fromSource(i.sourceFromHex(t))},e.Color.sourceFromHex=function(t){if(t.match(i.reHex)){var e=t.slice(t.indexOf("#")+1),n=3===e.length||4===e.length,r=8===e.length||4===e.length,s=n?e.charAt(0)+e.charAt(0):e.substring(0,2),o=n?e.charAt(1)+e.charAt(1):e.substring(2,4),a=n?e.charAt(2)+e.charAt(2):e.substring(4,6),h=r?n?e.charAt(3)+e.charAt(3):e.substring(6,8):"FF";return[parseInt(s,16),parseInt(o,16),parseInt(a,16),parseFloat((parseInt(h,16)/255).toFixed(2))]}},e.Color.fromSource=function(t){var e=new i;return e.setSource(t),e})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=["e","se","s","sw","w","nw","n","ne","e"],n=["ns","nesw","ew","nwse"],r={},s="left",o="top",a="right",h="bottom",l="center",c={top:h,bottom:o,left:a,right:s,center:l},u=e.util.radiansToDegrees,f=Math.sign||function(t){return(t>0)-(t<0)||+t};function d(t,e){var i=t.angle+u(Math.atan2(e.y,e.x))+360;return Math.round(i%360/45)}function g(t,i){var n=i.transform.target,r=n.canvas,s=e.util.object.clone(i);s.target=n,r&&r.fire("object:"+t,s),n.fire(t,i)}function p(t,e){var i=e.canvas,n=t[i.uniScaleKey];return i.uniformScaling&&!n||!i.uniformScaling&&n}function v(t){return t.originX===l&&t.originY===l}function m(t,e,i){var n=t.lockScalingX,r=t.lockScalingY;return!((!n||!r)&&(e||!n&&!r||!i)&&(!n||"x"!==e)&&(!r||"y"!==e))}function y(t,e,i,n){return{e:t,transform:e,pointer:{x:i,y:n}}}function _(t){return function(e,i,n,r){var s=i.target,o=s.getCenterPoint(),a=s.translateToOriginPoint(o,i.originX,i.originY),h=t(e,i,n,r);return s.setPositionByOrigin(a,i.originX,i.originY),h}}function x(t,e){return function(i,n,r,s){var o=e(i,n,r,s);return o&&g(t,y(i,n,r,s)),o}}function b(t,i,n,r,s){var o=t.target,a=o.controls[t.corner],h=o.canvas.getZoom(),l=o.padding/h,c=o.toLocalPoint(new e.Point(r,s),i,n);return c.x>=l&&(c.x-=l),c.x<=-l&&(c.x+=l),c.y>=l&&(c.y-=l),c.y<=l&&(c.y+=l),c.x-=a.offsetX,c.y-=a.offsetY,c}function C(t){return t.flipX!==t.flipY}function S(t,e,i,n,r){if(0!==t[e]){var s=r/t._getTransformedDimensions()[n]*t[i];t.set(i,s)}}function w(t,e,i,n){var r,l=e.target,c=l._getTransformedDimensions(0,l.skewY),f=b(e,e.originX,e.originY,i,n),d=Math.abs(2*f.x)-c.x,g=l.skewX;d<2?r=0:(r=u(Math.atan2(d/l.scaleX,c.y/l.scaleY)),e.originX===s&&e.originY===h&&(r=-r),e.originX===a&&e.originY===o&&(r=-r),C(l)&&(r=-r));var p=g!==r;if(p){var v=l._getTransformedDimensions().y;l.set("skewX",r),S(l,"skewY","scaleY","y",v)}return p}function T(t,e,i,n){var r,l=e.target,c=l._getTransformedDimensions(l.skewX,0),f=b(e,e.originX,e.originY,i,n),d=Math.abs(2*f.y)-c.y,g=l.skewY;d<2?r=0:(r=u(Math.atan2(d/l.scaleY,c.x/l.scaleX)),e.originX===s&&e.originY===h&&(r=-r),e.originX===a&&e.originY===o&&(r=-r),C(l)&&(r=-r));var p=g!==r;if(p){var v=l._getTransformedDimensions().x;l.set("skewY",r),S(l,"skewX","scaleX","x",v)}return p}function O(t,e,i,n,r){r=r||{};var s,o,a,h,l,u,d=e.target,g=d.lockScalingX,y=d.lockScalingY,_=r.by,x=p(t,d),C=m(d,_,x),S=e.gestureScale;if(C)return!1;if(S)o=e.scaleX*S,a=e.scaleY*S;else{if(s=b(e,e.originX,e.originY,i,n),l="y"!==_?f(s.x):1,u="x"!==_?f(s.y):1,e.signX||(e.signX=l),e.signY||(e.signY=u),d.lockScalingFlip&&(e.signX!==l||e.signY!==u))return!1;if(h=d._getTransformedDimensions(),x&&!_){var w=Math.abs(s.x)+Math.abs(s.y),T=e.original,O=w/(Math.abs(h.x*T.scaleX/d.scaleX)+Math.abs(h.y*T.scaleY/d.scaleY));o=T.scaleX*O,a=T.scaleY*O}else o=Math.abs(s.x*d.scaleX/h.x),a=Math.abs(s.y*d.scaleY/h.y);v(e)&&(o*=2,a*=2),e.signX!==l&&"y"!==_&&(e.originX=c[e.originX],o*=-1,e.signX=l),e.signY!==u&&"x"!==_&&(e.originY=c[e.originY],a*=-1,e.signY=u)}var k=d.scaleX,P=d.scaleY;return _?("x"===_&&d.set("scaleX",o),"y"===_&&d.set("scaleY",a)):(!g&&d.set("scaleX",o),!y&&d.set("scaleY",a)),k!==d.scaleX||P!==d.scaleY}r.scaleCursorStyleHandler=function(t,e,n){var r=p(t,n),s="";if(0!==e.x&&0===e.y?s="x":0===e.x&&0!==e.y&&(s="y"),m(n,s,r))return"not-allowed";var o=d(n,e);return i[o]+"-resize"},r.skewCursorStyleHandler=function(t,e,i){var r="not-allowed";if(0!==e.x&&i.lockSkewingY)return r;if(0!==e.y&&i.lockSkewingX)return r;var s=d(i,e)%4;return n[s]+"-resize"},r.scaleSkewCursorStyleHandler=function(t,e,i){return t[i.canvas.altActionKey]?r.skewCursorStyleHandler(t,e,i):r.scaleCursorStyleHandler(t,e,i)},r.rotationWithSnapping=x("rotating",_((function(t,e,i,n){var r=e,s=r.target,o=s.translateToOriginPoint(s.getCenterPoint(),r.originX,r.originY);if(s.lockRotation)return!1;var a,h=Math.atan2(r.ey-o.y,r.ex-o.x),l=Math.atan2(n-o.y,i-o.x),c=u(l-h+r.theta);if(s.snapAngle>0){var f=s.snapAngle,d=s.snapThreshold||f,g=Math.ceil(c/f)*f,p=Math.floor(c/f)*f;Math.abs(c-p)<d?c=p:Math.abs(c-g)<d&&(c=g)}return c<0&&(c=360+c),c%=360,a=s.angle!==c,s.angle=c,a}))),r.scalingEqually=x("scaling",_((function(t,e,i,n){return O(t,e,i,n)}))),r.scalingX=x("scaling",_((function(t,e,i,n){return O(t,e,i,n,{by:"x"})}))),r.scalingY=x("scaling",_((function(t,e,i,n){return O(t,e,i,n,{by:"y"})}))),r.scalingYOrSkewingX=function(t,e,i,n){return t[e.target.canvas.altActionKey]?r.skewHandlerX(t,e,i,n):r.scalingY(t,e,i,n)},r.scalingXOrSkewingY=function(t,e,i,n){return t[e.target.canvas.altActionKey]?r.skewHandlerY(t,e,i,n):r.scalingX(t,e,i,n)},r.changeWidth=x("resizing",_((function(t,e,i,n){var r=e.target,s=b(e,e.originX,e.originY,i,n),o=r.strokeWidth/(r.strokeUniform?r.scaleX:1),a=v(e)?2:1,h=r.width,l=Math.abs(s.x*a/r.scaleX)-o;return r.set("width",Math.max(l,0)),h!==l}))),r.skewHandlerX=function(t,e,i,n){var r,h=e.target,c=h.skewX,u=e.originY;return!h.lockSkewingX&&(0===c?r=b(e,l,l,i,n).x>0?s:a:(c>0&&(r=u===o?s:a),c<0&&(r=u===o?a:s),C(h)&&(r=r===s?a:s)),e.originX=r,x("skewing",_(w))(t,e,i,n))},r.skewHandlerY=function(t,e,i,n){var r,a=e.target,c=a.skewY,u=e.originX;return!a.lockSkewingY&&(0===c?r=b(e,l,l,i,n).y>0?o:h:(c>0&&(r=u===s?o:h),c<0&&(r=u===s?h:o),C(a)&&(r=r===o?h:o)),e.originY=r,x("skewing",_(T))(t,e,i,n))},r.dragHandler=function(t,e,i,n){var r=e.target,s=i-e.offsetX,o=n-e.offsetY,a=!r.get("lockMovementX")&&r.left!==s,h=!r.get("lockMovementY")&&r.top!==o;return a&&r.set("left",s),h&&r.set("top",o),(a||h)&&g("moving",y(t,e,i,n)),a||h},r.scaleOrSkewActionName=function(t,e,i){var n=t[i.canvas.altActionKey];return 0===e.x?n?"skewX":"scaleY":0===e.y?n?"skewY":"scaleX":void 0},r.rotationStyleHandler=function(t,e,i){return i.lockRotation?"not-allowed":e.cursorStyle},r.fireEvent=g,r.wrapWithFixedAnchor=_,r.wrapWithFireEvent=x,r.getLocalPoint=b,e.controlsUtils=r}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.degreesToRadians,n=e.controlsUtils;n.renderCircleControl=function(t,e,i,n,r){n=n||{};var s,o=this.sizeX||n.cornerSize||r.cornerSize,a=this.sizeY||n.cornerSize||r.cornerSize,h=void 0!==n.transparentCorners?n.transparentCorners:r.transparentCorners,l=h?"stroke":"fill",c=!h&&(n.cornerStrokeColor||r.cornerStrokeColor),u=e,f=i;t.save(),t.fillStyle=n.cornerColor||r.cornerColor,t.strokeStyle=n.cornerStrokeColor||r.cornerStrokeColor,o>a?(s=o,t.scale(1,a/o),f=i*o/a):a>o?(s=a,t.scale(o/a,1),u=e*a/o):s=o,t.lineWidth=1,t.beginPath(),t.arc(u,f,s/2,0,2*Math.PI,!1),t[l](),c&&t.stroke(),t.restore()},n.renderSquareControl=function(t,e,n,r,s){r=r||{};var o=this.sizeX||r.cornerSize||s.cornerSize,a=this.sizeY||r.cornerSize||s.cornerSize,h=void 0!==r.transparentCorners?r.transparentCorners:s.transparentCorners,l=h?"stroke":"fill",c=!h&&(r.cornerStrokeColor||s.cornerStrokeColor),u=o/2,f=a/2;t.save(),t.fillStyle=r.cornerColor||s.cornerColor,t.strokeStyle=r.cornerStrokeColor||s.cornerStrokeColor,t.lineWidth=1,t.translate(e,n),t.rotate(i(s.angle)),t[l+"Rect"](-u,-f,o,a),c&&t.strokeRect(-u,-f,o,a),t.restore()}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Control=function(t){for(var e in t)this[e]=t[e]},e.Control.prototype={visible:!0,actionName:"scale",angle:0,x:0,y:0,offsetX:0,offsetY:0,sizeX:null,sizeY:null,touchSizeX:null,touchSizeY:null,cursorStyle:"crosshair",withConnection:!1,actionHandler:function(){},mouseDownHandler:function(){},mouseUpHandler:function(){},getActionHandler:function(){return this.actionHandler},getMouseDownHandler:function(){return this.mouseDownHandler},getMouseUpHandler:function(){return this.mouseUpHandler},cursorStyleHandler:function(t,e){return e.cursorStyle},getActionName:function(t,e){return e.actionName},getVisibility:function(t,e){var i=t._controlsVisibility;return i&&void 0!==i[e]?i[e]:this.visible},setVisibility:function(t){this.visible=t},positionHandler:function(t,i){return e.util.transformPoint({x:this.x*t.x+this.offsetX,y:this.y*t.y+this.offsetY},i)},calcCornerCoords:function(t,i,n,r,s){var o,a,h,l,c=s?this.touchSizeX:this.sizeX,u=s?this.touchSizeY:this.sizeY;if(c&&u&&c!==u){var f=Math.atan2(u,c),d=Math.sqrt(c*c+u*u)/2,g=f-e.util.degreesToRadians(t),p=Math.PI/2-f-e.util.degreesToRadians(t);o=d*e.util.cos(g),a=d*e.util.sin(g),h=d*e.util.cos(p),l=d*e.util.sin(p)}else d=.7071067812*(c&&u?c:i),g=e.util.degreesToRadians(45-t),o=h=d*e.util.cos(g),a=l=d*e.util.sin(g);return{tl:{x:n-l,y:r-h},tr:{x:n+o,y:r-a},bl:{x:n-o,y:r+a},br:{x:n+l,y:r+h}}},render:function(t,i,n,r,s){"circle"===((r=r||{}).cornerStyle||s.cornerStyle)?e.controlsUtils.renderCircleControl.call(this,t,i,n,r,s):e.controlsUtils.renderSquareControl.call(this,t,i,n,r,s)}}}(e),function(){function t(t,e){var i,n,r,s,o=t.getAttribute("style"),a=t.getAttribute("offset")||0;if(a=(a=parseFloat(a)/(/%$/.test(a)?100:1))<0?0:a>1?1:a,o){var h=o.split(/\s*;\s*/);for(""===h[h.length-1]&&h.pop(),s=h.length;s--;){var l=h[s].split(/\s*:\s*/),c=l[0].trim(),u=l[1].trim();"stop-color"===c?i=u:"stop-opacity"===c&&(r=u)}}return i||(i=t.getAttribute("stop-color")||"rgb(0,0,0)"),r||(r=t.getAttribute("stop-opacity")),n=(i=new T.Color(i)).getAlpha(),r=isNaN(parseFloat(r))?1:parseFloat(r),r*=n*e,{offset:a,color:i.toRgb(),opacity:r}}var e=T.util.object.clone;T.Gradient=T.util.createClass({offsetX:0,offsetY:0,gradientTransform:null,gradientUnits:"pixels",type:"linear",initialize:function(t){t||(t={}),t.coords||(t.coords={});var e,i=this;Object.keys(t).forEach((function(e){i[e]=t[e]})),this.id?this.id+="_"+T.Object.__uid++:this.id=T.Object.__uid++,e={x1:t.coords.x1||0,y1:t.coords.y1||0,x2:t.coords.x2||0,y2:t.coords.y2||0},"radial"===this.type&&(e.r1=t.coords.r1||0,e.r2=t.coords.r2||0),this.coords=e,this.colorStops=t.colorStops.slice()},addColorStop:function(t){for(var e in t){var i=new T.Color(t[e]);this.colorStops.push({offset:parseFloat(e),color:i.toRgb(),opacity:i.getAlpha()})}return this},toObject:function(t){var e={type:this.type,coords:this.coords,colorStops:this.colorStops,offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?this.gradientTransform.concat():this.gradientTransform};return T.util.populateWithProperties(this,e,t),e},toSVG:function(t,i){var n,r,s,o,a=e(this.coords,!0),h=(i=i||{},e(this.colorStops,!0)),l=a.r1>a.r2,c=this.gradientTransform?this.gradientTransform.concat():T.iMatrix.concat(),u=-this.offsetX,f=-this.offsetY,d=!!i.additionalTransform,g="pixels"===this.gradientUnits?"userSpaceOnUse":"objectBoundingBox";if(h.sort((function(t,e){return t.offset-e.offset})),"objectBoundingBox"===g?(u/=t.width,f/=t.height):(u+=t.width/2,f+=t.height/2),"path"===t.type&&"percentage"!==this.gradientUnits&&(u-=t.pathOffset.x,f-=t.pathOffset.y),c[4]-=u,c[5]-=f,o='id="SVGID_'+this.id+'" gradientUnits="'+g+'"',o+=' gradientTransform="'+(d?i.additionalTransform+" ":"")+T.util.matrixToSVG(c)+'" ',"linear"===this.type?s=["<linearGradient ",o,' x1="',a.x1,'" y1="',a.y1,'" x2="',a.x2,'" y2="',a.y2,'">\n']:"radial"===this.type&&(s=["<radialGradient ",o,' cx="',l?a.x1:a.x2,'" cy="',l?a.y1:a.y2,'" r="',l?a.r1:a.r2,'" fx="',l?a.x2:a.x1,'" fy="',l?a.y2:a.y1,'">\n']),"radial"===this.type){if(l)for((h=h.concat()).reverse(),n=0,r=h.length;n<r;n++)h[n].offset=1-h[n].offset;var p=Math.min(a.r1,a.r2);if(p>0){var v=p/Math.max(a.r1,a.r2);for(n=0,r=h.length;n<r;n++)h[n].offset+=v*(1-h[n].offset)}}for(n=0,r=h.length;n<r;n++){var m=h[n];s.push("<stop ",'offset="',100*m.offset+"%",'" style="stop-color:',m.color,void 0!==m.opacity?";stop-opacity: "+m.opacity:";",'"/>\n')}return s.push("linear"===this.type?"</linearGradient>\n":"</radialGradient>\n"),s.join("")},toLive:function(t){var e,i,n,r=T.util.object.clone(this.coords);if(this.type){for("linear"===this.type?e=t.createLinearGradient(r.x1,r.y1,r.x2,r.y2):"radial"===this.type&&(e=t.createRadialGradient(r.x1,r.y1,r.r1,r.x2,r.y2,r.r2)),i=0,n=this.colorStops.length;i<n;i++){var s=this.colorStops[i].color,o=this.colorStops[i].opacity,a=this.colorStops[i].offset;void 0!==o&&(s=new T.Color(s).setAlpha(o).toRgba()),e.addColorStop(a,s)}return e}}}),T.util.object.extend(T.Gradient,{fromElement:function(e,i,n,r){var s=parseFloat(n)/(/%$/.test(n)?100:1);s=s<0?0:s>1?1:s,isNaN(s)&&(s=1);var o,a,h,l,c=e.getElementsByTagName("stop"),u="userSpaceOnUse"===e.getAttribute("gradientUnits")?"pixels":"percentage",f=e.getAttribute("gradientTransform")||"",d=[],g=0,p=0;for("linearGradient"===e.nodeName||"LINEARGRADIENT"===e.nodeName?(o="linear",a=function(t){return{x1:t.getAttribute("x1")||0,y1:t.getAttribute("y1")||0,x2:t.getAttribute("x2")||"100%",y2:t.getAttribute("y2")||0}}(e)):(o="radial",a=function(t){return{x1:t.getAttribute("fx")||t.getAttribute("cx")||"50%",y1:t.getAttribute("fy")||t.getAttribute("cy")||"50%",r1:0,x2:t.getAttribute("cx")||"50%",y2:t.getAttribute("cy")||"50%",r2:t.getAttribute("r")||"50%"}}(e)),h=c.length;h--;)d.push(t(c[h],s));return l=T.parseTransformAttribute(f),function(t,e,i,n){var r,s;Object.keys(e).forEach((function(t){"Infinity"===(r=e[t])?s=1:"-Infinity"===r?s=0:(s=parseFloat(e[t],10),"string"==typeof r&&/^(\d+\.\d+)%|(\d+)%$/.test(r)&&(s*=.01,"pixels"===n&&("x1"!==t&&"x2"!==t&&"r2"!==t||(s*=i.viewBoxWidth||i.width),"y1"!==t&&"y2"!==t||(s*=i.viewBoxHeight||i.height)))),e[t]=s}))}(0,a,r,u),"pixels"===u&&(g=-i.left,p=-i.top),new T.Gradient({id:e.getAttribute("id"),type:o,coords:a,colorStops:d,gradientUnits:u,gradientTransform:l,offsetX:g,offsetY:p})}})}(),function(){"use strict";var t=T.util.toFixed;T.Pattern=T.util.createClass({repeat:"repeat",offsetX:0,offsetY:0,crossOrigin:"",patternTransform:null,initialize:function(t,e){if(t||(t={}),this.id=T.Object.__uid++,this.setOptions(t),!t.source||t.source&&"string"!=typeof t.source)e&&e(this);else{var i=this;this.source=T.util.createImage(),T.util.loadImage(t.source,(function(t,n){i.source=t,e&&e(i,n)}),null,this.crossOrigin)}},toObject:function(e){var i,n,r=T.Object.NUM_FRACTION_DIGITS;return"string"==typeof this.source.src?i=this.source.src:"object"==typeof this.source&&this.source.toDataURL&&(i=this.source.toDataURL()),n={type:"pattern",source:i,repeat:this.repeat,crossOrigin:this.crossOrigin,offsetX:t(this.offsetX,r),offsetY:t(this.offsetY,r),patternTransform:this.patternTransform?this.patternTransform.concat():null},T.util.populateWithProperties(this,n,e),n},toSVG:function(t){var e="function"==typeof this.source?this.source():this.source,i=e.width/t.width,n=e.height/t.height,r=this.offsetX/t.width,s=this.offsetY/t.height,o="";return"repeat-x"!==this.repeat&&"no-repeat"!==this.repeat||(n=1,s&&(n+=Math.abs(s))),"repeat-y"!==this.repeat&&"no-repeat"!==this.repeat||(i=1,r&&(i+=Math.abs(r))),e.src?o=e.src:e.toDataURL&&(o=e.toDataURL()),'<pattern id="SVGID_'+this.id+'" x="'+r+'" y="'+s+'" width="'+i+'" height="'+n+'">\n<image x="0" y="0" width="'+e.width+'" height="'+e.height+'" xlink:href="'+o+'"></image>\n</pattern>\n'},setOptions:function(t){for(var e in t)this[e]=t[e]},toLive:function(t){var e=this.source;if(!e)return"";if(void 0!==e.src){if(!e.complete)return"";if(0===e.naturalWidth||0===e.naturalHeight)return""}return t.createPattern(e,this.repeat)}})}(),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.toFixed;e.Shadow?e.warn("fabric.Shadow is already defined."):(e.Shadow=e.util.createClass({color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1,initialize:function(t){for(var i in"string"==typeof t&&(t=this._parseShadow(t)),t)this[i]=t[i];this.id=e.Object.__uid++},_parseShadow:function(t){var i=t.trim(),n=e.Shadow.reOffsetsAndBlur.exec(i)||[];return{color:(i.replace(e.Shadow.reOffsetsAndBlur,"")||"rgb(0,0,0)").trim(),offsetX:parseFloat(n[1],10)||0,offsetY:parseFloat(n[2],10)||0,blur:parseFloat(n[3],10)||0}},toString:function(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")},toSVG:function(t){var n=40,r=40,s=e.Object.NUM_FRACTION_DIGITS,o=e.util.rotateVector({x:this.offsetX,y:this.offsetY},e.util.degreesToRadians(-t.angle)),a=new e.Color(this.color);return t.width&&t.height&&(n=100*i((Math.abs(o.x)+this.blur)/t.width,s)+20,r=100*i((Math.abs(o.y)+this.blur)/t.height,s)+20),t.flipX&&(o.x*=-1),t.flipY&&(o.y*=-1),'<filter id="SVGID_'+this.id+'" y="-'+r+'%" height="'+(100+2*r)+'%" x="-'+n+'%" width="'+(100+2*n)+'%" >\n\t<feGaussianBlur in="SourceAlpha" stdDeviation="'+i(this.blur?this.blur/2:0,s)+'"></feGaussianBlur>\n\t<feOffset dx="'+i(o.x,s)+'" dy="'+i(o.y,s)+'" result="oBlur" ></feOffset>\n\t<feFlood flood-color="'+a.toRgb()+'" flood-opacity="'+a.getAlpha()+'"/>\n\t<feComposite in2="oBlur" operator="in" />\n\t<feMerge>\n\t\t<feMergeNode></feMergeNode>\n\t\t<feMergeNode in="SourceGraphic"></feMergeNode>\n\t</feMerge>\n</filter>\n'},toObject:function(){if(this.includeDefaultValues)return{color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling};var t={},i=e.Shadow.prototype;return["color","blur","offsetX","offsetY","affectStroke","nonScaling"].forEach((function(e){this[e]!==i[e]&&(t[e]=this[e])}),this),t}}),e.Shadow.reOffsetsAndBlur=/(?:\s|^)(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(-?\d+(?:\.\d*)?(?:px)?(?:\s?|$))?(\d+(?:\.\d*)?(?:px)?)?(?:\s?|$)(?:$|\s)/)}(e),function(){"use strict";if(T.StaticCanvas)T.warn("fabric.StaticCanvas is already defined.");else{var t=T.util.object.extend,e=T.util.getElementOffset,i=T.util.removeFromArray,n=T.util.toFixed,r=T.util.transformPoint,s=T.util.invertTransform,o=T.util.getNodeCanvas,a=T.util.createCanvasElement,h=new Error("Could not initialize `canvas` element");T.StaticCanvas=T.util.createClass(T.CommonMethods,{initialize:function(t,e){e||(e={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(t,e)},backgroundColor:"",backgroundImage:null,overlayColor:"",overlayImage:null,includeDefaultValues:!0,stateful:!1,renderOnAddRemove:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,imageSmoothingEnabled:!0,viewportTransform:T.iMatrix.concat(),backgroundVpt:!0,overlayVpt:!0,enableRetinaScaling:!0,vptCoords:{},skipOffscreen:!0,clipPath:void 0,_initStatic:function(t,e){var i=this.requestRenderAllBound;this._objects=[],this._createLowerCanvas(t),this._initOptions(e),this.interactive||this._initRetinaScaling(),e.overlayImage&&this.setOverlayImage(e.overlayImage,i),e.backgroundImage&&this.setBackgroundImage(e.backgroundImage,i),e.backgroundColor&&this.setBackgroundColor(e.backgroundColor,i),e.overlayColor&&this.setOverlayColor(e.overlayColor,i),this.calcOffset()},_isRetinaScaling:function(){return 1!==T.devicePixelRatio&&this.enableRetinaScaling},getRetinaScaling:function(){return this._isRetinaScaling()?T.devicePixelRatio:1},_initRetinaScaling:function(){if(this._isRetinaScaling()){var t=T.devicePixelRatio;this.__initRetinaScaling(t,this.lowerCanvasEl,this.contextContainer),this.upperCanvasEl&&this.__initRetinaScaling(t,this.upperCanvasEl,this.contextTop)}},__initRetinaScaling:function(t,e,i){e.setAttribute("width",this.width*t),e.setAttribute("height",this.height*t),i.scale(t,t)},calcOffset:function(){return this._offset=e(this.lowerCanvasEl),this},setOverlayImage:function(t,e,i){return this.__setBgOverlayImage("overlayImage",t,e,i)},setBackgroundImage:function(t,e,i){return this.__setBgOverlayImage("backgroundImage",t,e,i)},setOverlayColor:function(t,e){return this.__setBgOverlayColor("overlayColor",t,e)},setBackgroundColor:function(t,e){return this.__setBgOverlayColor("backgroundColor",t,e)},__setBgOverlayImage:function(t,e,i,n){return"string"==typeof e?T.util.loadImage(e,(function(e,r){if(e){var s=new T.Image(e,n);this[t]=s,s.canvas=this}i&&i(e,r)}),this,n&&n.crossOrigin):(n&&e.setOptions(n),this[t]=e,e&&(e.canvas=this),i&&i(e,!1)),this},__setBgOverlayColor:function(t,e,i){return this[t]=e,this._initGradient(e,t),this._initPattern(e,t,i),this},_createCanvasElement:function(){var t=a();if(!t)throw h;if(t.style||(t.style={}),void 0===t.getContext)throw h;return t},_initOptions:function(t){var e=this.lowerCanvasEl;this._setOptions(t),this.width=this.width||parseInt(e.width,10)||0,this.height=this.height||parseInt(e.height,10)||0,this.lowerCanvasEl.style&&(e.width=this.width,e.height=this.height,e.style.width=this.width+"px",e.style.height=this.height+"px",this.viewportTransform=this.viewportTransform.slice())},_createLowerCanvas:function(t){t&&t.getContext?this.lowerCanvasEl=t:this.lowerCanvasEl=T.util.getById(t)||this._createCanvasElement(),T.util.addClass(this.lowerCanvasEl,"lower-canvas"),this.interactive&&this._applyCanvasStyle(this.lowerCanvasEl),this.contextContainer=this.lowerCanvasEl.getContext("2d")},getWidth:function(){return this.width},getHeight:function(){return this.height},setWidth:function(t,e){return this.setDimensions({width:t},e)},setHeight:function(t,e){return this.setDimensions({height:t},e)},setDimensions:function(t,e){var i;for(var n in e=e||{},t)i=t[n],e.cssOnly||(this._setBackstoreDimension(n,t[n]),i+="px",this.hasLostContext=!0),e.backstoreOnly||this._setCssDimension(n,i);return this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(),this._initRetinaScaling(),this.calcOffset(),e.cssOnly||this.requestRenderAll(),this},_setBackstoreDimension:function(t,e){return this.lowerCanvasEl[t]=e,this.upperCanvasEl&&(this.upperCanvasEl[t]=e),this.cacheCanvasEl&&(this.cacheCanvasEl[t]=e),this[t]=e,this},_setCssDimension:function(t,e){return this.lowerCanvasEl.style[t]=e,this.upperCanvasEl&&(this.upperCanvasEl.style[t]=e),this.wrapperEl&&(this.wrapperEl.style[t]=e),this},getZoom:function(){return this.viewportTransform[0]},setViewportTransform:function(t){var e,i,n,r=this._activeObject,s=this.backgroundImage,o=this.overlayImage;for(this.viewportTransform=t,i=0,n=this._objects.length;i<n;i++)(e=this._objects[i]).group||e.setCoords(!0);return r&&r.setCoords(),s&&s.setCoords(!0),o&&o.setCoords(!0),this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll(),this},zoomToPoint:function(t,e){var i=t,n=this.viewportTransform.slice(0);t=r(t,s(this.viewportTransform)),n[0]=e,n[3]=e;var o=r(t,n);return n[4]+=i.x-o.x,n[5]+=i.y-o.y,this.setViewportTransform(n)},setZoom:function(t){return this.zoomToPoint(new T.Point(0,0),t),this},absolutePan:function(t){var e=this.viewportTransform.slice(0);return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)},relativePan:function(t){return this.absolutePan(new T.Point(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))},getElement:function(){return this.lowerCanvasEl},_onObjectAdded:function(t){this.stateful&&t.setupState(),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added")},_onObjectRemoved:function(t){this.fire("object:removed",{target:t}),t.fire("removed"),delete t.canvas},clearContext:function(t){return t.clearRect(0,0,this.width,this.height),this},getContext:function(){return this.contextContainer},clear:function(){return this._objects.length=0,this.backgroundImage=null,this.overlayImage=null,this.backgroundColor="",this.overlayColor="",this._hasITextHandlers&&(this.off("mouse:up",this._mouseUpITextHandler),this._iTextInstances=null,this._hasITextHandlers=!1),this.clearContext(this.contextContainer),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll(),this},renderAll:function(){var t=this.contextContainer;return this.renderCanvas(t,this._objects),this},renderAndReset:function(){this.isRendering=0,this.renderAll()},requestRenderAll:function(){return this.isRendering||(this.isRendering=T.util.requestAnimFrame(this.renderAndResetBound)),this},calcViewportBoundaries:function(){var t={},e=this.width,i=this.height,n=s(this.viewportTransform);return t.tl=r({x:0,y:0},n),t.br=r({x:e,y:i},n),t.tr=new T.Point(t.br.x,t.tl.y),t.bl=new T.Point(t.tl.x,t.br.y),this.vptCoords=t,t},cancelRequestedRender:function(){this.isRendering&&(T.util.cancelAnimFrame(this.isRendering),this.isRendering=0)},renderCanvas:function(t,e){var i=this.viewportTransform,n=this.clipPath;this.cancelRequestedRender(),this.calcViewportBoundaries(),this.clearContext(t),T.util.setImageSmoothing(t,this.imageSmoothingEnabled),this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this._renderObjects(t,e),t.restore(),!this.controlsAboveOverlay&&this.interactive&&this.drawControls(t),n&&(n.canvas=this,n.shouldCache(),n._transformDone=!0,n.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t)),this._renderOverlay(t),this.controlsAboveOverlay&&this.interactive&&this.drawControls(t),this.fire("after:render",{ctx:t})},drawClipPathOnCanvas:function(t){var e=this.viewportTransform,i=this.clipPath;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t.globalCompositeOperation="destination-in",i.transform(t),t.scale(1/i.zoomX,1/i.zoomY),t.drawImage(i._cacheCanvas,-i.cacheTranslationX,-i.cacheTranslationY),t.restore()},_renderObjects:function(t,e){var i,n;for(i=0,n=e.length;i<n;++i)e[i]&&e[i].render(t)},_renderBackgroundOrOverlay:function(t,e){var i=this[e+"Color"],n=this[e+"Image"],r=this.viewportTransform,s=this[e+"Vpt"];if(i||n){if(i){t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=i.toLive?i.toLive(t,this):i,s&&t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),t.transform(1,0,0,1,i.offsetX||0,i.offsetY||0);var o=i.gradientTransform||i.patternTransform;o&&t.transform(o[0],o[1],o[2],o[3],o[4],o[5]),t.fill(),t.restore()}n&&(t.save(),s&&t.transform(r[0],r[1],r[2],r[3],r[4],r[5]),n.render(t),t.restore())}},_renderBackground:function(t){this._renderBackgroundOrOverlay(t,"background")},_renderOverlay:function(t){this._renderBackgroundOrOverlay(t,"overlay")},getCenter:function(){return{top:this.height/2,left:this.width/2}},centerObjectH:function(t){return this._centerObject(t,new T.Point(this.getCenter().left,t.getCenterPoint().y))},centerObjectV:function(t){return this._centerObject(t,new T.Point(t.getCenterPoint().x,this.getCenter().top))},centerObject:function(t){var e=this.getCenter();return this._centerObject(t,new T.Point(e.left,e.top))},viewportCenterObject:function(t){var e=this.getVpCenter();return this._centerObject(t,e)},viewportCenterObjectH:function(t){var e=this.getVpCenter();return this._centerObject(t,new T.Point(e.x,t.getCenterPoint().y)),this},viewportCenterObjectV:function(t){var e=this.getVpCenter();return this._centerObject(t,new T.Point(t.getCenterPoint().x,e.y))},getVpCenter:function(){var t=this.getCenter(),e=s(this.viewportTransform);return r({x:t.left,y:t.top},e)},_centerObject:function(t,e){return t.setPositionByOrigin(e,"center","center"),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll(),this},toDatalessJSON:function(t){return this.toDatalessObject(t)},toObject:function(t){return this._toObjectMethod("toObject",t)},toDatalessObject:function(t){return this._toObjectMethod("toDatalessObject",t)},_toObjectMethod:function(e,i){var n=this.clipPath,r={version:T.version,objects:this._toObjects(e,i)};return n&&(r.clipPath=this._toObject(this.clipPath,e,i)),t(r,this.__serializeBgOverlay(e,i)),T.util.populateWithProperties(this,r,i),r},_toObjects:function(t,e){return this._objects.filter((function(t){return!t.excludeFromExport})).map((function(i){return this._toObject(i,t,e)}),this)},_toObject:function(t,e,i){var n;this.includeDefaultValues||(n=t.includeDefaultValues,t.includeDefaultValues=!1);var r=t[e](i);return this.includeDefaultValues||(t.includeDefaultValues=n),r},__serializeBgOverlay:function(t,e){var i={},n=this.backgroundImage,r=this.overlayImage;return this.backgroundColor&&(i.background=this.backgroundColor.toObject?this.backgroundColor.toObject(e):this.backgroundColor),this.overlayColor&&(i.overlay=this.overlayColor.toObject?this.overlayColor.toObject(e):this.overlayColor),n&&!n.excludeFromExport&&(i.backgroundImage=this._toObject(n,t,e)),r&&!r.excludeFromExport&&(i.overlayImage=this._toObject(r,t,e)),i},svgViewportTransformation:!0,toSVG:function(t,e){t||(t={}),t.reviver=e;var i=[];return this._setSVGPreamble(i,t),this._setSVGHeader(i,t),this.clipPath&&i.push('<g clip-path="url(#'+this.clipPath.clipPathId+')" >\n'),this._setSVGBgOverlayColor(i,"background"),this._setSVGBgOverlayImage(i,"backgroundImage",e),this._setSVGObjects(i,e),this.clipPath&&i.push("</g>\n"),this._setSVGBgOverlayColor(i,"overlay"),this._setSVGBgOverlayImage(i,"overlayImage",e),i.push("</svg>"),i.join("")},_setSVGPreamble:function(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",'" standalone="no" ?>\n','<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ','"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n')},_setSVGHeader:function(t,e){var i,r=e.width||this.width,s=e.height||this.height,o='viewBox="0 0 '+this.width+" "+this.height+'" ',a=T.Object.NUM_FRACTION_DIGITS;e.viewBox?o='viewBox="'+e.viewBox.x+" "+e.viewBox.y+" "+e.viewBox.width+" "+e.viewBox.height+'" ':this.svgViewportTransformation&&(i=this.viewportTransform,o='viewBox="'+n(-i[4]/i[0],a)+" "+n(-i[5]/i[3],a)+" "+n(this.width/i[0],a)+" "+n(this.height/i[3],a)+'" '),t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',r,'" ','height="',s,'" ',o,'xml:space="preserve">\n',"<desc>Created with Fabric.js ",T.version,"</desc>\n","<defs>\n",this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),"</defs>\n")},createSVGClipPathMarkup:function(t){var e=this.clipPath;return e?(e.clipPathId="CLIPPATH_"+T.Object.__uid++,'<clipPath id="'+e.clipPathId+'" >\n'+this.clipPath.toClipPathSVG(t.reviver)+"</clipPath>\n"):""},createSVGRefElementsMarkup:function(){var t=this;return["background","overlay"].map((function(e){var i=t[e+"Color"];if(i&&i.toLive){var n=t[e+"Vpt"],r=t.viewportTransform,s={width:t.width/(n?r[0]:1),height:t.height/(n?r[3]:1)};return i.toSVG(s,{additionalTransform:n?T.util.matrixToSVG(r):""})}})).join("")},createSVGFontFacesMarkup:function(){var t,e,i,n,r,s,o,a,h="",l={},c=T.fontPaths,u=[];for(this._objects.forEach((function t(e){u.push(e),e._objects&&e._objects.forEach(t)})),o=0,a=u.length;o<a;o++)if(e=(t=u[o]).fontFamily,-1!==t.type.indexOf("text")&&!l[e]&&c[e]&&(l[e]=!0,t.styles))for(r in i=t.styles)for(s in n=i[r])!l[e=n[s].fontFamily]&&c[e]&&(l[e]=!0);for(var f in l)h+=["\t\t@font-face {\n","\t\t\tfont-family: '",f,"';\n","\t\t\tsrc: url('",c[f],"');\n","\t\t}\n"].join("");return h&&(h=['\t<style type="text/css">',"<![CDATA[\n",h,"]]>","</style>\n"].join("")),h},_setSVGObjects:function(t,e){var i,n,r,s=this._objects;for(n=0,r=s.length;n<r;n++)(i=s[n]).excludeFromExport||this._setSVGObject(t,i,e)},_setSVGObject:function(t,e,i){t.push(e.toSVG(i))},_setSVGBgOverlayImage:function(t,e,i){this[e]&&!this[e].excludeFromExport&&this[e].toSVG&&t.push(this[e].toSVG(i))},_setSVGBgOverlayColor:function(t,e){var i=this[e+"Color"],n=this.viewportTransform,r=this.width,s=this.height;if(i)if(i.toLive){var o=i.repeat,a=T.util.invertTransform(n),h=this[e+"Vpt"]?T.util.matrixToSVG(a):"";t.push('<rect transform="'+h+" translate(",r/2,",",s/2,')"',' x="',i.offsetX-r/2,'" y="',i.offsetY-s/2,'" ','width="',"repeat-y"===o||"no-repeat"===o?i.source.width:r,'" height="',"repeat-x"===o||"no-repeat"===o?i.source.height:s,'" fill="url(#SVGID_'+i.id+')"',"></rect>\n")}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',i,'"',"></rect>\n")},sendToBack:function(t){if(!t)return this;var e,n,r,s=this._activeObject;if(t===s&&"activeSelection"===t.type)for(e=(r=s._objects).length;e--;)n=r[e],i(this._objects,n),this._objects.unshift(n);else i(this._objects,t),this._objects.unshift(t);return this.renderOnAddRemove&&this.requestRenderAll(),this},bringToFront:function(t){if(!t)return this;var e,n,r,s=this._activeObject;if(t===s&&"activeSelection"===t.type)for(r=s._objects,e=0;e<r.length;e++)n=r[e],i(this._objects,n),this._objects.push(n);else i(this._objects,t),this._objects.push(t);return this.renderOnAddRemove&&this.requestRenderAll(),this},sendBackwards:function(t,e){if(!t)return this;var n,r,s,o,a,h=this._activeObject,l=0;if(t===h&&"activeSelection"===t.type)for(a=h._objects,n=0;n<a.length;n++)r=a[n],(s=this._objects.indexOf(r))>0+l&&(o=s-1,i(this._objects,r),this._objects.splice(o,0,r)),l++;else 0!==(s=this._objects.indexOf(t))&&(o=this._findNewLowerIndex(t,s,e),i(this._objects,t),this._objects.splice(o,0,t));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewLowerIndex:function(t,e,i){var n,r;if(i){for(n=e,r=e-1;r>=0;--r)if(t.intersectsWithObject(this._objects[r])||t.isContainedWithinObject(this._objects[r])||this._objects[r].isContainedWithinObject(t)){n=r;break}}else n=e-1;return n},bringForward:function(t,e){if(!t)return this;var n,r,s,o,a,h=this._activeObject,l=0;if(t===h&&"activeSelection"===t.type)for(n=(a=h._objects).length;n--;)r=a[n],(s=this._objects.indexOf(r))<this._objects.length-1-l&&(o=s+1,i(this._objects,r),this._objects.splice(o,0,r)),l++;else(s=this._objects.indexOf(t))!==this._objects.length-1&&(o=this._findNewUpperIndex(t,s,e),i(this._objects,t),this._objects.splice(o,0,t));return this.renderOnAddRemove&&this.requestRenderAll(),this},_findNewUpperIndex:function(t,e,i){var n,r,s;if(i){for(n=e,r=e+1,s=this._objects.length;r<s;++r)if(t.intersectsWithObject(this._objects[r])||t.isContainedWithinObject(this._objects[r])||this._objects[r].isContainedWithinObject(t)){n=r;break}}else n=e+1;return n},moveTo:function(t,e){return i(this._objects,t),this._objects.splice(e,0,t),this.renderOnAddRemove&&this.requestRenderAll()},dispose:function(){return this.isRendering&&(T.util.cancelAnimFrame(this.isRendering),this.isRendering=0),this.forEachObject((function(t){t.dispose&&t.dispose()})),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose&&this.backgroundImage.dispose(),this.backgroundImage=null,this.overlayImage&&this.overlayImage.dispose&&this.overlayImage.dispose(),this.overlayImage=null,this._iTextInstances=null,this.contextContainer=null,T.util.cleanUpJsdomNode(this.lowerCanvasEl),this.lowerCanvasEl=void 0,this},toString:function(){return"#<fabric.Canvas ("+this.complexity()+"): { objects: "+this._objects.length+" }>"}}),t(T.StaticCanvas.prototype,T.Observable),t(T.StaticCanvas.prototype,T.Collection),t(T.StaticCanvas.prototype,T.DataURLExporter),t(T.StaticCanvas,{EMPTY_JSON:'{"objects": [], "background": "white"}',supports:function(t){var e=a();if(!e||!e.getContext)return null;var i=e.getContext("2d");return i&&"setLineDash"===t?void 0!==i.setLineDash:null}}),T.StaticCanvas.prototype.toJSON=T.StaticCanvas.prototype.toObject,T.isLikelyNode&&(T.StaticCanvas.prototype.createPNGStream=function(){var t=o(this.lowerCanvasEl);return t&&t.createPNGStream()},T.StaticCanvas.prototype.createJPEGStream=function(t){var e=o(this.lowerCanvasEl);return e&&e.createJPEGStream(t)})}}(),T.BaseBrush=T.util.createClass({color:"rgb(0, 0, 0)",width:1,shadow:null,strokeLineCap:"round",strokeLineJoin:"round",strokeMiterLimit:10,strokeDashArray:null,limitedToCanvasSize:!1,_setBrushStyles:function(){var t=this.canvas.contextTop;t.strokeStyle=this.color,t.lineWidth=this.width,t.lineCap=this.strokeLineCap,t.miterLimit=this.strokeMiterLimit,t.lineJoin=this.strokeLineJoin,T.StaticCanvas.supports("setLineDash")&&t.setLineDash(this.strokeDashArray||[])},_saveAndTransform:function(t){var e=this.canvas.viewportTransform;t.save(),t.transform(e[0],e[1],e[2],e[3],e[4],e[5])},_setShadow:function(){if(this.shadow){var t=this.canvas,e=this.shadow,i=t.contextTop,n=t.getZoom();t&&t._isRetinaScaling()&&(n*=T.devicePixelRatio),i.shadowColor=e.color,i.shadowBlur=e.blur*n,i.shadowOffsetX=e.offsetX*n,i.shadowOffsetY=e.offsetY*n}},needsFullRender:function(){return new T.Color(this.color).getAlpha()<1||!!this.shadow},_resetShadow:function(){var t=this.canvas.contextTop;t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0},_isOutSideCanvas:function(t){return t.x<0||t.x>this.canvas.getWidth()||t.y<0||t.y>this.canvas.getHeight()}}),T.PencilBrush=T.util.createClass(T.BaseBrush,{decimate:.4,initialize:function(t){this.canvas=t,this._points=[]},_drawSegment:function(t,e,i){var n=e.midPointFrom(i);return t.quadraticCurveTo(e.x,e.y,n.x,n.y),n},onMouseDown:function(t,e){this.canvas._isMainEvent(e.e)&&(this._prepareForDrawing(t),this._captureDrawingPath(t),this._render())},onMouseMove:function(t,e){if(this.canvas._isMainEvent(e.e)&&(!0!==this.limitedToCanvasSize||!this._isOutSideCanvas(t))&&this._captureDrawingPath(t)&&this._points.length>1)if(this.needsFullRender())this.canvas.clearContext(this.canvas.contextTop),this._render();else{var i=this._points,n=i.length,r=this.canvas.contextTop;this._saveAndTransform(r),this.oldEnd&&(r.beginPath(),r.moveTo(this.oldEnd.x,this.oldEnd.y)),this.oldEnd=this._drawSegment(r,i[n-2],i[n-1],!0),r.stroke(),r.restore()}},onMouseUp:function(t){return!this.canvas._isMainEvent(t.e)||(this.oldEnd=void 0,this._finalizeAndAddPath(),!1)},_prepareForDrawing:function(t){var e=new T.Point(t.x,t.y);this._reset(),this._addPoint(e),this.canvas.contextTop.moveTo(e.x,e.y)},_addPoint:function(t){return!(this._points.length>1&&t.eq(this._points[this._points.length-1])||(this._points.push(t),0))},_reset:function(){this._points=[],this._setBrushStyles(),this._setShadow()},_captureDrawingPath:function(t){var e=new T.Point(t.x,t.y);return this._addPoint(e)},_render:function(){var t,e,i=this.canvas.contextTop,n=this._points[0],r=this._points[1];if(this._saveAndTransform(i),i.beginPath(),2===this._points.length&&n.x===r.x&&n.y===r.y){var s=this.width/1e3;n=new T.Point(n.x,n.y),r=new T.Point(r.x,r.y),n.x-=s,r.x+=s}for(i.moveTo(n.x,n.y),t=1,e=this._points.length;t<e;t++)this._drawSegment(i,n,r),n=this._points[t],r=this._points[t+1];i.lineTo(n.x,n.y),i.stroke(),i.restore()},convertPointsToSVGPath:function(t){var e,i=[],n=this.width/1e3,r=new T.Point(t[0].x,t[0].y),s=new T.Point(t[1].x,t[1].y),o=t.length,a=1,h=0,l=o>2;for(l&&(a=t[2].x<s.x?-1:t[2].x===s.x?0:1,h=t[2].y<s.y?-1:t[2].y===s.y?0:1),i.push("M ",r.x-a*n," ",r.y-h*n," "),e=1;e<o;e++){if(!r.eq(s)){var c=r.midPointFrom(s);i.push("Q ",r.x," ",r.y," ",c.x," ",c.y," ")}r=t[e],e+1<t.length&&(s=t[e+1])}return l&&(a=r.x>t[e-2].x?1:r.x===t[e-2].x?0:-1,h=r.y>t[e-2].y?1:r.y===t[e-2].y?0:-1),i.push("L ",r.x+a*n," ",r.y+h*n),i},createPath:function(t){var e=new T.Path(t,{fill:null,stroke:this.color,strokeWidth:this.width,strokeLineCap:this.strokeLineCap,strokeMiterLimit:this.strokeMiterLimit,strokeLineJoin:this.strokeLineJoin,strokeDashArray:this.strokeDashArray});return this.shadow&&(this.shadow.affectStroke=!0,e.shadow=new T.Shadow(this.shadow)),e},decimatePoints:function(t,e){if(t.length<=2)return t;var i,n=this.canvas.getZoom(),r=Math.pow(e/n,2),s=t.length-1,o=t[0],a=[o];for(i=1;i<s-1;i++)Math.pow(o.x-t[i].x,2)+Math.pow(o.y-t[i].y,2)>=r&&(o=t[i],a.push(o));return a.push(t[s]),a},_finalizeAndAddPath:function(){this.canvas.contextTop.closePath(),this.decimate&&(this._points=this.decimatePoints(this._points,this.decimate));var t=this.convertPointsToSVGPath(this._points).join("");if("M 0 0 Q 0 0 0 0 L 0 0"!==t){var e=this.createPath(t);this.canvas.clearContext(this.canvas.contextTop),this.canvas.fire("before:path:created",{path:e}),this.canvas.add(e),this.canvas.requestRenderAll(),e.setCoords(),this._resetShadow(),this.canvas.fire("path:created",{path:e})}else this.canvas.requestRenderAll()}}),T.CircleBrush=T.util.createClass(T.BaseBrush,{width:10,initialize:function(t){this.canvas=t,this.points=[]},drawDot:function(t){var e=this.addPoint(t),i=this.canvas.contextTop;this._saveAndTransform(i),this.dot(i,e),i.restore()},dot:function(t,e){t.fillStyle=e.fill,t.beginPath(),t.arc(e.x,e.y,e.radius,0,2*Math.PI,!1),t.closePath(),t.fill()},onMouseDown:function(t){this.points.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.drawDot(t)},_render:function(){var t,e,i=this.canvas.contextTop,n=this.points;for(this._saveAndTransform(i),t=0,e=n.length;t<e;t++)this.dot(i,n[t]);i.restore()},onMouseMove:function(t){!0===this.limitedToCanvasSize&&this._isOutSideCanvas(t)||(this.needsFullRender()?(this.canvas.clearContext(this.canvas.contextTop),this.addPoint(t),this._render()):this.drawDot(t))},onMouseUp:function(){var t,e,i=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;var n=[];for(t=0,e=this.points.length;t<e;t++){var r=this.points[t],s=new T.Circle({radius:r.radius,left:r.x,top:r.y,originX:"center",originY:"center",fill:r.fill});this.shadow&&(s.shadow=new T.Shadow(this.shadow)),n.push(s)}var o=new T.Group(n);o.canvas=this.canvas,this.canvas.fire("before:path:created",{path:o}),this.canvas.add(o),this.canvas.fire("path:created",{path:o}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=i,this.canvas.requestRenderAll()},addPoint:function(t){var e=new T.Point(t.x,t.y),i=T.util.getRandomInt(Math.max(0,this.width-20),this.width+20)/2,n=new T.Color(this.color).setAlpha(T.util.getRandomInt(0,100)/100).toRgba();return e.radius=i,e.fill=n,this.points.push(e),e}}),T.SprayBrush=T.util.createClass(T.BaseBrush,{width:10,density:20,dotWidth:1,dotWidthVariance:1,randomOpacity:!1,optimizeOverlapping:!0,initialize:function(t){this.canvas=t,this.sprayChunks=[]},onMouseDown:function(t){this.sprayChunks.length=0,this.canvas.clearContext(this.canvas.contextTop),this._setShadow(),this.addSprayChunk(t),this.render(this.sprayChunkPoints)},onMouseMove:function(t){!0===this.limitedToCanvasSize&&this._isOutSideCanvas(t)||(this.addSprayChunk(t),this.render(this.sprayChunkPoints))},onMouseUp:function(){var t=this.canvas.renderOnAddRemove;this.canvas.renderOnAddRemove=!1;for(var e=[],i=0,n=this.sprayChunks.length;i<n;i++)for(var r=this.sprayChunks[i],s=0,o=r.length;s<o;s++){var a=new T.Rect({width:r[s].width,height:r[s].width,left:r[s].x+1,top:r[s].y+1,originX:"center",originY:"center",fill:this.color});e.push(a)}this.optimizeOverlapping&&(e=this._getOptimizedRects(e));var h=new T.Group(e);this.shadow&&h.set("shadow",new T.Shadow(this.shadow)),this.canvas.fire("before:path:created",{path:h}),this.canvas.add(h),this.canvas.fire("path:created",{path:h}),this.canvas.clearContext(this.canvas.contextTop),this._resetShadow(),this.canvas.renderOnAddRemove=t,this.canvas.requestRenderAll()},_getOptimizedRects:function(t){var e,i,n,r={};for(i=0,n=t.length;i<n;i++)r[e=t[i].left+""+t[i].top]||(r[e]=t[i]);var s=[];for(e in r)s.push(r[e]);return s},render:function(t){var e,i,n=this.canvas.contextTop;for(n.fillStyle=this.color,this._saveAndTransform(n),e=0,i=t.length;e<i;e++){var r=t[e];void 0!==r.opacity&&(n.globalAlpha=r.opacity),n.fillRect(r.x,r.y,r.width,r.width)}n.restore()},_render:function(){var t,e,i=this.canvas.contextTop;for(i.fillStyle=this.color,this._saveAndTransform(i),t=0,e=this.sprayChunks.length;t<e;t++)this.render(this.sprayChunks[t]);i.restore()},addSprayChunk:function(t){this.sprayChunkPoints=[];var e,i,n,r,s=this.width/2;for(r=0;r<this.density;r++){e=T.util.getRandomInt(t.x-s,t.x+s),i=T.util.getRandomInt(t.y-s,t.y+s),n=this.dotWidthVariance?T.util.getRandomInt(Math.max(1,this.dotWidth-this.dotWidthVariance),this.dotWidth+this.dotWidthVariance):this.dotWidth;var o=new T.Point(e,i);o.width=n,this.randomOpacity&&(o.opacity=T.util.getRandomInt(0,100)/100),this.sprayChunkPoints.push(o)}this.sprayChunks.push(this.sprayChunkPoints)}}),T.PatternBrush=T.util.createClass(T.PencilBrush,{getPatternSrc:function(){var t=T.util.createCanvasElement(),e=t.getContext("2d");return t.width=t.height=25,e.fillStyle=this.color,e.beginPath(),e.arc(10,10,10,0,2*Math.PI,!1),e.closePath(),e.fill(),t},getPatternSrcFunction:function(){return String(this.getPatternSrc).replace("this.color",'"'+this.color+'"')},getPattern:function(){return this.canvas.contextTop.createPattern(this.source||this.getPatternSrc(),"repeat")},_setBrushStyles:function(){this.callSuper("_setBrushStyles"),this.canvas.contextTop.strokeStyle=this.getPattern()},createPath:function(t){var e=this.callSuper("createPath",t),i=e._getLeftTopCoords().scalarAdd(e.strokeWidth/2);return e.stroke=new T.Pattern({source:this.source||this.getPatternSrcFunction(),offsetX:-i.x,offsetY:-i.y}),e}}),function(){var t=T.util.getPointer,e=T.util.degreesToRadians,i=Math.abs,n=T.StaticCanvas.supports("setLineDash"),r=T.util.isTouchEvent,s=.5;for(var o in T.Canvas=T.util.createClass(T.StaticCanvas,{initialize:function(t,e){e||(e={}),this.renderAndResetBound=this.renderAndReset.bind(this),this.requestRenderAllBound=this.requestRenderAll.bind(this),this._initStatic(t,e),this._initInteractive(),this._createCacheCanvas()},uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",interactive:!0,selection:!0,selectionKey:"shiftKey",altSelectionKey:null,selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",rotationCursor:"crosshair",notAllowedCursor:"not-allowed",containerClass:"canvas-container",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,isDrawingMode:!1,preserveObjectStacking:!1,snapAngle:0,snapThreshold:null,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,targets:[],_hoveredTarget:null,_hoveredTargets:[],_initInteractive:function(){this._currentTransform=null,this._groupSelector=null,this._initWrapperElement(),this._createUpperCanvas(),this._initEventListeners(),this._initRetinaScaling(),this.freeDrawingBrush=T.PencilBrush&&new T.PencilBrush(this),this.calcOffset()},_chooseObjectsToRender:function(){var t,e,i,n=this.getActiveObjects();if(n.length>0&&!this.preserveObjectStacking){e=[],i=[];for(var r=0,s=this._objects.length;r<s;r++)t=this._objects[r],-1===n.indexOf(t)?e.push(t):i.push(t);n.length>1&&(this._activeObject._objects=i),e.push.apply(e,i)}else e=this._objects;return e},renderAll:function(){!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&this.renderTopLayer(this.contextTop);var t=this.contextContainer;return this.renderCanvas(t,this._chooseObjectsToRender()),this},renderTopLayer:function(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()},renderTop:function(){var t=this.contextTop;return this.clearContext(t),this.renderTopLayer(t),this.fire("after:render"),this},_normalizePointer:function(t,e){var i=t.calcTransformMatrix(),n=T.util.invertTransform(i),r=this.restorePointerVpt(e);return T.util.transformPoint(r,n)},isTargetTransparent:function(t,e,i){if(t.shouldCache()&&t._cacheCanvas&&t!==this._activeObject){var n=this._normalizePointer(t,{x:e,y:i}),r=Math.max(t.cacheTranslationX+n.x*t.zoomX,0),s=Math.max(t.cacheTranslationY+n.y*t.zoomY,0);return T.util.isTransparent(t._cacheContext,Math.round(r),Math.round(s),this.targetFindTolerance)}var o=this.contextCache,a=t.selectionBackgroundColor,h=this.viewportTransform;return t.selectionBackgroundColor="",this.clearContext(o),o.save(),o.transform(h[0],h[1],h[2],h[3],h[4],h[5]),t.render(o),o.restore(),t.selectionBackgroundColor=a,T.util.isTransparent(o,e,i,this.targetFindTolerance)},_isSelectionKeyPressed:function(t){return"[object Array]"===Object.prototype.toString.call(this.selectionKey)?!!this.selectionKey.find((function(e){return!0===t[e]})):t[this.selectionKey]},_shouldClearSelection:function(t,e){var i=this.getActiveObjects(),n=this._activeObject;return!e||e&&n&&i.length>1&&-1===i.indexOf(e)&&n!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&n&&n!==e},_shouldCenterTransform:function(t,e,i){var n;if(t)return"scale"===e||"scaleX"===e||"scaleY"===e||"resizing"===e?n=this.centeredScaling||t.centeredScaling:"rotate"===e&&(n=this.centeredRotation||t.centeredRotation),n?!i:i},_getOriginFromCorner:function(t,e){var i={x:t.originX,y:t.originY};return"ml"===e||"tl"===e||"bl"===e?i.x="right":"mr"!==e&&"tr"!==e&&"br"!==e||(i.x="left"),"tl"===e||"mt"===e||"tr"===e?i.y="bottom":"bl"!==e&&"mb"!==e&&"br"!==e||(i.y="top"),i},_getActionFromCorner:function(t,e,i,n){if(!e||!t)return"drag";var r=n.controls[e];return r.getActionName(i,r,n)},_setupCurrentTransform:function(t,i,n){if(i){var r=this.getPointer(t),s=i.__corner,o=i.controls[s],a=n&&s?o.getActionHandler(t,i,o):T.controlsUtils.dragHandler,h=this._getActionFromCorner(n,s,t,i),l=this._getOriginFromCorner(i,s),c=t[this.centeredKey],u={target:i,action:h,actionHandler:a,corner:s,scaleX:i.scaleX,scaleY:i.scaleY,skewX:i.skewX,skewY:i.skewY,offsetX:r.x-i.left,offsetY:r.y-i.top,originX:l.x,originY:l.y,ex:r.x,ey:r.y,lastX:r.x,lastY:r.y,theta:e(i.angle),width:i.width*i.scaleX,shiftKey:t.shiftKey,altKey:c,original:T.util.saveObjectTransform(i)};this._shouldCenterTransform(i,h,c)&&(u.originX="center",u.originY="center"),u.original.originX=l.x,u.original.originY=l.y,this._currentTransform=u,this._beforeTransform(t)}},setCursor:function(t){this.upperCanvasEl.style.cursor=t},_drawSelection:function(t){var e=this._groupSelector,r=e.left,o=e.top,a=i(r),h=i(o);if(this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(e.ex-(r>0?0:-r),e.ey-(o>0?0:-o),a,h)),this.selectionLineWidth&&this.selectionBorderColor)if(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,this.selectionDashArray.length>1&&!n){var l=e.ex+s-(r>0?0:a),c=e.ey+s-(o>0?0:h);t.beginPath(),T.util.drawDashedLine(t,l,c,l+a,c,this.selectionDashArray),T.util.drawDashedLine(t,l,c+h-1,l+a,c+h-1,this.selectionDashArray),T.util.drawDashedLine(t,l,c,l,c+h,this.selectionDashArray),T.util.drawDashedLine(t,l+a-1,c,l+a-1,c+h,this.selectionDashArray),t.closePath(),t.stroke()}else T.Object.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(e.ex+s-(r>0?0:a),e.ey+s-(o>0?0:h),a,h)},findTarget:function(t,e){if(!this.skipTargetFind){var i,n,s=this.getPointer(t,!0),o=this._activeObject,a=this.getActiveObjects(),h=r(t),l=a.length>1&&!e||1===a.length;if(this.targets=[],l&&o._findTargetCorner(s,h))return o;if(a.length>1&&!e&&o===this._searchPossibleTargets([o],s))return o;if(1===a.length&&o===this._searchPossibleTargets([o],s)){if(!this.preserveObjectStacking)return o;i=o,n=this.targets,this.targets=[]}var c=this._searchPossibleTargets(this._objects,s);return t[this.altSelectionKey]&&c&&i&&c!==i&&(c=i,this.targets=n),c}},_checkTarget:function(t,e,i){if(e&&e.visible&&e.evented&&e.containsPoint(t)){if(!this.perPixelTargetFind&&!e.perPixelTargetFind||e.isEditing)return!0;if(!this.isTargetTransparent(e,i.x,i.y))return!0}},_searchPossibleTargets:function(t,e){for(var i,n,r=t.length;r--;){var s=t[r],o=s.group?this._normalizePointer(s.group,e):e;if(this._checkTarget(o,s,e)){(i=t[r]).subTargetCheck&&i instanceof T.Group&&(n=this._searchPossibleTargets(i._objects,e))&&this.targets.push(n);break}}return i},restorePointerVpt:function(t){return T.util.transformPoint(t,T.util.invertTransform(this.viewportTransform))},getPointer:function(e,i){if(this._absolutePointer&&!i)return this._absolutePointer;if(this._pointer&&i)return this._pointer;var n,r=t(e),s=this.upperCanvasEl,o=s.getBoundingClientRect(),a=o.width||0,h=o.height||0;a&&h||("top"in o&&"bottom"in o&&(h=Math.abs(o.top-o.bottom)),"right"in o&&"left"in o&&(a=Math.abs(o.right-o.left))),this.calcOffset(),r.x=r.x-this._offset.left,r.y=r.y-this._offset.top,i||(r=this.restorePointerVpt(r));var l=this.getRetinaScaling();return 1!==l&&(r.x/=l,r.y/=l),n=0===a||0===h?{width:1,height:1}:{width:s.width/a,height:s.height/h},{x:r.x*n.width,y:r.y*n.height}},_createUpperCanvas:function(){var t=this.lowerCanvasEl.className.replace(/\s*lower-canvas\s*/,""),e=this.lowerCanvasEl,i=this.upperCanvasEl;i?i.className="":(i=this._createCanvasElement(),this.upperCanvasEl=i),T.util.addClass(i,"upper-canvas "+t),this.wrapperEl.appendChild(i),this._copyCanvasStyle(e,i),this._applyCanvasStyle(i),this.contextTop=i.getContext("2d")},_createCacheCanvas:function(){this.cacheCanvasEl=this._createCanvasElement(),this.cacheCanvasEl.setAttribute("width",this.width),this.cacheCanvasEl.setAttribute("height",this.height),this.contextCache=this.cacheCanvasEl.getContext("2d")},_initWrapperElement:function(){this.wrapperEl=T.util.wrapElement(this.lowerCanvasEl,"div",{class:this.containerClass}),T.util.setStyle(this.wrapperEl,{width:this.width+"px",height:this.height+"px",position:"relative"}),T.util.makeElementUnselectable(this.wrapperEl)},_applyCanvasStyle:function(t){var e=this.width||t.width,i=this.height||t.height;T.util.setStyle(t,{position:"absolute",width:e+"px",height:i+"px",left:0,top:0,"touch-action":this.allowTouchScrolling?"manipulation":"none","-ms-touch-action":this.allowTouchScrolling?"manipulation":"none"}),t.width=e,t.height=i,T.util.makeElementUnselectable(t)},_copyCanvasStyle:function(t,e){e.style.cssText=t.style.cssText},getSelectionContext:function(){return this.contextTop},getSelectionElement:function(){return this.upperCanvasEl},getActiveObject:function(){return this._activeObject},getActiveObjects:function(){var t=this._activeObject;return t?"activeSelection"===t.type&&t._objects?t._objects.slice(0):[t]:[]},_onObjectRemoved:function(t){t===this._activeObject&&(this.fire("before:selection:cleared",{target:t}),this._discardActiveObject(),this.fire("selection:cleared",{target:t}),t.fire("deselected")),t===this._hoveredTarget&&(this._hoveredTarget=null,this._hoveredTargets=[]),this.callSuper("_onObjectRemoved",t)},_fireSelectionEvents:function(t,e){var i=!1,n=this.getActiveObjects(),r=[],s=[];t.forEach((function(t){-1===n.indexOf(t)&&(i=!0,t.fire("deselected",{e,target:t}),s.push(t))})),n.forEach((function(n){-1===t.indexOf(n)&&(i=!0,n.fire("selected",{e,target:n}),r.push(n))})),t.length>0&&n.length>0?i&&this.fire("selection:updated",{e,selected:r,deselected:s,updated:r[0]||s[0],target:this._activeObject}):n.length>0?this.fire("selection:created",{e,selected:r,target:this._activeObject}):t.length>0&&this.fire("selection:cleared",{e,deselected:s})},setActiveObject:function(t,e){var i=this.getActiveObjects();return this._setActiveObject(t,e),this._fireSelectionEvents(i,e),this},_setActiveObject:function(t,e){return this._activeObject!==t&&!!this._discardActiveObject(e,t)&&!t.onSelect({e})&&(this._activeObject=t,!0)},_discardActiveObject:function(t,e){var i=this._activeObject;if(i){if(i.onDeselect({e:t,object:e}))return!1;this._activeObject=null}return!0},discardActiveObject:function(t){var e=this.getActiveObjects(),i=this.getActiveObject();return e.length&&this.fire("before:selection:cleared",{target:i,e:t}),this._discardActiveObject(t),this._fireSelectionEvents(e,t),this},dispose:function(){var t=this.wrapperEl;return this.removeListeners(),t.removeChild(this.upperCanvasEl),t.removeChild(this.lowerCanvasEl),this.contextCache=null,this.contextTop=null,["upperCanvasEl","cacheCanvasEl"].forEach(function(t){T.util.cleanUpJsdomNode(this[t]),this[t]=void 0}.bind(this)),t.parentNode&&t.parentNode.replaceChild(this.lowerCanvasEl,this.wrapperEl),delete this.wrapperEl,T.StaticCanvas.prototype.dispose.call(this),this},clear:function(){return this.discardActiveObject(),this.clearContext(this.contextTop),this.callSuper("clear")},drawControls:function(t){var e=this._activeObject;e&&e._renderControls(t)},_toObject:function(t,e,i){var n=this._realizeGroupTransformOnObject(t),r=this.callSuper("_toObject",t,e,i);return this._unwindGroupTransformOnObject(t,n),r},_realizeGroupTransformOnObject:function(t){if(t.group&&"activeSelection"===t.group.type&&this._activeObject===t.group){var e={};return["angle","flipX","flipY","left","scaleX","scaleY","skewX","skewY","top"].forEach((function(i){e[i]=t[i]})),T.util.addTransformToObject(t,this._activeObject.calcOwnMatrix()),e}return null},_unwindGroupTransformOnObject:function(t,e){e&&t.set(e)},_setSVGObject:function(t,e,i){var n=this._realizeGroupTransformOnObject(e);this.callSuper("_setSVGObject",t,e,i),this._unwindGroupTransformOnObject(e,n)},setViewportTransform:function(t){this.renderOnAddRemove&&this._activeObject&&this._activeObject.isEditing&&this._activeObject.clearContextTop(),T.StaticCanvas.prototype.setViewportTransform.call(this,t)}}),T.StaticCanvas)"prototype"!==o&&(T.Canvas[o]=T.StaticCanvas[o])}(),function(){var t=T.util.addListener,e=T.util.removeListener,i={passive:!1};function n(t,e){return t.button&&t.button===e-1}T.util.object.extend(T.Canvas.prototype,{mainTouchId:null,_initEventListeners:function(){this.removeListeners(),this._bindEvents(),this.addOrRemove(t,"add")},_getEventPrefix:function(){return this.enablePointerEvents?"pointer":"mouse"},addOrRemove:function(t,e){var n=this.upperCanvasEl,r=this._getEventPrefix();t(T.window,"resize",this._onResize),t(n,r+"down",this._onMouseDown),t(n,r+"move",this._onMouseMove,i),t(n,r+"out",this._onMouseOut),t(n,r+"enter",this._onMouseEnter),t(n,"wheel",this._onMouseWheel),t(n,"contextmenu",this._onContextMenu),t(n,"dblclick",this._onDoubleClick),t(n,"dragover",this._onDragOver),t(n,"dragenter",this._onDragEnter),t(n,"dragleave",this._onDragLeave),t(n,"drop",this._onDrop),this.enablePointerEvents||t(n,"touchstart",this._onTouchStart,i),"undefined"!=typeof eventjs&&e in eventjs&&(eventjs[e](n,"gesture",this._onGesture),eventjs[e](n,"drag",this._onDrag),eventjs[e](n,"orientation",this._onOrientationChange),eventjs[e](n,"shake",this._onShake),eventjs[e](n,"longpress",this._onLongPress))},removeListeners:function(){this.addOrRemove(e,"remove");var t=this._getEventPrefix();e(T.document,t+"up",this._onMouseUp),e(T.document,"touchend",this._onTouchEnd,i),e(T.document,t+"move",this._onMouseMove,i),e(T.document,"touchmove",this._onMouseMove,i)},_bindEvents:function(){this.eventsBound||(this._onMouseDown=this._onMouseDown.bind(this),this._onTouchStart=this._onTouchStart.bind(this),this._onMouseMove=this._onMouseMove.bind(this),this._onMouseUp=this._onMouseUp.bind(this),this._onTouchEnd=this._onTouchEnd.bind(this),this._onResize=this._onResize.bind(this),this._onGesture=this._onGesture.bind(this),this._onDrag=this._onDrag.bind(this),this._onShake=this._onShake.bind(this),this._onLongPress=this._onLongPress.bind(this),this._onOrientationChange=this._onOrientationChange.bind(this),this._onMouseWheel=this._onMouseWheel.bind(this),this._onMouseOut=this._onMouseOut.bind(this),this._onMouseEnter=this._onMouseEnter.bind(this),this._onContextMenu=this._onContextMenu.bind(this),this._onDoubleClick=this._onDoubleClick.bind(this),this._onDragOver=this._onDragOver.bind(this),this._onDragEnter=this._simpleEventHandler.bind(this,"dragenter"),this._onDragLeave=this._simpleEventHandler.bind(this,"dragleave"),this._onDrop=this._simpleEventHandler.bind(this,"drop"),this.eventsBound=!0)},_onGesture:function(t,e){this.__onTransformGesture&&this.__onTransformGesture(t,e)},_onDrag:function(t,e){this.__onDrag&&this.__onDrag(t,e)},_onMouseWheel:function(t){this.__onMouseWheel(t)},_onMouseOut:function(t){var e=this._hoveredTarget;this.fire("mouse:out",{target:e,e:t}),this._hoveredTarget=null,e&&e.fire("mouseout",{e:t});var i=this;this._hoveredTargets.forEach((function(n){i.fire("mouse:out",{target:e,e:t}),n&&e.fire("mouseout",{e:t})})),this._hoveredTargets=[],this._iTextInstances&&this._iTextInstances.forEach((function(t){t.isEditing&&t.hiddenTextarea.focus()}))},_onMouseEnter:function(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",{target:null,e:t}),this._hoveredTarget=null,this._hoveredTargets=[])},_onOrientationChange:function(t,e){this.__onOrientationChange&&this.__onOrientationChange(t,e)},_onShake:function(t,e){this.__onShake&&this.__onShake(t,e)},_onLongPress:function(t,e){this.__onLongPress&&this.__onLongPress(t,e)},_onDragOver:function(t){t.preventDefault();var e=this._simpleEventHandler("dragover",t);this._fireEnterLeaveEvents(e,t)},_onContextMenu:function(t){return this.stopContextMenu&&(t.stopPropagation(),t.preventDefault()),!1},_onDoubleClick:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"dblclick"),this._resetTransformEventData(t)},getPointerId:function(t){var e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1},_isMainEvent:function(t){return!0===t.isPrimary||!1!==t.isPrimary&&("touchend"===t.type&&0===t.touches.length||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)},_onTouchStart:function(n){n.preventDefault(),null===this.mainTouchId&&(this.mainTouchId=this.getPointerId(n)),this.__onMouseDown(n),this._resetTransformEventData();var r=this.upperCanvasEl,s=this._getEventPrefix();t(T.document,"touchend",this._onTouchEnd,i),t(T.document,"touchmove",this._onMouseMove,i),e(r,s+"down",this._onMouseDown)},_onMouseDown:function(n){this.__onMouseDown(n),this._resetTransformEventData();var r=this.upperCanvasEl,s=this._getEventPrefix();e(r,s+"move",this._onMouseMove,i),t(T.document,s+"up",this._onMouseUp),t(T.document,s+"move",this._onMouseMove,i)},_onTouchEnd:function(n){if(!(n.touches.length>0)){this.__onMouseUp(n),this._resetTransformEventData(),this.mainTouchId=null;var r=this._getEventPrefix();e(T.document,"touchend",this._onTouchEnd,i),e(T.document,"touchmove",this._onMouseMove,i);var s=this;this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout((function(){t(s.upperCanvasEl,r+"down",s._onMouseDown),s._willAddMouseDown=0}),400)}},_onMouseUp:function(n){this.__onMouseUp(n),this._resetTransformEventData();var r=this.upperCanvasEl,s=this._getEventPrefix();this._isMainEvent(n)&&(e(T.document,s+"up",this._onMouseUp),e(T.document,s+"move",this._onMouseMove,i),t(r,s+"move",this._onMouseMove,i))},_onMouseMove:function(t){!this.allowTouchScrolling&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)},_onResize:function(){this.calcOffset()},_shouldRender:function(t){var e=this._activeObject;return!!(!!e!=!!t||e&&t&&e!==t)||(e&&e.isEditing,!1)},__onMouseUp:function(t){var e,i=this._currentTransform,r=this._groupSelector,s=!1,o=!r||0===r.left&&0===r.top;if(this._cacheTransformEventData(t),e=this._target,this._handleEvent(t,"up:before"),n(t,3))this.fireRightClick&&this._handleEvent(t,"up",3,o);else{if(n(t,2))return this.fireMiddleClick&&this._handleEvent(t,"up",2,o),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)this._onMouseUpInDrawingMode(t);else if(this._isMainEvent(t)){if(i&&(this._finalizeCurrentTransform(t),s=i.actionPerformed),!o){var a=e===this._activeObject;this._maybeGroupObjects(t),s||(s=this._shouldRender(e)||!a&&e===this._activeObject)}if(e){if(e.selectable&&e!==this._activeObject&&"up"===e.activeOn)this.setActiveObject(e,t),s=!0;else{var h=e._findTargetCorner(this.getPointer(t,!0),T.util.isTouchEvent(t)),l=e.controls[h],c=l&&l.getMouseUpHandler(t,e,l);if(c){var u=this.getPointer(t);c(t,i,u.x,u.y)}}e.isMoving=!1}this._setCursorFromEvent(t,e),this._handleEvent(t,"up",1,o),this._groupSelector=null,this._currentTransform=null,e&&(e.__corner=0),s?this.requestRenderAll():o||this.renderTop()}}},_simpleEventHandler:function(t,e){var i=this.findTarget(e),n=this.targets,r={e,target:i,subTargets:n};if(this.fire(t,r),i&&i.fire(t,r),!n)return i;for(var s=0;s<n.length;s++)n[s].fire(t,r);return i},_handleEvent:function(t,e,i,n){var r=this._target,s=this.targets||[],o={e:t,target:r,subTargets:s,button:i||1,isClick:n||!1,pointer:this._pointer,absolutePointer:this._absolutePointer,transform:this._currentTransform};"up"===e&&(o.currentTarget=this.findTarget(t),o.currentSubTargets=this.targets),this.fire("mouse:"+e,o),r&&r.fire("mouse"+e,o);for(var a=0;a<s.length;a++)s[a].fire("mouse"+e,o)},_finalizeCurrentTransform:function(t){var e,i=this._currentTransform,n=i.target,r={e:t,target:n,transform:i,action:i.action};n._scaling&&(n._scaling=!1),n.setCoords(),(i.actionPerformed||this.stateful&&n.hasStateChanged())&&(i.actionPerformed&&(e=this._addEventOptions(r,i),this._fire(e,r)),this._fire("modified",r))},_addEventOptions:function(t,e){var i,n;switch(e.action){case"scaleX":i="scaled",n="x";break;case"scaleY":i="scaled",n="y";break;case"skewX":i="skewed",n="x";break;case"skewY":i="skewed",n="y";break;case"scale":i="scaled",n="equally";break;case"rotate":i="rotated";break;case"drag":i="moved"}return t.by=n,i},_onMouseDownInDrawingMode:function(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&this.discardActiveObject(t).requestRenderAll();var e=this.getPointer(t);this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down")},_onMouseMoveInDrawingMode:function(t){if(this._isCurrentlyDrawing){var e=this.getPointer(t);this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")},_onMouseUpInDrawingMode:function(t){var e=this.getPointer(t);this._isCurrentlyDrawing=this.freeDrawingBrush.onMouseUp({e:t,pointer:e}),this._handleEvent(t,"up")},__onMouseDown:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"down:before");var e=this._target;if(n(t,3))this.fireRightClick&&this._handleEvent(t,"down",3);else if(n(t,2))this.fireMiddleClick&&this._handleEvent(t,"down",2);else if(this.isDrawingMode)this._onMouseDownInDrawingMode(t);else if(this._isMainEvent(t)&&!this._currentTransform){var i=this._pointer;this._previousPointer=i;var r=this._shouldRender(e),s=this._shouldGroup(t,e);if(this._shouldClearSelection(t,e)?this.discardActiveObject(t):s&&(this._handleGrouping(t,e),e=this._activeObject),!this.selection||e&&(e.selectable||e.isEditing||e===this._activeObject)||(this._groupSelector={ex:i.x,ey:i.y,top:0,left:0}),e){var o=e===this._activeObject;e.selectable&&"down"===e.activeOn&&this.setActiveObject(e,t);var a=e._findTargetCorner(this.getPointer(t,!0),T.util.isTouchEvent(t));if(e.__corner=a,e===this._activeObject&&(a||!s)){this._setupCurrentTransform(t,e,o);var h=e.controls[a],l=(i=this.getPointer(t),h&&h.getMouseDownHandler(t,e,h));l&&l(t,this._currentTransform,i.x,i.y)}}this._handleEvent(t,"down"),(r||s)&&this.requestRenderAll()}},_resetTransformEventData:function(){this._target=null,this._pointer=null,this._absolutePointer=null},_cacheTransformEventData:function(t){this._resetTransformEventData(),this._pointer=this.getPointer(t,!0),this._absolutePointer=this.restorePointerVpt(this._pointer),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)||null},_beforeTransform:function(t){var e=this._currentTransform;this.stateful&&e.target.saveState(),this.fire("before:transform",{e:t,transform:e})},__onMouseMove:function(t){var e,i;if(this._handleEvent(t,"move:before"),this._cacheTransformEventData(t),this.isDrawingMode)this._onMouseMoveInDrawingMode(t);else if(this._isMainEvent(t)){var n=this._groupSelector;n?(i=this._pointer,n.left=i.x-n.ex,n.top=i.y-n.ey,this.renderTop()):this._currentTransform?this._transformObject(t):(e=this.findTarget(t)||null,this._setCursorFromEvent(t,e),this._fireOverOutEvents(e,t)),this._handleEvent(t,"move"),this._resetTransformEventData()}},_fireOverOutEvents:function(t,e){var i=this._hoveredTarget,n=this._hoveredTargets,r=this.targets,s=Math.max(n.length,r.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"mouseout",canvasEvtOut:"mouse:out",evtIn:"mouseover",canvasEvtIn:"mouse:over"});for(var o=0;o<s;o++)this.fireSyntheticInOutEvents(r[o],e,{oldTarget:n[o],evtOut:"mouseout",evtIn:"mouseover"});this._hoveredTarget=t,this._hoveredTargets=this.targets.concat()},_fireEnterLeaveEvents:function(t,e){var i=this._draggedoverTarget,n=this._hoveredTargets,r=this.targets,s=Math.max(n.length,r.length);this.fireSyntheticInOutEvents(t,e,{oldTarget:i,evtOut:"dragleave",evtIn:"dragenter"});for(var o=0;o<s;o++)this.fireSyntheticInOutEvents(r[o],e,{oldTarget:n[o],evtOut:"dragleave",evtIn:"dragenter"});this._draggedoverTarget=t},fireSyntheticInOutEvents:function(t,e,i){var n,r,s,o=i.oldTarget,a=o!==t,h=i.canvasEvtIn,l=i.canvasEvtOut;a&&(n={e,target:t,previousTarget:o},r={e,target:o,nextTarget:t}),s=t&&a,o&&a&&(l&&this.fire(l,r),o.fire(i.evtOut,r)),s&&(h&&this.fire(h,n),t.fire(i.evtIn,n))},__onMouseWheel:function(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()},_transformObject:function(t){var e=this.getPointer(t),i=this._currentTransform;i.reset=!1,i.shiftKey=t.shiftKey,i.altKey=t[this.centeredKey],this._performTransformAction(t,i,e),i.actionPerformed&&this.requestRenderAll()},_performTransformAction:function(t,e,i){var n=i.x,r=i.y,s=e.action,o=!1,a=e.actionHandler;a&&(o=a(t,e,n,r)),"drag"===s&&o&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||o},_fire:T.controlsUtils.fireEvent,_setCursorFromEvent:function(t,e){if(!e)return this.setCursor(this.defaultCursor),!1;var i=e.hoverCursor||this.hoverCursor,n=this._activeObject&&"activeSelection"===this._activeObject.type?this._activeObject:null,r=(!n||!n.contains(e))&&e._findTargetCorner(this.getPointer(t,!0));r?this.setCursor(this.getCornerCursor(r,e,t)):(e.subTargetCheck&&this.targets.concat().reverse().map((function(t){i=t.hoverCursor||i})),this.setCursor(i))},getCornerCursor:function(t,e,i){var n=e.controls[t];return n.cursorStyleHandler(i,n,e)}})}(),m=Math.min,y=Math.max,T.util.object.extend(T.Canvas.prototype,{_shouldGroup:function(t,e){var i=this._activeObject;return i&&this._isSelectionKeyPressed(t)&&e&&e.selectable&&this.selection&&(i!==e||"activeSelection"===i.type)&&!e.onSelect({e:t})},_handleGrouping:function(t,e){var i=this._activeObject;i.__corner||(e!==i||(e=this.findTarget(t,!0))&&e.selectable)&&(i&&"activeSelection"===i.type?this._updateActiveSelection(e,t):this._createActiveSelection(e,t))},_updateActiveSelection:function(t,e){var i=this._activeObject,n=i._objects.slice(0);i.contains(t)?(i.removeWithUpdate(t),this._hoveredTarget=t,this._hoveredTargets=this.targets.concat(),1===i.size()&&this._setActiveObject(i.item(0),e)):(i.addWithUpdate(t),this._hoveredTarget=i,this._hoveredTargets=this.targets.concat()),this._fireSelectionEvents(n,e)},_createActiveSelection:function(t,e){var i=this.getActiveObjects(),n=this._createGroup(t);this._hoveredTarget=n,this._setActiveObject(n,e),this._fireSelectionEvents(i,e)},_createGroup:function(t){var e=this._objects,i=e.indexOf(this._activeObject)<e.indexOf(t)?[this._activeObject,t]:[t,this._activeObject];return this._activeObject.isEditing&&this._activeObject.exitEditing(),new T.ActiveSelection(i,{canvas:this})},_groupSelectedObjects:function(t){var e,i=this._collectObjects(t);1===i.length?this.setActiveObject(i[0],t):i.length>1&&(e=new T.ActiveSelection(i.reverse(),{canvas:this}),this.setActiveObject(e,t))},_collectObjects:function(t){for(var e,i=[],n=this._groupSelector.ex,r=this._groupSelector.ey,s=n+this._groupSelector.left,o=r+this._groupSelector.top,a=new T.Point(m(n,s),m(r,o)),h=new T.Point(y(n,s),y(r,o)),l=!this.selectionFullyContained,c=n===s&&r===o,u=this._objects.length;u--&&!((e=this._objects[u])&&e.selectable&&e.visible&&(l&&e.intersectsWithRect(a,h)||e.isContainedWithinRect(a,h)||l&&e.containsPoint(a)||l&&e.containsPoint(h))&&(i.push(e),c)););return i.length>1&&(i=i.filter((function(e){return!e.onSelect({e:t})}))),i},_maybeGroupObjects:function(t){this.selection&&this._groupSelector&&this._groupSelectedObjects(t),this.setCursor(this.defaultCursor),this._groupSelector=null}}),T.util.object.extend(T.StaticCanvas.prototype,{toDataURL:function(t){t||(t={});var e=t.format||"png",i=t.quality||1,n=(t.multiplier||1)*(t.enableRetinaScaling?this.getRetinaScaling():1),r=this.toCanvasElement(n,t);return T.util.toDataURL(r,e,i)},toCanvasElement:function(t,e){t=t||1;var i=((e=e||{}).width||this.width)*t,n=(e.height||this.height)*t,r=this.getZoom(),s=this.width,o=this.height,a=r*t,h=this.viewportTransform,l=(h[4]-(e.left||0))*t,c=(h[5]-(e.top||0))*t,u=this.interactive,f=[a,0,0,a,l,c],d=this.enableRetinaScaling,g=T.util.createCanvasElement(),p=this.contextTop;return g.width=i,g.height=n,this.contextTop=null,this.enableRetinaScaling=!1,this.interactive=!1,this.viewportTransform=f,this.width=i,this.height=n,this.calcViewportBoundaries(),this.renderCanvas(g.getContext("2d"),this._objects),this.viewportTransform=h,this.width=s,this.height=o,this.calcViewportBoundaries(),this.interactive=u,this.enableRetinaScaling=d,this.contextTop=p,g}}),T.util.object.extend(T.StaticCanvas.prototype,{loadFromJSON:function(t,e,i){if(t){var n="string"==typeof t?JSON.parse(t):T.util.object.clone(t),r=this,s=n.clipPath,o=this.renderOnAddRemove;return this.renderOnAddRemove=!1,delete n.clipPath,this._enlivenObjects(n.objects,(function(t){r.clear(),r._setBgOverlay(n,(function(){s?r._enlivenObjects([s],(function(i){r.clipPath=i[0],r.__setupCanvas.call(r,n,t,o,e)})):r.__setupCanvas.call(r,n,t,o,e)}))}),i),this}},__setupCanvas:function(t,e,i,n){var r=this;e.forEach((function(t,e){r.insertAt(t,e)})),this.renderOnAddRemove=i,delete t.objects,delete t.backgroundImage,delete t.overlayImage,delete t.background,delete t.overlay,this._setOptions(t),this.renderAll(),n&&n()},_setBgOverlay:function(t,e){var i={backgroundColor:!1,overlayColor:!1,backgroundImage:!1,overlayImage:!1};if(t.backgroundImage||t.overlayImage||t.background||t.overlay){var n=function(){i.backgroundImage&&i.overlayImage&&i.backgroundColor&&i.overlayColor&&e&&e()};this.__setBgOverlay("backgroundImage",t.backgroundImage,i,n),this.__setBgOverlay("overlayImage",t.overlayImage,i,n),this.__setBgOverlay("backgroundColor",t.background,i,n),this.__setBgOverlay("overlayColor",t.overlay,i,n)}else e&&e()},__setBgOverlay:function(t,e,i,n){var r=this;if(!e)return i[t]=!0,void(n&&n());"backgroundImage"===t||"overlayImage"===t?T.util.enlivenObjects([e],(function(e){r[t]=e[0],i[t]=!0,n&&n()})):this["set"+T.util.string.capitalize(t,!0)](e,(function(){i[t]=!0,n&&n()}))},_enlivenObjects:function(t,e,i){t&&0!==t.length?T.util.enlivenObjects(t,(function(t){e&&e(t)}),null,i):e&&e([])},_toDataURL:function(t,e){this.clone((function(i){e(i.toDataURL(t))}))},_toDataURLWithMultiplier:function(t,e,i){this.clone((function(n){i(n.toDataURLWithMultiplier(t,e))}))},clone:function(t,e){var i=JSON.stringify(this.toJSON(e));this.cloneWithoutData((function(e){e.loadFromJSON(i,(function(){t&&t(e)}))}))},cloneWithoutData:function(t){var e=T.util.createCanvasElement();e.width=this.width,e.height=this.height;var i=new T.Canvas(e);this.backgroundImage?(i.setBackgroundImage(this.backgroundImage.src,(function(){i.renderAll(),t&&t(i)})),i.backgroundImageOpacity=this.backgroundImageOpacity,i.backgroundImageStretch=this.backgroundImageStretch):t&&t(i)}}),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r=e.util.toFixed,s=e.util.string.capitalize,o=e.util.degreesToRadians,a=e.StaticCanvas.supports("setLineDash"),h=!e.isLikelyNode;e.Object||(e.Object=e.util.createClass(e.CommonMethods,{type:"object",originX:"left",originY:"top",top:0,left:0,width:0,height:0,scaleX:1,scaleY:1,flipX:!1,flipY:!1,opacity:1,angle:0,skewX:0,skewY:0,cornerSize:13,touchCornerSize:24,transparentCorners:!0,hoverCursor:null,moveCursor:null,padding:0,borderColor:"rgb(178,204,255)",borderDashArray:null,cornerColor:"rgb(178,204,255)",cornerStrokeColor:null,cornerStyle:"rect",cornerDashArray:null,centeredScaling:!1,centeredRotation:!0,fill:"rgb(0,0,0)",fillRule:"nonzero",globalCompositeOperation:"source-over",backgroundColor:"",selectionBackgroundColor:"",stroke:null,strokeWidth:1,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,shadow:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,minScaleLimit:0,selectable:!0,evented:!0,visible:!0,hasControls:!0,hasBorders:!0,perPixelTargetFind:!1,includeDefaultValues:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,excludeFromExport:!1,objectCaching:h,statefullCache:!1,noScaleCache:!0,strokeUniform:!1,dirty:!0,__corner:0,paintFirst:"fill",activeOn:"down",stateProperties:"top left width height scaleX scaleY flipX flipY originX originY transformMatrix stroke strokeWidth strokeDashArray strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit angle opacity fill globalCompositeOperation shadow visible backgroundColor skewX skewY fillRule paintFirst clipPath strokeUniform".split(" "),cacheProperties:"fill stroke strokeWidth strokeDashArray width height paintFirst strokeUniform strokeLineCap strokeDashOffset strokeLineJoin strokeMiterLimit backgroundColor clipPath".split(" "),colorProperties:"fill stroke backgroundColor".split(" "),clipPath:void 0,inverted:!1,absolutePositioned:!1,initialize:function(t){t&&this.setOptions(t)},_createCacheCanvas:function(){this._cacheProperties={},this._cacheCanvas=e.util.createCanvasElement(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0},_limitCacheSize:function(t){var i=e.perfLimitSizeTotal,n=t.width,r=t.height,s=e.maxCacheSideLimit,o=e.minCacheSideLimit;if(n<=s&&r<=s&&n*r<=i)return n<o&&(t.width=o),r<o&&(t.height=o),t;var a=n/r,h=e.util.limitDimsByArea(a,i),l=e.util.capValue,c=l(o,h.x,s),u=l(o,h.y,s);return n>c&&(t.zoomX/=n/c,t.width=c,t.capped=!0),r>u&&(t.zoomY/=r/u,t.height=u,t.capped=!0),t},_getCacheCanvasDimensions:function(){var t=this.getTotalObjectScaling(),e=this._getTransformedDimensions(0,0),i=e.x*t.scaleX/this.scaleX,n=e.y*t.scaleY/this.scaleY;return{width:i+2,height:n+2,zoomX:t.scaleX,zoomY:t.scaleY,x:i,y:n}},_updateCacheCanvas:function(){var t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){var i=t._currentTransform.target,n=t._currentTransform.action;if(this===i&&n.slice&&"scale"===n.slice(0,5))return!1}var r,s,o=this._cacheCanvas,a=this._limitCacheSize(this._getCacheCanvasDimensions()),h=e.minCacheSideLimit,l=a.width,c=a.height,u=a.zoomX,f=a.zoomY,d=l!==this.cacheWidth||c!==this.cacheHeight,g=this.zoomX!==u||this.zoomY!==f,p=d||g,v=0,m=0,y=!1;if(d){var _=this._cacheCanvas.width,x=this._cacheCanvas.height,b=l>_||c>x;y=b||(l<.9*_||c<.9*x)&&_>h&&x>h,b&&!a.capped&&(l>h||c>h)&&(v=.1*l,m=.1*c)}return!!p&&(y?(o.width=Math.ceil(l+v),o.height=Math.ceil(c+m)):(this._cacheContext.setTransform(1,0,0,1,0,0),this._cacheContext.clearRect(0,0,o.width,o.height)),r=a.x/2,s=a.y/2,this.cacheTranslationX=Math.round(o.width/2-r)+r,this.cacheTranslationY=Math.round(o.height/2-s)+s,this.cacheWidth=l,this.cacheHeight=c,this._cacheContext.translate(this.cacheTranslationX,this.cacheTranslationY),this._cacheContext.scale(u,f),this.zoomX=u,this.zoomY=f,!0)},setOptions:function(t){this._setOptions(t),this._initGradient(t.fill,"fill"),this._initGradient(t.stroke,"stroke"),this._initPattern(t.fill,"fill"),this._initPattern(t.stroke,"stroke")},transform:function(t){var e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,i=this.calcTransformMatrix(!e);t.transform(i[0],i[1],i[2],i[3],i[4],i[5])},toObject:function(t){var i=e.Object.NUM_FRACTION_DIGITS,n={type:this.type,version:e.version,originX:this.originX,originY:this.originY,left:r(this.left,i),top:r(this.top,i),width:r(this.width,i),height:r(this.height,i),fill:this.fill&&this.fill.toObject?this.fill.toObject():this.fill,stroke:this.stroke&&this.stroke.toObject?this.stroke.toObject():this.stroke,strokeWidth:r(this.strokeWidth,i),strokeDashArray:this.strokeDashArray?this.strokeDashArray.concat():this.strokeDashArray,strokeLineCap:this.strokeLineCap,strokeDashOffset:this.strokeDashOffset,strokeLineJoin:this.strokeLineJoin,strokeUniform:this.strokeUniform,strokeMiterLimit:r(this.strokeMiterLimit,i),scaleX:r(this.scaleX,i),scaleY:r(this.scaleY,i),angle:r(this.angle,i),flipX:this.flipX,flipY:this.flipY,opacity:r(this.opacity,i),shadow:this.shadow&&this.shadow.toObject?this.shadow.toObject():this.shadow,visible:this.visible,backgroundColor:this.backgroundColor,fillRule:this.fillRule,paintFirst:this.paintFirst,globalCompositeOperation:this.globalCompositeOperation,skewX:r(this.skewX,i),skewY:r(this.skewY,i)};return this.clipPath&&(n.clipPath=this.clipPath.toObject(t),n.clipPath.inverted=this.clipPath.inverted,n.clipPath.absolutePositioned=this.clipPath.absolutePositioned),e.util.populateWithProperties(this,n,t),this.includeDefaultValues||(n=this._removeDefaultValues(n)),n},toDatalessObject:function(t){return this.toObject(t)},_removeDefaultValues:function(t){var i=e.util.getKlass(t.type).prototype;return i.stateProperties.forEach((function(e){"left"!==e&&"top"!==e&&(t[e]===i[e]&&delete t[e],"[object Array]"===Object.prototype.toString.call(t[e])&&"[object Array]"===Object.prototype.toString.call(i[e])&&0===t[e].length&&0===i[e].length&&delete t[e])})),t},toString:function(){return"#<fabric."+s(this.type)+">"},getObjectScaling:function(){var t=e.util.qrDecompose(this.calcTransformMatrix());return{scaleX:Math.abs(t.scaleX),scaleY:Math.abs(t.scaleY)}},getTotalObjectScaling:function(){var t=this.getObjectScaling(),e=t.scaleX,i=t.scaleY;if(this.canvas){var n=this.canvas.getZoom(),r=this.canvas.getRetinaScaling();e*=n*r,i*=n*r}return{scaleX:e,scaleY:i}},getObjectOpacity:function(){var t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t},_set:function(t,i){var n="scaleX"===t||"scaleY"===t,r=this[t]!==i,s=!1;return n&&(i=this._constrainScale(i)),"scaleX"===t&&i<0?(this.flipX=!this.flipX,i*=-1):"scaleY"===t&&i<0?(this.flipY=!this.flipY,i*=-1):"shadow"!==t||!i||i instanceof e.Shadow?"dirty"===t&&this.group&&this.group.set("dirty",i):i=new e.Shadow(i),this[t]=i,r&&(s=this.group&&this.group.isOnACache(),this.cacheProperties.indexOf(t)>-1?(this.dirty=!0,s&&this.group.set("dirty",!0)):s&&this.stateProperties.indexOf(t)>-1&&this.group.set("dirty",!0)),this},setOnGroup:function(){},getViewportTransform:function(){return this.canvas&&this.canvas.viewportTransform?this.canvas.viewportTransform:e.iMatrix.concat()},isNotVisible:function(){return 0===this.opacity||!this.width&&!this.height&&0===this.strokeWidth||!this.visible},render:function(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t,this),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.dirty=!1,this.drawObject(t),this.objectCaching&&this.statefullCache&&this.saveState({propertySet:"cacheProperties"})),t.restore())},renderCache:function(t){t=t||{},this._cacheCanvas||this._createCacheCanvas(),this.isCacheDirty()&&(this.statefullCache&&this.saveState({propertySet:"cacheProperties"}),this.drawObject(this._cacheContext,t.forClipping),this.dirty=!1)},_removeCacheCanvas:function(){this._cacheCanvas=null,this.cacheWidth=0,this.cacheHeight=0},hasStroke:function(){return this.stroke&&"transparent"!==this.stroke&&0!==this.strokeWidth},hasFill:function(){return this.fill&&"transparent"!==this.fill},needsItsOwnCache:function(){return!("stroke"!==this.paintFirst||!this.hasFill()||!this.hasStroke()||"object"!=typeof this.shadow)||!!this.clipPath},shouldCache:function(){return this.ownCaching=this.needsItsOwnCache()||this.objectCaching&&(!this.group||!this.group.isOnACache()),this.ownCaching},willDrawShadow:function(){return!!this.shadow&&(0!==this.shadow.offsetX||0!==this.shadow.offsetY)},drawClipPathOnCache:function(t){var i=this.clipPath;if(t.save(),i.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",i.absolutePositioned){var n=e.util.invertTransform(this.calcTransformMatrix());t.transform(n[0],n[1],n[2],n[3],n[4],n[5])}i.transform(t),t.scale(1/i.zoomX,1/i.zoomY),t.drawImage(i._cacheCanvas,-i.cacheTranslationX,-i.cacheTranslationY),t.restore()},drawObject:function(t,e){var i=this.fill,n=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t),this.fill=i,this.stroke=n},_drawClipPath:function(t){var e=this.clipPath;e&&(e.canvas=this.canvas,e.shouldCache(),e._transformDone=!0,e.renderCache({forClipping:!0}),this.drawClipPathOnCache(t))},drawCacheOnCanvas:function(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)},isCacheDirty:function(t){if(this.isNotVisible())return!1;if(this._cacheCanvas&&!t&&this._updateCacheCanvas())return!0;if(this.dirty||this.clipPath&&this.clipPath.absolutePositioned||this.statefullCache&&this.hasStateChanged("cacheProperties")){if(this._cacheCanvas&&!t){var e=this.cacheWidth/this.zoomX,i=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-e/2,-i/2,e,i)}return!0}return!1},_renderBackground:function(t){if(this.backgroundColor){var e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}},_setOpacity:function(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity},_setStrokeStyles:function(t,e){var i=e.stroke;i&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,i.toLive?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?this._applyPatternForTransformedGradient(t,i):(t.strokeStyle=i.toLive(t,this),this._applyPatternGradientTransform(t,i)):t.strokeStyle=e.stroke)},_setFillStyles:function(t,e){var i=e.fill;i&&(i.toLive?(t.fillStyle=i.toLive(t,this),this._applyPatternGradientTransform(t,e.fill)):t.fillStyle=i)},_setClippingProperties:function(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"},_setLineDash:function(t,e,i){e&&0!==e.length&&(1&e.length&&e.push.apply(e,e),a?t.setLineDash(e):i&&i(t))},_renderControls:function(t,i){var n,r,s,a=this.getViewportTransform(),h=this.calcTransformMatrix();r=void 0!==(i=i||{}).hasBorders?i.hasBorders:this.hasBorders,s=void 0!==i.hasControls?i.hasControls:this.hasControls,h=e.util.multiplyTransformMatrices(a,h),n=e.util.qrDecompose(h),t.save(),t.translate(n.translateX,n.translateY),t.lineWidth=1*this.borderScaleFactor,this.group||(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),i.forActiveSelection?(t.rotate(o(n.angle)),r&&this.drawBordersInGroup(t,n,i)):(t.rotate(o(this.angle)),r&&this.drawBorders(t,i)),s&&this.drawControls(t,i),t.restore()},_setShadow:function(t){if(this.shadow){var i,n=this.shadow,r=this.canvas,s=r&&r.viewportTransform[0]||1,o=r&&r.viewportTransform[3]||1;i=n.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),r&&r._isRetinaScaling()&&(s*=e.devicePixelRatio,o*=e.devicePixelRatio),t.shadowColor=n.color,t.shadowBlur=n.blur*e.browserShadowBlurConstant*(s+o)*(i.scaleX+i.scaleY)/4,t.shadowOffsetX=n.offsetX*s*i.scaleX,t.shadowOffsetY=n.offsetY*o*i.scaleY}},_removeShadow:function(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)},_applyPatternGradientTransform:function(t,e){if(!e||!e.toLive)return{offsetX:0,offsetY:0};var i=e.gradientTransform||e.patternTransform,n=-this.width/2+e.offsetX||0,r=-this.height/2+e.offsetY||0;return"percentage"===e.gradientUnits?t.transform(this.width,0,0,this.height,n,r):t.transform(1,0,0,1,n,r),i&&t.transform(i[0],i[1],i[2],i[3],i[4],i[5]),{offsetX:n,offsetY:r}},_renderPaintInOrder:function(t){"stroke"===this.paintFirst?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))},_render:function(){},_renderFill:function(t){this.fill&&(t.save(),this._setFillStyles(t,this),"evenodd"===this.fillRule?t.fill("evenodd"):t.fill(),t.restore())},_renderStroke:function(t){if(this.stroke&&0!==this.strokeWidth){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform&&this.group){var e=this.getObjectScaling();t.scale(1/e.scaleX,1/e.scaleY)}else this.strokeUniform&&t.scale(1/this.scaleX,1/this.scaleY);this._setLineDash(t,this.strokeDashArray,this._renderDashedStroke),this._setStrokeStyles(t,this),t.stroke(),t.restore()}},_applyPatternForTransformedGradient:function(t,i){var n,r=this._limitCacheSize(this._getCacheCanvasDimensions()),s=e.util.createCanvasElement(),o=this.canvas.getRetinaScaling(),a=r.x/this.scaleX/o,h=r.y/this.scaleY/o;s.width=a,s.height=h,(n=s.getContext("2d")).beginPath(),n.moveTo(0,0),n.lineTo(a,0),n.lineTo(a,h),n.lineTo(0,h),n.closePath(),n.translate(a/2,h/2),n.scale(r.zoomX/this.scaleX/o,r.zoomY/this.scaleY/o),this._applyPatternGradientTransform(n,i),n.fillStyle=i.toLive(t),n.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(o*this.scaleX/r.zoomX,o*this.scaleY/r.zoomY),t.strokeStyle=n.createPattern(s,"no-repeat")},_findCenterFromElement:function(){return{x:this.left+this.width/2,y:this.top+this.height/2}},_assignTransformMatrixProps:function(){if(this.transformMatrix){var t=e.util.qrDecompose(this.transformMatrix);this.flipX=!1,this.flipY=!1,this.set("scaleX",t.scaleX),this.set("scaleY",t.scaleY),this.angle=t.angle,this.skewX=t.skewX,this.skewY=0}},_removeTransformMatrix:function(t){var i=this._findCenterFromElement();this.transformMatrix&&(this._assignTransformMatrixProps(),i=e.util.transformPoint(i,this.transformMatrix)),this.transformMatrix=null,t&&(this.scaleX*=t.scaleX,this.scaleY*=t.scaleY,this.cropX=t.cropX,this.cropY=t.cropY,i.x+=t.offsetLeft,i.y+=t.offsetTop,this.width=t.width,this.height=t.height),this.setPositionByOrigin(i,"center","center")},clone:function(t,i){var n=this.toObject(i);this.constructor.fromObject?this.constructor.fromObject(n,t):e.Object._fromObject("Object",n,t)},cloneAsImage:function(t,i){var n=this.toCanvasElement(i);return t&&t(new e.Image(n)),this},toCanvasElement:function(t){t||(t={});var i=e.util,n=i.saveObjectTransform(this),r=this.group,s=this.shadow,o=Math.abs,a=(t.multiplier||1)*(t.enableRetinaScaling?e.devicePixelRatio:1);delete this.group,t.withoutTransform&&i.resetObjectTransform(this),t.withoutShadow&&(this.shadow=null);var h,l,c,u,f=e.util.createCanvasElement(),d=this.getBoundingRect(!0,!0),g=this.shadow,p={x:0,y:0};g&&(l=g.blur,h=g.nonScaling?{scaleX:1,scaleY:1}:this.getObjectScaling(),p.x=2*Math.round(o(g.offsetX)+l)*o(h.scaleX),p.y=2*Math.round(o(g.offsetY)+l)*o(h.scaleY)),c=d.width+p.x,u=d.height+p.y,f.width=Math.ceil(c),f.height=Math.ceil(u);var v=new e.StaticCanvas(f,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1});"jpeg"===t.format&&(v.backgroundColor="#fff"),this.setPositionByOrigin(new e.Point(v.width/2,v.height/2),"center","center");var m=this.canvas;v.add(this);var y=v.toCanvasElement(a||1,t);return this.shadow=s,this.set("canvas",m),r&&(this.group=r),this.set(n).setCoords(),v._objects=[],v.dispose(),v=null,y},toDataURL:function(t){return t||(t={}),e.util.toDataURL(this.toCanvasElement(t),t.format||"png",t.quality||1)},isType:function(t){return this.type===t},complexity:function(){return 1},toJSON:function(t){return this.toObject(t)},rotate:function(t){var e=("center"!==this.originX||"center"!==this.originY)&&this.centeredRotation;return e&&this._setOriginToCenter(),this.set("angle",t),e&&this._resetOrigin(),this},centerH:function(){return this.canvas&&this.canvas.centerObjectH(this),this},viewportCenterH:function(){return this.canvas&&this.canvas.viewportCenterObjectH(this),this},centerV:function(){return this.canvas&&this.canvas.centerObjectV(this),this},viewportCenterV:function(){return this.canvas&&this.canvas.viewportCenterObjectV(this),this},center:function(){return this.canvas&&this.canvas.centerObject(this),this},viewportCenter:function(){return this.canvas&&this.canvas.viewportCenterObject(this),this},getLocalPointer:function(t,i){i=i||this.canvas.getPointer(t);var n=new e.Point(i.x,i.y),r=this._getLeftTopCoords();return this.angle&&(n=e.util.rotatePoint(n,r,o(-this.angle))),{x:n.x-r.x,y:n.y-r.y}},_setupCompositeOperation:function(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}}),e.util.createAccessors&&e.util.createAccessors(e.Object),i(e.Object.prototype,e.Observable),e.Object.NUM_FRACTION_DIGITS=2,e.Object._fromObject=function(t,i,r,s){var o=e[t];i=n(i,!0),e.util.enlivenPatterns([i.fill,i.stroke],(function(t){void 0!==t[0]&&(i.fill=t[0]),void 0!==t[1]&&(i.stroke=t[1]),e.util.enlivenObjects([i.clipPath],(function(t){i.clipPath=t[0];var e=s?new o(i[s],i):new o(i);r&&r(e)}))}))},e.Object.__uid=0)}(e),_=T.util.degreesToRadians,x={left:-.5,center:0,right:.5},b={top:-.5,center:0,bottom:.5},T.util.object.extend(T.Object.prototype,{translateToGivenOrigin:function(t,e,i,n,r){var s,o,a,h=t.x,l=t.y;return"string"==typeof e?e=x[e]:e-=.5,"string"==typeof n?n=x[n]:n-=.5,"string"==typeof i?i=b[i]:i-=.5,"string"==typeof r?r=b[r]:r-=.5,o=r-i,((s=n-e)||o)&&(a=this._getTransformedDimensions(),h=t.x+s*a.x,l=t.y+o*a.y),new T.Point(h,l)},translateToCenterPoint:function(t,e,i){var n=this.translateToGivenOrigin(t,e,i,"center","center");return this.angle?T.util.rotatePoint(n,t,_(this.angle)):n},translateToOriginPoint:function(t,e,i){var n=this.translateToGivenOrigin(t,"center","center",e,i);return this.angle?T.util.rotatePoint(n,t,_(this.angle)):n},getCenterPoint:function(){var t=new T.Point(this.left,this.top);return this.translateToCenterPoint(t,this.originX,this.originY)},getPointByOrigin:function(t,e){var i=this.getCenterPoint();return this.translateToOriginPoint(i,t,e)},toLocalPoint:function(t,e,i){var n,r,s=this.getCenterPoint();return n=void 0!==e&&void 0!==i?this.translateToGivenOrigin(s,"center","center",e,i):new T.Point(this.left,this.top),r=new T.Point(t.x,t.y),this.angle&&(r=T.util.rotatePoint(r,s,-_(this.angle))),r.subtractEquals(n)},setPositionByOrigin:function(t,e,i){var n=this.translateToCenterPoint(t,e,i),r=this.translateToOriginPoint(n,this.originX,this.originY);this.set("left",r.x),this.set("top",r.y)},adjustPosition:function(t){var e,i,n=_(this.angle),r=this.getScaledWidth(),s=T.util.cos(n)*r,o=T.util.sin(n)*r;e="string"==typeof this.originX?x[this.originX]:this.originX-.5,i="string"==typeof t?x[t]:t-.5,this.left+=s*(i-e),this.top+=o*(i-e),this.setCoords(),this.originX=t},_setOriginToCenter:function(){this._originalOriginX=this.originX,this._originalOriginY=this.originY;var t=this.getCenterPoint();this.originX="center",this.originY="center",this.left=t.x,this.top=t.y},_resetOrigin:function(){var t=this.translateToOriginPoint(this.getCenterPoint(),this._originalOriginX,this._originalOriginY);this.originX=this._originalOriginX,this.originY=this._originalOriginY,this.left=t.x,this.top=t.y,this._originalOriginX=null,this._originalOriginY=null},_getLeftTopCoords:function(){return this.translateToOriginPoint(this.getCenterPoint(),"left","top")}}),function(){var t=T.util,e=t.degreesToRadians,i=t.multiplyTransformMatrices,n=t.transformPoint;t.object.extend(T.Object.prototype,{oCoords:null,aCoords:null,lineCoords:null,ownMatrixCache:null,matrixCache:null,controls:{},_getCoords:function(t,e){return e?t?this.calcACoords():this.calcLineCoords():(this.aCoords&&this.lineCoords||this.setCoords(!0),t?this.aCoords:this.lineCoords)},getCoords:function(t,e){return i=this._getCoords(t,e),[new T.Point(i.tl.x,i.tl.y),new T.Point(i.tr.x,i.tr.y),new T.Point(i.br.x,i.br.y),new T.Point(i.bl.x,i.bl.y)];var i},intersectsWithRect:function(t,e,i,n){var r=this.getCoords(i,n);return"Intersection"===T.Intersection.intersectPolygonRectangle(r,t,e).status},intersectsWithObject:function(t,e,i){return"Intersection"===T.Intersection.intersectPolygonPolygon(this.getCoords(e,i),t.getCoords(e,i)).status||t.isContainedWithinObject(this,e,i)||this.isContainedWithinObject(t,e,i)},isContainedWithinObject:function(t,e,i){for(var n=this.getCoords(e,i),r=e?t.aCoords:t.lineCoords,s=0,o=t._getImageLines(r);s<4;s++)if(!t.containsPoint(n[s],o))return!1;return!0},isContainedWithinRect:function(t,e,i,n){var r=this.getBoundingRect(i,n);return r.left>=t.x&&r.left+r.width<=e.x&&r.top>=t.y&&r.top+r.height<=e.y},containsPoint:function(t,e,i,n){var r=this._getCoords(i,n),s=(e=e||this._getImageLines(r),this._findCrossPoints(t,e));return 0!==s&&s%2==1},isOnScreen:function(t){if(!this.canvas)return!1;var e=this.canvas.vptCoords.tl,i=this.canvas.vptCoords.br;return!!this.getCoords(!0,t).some((function(t){return t.x<=i.x&&t.x>=e.x&&t.y<=i.y&&t.y>=e.y}))||!!this.intersectsWithRect(e,i,!0,t)||this._containsCenterOfCanvas(e,i,t)},_containsCenterOfCanvas:function(t,e,i){var n={x:(t.x+e.x)/2,y:(t.y+e.y)/2};return!!this.containsPoint(n,null,!0,i)},isPartiallyOnScreen:function(t){if(!this.canvas)return!1;var e=this.canvas.vptCoords.tl,i=this.canvas.vptCoords.br;return!!this.intersectsWithRect(e,i,!0,t)||this.getCoords(!0,t).every((function(t){return(t.x>=i.x||t.x<=e.x)&&(t.y>=i.y||t.y<=e.y)}))&&this._containsCenterOfCanvas(e,i,t)},_getImageLines:function(t){return{topline:{o:t.tl,d:t.tr},rightline:{o:t.tr,d:t.br},bottomline:{o:t.br,d:t.bl},leftline:{o:t.bl,d:t.tl}}},_findCrossPoints:function(t,e){var i,n,r,s=0;for(var o in e)if(!((r=e[o]).o.y<t.y&&r.d.y<t.y||r.o.y>=t.y&&r.d.y>=t.y||(r.o.x===r.d.x&&r.o.x>=t.x?n=r.o.x:(i=(r.d.y-r.o.y)/(r.d.x-r.o.x),n=-(t.y-0*t.x-(r.o.y-i*r.o.x))/(0-i)),n>=t.x&&(s+=1),2!==s)))break;return s},getBoundingRect:function(e,i){var n=this.getCoords(e,i);return t.makeBoundingBoxFromPoints(n)},getScaledWidth:function(){return this._getTransformedDimensions().x},getScaledHeight:function(){return this._getTransformedDimensions().y},_constrainScale:function(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:0===t?1e-4:t},scale:function(t){return this._set("scaleX",t),this._set("scaleY",t),this.setCoords()},scaleToWidth:function(t,e){var i=this.getBoundingRect(e).width/this.getScaledWidth();return this.scale(t/this.width/i)},scaleToHeight:function(t,e){var i=this.getBoundingRect(e).height/this.getScaledHeight();return this.scale(t/this.height/i)},calcCoords:function(t){return t?this.calcACoords():this.calcOCoords()},calcLineCoords:function(){var i=this.getViewportTransform(),r=this.padding,s=e(this.angle),o=t.cos(s)*r,a=t.sin(s)*r,h=o+a,l=o-a,c=this.calcACoords(),u={tl:n(c.tl,i),tr:n(c.tr,i),bl:n(c.bl,i),br:n(c.br,i)};return r&&(u.tl.x-=l,u.tl.y-=h,u.tr.x+=h,u.tr.y-=l,u.bl.x-=h,u.bl.y+=l,u.br.x+=l,u.br.y+=h),u},calcOCoords:function(){var t=this._calcRotateMatrix(),e=this._calcTranslateMatrix(),n=this.getViewportTransform(),r=i(n,e),s=i(r,t),o=(s=i(s,[1/n[0],0,0,1/n[3],0,0]),this._calculateCurrentDimensions()),a={};return this.forEachControl((function(t,e,i){a[e]=t.positionHandler(o,s,i)})),a},calcACoords:function(){var t=this._calcRotateMatrix(),e=this._calcTranslateMatrix(),r=i(e,t),s=this._getTransformedDimensions(),o=s.x/2,a=s.y/2;return{tl:n({x:-o,y:-a},r),tr:n({x:o,y:-a},r),bl:n({x:-o,y:a},r),br:n({x:o,y:a},r)}},setCoords:function(t){return this.aCoords=this.calcACoords(),this.lineCoords=this.group?this.aCoords:this.calcLineCoords(),t||(this.oCoords=this.calcOCoords(),this._setCornerCoords&&this._setCornerCoords()),this},_calcRotateMatrix:function(){return t.calcRotateMatrix(this)},_calcTranslateMatrix:function(){var t=this.getCenterPoint();return[1,0,0,1,t.x,t.y]},transformMatrixKey:function(t){var e="_",i="";return!t&&this.group&&(i=this.group.transformMatrixKey(t)+e),i+this.top+e+this.left+e+this.scaleX+e+this.scaleY+e+this.skewX+e+this.skewY+e+this.angle+e+this.originX+e+this.originY+e+this.width+e+this.height+e+this.strokeWidth+this.flipX+this.flipY},calcTransformMatrix:function(t){var e=this.calcOwnMatrix();if(t||!this.group)return e;var n=this.transformMatrixKey(t),r=this.matrixCache||(this.matrixCache={});return r.key===n?r.value:(this.group&&(e=i(this.group.calcTransformMatrix(!1),e)),r.key=n,r.value=e,e)},calcOwnMatrix:function(){var e=this.transformMatrixKey(!0),i=this.ownMatrixCache||(this.ownMatrixCache={});if(i.key===e)return i.value;var n=this._calcTranslateMatrix(),r={angle:this.angle,translateX:n[4],translateY:n[5],scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY};return i.key=e,i.value=t.composeMatrix(r),i.value},_calcDimensionsTransformMatrix:function(e,i,n){return t.calcDimensionsMatrix({skewX:e,skewY:i,scaleX:this.scaleX*(n&&this.flipX?-1:1),scaleY:this.scaleY*(n&&this.flipY?-1:1)})},_getNonTransformedDimensions:function(){var t=this.strokeWidth;return{x:this.width+t,y:this.height+t}},_getTransformedDimensions:function(e,i){void 0===e&&(e=this.skewX),void 0===i&&(i=this.skewY);var n,r,s=this._getNonTransformedDimensions(),o=0===e&&0===i;if(this.strokeUniform?(n=this.width,r=this.height):(n=s.x,r=s.y),o)return this._finalizeDimensions(n*this.scaleX,r*this.scaleY);var a=t.sizeAfterTransform(n,r,{scaleX:this.scaleX,scaleY:this.scaleY,skewX:e,skewY:i});return this._finalizeDimensions(a.x,a.y)},_finalizeDimensions:function(t,e){return this.strokeUniform?{x:t+this.strokeWidth,y:e+this.strokeWidth}:{x:t,y:e}},_calculateCurrentDimensions:function(){var t=this.getViewportTransform(),e=this._getTransformedDimensions();return n(e,t,!0).scalarAdd(2*this.padding)}})}(),T.util.object.extend(T.Object.prototype,{sendToBack:function(){return this.group?T.StaticCanvas.prototype.sendToBack.call(this.group,this):this.canvas&&this.canvas.sendToBack(this),this},bringToFront:function(){return this.group?T.StaticCanvas.prototype.bringToFront.call(this.group,this):this.canvas&&this.canvas.bringToFront(this),this},sendBackwards:function(t){return this.group?T.StaticCanvas.prototype.sendBackwards.call(this.group,this,t):this.canvas&&this.canvas.sendBackwards(this,t),this},bringForward:function(t){return this.group?T.StaticCanvas.prototype.bringForward.call(this.group,this,t):this.canvas&&this.canvas.bringForward(this,t),this},moveTo:function(t){return this.group&&"activeSelection"!==this.group.type?T.StaticCanvas.prototype.moveTo.call(this.group,this,t):this.canvas&&this.canvas.moveTo(this,t),this}}),function(){function t(t,e){if(e){if(e.toLive)return t+": url(#SVGID_"+e.id+"); ";var i=new T.Color(e),n=t+": "+i.toRgb()+"; ",r=i.getAlpha();return 1!==r&&(n+=t+"-opacity: "+r.toString()+"; "),n}return t+": none; "}var e=T.util.toFixed;T.util.object.extend(T.Object.prototype,{getSvgStyles:function(e){var i=this.fillRule?this.fillRule:"nonzero",n=this.strokeWidth?this.strokeWidth:"0",r=this.strokeDashArray?this.strokeDashArray.join(" "):"none",s=this.strokeDashOffset?this.strokeDashOffset:"0",o=this.strokeLineCap?this.strokeLineCap:"butt",a=this.strokeLineJoin?this.strokeLineJoin:"miter",h=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=void 0!==this.opacity?this.opacity:"1",c=this.visible?"":" visibility: hidden;",u=e?"":this.getSvgFilter(),f=t("fill",this.fill);return[t("stroke",this.stroke),"stroke-width: ",n,"; ","stroke-dasharray: ",r,"; ","stroke-linecap: ",o,"; ","stroke-dashoffset: ",s,"; ","stroke-linejoin: ",a,"; ","stroke-miterlimit: ",h,"; ",f,"fill-rule: ",i,"; ","opacity: ",l,";",u,c].join("")},getSvgSpanStyles:function(e,i){var n="; ",r=e.fontFamily?"font-family: "+(-1===e.fontFamily.indexOf("'")&&-1===e.fontFamily.indexOf('"')?"'"+e.fontFamily+"'":e.fontFamily)+n:"",s=e.strokeWidth?"stroke-width: "+e.strokeWidth+n:"",o=e.fontSize?"font-size: "+e.fontSize+"px"+n:"",a=e.fontStyle?"font-style: "+e.fontStyle+n:"",h=e.fontWeight?"font-weight: "+e.fontWeight+n:"",l=e.fill?t("fill",e.fill):"",c=e.stroke?t("stroke",e.stroke):"",u=this.getSvgTextDecoration(e);return u&&(u="text-decoration: "+u+n),[c,s,r,o,a,h,u,l,e.deltaY?"baseline-shift: "+-e.deltaY+"; ":"",i?"white-space: pre; ":""].join("")},getSvgTextDecoration:function(t){return["overline","underline","line-through"].filter((function(e){return t[e.replace("-","")]})).join(" ")},getSvgFilter:function(){return this.shadow?"filter: url(#SVGID_"+this.shadow.id+");":""},getSvgCommons:function(){return[this.id?'id="'+this.id+'" ':"",this.clipPath?'clip-path="url(#'+this.clipPath.clipPathId+')" ':""].join("")},getSvgTransform:function(t,e){var i=t?this.calcTransformMatrix():this.calcOwnMatrix();return'transform="'+T.util.matrixToSVG(i)+(e||"")+'" '},_setSVGBg:function(t){if(this.backgroundColor){var i=T.Object.NUM_FRACTION_DIGITS;t.push("\t\t<rect ",this._getFillAttributes(this.backgroundColor),' x="',e(-this.width/2,i),'" y="',e(-this.height/2,i),'" width="',e(this.width,i),'" height="',e(this.height,i),'"></rect>\n')}},toSVG:function(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})},toClipPathSVG:function(t){return"\t"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})},_createBaseClipPathSVGMarkup:function(t,e){var i=(e=e||{}).reviver,n=e.additionalTransform||"",r=[this.getSvgTransform(!0,n),this.getSvgCommons()].join(""),s=t.indexOf("COMMON_PARTS");return t[s]=r,i?i(t.join("")):t.join("")},_createBaseSVGMarkup:function(t,e){var i,n,r=(e=e||{}).noStyle,s=e.reviver,o=r?"":'style="'+this.getSvgStyles()+'" ',a=e.withShadow?'style="'+this.getSvgFilter()+'" ':"",h=this.clipPath,l=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=h&&h.absolutePositioned,u=this.stroke,f=this.fill,d=this.shadow,g=[],p=t.indexOf("COMMON_PARTS"),v=e.additionalTransform;return h&&(h.clipPathId="CLIPPATH_"+T.Object.__uid++,n='<clipPath id="'+h.clipPathId+'" >\n'+h.toClipPathSVG(s)+"</clipPath>\n"),c&&g.push("<g ",a,this.getSvgCommons()," >\n"),g.push("<g ",this.getSvgTransform(!1),c?"":a+this.getSvgCommons()," >\n"),i=[o,l,r?"":this.addPaintOrder()," ",v?'transform="'+v+'" ':""].join(""),t[p]=i,f&&f.toLive&&g.push(f.toSVG(this)),u&&u.toLive&&g.push(u.toSVG(this)),d&&g.push(d.toSVG(this)),h&&g.push(n),g.push(t.join("")),g.push("</g>\n"),c&&g.push("</g>\n"),s?s(g.join("")):g.join("")},addPaintOrder:function(){return"fill"!==this.paintFirst?' paint-order="'+this.paintFirst+'" ':""}})}(),function(){var t=T.util.object.extend,e="stateProperties";function i(e,i,n){var r={};n.forEach((function(t){r[t]=e[t]})),t(e[i],r,!0)}function n(t,e,i){if(t===e)return!0;if(Array.isArray(t)){if(!Array.isArray(e)||t.length!==e.length)return!1;for(var r=0,s=t.length;r<s;r++)if(!n(t[r],e[r]))return!1;return!0}if(t&&"object"==typeof t){var o,a=Object.keys(t);if(!e||"object"!=typeof e||!i&&a.length!==Object.keys(e).length)return!1;for(r=0,s=a.length;r<s;r++)if("canvas"!==(o=a[r])&&"group"!==o&&!n(t[o],e[o]))return!1;return!0}}T.util.object.extend(T.Object.prototype,{hasStateChanged:function(t){var i="_"+(t=t||e);return Object.keys(this[i]).length<this[t].length||!n(this[i],this,!0)},saveState:function(t){var n=t&&t.propertySet||e,r="_"+n;return this[r]?(i(this,r,this[n]),t&&t.stateProperties&&i(this,r,t.stateProperties),this):this.setupState(t)},setupState:function(t){var i=(t=t||{}).propertySet||e;return t.propertySet=i,this["_"+i]={},this.saveState(t),this}})}(),function(){var t=T.util.degreesToRadians;T.util.object.extend(T.Object.prototype,{_findTargetCorner:function(t,e){if(!this.hasControls||this.group||!this.canvas||this.canvas._activeObject!==this)return!1;var i,n,r,s=t.x,o=t.y,a=Object.keys(this.oCoords),h=a.length-1;for(this.__corner=0;h>=0;h--)if(r=a[h],this.isControlVisible(r)&&(n=this._getImageLines(e?this.oCoords[r].touchCorner:this.oCoords[r].corner),0!==(i=this._findCrossPoints({x:s,y:o},n))&&i%2==1))return this.__corner=r,r;return!1},forEachControl:function(t){for(var e in this.controls)t(this.controls[e],e,this)},_setCornerCoords:function(){var t=this.oCoords;for(var e in t){var i=this.controls[e];t[e].corner=i.calcCornerCoords(this.angle,this.cornerSize,t[e].x,t[e].y,!1),t[e].touchCorner=i.calcCornerCoords(this.angle,this.touchCornerSize,t[e].x,t[e].y,!0)}},drawSelectionBackground:function(e){if(!this.selectionBackgroundColor||this.canvas&&!this.canvas.interactive||this.canvas&&this.canvas._activeObject!==this)return this;e.save();var i=this.getCenterPoint(),n=this._calculateCurrentDimensions(),r=this.canvas.viewportTransform;return e.translate(i.x,i.y),e.scale(1/r[0],1/r[3]),e.rotate(t(this.angle)),e.fillStyle=this.selectionBackgroundColor,e.fillRect(-n.x/2,-n.y/2,n.x,n.y),e.restore(),this},drawBorders:function(t,e){e=e||{};var i=this._calculateCurrentDimensions(),n=this.borderScaleFactor,r=i.x+n,s=i.y+n,o=void 0!==e.hasControls?e.hasControls:this.hasControls,a=!1;return t.save(),t.strokeStyle=e.borderColor||this.borderColor,this._setLineDash(t,e.borderDashArray||this.borderDashArray,null),t.strokeRect(-r/2,-s/2,r,s),o&&(t.beginPath(),this.forEachControl((function(e,i,n){e.withConnection&&e.getVisibility(n,i)&&(a=!0,t.moveTo(e.x*r,e.y*s),t.lineTo(e.x*r+e.offsetX,e.y*s+e.offsetY))})),a&&t.stroke()),t.restore(),this},drawBordersInGroup:function(t,e,i){i=i||{};var n=T.util.sizeAfterTransform(this.width,this.height,e),r=this.strokeWidth,s=this.strokeUniform,o=this.borderScaleFactor,a=n.x+r*(s?this.canvas.getZoom():e.scaleX)+o,h=n.y+r*(s?this.canvas.getZoom():e.scaleY)+o;return t.save(),this._setLineDash(t,i.borderDashArray||this.borderDashArray,null),t.strokeStyle=i.borderColor||this.borderColor,t.strokeRect(-a/2,-h/2,a,h),t.restore(),this},drawControls:function(t,e){return e=e||{},t.save(),t.setTransform(this.canvas.getRetinaScaling(),0,0,this.canvas.getRetinaScaling(),0,0),t.strokeStyle=t.fillStyle=e.cornerColor||this.cornerColor,this.transparentCorners||(t.strokeStyle=e.cornerStrokeColor||this.cornerStrokeColor),this._setLineDash(t,e.cornerDashArray||this.cornerDashArray,null),this.setCoords(),this.forEachControl((function(i,n,r){i.getVisibility(r,n)&&i.render(t,r.oCoords[n].x,r.oCoords[n].y,e,r)})),t.restore(),this},isControlVisible:function(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)},setControlVisible:function(t,e){return this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e,this},setControlsVisibility:function(t){for(var e in t||(t={}),t)this.setControlVisible(e,t[e]);return this},onDeselect:function(){},onSelect:function(){}})}(),T.util.object.extend(T.StaticCanvas.prototype,{FX_DURATION:500,fxCenterObjectH:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,r=e.onChange||i,s=this;return T.util.animate({startValue:t.left,endValue:this.getCenter().left,duration:this.FX_DURATION,onChange:function(e){t.set("left",e),s.requestRenderAll(),r()},onComplete:function(){t.setCoords(),n()}}),this},fxCenterObjectV:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,r=e.onChange||i,s=this;return T.util.animate({startValue:t.top,endValue:this.getCenter().top,duration:this.FX_DURATION,onChange:function(e){t.set("top",e),s.requestRenderAll(),r()},onComplete:function(){t.setCoords(),n()}}),this},fxRemove:function(t,e){var i=function(){},n=(e=e||{}).onComplete||i,r=e.onChange||i,s=this;return T.util.animate({startValue:t.opacity,endValue:0,duration:this.FX_DURATION,onChange:function(e){t.set("opacity",e),s.requestRenderAll(),r()},onComplete:function(){s.remove(t),n()}}),this}}),T.util.object.extend(T.Object.prototype,{animate:function(){if(arguments[0]&&"object"==typeof arguments[0]){var t,e,i=[];for(t in arguments[0])i.push(t);for(var n=0,r=i.length;n<r;n++)t=i[n],e=n!==r-1,this._animate(t,arguments[0][t],arguments[1],e)}else this._animate.apply(this,arguments);return this},_animate:function(t,e,i,n){var r,s=this;e=e.toString(),i=i?T.util.object.clone(i):{},~t.indexOf(".")&&(r=t.split("."));var o=s.colorProperties.indexOf(t)>-1||r&&s.colorProperties.indexOf(r[1])>-1,a=r?this.get(r[0])[r[1]]:this.get(t);"from"in i||(i.from=a),o||(e=~e.indexOf("=")?a+parseFloat(e.replace("=","")):parseFloat(e));var h={startValue:i.from,endValue:e,byValue:i.by,easing:i.easing,duration:i.duration,abort:i.abort&&function(){return i.abort.call(s)},onChange:function(e,o,a){r?s[r[0]][r[1]]=e:s.set(t,e),n||i.onChange&&i.onChange(e,o,a)},onComplete:function(t,e,r){n||(s.setCoords(),i.onComplete&&i.onComplete(t,e,r))}};o?T.util.animateColor(h.startValue,h.endValue,h.duration,h):T.util.animate(h)}}),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.object.clone,r={x1:1,x2:1,y1:1,y2:1},s=e.StaticCanvas.supports("setLineDash");function o(t,e){var i=t.origin,n=t.axis1,r=t.axis2,s=t.dimension,o=e.nearest,a=e.center,h=e.farthest;return function(){switch(this.get(i)){case o:return Math.min(this.get(n),this.get(r));case a:return Math.min(this.get(n),this.get(r))+.5*this.get(s);case h:return Math.max(this.get(n),this.get(r))}}}e.Line?e.warn("fabric.Line is already defined"):(e.Line=e.util.createClass(e.Object,{type:"line",x1:0,y1:0,x2:0,y2:0,cacheProperties:e.Object.prototype.cacheProperties.concat("x1","x2","y1","y2"),initialize:function(t,e){t||(t=[0,0,0,0]),this.callSuper("initialize",e),this.set("x1",t[0]),this.set("y1",t[1]),this.set("x2",t[2]),this.set("y2",t[3]),this._setWidthHeight(e)},_setWidthHeight:function(t){t||(t={}),this.width=Math.abs(this.x2-this.x1),this.height=Math.abs(this.y2-this.y1),this.left="left"in t?t.left:this._getLeftToOriginX(),this.top="top"in t?t.top:this._getTopToOriginY()},_set:function(t,e){return this.callSuper("_set",t,e),void 0!==r[t]&&this._setWidthHeight(),this},_getLeftToOriginX:o({origin:"originX",axis1:"x1",axis2:"x2",dimension:"width"},{nearest:"left",center:"center",farthest:"right"}),_getTopToOriginY:o({origin:"originY",axis1:"y1",axis2:"y2",dimension:"height"},{nearest:"top",center:"center",farthest:"bottom"}),_render:function(t){if(t.beginPath(),!this.strokeDashArray||this.strokeDashArray&&s){var e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2)}t.lineWidth=this.strokeWidth;var i=t.strokeStyle;t.strokeStyle=this.stroke||t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=i},_renderDashedStroke:function(t){var i=this.calcLinePoints();t.beginPath(),e.util.drawDashedLine(t,i.x1,i.y1,i.x2,i.y2,this.strokeDashArray),t.closePath()},_findCenterFromElement:function(){return{x:(this.x1+this.x2)/2,y:(this.y1+this.y2)/2}},toObject:function(t){return i(this.callSuper("toObject",t),this.calcLinePoints())},_getNonTransformedDimensions:function(){var t=this.callSuper("_getNonTransformedDimensions");return"butt"===this.strokeLineCap&&(0===this.width&&(t.y-=this.strokeWidth),0===this.height&&(t.x-=this.strokeWidth)),t},calcLinePoints:function(){var t=this.x1<=this.x2?-1:1,e=this.y1<=this.y2?-1:1,i=t*this.width*.5,n=e*this.height*.5;return{x1:i,x2:t*this.width*-.5,y1:n,y2:e*this.height*-.5}},_toSVG:function(){var t=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="',t.x1,'" y1="',t.y1,'" x2="',t.x2,'" y2="',t.y2,'" />\n']}}),e.Line.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x1 y1 x2 y2".split(" ")),e.Line.fromElement=function(t,n,r){r=r||{};var s=e.parseAttributes(t,e.Line.ATTRIBUTE_NAMES),o=[s.x1||0,s.y1||0,s.x2||0,s.y2||0];n(new e.Line(o,i(s,r)))},e.Line.fromObject=function(t,i){var r=n(t,!0);r.points=[t.x1,t.y1,t.x2,t.y2],e.Object._fromObject("Line",r,(function(t){delete t.points,i&&i(t)}),"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=Math.PI;e.Circle?e.warn("fabric.Circle is already defined."):(e.Circle=e.util.createClass(e.Object,{type:"circle",radius:0,startAngle:0,endAngle:2*i,cacheProperties:e.Object.prototype.cacheProperties.concat("radius","startAngle","endAngle"),_set:function(t,e){return this.callSuper("_set",t,e),"radius"===t&&this.setRadius(e),this},toObject:function(t){return this.callSuper("toObject",["radius","startAngle","endAngle"].concat(t))},_toSVG:function(){var t,n=(this.endAngle-this.startAngle)%(2*i);if(0===n)t=["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',this.radius,'" />\n'];else{var r=e.util.cos(this.startAngle)*this.radius,s=e.util.sin(this.startAngle)*this.radius,o=e.util.cos(this.endAngle)*this.radius,a=e.util.sin(this.endAngle)*this.radius,h=n>i?"1":"0";t=['<path d="M '+r+" "+s," A "+this.radius+" "+this.radius," 0 ",+h+" 1"," "+o+" "+a,'" ',"COMMON_PARTS"," />\n"]}return t},_render:function(t){t.beginPath(),t.arc(0,0,this.radius,this.startAngle,this.endAngle,!1),this._renderPaintInOrder(t)},getRadiusX:function(){return this.get("radius")*this.get("scaleX")},getRadiusY:function(){return this.get("radius")*this.get("scaleY")},setRadius:function(t){return this.radius=t,this.set("width",2*t).set("height",2*t)}}),e.Circle.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("cx cy r".split(" ")),e.Circle.fromElement=function(t,i){var n,r=e.parseAttributes(t,e.Circle.ATTRIBUTE_NAMES);if(!("radius"in(n=r)&&n.radius>=0))throw new Error("value of `r` attribute is required and can not be negative");r.left=(r.left||0)-r.radius,r.top=(r.top||0)-r.radius,i(new e.Circle(r))},e.Circle.fromObject=function(t,i){return e.Object._fromObject("Circle",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Triangle?e.warn("fabric.Triangle is already defined"):(e.Triangle=e.util.createClass(e.Object,{type:"triangle",width:100,height:100,_render:function(t){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,i),t.lineTo(0,-i),t.lineTo(e,i),t.closePath(),this._renderPaintInOrder(t)},_renderDashedStroke:function(t){var i=this.width/2,n=this.height/2;t.beginPath(),e.util.drawDashedLine(t,-i,n,0,-n,this.strokeDashArray),e.util.drawDashedLine(t,0,-n,i,n,this.strokeDashArray),e.util.drawDashedLine(t,i,n,-i,n,this.strokeDashArray),t.closePath()},_toSVG:function(){var t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',[-t+" "+e,"0 "+-e,t+" "+e].join(","),'" />']}}),e.Triangle.fromObject=function(t,i){return e.Object._fromObject("Triangle",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=2*Math.PI;e.Ellipse?e.warn("fabric.Ellipse is already defined."):(e.Ellipse=e.util.createClass(e.Object,{type:"ellipse",rx:0,ry:0,cacheProperties:e.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(t){this.callSuper("initialize",t),this.set("rx",t&&t.rx||0),this.set("ry",t&&t.ry||0)},_set:function(t,e){switch(this.callSuper("_set",t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this},getRx:function(){return this.get("rx")*this.get("scaleX")},getRy:function(){return this.get("ry")*this.get("scaleY")},toObject:function(t){return this.callSuper("toObject",["rx","ry"].concat(t))},_toSVG:function(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" ','rx="',this.rx,'" ry="',this.ry,'" />\n']},_render:function(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,i,!1),t.restore(),this._renderPaintInOrder(t)}}),e.Ellipse.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("cx cy rx ry".split(" ")),e.Ellipse.fromElement=function(t,i){var n=e.parseAttributes(t,e.Ellipse.ATTRIBUTE_NAMES);n.left=(n.left||0)-n.rx,n.top=(n.top||0)-n.ry,i(new e.Ellipse(n))},e.Ellipse.fromObject=function(t,i){return e.Object._fromObject("Ellipse",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend;e.Rect?e.warn("fabric.Rect is already defined"):(e.Rect=e.util.createClass(e.Object,{stateProperties:e.Object.prototype.stateProperties.concat("rx","ry"),type:"rect",rx:0,ry:0,cacheProperties:e.Object.prototype.cacheProperties.concat("rx","ry"),initialize:function(t){this.callSuper("initialize",t),this._initRxRy()},_initRxRy:function(){this.rx&&!this.ry?this.ry=this.rx:this.ry&&!this.rx&&(this.rx=this.ry)},_render:function(t){var e=this.rx?Math.min(this.rx,this.width/2):0,i=this.ry?Math.min(this.ry,this.height/2):0,n=this.width,r=this.height,s=-this.width/2,o=-this.height/2,a=0!==e||0!==i,h=.4477152502;t.beginPath(),t.moveTo(s+e,o),t.lineTo(s+n-e,o),a&&t.bezierCurveTo(s+n-h*e,o,s+n,o+h*i,s+n,o+i),t.lineTo(s+n,o+r-i),a&&t.bezierCurveTo(s+n,o+r-h*i,s+n-h*e,o+r,s+n-e,o+r),t.lineTo(s+e,o+r),a&&t.bezierCurveTo(s+h*e,o+r,s,o+r-h*i,s,o+r-i),t.lineTo(s,o+i),a&&t.bezierCurveTo(s,o+h*i,s+h*e,o,s+e,o),t.closePath(),this._renderPaintInOrder(t)},_renderDashedStroke:function(t){var i=-this.width/2,n=-this.height/2,r=this.width,s=this.height;t.beginPath(),e.util.drawDashedLine(t,i,n,i+r,n,this.strokeDashArray),e.util.drawDashedLine(t,i+r,n,i+r,n+s,this.strokeDashArray),e.util.drawDashedLine(t,i+r,n+s,i,n+s,this.strokeDashArray),e.util.drawDashedLine(t,i,n+s,i,n,this.strokeDashArray),t.closePath()},toObject:function(t){return this.callSuper("toObject",["rx","ry"].concat(t))},_toSVG:function(){return["<rect ","COMMON_PARTS",'x="',-this.width/2,'" y="',-this.height/2,'" rx="',this.rx,'" ry="',this.ry,'" width="',this.width,'" height="',this.height,'" />\n']}}),e.Rect.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x y rx ry width height".split(" ")),e.Rect.fromElement=function(t,n,r){if(!t)return n(null);r=r||{};var s=e.parseAttributes(t,e.Rect.ATTRIBUTE_NAMES);s.left=s.left||0,s.top=s.top||0,s.height=s.height||0,s.width=s.width||0;var o=new e.Rect(i(r?e.util.object.clone(r):{},s));o.visible=o.visible&&o.width>0&&o.height>0,n(o)},e.Rect.fromObject=function(t,i){return e.Object._fromObject("Rect",t,i)})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.util.array.min,r=e.util.array.max,s=e.util.toFixed;e.Polyline?e.warn("fabric.Polyline is already defined"):(e.Polyline=e.util.createClass(e.Object,{type:"polyline",points:null,cacheProperties:e.Object.prototype.cacheProperties.concat("points"),initialize:function(t,e){e=e||{},this.points=t||[],this.callSuper("initialize",e),this._setPositionDimensions(e)},_setPositionDimensions:function(t){var e,i=this._calcDimensions(t);this.width=i.width,this.height=i.height,t.fromSVG||(e=this.translateToGivenOrigin({x:i.left-this.strokeWidth/2,y:i.top-this.strokeWidth/2},"left","top",this.originX,this.originY)),void 0===t.left&&(this.left=t.fromSVG?i.left:e.x),void 0===t.top&&(this.top=t.fromSVG?i.top:e.y),this.pathOffset={x:i.left+this.width/2,y:i.top+this.height/2}},_calcDimensions:function(){var t=this.points,e=n(t,"x")||0,i=n(t,"y")||0;return{left:e,top:i,width:(r(t,"x")||0)-e,height:(r(t,"y")||0)-i}},toObject:function(t){return i(this.callSuper("toObject",t),{points:this.points.concat()})},_toSVG:function(){for(var t=[],i=this.pathOffset.x,n=this.pathOffset.y,r=e.Object.NUM_FRACTION_DIGITS,o=0,a=this.points.length;o<a;o++)t.push(s(this.points[o].x-i,r),",",s(this.points[o].y-n,r)," ");return["<"+this.type+" ","COMMON_PARTS",'points="',t.join(""),'" />\n']},commonRender:function(t){var e,i=this.points.length,n=this.pathOffset.x,r=this.pathOffset.y;if(!i||isNaN(this.points[i-1].y))return!1;t.beginPath(),t.moveTo(this.points[0].x-n,this.points[0].y-r);for(var s=0;s<i;s++)e=this.points[s],t.lineTo(e.x-n,e.y-r);return!0},_render:function(t){this.commonRender(t)&&this._renderPaintInOrder(t)},_renderDashedStroke:function(t){var i,n;t.beginPath();for(var r=0,s=this.points.length;r<s;r++)i=this.points[r],n=this.points[r+1]||i,e.util.drawDashedLine(t,i.x,i.y,n.x,n.y,this.strokeDashArray)},complexity:function(){return this.get("points").length}}),e.Polyline.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(),e.Polyline.fromElementGenerator=function(t){return function(n,r,s){if(!n)return r(null);s||(s={});var o=e.parsePointsAttribute(n.getAttribute("points")),a=e.parseAttributes(n,e[t].ATTRIBUTE_NAMES);a.fromSVG=!0,r(new e[t](o,i(a,s)))}},e.Polyline.fromElement=e.Polyline.fromElementGenerator("Polyline"),e.Polyline.fromObject=function(t,i){return e.Object._fromObject("Polyline",t,i,"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Polygon?e.warn("fabric.Polygon is already defined"):(e.Polygon=e.util.createClass(e.Polyline,{type:"polygon",_render:function(t){this.commonRender(t)&&(t.closePath(),this._renderPaintInOrder(t))},_renderDashedStroke:function(t){this.callSuper("_renderDashedStroke",t),t.closePath()}}),e.Polygon.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(),e.Polygon.fromElement=e.Polyline.fromElementGenerator("Polygon"),e.Polygon.fromObject=function(t,i){return e.Object._fromObject("Polygon",t,i,"points")})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.array.min,n=e.util.array.max,r=e.util.object.extend,s=Object.prototype.toString,o=e.util.toFixed;e.Path?e.warn("fabric.Path is already defined"):(e.Path=e.util.createClass(e.Object,{type:"path",path:null,cacheProperties:e.Object.prototype.cacheProperties.concat("path","fillRule"),stateProperties:e.Object.prototype.stateProperties.concat("path"),initialize:function(t,i){i=i||{},this.callSuper("initialize",i),t||(t=[]);var n="[object Array]"===s.call(t);this.path=n?e.util.makePathSimpler(t):e.util.makePathSimpler(e.util.parsePath(t)),this.path&&e.Polyline.prototype._setPositionDimensions.call(this,i)},_renderPathCommands:function(t){var e,i=0,n=0,r=0,s=0,o=0,a=0,h=-this.pathOffset.x,l=-this.pathOffset.y;t.beginPath();for(var c=0,u=this.path.length;c<u;++c)switch((e=this.path[c])[0]){case"L":r=e[1],s=e[2],t.lineTo(r+h,s+l);break;case"M":i=r=e[1],n=s=e[2],t.moveTo(r+h,s+l);break;case"C":r=e[5],s=e[6],o=e[3],a=e[4],t.bezierCurveTo(e[1]+h,e[2]+l,o+h,a+l,r+h,s+l);break;case"Q":t.quadraticCurveTo(e[1]+h,e[2]+l,e[3]+h,e[4]+l),r=e[3],s=e[4],o=e[1],a=e[2];break;case"z":case"Z":r=i,s=n,t.closePath()}},_render:function(t){this._renderPathCommands(t),this._renderPaintInOrder(t)},toString:function(){return"#<fabric.Path ("+this.complexity()+'): { "top": '+this.top+', "left": '+this.left+" }>"},toObject:function(t){return r(this.callSuper("toObject",t),{path:this.path.map((function(t){return t.slice()}))})},toDatalessObject:function(t){var e=this.toObject(["sourcePath"].concat(t));return e.sourcePath&&delete e.path,e},_toSVG:function(){return["<path ","COMMON_PARTS",'d="',this.path.map((function(t){return t.join(" ")})).join(" "),'" stroke-linecap="round" ',"/>\n"]},_getOffsetTransform:function(){var t=e.Object.NUM_FRACTION_DIGITS;return" translate("+o(-this.pathOffset.x,t)+", "+o(-this.pathOffset.y,t)+")"},toClipPathSVG:function(t){var e=this._getOffsetTransform();return"\t"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})},toSVG:function(t){var e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})},complexity:function(){return this.path.length},_calcDimensions:function(){for(var t,r,s=[],o=[],a=0,h=0,l=0,c=0,u=0,f=this.path.length;u<f;++u){switch((t=this.path[u])[0]){case"L":l=t[1],c=t[2],r=[];break;case"M":a=l=t[1],h=c=t[2],r=[];break;case"C":r=e.util.getBoundsOfCurve(l,c,t[1],t[2],t[3],t[4],t[5],t[6]),l=t[5],c=t[6];break;case"Q":r=e.util.getBoundsOfCurve(l,c,t[1],t[2],t[1],t[2],t[3],t[4]),l=t[3],c=t[4];break;case"z":case"Z":l=a,c=h}r.forEach((function(t){s.push(t.x),o.push(t.y)})),s.push(l),o.push(c)}var d=i(s)||0,g=i(o)||0;return{left:d,top:g,width:(n(s)||0)-d,height:(n(o)||0)-g}}}),e.Path.fromObject=function(t,i){if("string"==typeof t.sourcePath){var n=t.sourcePath;e.loadSVGFromURL(n,(function(e){var n=e[0];n.setOptions(t),i&&i(n)}))}else e.Object._fromObject("Path",t,i,"path")},e.Path.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat(["d"]),e.Path.fromElement=function(t,i,n){var s=e.parseAttributes(t,e.Path.ATTRIBUTE_NAMES);s.fromSVG=!0,i(new e.Path(s.d,r(s,n)))})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.array.min,n=e.util.array.max;e.Group||(e.Group=e.util.createClass(e.Object,e.Collection,{type:"group",strokeWidth:0,subTargetCheck:!1,cacheProperties:[],useSetOnGroup:!1,initialize:function(t,e,i){e=e||{},this._objects=[],i&&this.callSuper("initialize",e),this._objects=t||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;if(i)this._updateObjectsACoords();else{var r=e&&e.centerPoint;void 0!==e.originX&&(this.originX=e.originX),void 0!==e.originY&&(this.originY=e.originY),r||this._calcBounds(),this._updateObjectsCoords(r),delete e.centerPoint,this.callSuper("initialize",e)}this.setCoords()},_updateObjectsACoords:function(){for(var t=this._objects.length;t--;)this._objects[t].setCoords(!0)},_updateObjectsCoords:function(t){t=t||this.getCenterPoint();for(var e=this._objects.length;e--;)this._updateObjectCoords(this._objects[e],t)},_updateObjectCoords:function(t,e){var i=t.left,n=t.top;t.set({left:i-e.x,top:n-e.y}),t.group=this,t.setCoords(!0)},toString:function(){return"#<fabric.Group: ("+this.complexity()+")>"},addWithUpdate:function(t){var i=!!this.group;return this._restoreObjectsState(),e.util.resetObjectTransform(this),t&&(i&&e.util.removeTransformFromObject(t,this.group.calcTransformMatrix()),this._objects.push(t),t.group=this,t._set("canvas",this.canvas)),this._calcBounds(),this._updateObjectsCoords(),this.dirty=!0,i?this.group.addWithUpdate():this.setCoords(),this},removeWithUpdate:function(t){return this._restoreObjectsState(),e.util.resetObjectTransform(this),this.remove(t),this._calcBounds(),this._updateObjectsCoords(),this.setCoords(),this.dirty=!0,this},_onObjectAdded:function(t){this.dirty=!0,t.group=this,t._set("canvas",this.canvas)},_onObjectRemoved:function(t){this.dirty=!0,delete t.group},_set:function(t,i){var n=this._objects.length;if(this.useSetOnGroup)for(;n--;)this._objects[n].setOnGroup(t,i);if("canvas"===t)for(;n--;)this._objects[n]._set(t,i);e.Object.prototype._set.call(this,t,i)},toObject:function(t){var i=this.includeDefaultValues,n=this._objects.map((function(e){var n=e.includeDefaultValues;e.includeDefaultValues=i;var r=e.toObject(t);return e.includeDefaultValues=n,r})),r=e.Object.prototype.toObject.call(this,t);return r.objects=n,r},toDatalessObject:function(t){var i,n=this.sourcePath;if(n)i=n;else{var r=this.includeDefaultValues;i=this._objects.map((function(e){var i=e.includeDefaultValues;e.includeDefaultValues=r;var n=e.toDatalessObject(t);return e.includeDefaultValues=i,n}))}var s=e.Object.prototype.toDatalessObject.call(this,t);return s.objects=i,s},render:function(t){this._transformDone=!0,this.callSuper("render",t),this._transformDone=!1},shouldCache:function(){var t=e.Object.prototype.shouldCache.call(this);if(t)for(var i=0,n=this._objects.length;i<n;i++)if(this._objects[i].willDrawShadow())return this.ownCaching=!1,!1;return t},willDrawShadow:function(){if(e.Object.prototype.willDrawShadow.call(this))return!0;for(var t=0,i=this._objects.length;t<i;t++)if(this._objects[t].willDrawShadow())return!0;return!1},isOnACache:function(){return this.ownCaching||this.group&&this.group.isOnACache()},drawObject:function(t){for(var e=0,i=this._objects.length;e<i;e++)this._objects[e].render(t);this._drawClipPath(t)},isCacheDirty:function(t){if(this.callSuper("isCacheDirty",t))return!0;if(!this.statefullCache)return!1;for(var e=0,i=this._objects.length;e<i;e++)if(this._objects[e].isCacheDirty(!0)){if(this._cacheCanvas){var n=this.cacheWidth/this.zoomX,r=this.cacheHeight/this.zoomY;this._cacheContext.clearRect(-n/2,-r/2,n,r)}return!0}return!1},_restoreObjectsState:function(){var t=this.calcOwnMatrix();return this._objects.forEach((function(i){e.util.addTransformToObject(i,t),delete i.group,i.setCoords()})),this},realizeTransform:function(t,i){return e.util.addTransformToObject(t,i),t},destroy:function(){return this._objects.forEach((function(t){t.set("dirty",!0)})),this._restoreObjectsState()},toActiveSelection:function(){if(this.canvas){var t=this._objects,i=this.canvas;this._objects=[];var n=this.toObject();delete n.objects;var r=new e.ActiveSelection([]);return r.set(n),r.type="activeSelection",i.remove(this),t.forEach((function(t){t.group=r,t.dirty=!0,i.add(t)})),r.canvas=i,r._objects=t,i._activeObject=r,r.setCoords(),r}},ungroupOnCanvas:function(){return this._restoreObjectsState()},setObjectsCoords:function(){return this.forEachObject((function(t){t.setCoords(!0)})),this},_calcBounds:function(t){for(var e,i,n,r,s=[],o=[],a=["tr","br","bl","tl"],h=0,l=this._objects.length,c=a.length;h<l;++h){for(n=(e=this._objects[h]).calcACoords(),r=0;r<c;r++)i=a[r],s.push(n[i].x),o.push(n[i].y);e.aCoords=n}this._getBounds(s,o,t)},_getBounds:function(t,r,s){var o=new e.Point(i(t),i(r)),a=new e.Point(n(t),n(r)),h=o.y||0,l=o.x||0,c=a.x-o.x||0,u=a.y-o.y||0;this.width=c,this.height=u,s||this.setPositionByOrigin({x:l,y:h},"left","top")},_toSVG:function(t){for(var e=["<g ","COMMON_PARTS"," >\n"],i=0,n=this._objects.length;i<n;i++)e.push("\t\t",this._objects[i].toSVG(t));return e.push("</g>\n"),e},getSvgStyles:function(){var t=void 0!==this.opacity&&1!==this.opacity?"opacity: "+this.opacity+";":"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")},toClipPathSVG:function(t){for(var e=[],i=0,n=this._objects.length;i<n;i++)e.push("\t",this._objects[i].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}}),e.Group.fromObject=function(t,i){var n=t.objects,r=e.util.object.clone(t,!0);delete r.objects,"string"!=typeof n?e.util.enlivenObjects(n,(function(n){e.util.enlivenObjects([t.clipPath],(function(r){var s=e.util.object.clone(t,!0);s.clipPath=r[0],delete s.objects,i&&i(new e.Group(n,s,!0))}))})):e.loadSVGFromURL(n,(function(s){var o=e.util.groupSVGElements(s,t,n);o.set(r),i&&i(o)}))})}(e),function(t){"use strict";var e=t.fabric||(t.fabric={});e.ActiveSelection||(e.ActiveSelection=e.util.createClass(e.Group,{type:"activeSelection",initialize:function(t,i){i=i||{},this._objects=t||[];for(var n=this._objects.length;n--;)this._objects[n].group=this;i.originX&&(this.originX=i.originX),i.originY&&(this.originY=i.originY),this._calcBounds(),this._updateObjectsCoords(),e.Object.prototype.initialize.call(this,i),this.setCoords()},toGroup:function(){var t=this._objects.concat();this._objects=[];var i=e.Object.prototype.toObject.call(this),n=new e.Group([]);if(delete i.type,n.set(i),t.forEach((function(t){t.canvas.remove(t),t.group=n})),n._objects=t,!this.canvas)return n;var r=this.canvas;return r.add(n),r._activeObject=n,n.setCoords(),n},onDeselect:function(){return this.destroy(),!1},toString:function(){return"#<fabric.ActiveSelection: ("+this.complexity()+")>"},shouldCache:function(){return!1},isOnACache:function(){return!1},_renderControls:function(t,e,i){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1,this.callSuper("_renderControls",t,e),void 0===(i=i||{}).hasControls&&(i.hasControls=!1),i.forActiveSelection=!0;for(var n=0,r=this._objects.length;n<r;n++)this._objects[n]._renderControls(t,i);t.restore()}}),e.ActiveSelection.fromObject=function(t,i){e.util.enlivenObjects(t.objects,(function(n){delete t.objects,i&&i(new e.ActiveSelection(n,t,!0))}))})}(e),function(t){"use strict";var e=T.util.object.extend;t.fabric||(t.fabric={}),t.fabric.Image?T.warn("fabric.Image is already defined."):(T.Image=T.util.createClass(T.Object,{type:"image",strokeWidth:0,srcFromAttribute:!1,_lastScaleX:1,_lastScaleY:1,_filterScalingX:1,_filterScalingY:1,minimumScaleTrigger:.5,stateProperties:T.Object.prototype.stateProperties.concat("cropX","cropY"),cacheProperties:T.Object.prototype.cacheProperties.concat("cropX","cropY"),cacheKey:"",cropX:0,cropY:0,imageSmoothing:!0,initialize:function(t,e){e||(e={}),this.filters=[],this.cacheKey="texture"+T.Object.__uid++,this.callSuper("initialize",e),this._initElement(t,e)},getElement:function(){return this._element||{}},setElement:function(t,e){return this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._element=t,this._originalElement=t,this._initConfig(e),0!==this.filters.length&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters(),this},removeTexture:function(t){var e=T.filterBackend;e&&e.evictCachesForKey&&e.evictCachesForKey(t)},dispose:function(){this.removeTexture(this.cacheKey),this.removeTexture(this.cacheKey+"_filtered"),this._cacheContext=void 0,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach(function(t){T.util.cleanUpJsdomNode(this[t]),this[t]=void 0}.bind(this))},getCrossOrigin:function(){return this._originalElement&&(this._originalElement.crossOrigin||null)},getOriginalSize:function(){var t=this.getElement();return{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}},_stroke:function(t){if(this.stroke&&0!==this.strokeWidth){var e=this.width/2,i=this.height/2;t.beginPath(),t.moveTo(-e,-i),t.lineTo(e,-i),t.lineTo(e,i),t.lineTo(-e,i),t.lineTo(-e,-i),t.closePath()}},_renderDashedStroke:function(t){var e=-this.width/2,i=-this.height/2,n=this.width,r=this.height;t.save(),this._setStrokeStyles(t,this),t.beginPath(),T.util.drawDashedLine(t,e,i,e+n,i,this.strokeDashArray),T.util.drawDashedLine(t,e+n,i,e+n,i+r,this.strokeDashArray),T.util.drawDashedLine(t,e+n,i+r,e,i+r,this.strokeDashArray),T.util.drawDashedLine(t,e,i+r,e,i,this.strokeDashArray),t.closePath(),t.restore()},toObject:function(t){var i=[];this.filters.forEach((function(t){t&&i.push(t.toObject())}));var n=e(this.callSuper("toObject",["cropX","cropY"].concat(t)),{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:i});return this.resizeFilter&&(n.resizeFilter=this.resizeFilter.toObject()),n},hasCrop:function(){return this.cropX||this.cropY||this.width<this._element.width||this.height<this._element.height},_toSVG:function(){var t,e=[],i=[],n=this._element,r=-this.width/2,s=-this.height/2,o="",a="";if(!n)return[];if(this.hasCrop()){var h=T.Object.__uid++;e.push('<clipPath id="imageCrop_'+h+'">\n','\t<rect x="'+r+'" y="'+s+'" width="'+this.width+'" height="'+this.height+'" />\n',"</clipPath>\n"),o=' clip-path="url(#imageCrop_'+h+')" '}if(this.imageSmoothing||(a='" image-rendering="optimizeSpeed'),i.push("\t<image ","COMMON_PARTS",'xlink:href="',this.getSvgSrc(!0),'" x="',r-this.cropX,'" y="',s-this.cropY,'" width="',n.width||n.naturalWidth,'" height="',n.height||n.height,a,'"',o,"></image>\n"),this.stroke||this.strokeDashArray){var l=this.fill;this.fill=null,t=["\t<rect ",'x="',r,'" y="',s,'" width="',this.width,'" height="',this.height,'" style="',this.getSvgStyles(),'"/>\n'],this.fill=l}return"fill"!==this.paintFirst?e.concat(t,i):e.concat(i,t)},getSrc:function(t){var e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src"):e.src:this.src||""},setSrc:function(t,e,i){return T.util.loadImage(t,(function(t,n){this.setElement(t,i),this._setWidthHeight(),e&&e(this,n)}),this,i&&i.crossOrigin),this},toString:function(){return'#<fabric.Image: { src: "'+this.getSrc()+'" }>'},applyResizeFilters:function(){var t=this.resizeFilter,e=this.minimumScaleTrigger,i=this.getTotalObjectScaling(),n=i.scaleX,r=i.scaleY,s=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||n>e&&r>e)return this._element=s,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=n,void(this._lastScaleY=r);T.filterBackend||(T.filterBackend=T.initFilterBackend());var o=T.util.createCanvasElement(),a=this._filteredEl?this.cacheKey+"_filtered":this.cacheKey,h=s.width,l=s.height;o.width=h,o.height=l,this._element=o,this._lastScaleX=t.scaleX=n,this._lastScaleY=t.scaleY=r,T.filterBackend.applyFilters([t],s,h,l,this._element,a),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height},applyFilters:function(t){if(t=(t=t||this.filters||[]).filter((function(t){return t&&!t.isNeutralState()})),this.set("dirty",!0),this.removeTexture(this.cacheKey+"_filtered"),0===t.length)return this._element=this._originalElement,this._filteredEl=null,this._filterScalingX=1,this._filterScalingY=1,this;var e=this._originalElement,i=e.naturalWidth||e.width,n=e.naturalHeight||e.height;if(this._element===this._originalElement){var r=T.util.createCanvasElement();r.width=i,r.height=n,this._element=r,this._filteredEl=r}else this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,i,n),this._lastScaleX=1,this._lastScaleY=1;return T.filterBackend||(T.filterBackend=T.initFilterBackend()),T.filterBackend.applyFilters(t,this._originalElement,i,n,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height),this},_render:function(t){T.util.setImageSmoothing(t,this.imageSmoothing),!0!==this.isMoving&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)},drawCacheOnCanvas:function(t){T.util.setImageSmoothing(t,this.imageSmoothing),T.Object.prototype.drawCacheOnCanvas.call(this,t)},shouldCache:function(){return this.needsItsOwnCache()},_renderFill:function(t){var e=this._element;if(e){var i=this._filterScalingX,n=this._filterScalingY,r=this.width,s=this.height,o=Math.min,a=Math.max,h=a(this.cropX,0),l=a(this.cropY,0),c=e.naturalWidth||e.width,u=e.naturalHeight||e.height,f=h*i,d=l*n,g=o(r*i,c-f),p=o(s*n,u-d),v=-r/2,m=-s/2,y=o(r,c/i-h),_=o(s,u/n-l);e&&t.drawImage(e,f,d,g,p,v,m,y,_)}},_needsResize:function(){var t=this.getTotalObjectScaling();return t.scaleX!==this._lastScaleX||t.scaleY!==this._lastScaleY},_resetWidthHeight:function(){this.set(this.getOriginalSize())},_initElement:function(t,e){this.setElement(T.util.getById(t),e),T.util.addClass(this.getElement(),T.Image.CSS_CANVAS)},_initConfig:function(t){t||(t={}),this.setOptions(t),this._setWidthHeight(t)},_initFilters:function(t,e){t&&t.length?T.util.enlivenObjects(t,(function(t){e&&e(t)}),"fabric.Image.filters"):e&&e()},_setWidthHeight:function(t){t||(t={});var e=this.getElement();this.width=t.width||e.naturalWidth||e.width||0,this.height=t.height||e.naturalHeight||e.height||0},parsePreserveAspectRatioAttribute:function(){var t,e=T.util.parsePreserveAspectRatioAttribute(this.preserveAspectRatio||""),i=this._element.width,n=this._element.height,r=1,s=1,o=0,a=0,h=0,l=0,c=this.width,u=this.height,f={width:c,height:u};return!e||"none"===e.alignX&&"none"===e.alignY?(r=c/i,s=u/n):("meet"===e.meetOrSlice&&(t=(c-i*(r=s=T.util.findScaleToFit(this._element,f)))/2,"Min"===e.alignX&&(o=-t),"Max"===e.alignX&&(o=t),t=(u-n*s)/2,"Min"===e.alignY&&(a=-t),"Max"===e.alignY&&(a=t)),"slice"===e.meetOrSlice&&(t=i-c/(r=s=T.util.findScaleToCover(this._element,f)),"Mid"===e.alignX&&(h=t/2),"Max"===e.alignX&&(h=t),t=n-u/s,"Mid"===e.alignY&&(l=t/2),"Max"===e.alignY&&(l=t),i=c/r,n=u/s)),{width:i,height:n,scaleX:r,scaleY:s,offsetLeft:o,offsetTop:a,cropX:h,cropY:l}}}),T.Image.CSS_CANVAS="canvas-img",T.Image.prototype.getSvgSrc=T.Image.prototype.getSrc,T.Image.fromObject=function(t,e){var i=T.util.object.clone(t);T.util.loadImage(i.src,(function(t,n){n?e&&e(null,!0):T.Image.prototype._initFilters.call(i,i.filters,(function(n){i.filters=n||[],T.Image.prototype._initFilters.call(i,[i.resizeFilter],(function(n){i.resizeFilter=n[0],T.util.enlivenObjects([i.clipPath],(function(n){i.clipPath=n[0];var r=new T.Image(t,i);e(r,!1)}))}))}))}),null,i.crossOrigin)},T.Image.fromURL=function(t,e,i){T.util.loadImage(t,(function(t,n){e&&e(new T.Image(t,i),n)}),null,i&&i.crossOrigin)},T.Image.ATTRIBUTE_NAMES=T.SHARED_ATTRIBUTES.concat("x y width height preserveAspectRatio xlink:href crossOrigin image-rendering".split(" ")),T.Image.fromElement=function(t,i,n){var r=T.parseAttributes(t,T.Image.ATTRIBUTE_NAMES);T.Image.fromURL(r["xlink:href"],i,e(n?T.util.object.clone(n):{},r))})}(e),T.util.object.extend(T.Object.prototype,{_getAngleValueForStraighten:function(){var t=this.angle%360;return t>0?90*Math.round((t-1)/90):90*Math.round(t/90)},straighten:function(){return this.rotate(this._getAngleValueForStraighten()),this},fxStraighten:function(t){var e=function(){},i=(t=t||{}).onComplete||e,n=t.onChange||e,r=this;return T.util.animate({startValue:this.get("angle"),endValue:this._getAngleValueForStraighten(),duration:this.FX_DURATION,onChange:function(t){r.rotate(t),n()},onComplete:function(){r.setCoords(),i()}}),this}}),T.util.object.extend(T.StaticCanvas.prototype,{straightenObject:function(t){return t.straighten(),this.requestRenderAll(),this},fxStraightenObject:function(t){return t.fxStraighten({onChange:this.requestRenderAllBound}),this}}),function(){"use strict";function t(t,e){var i="precision "+e+" float;\nvoid main(){}",n=t.createShader(t.FRAGMENT_SHADER);return t.shaderSource(n,i),t.compileShader(n),!!t.getShaderParameter(n,t.COMPILE_STATUS)}function e(t){t&&t.tileSize&&(this.tileSize=t.tileSize),this.setupGLContext(this.tileSize,this.tileSize),this.captureGPUInfo()}T.isWebglSupported=function(e){if(T.isLikelyNode)return!1;e=e||T.WebglFilterBackend.prototype.tileSize;var i=document.createElement("canvas"),n=i.getContext("webgl")||i.getContext("experimental-webgl"),r=!1;if(n){T.maxTextureSize=n.getParameter(n.MAX_TEXTURE_SIZE),r=T.maxTextureSize>=e;for(var s=["highp","mediump","lowp"],o=0;o<3;o++)if(t(n,s[o])){T.webGlPrecision=s[o];break}}return this.isSupported=r,r},T.WebglFilterBackend=e,e.prototype={tileSize:2048,resources:{},setupGLContext:function(t,e){this.dispose(),this.createWebGLCanvas(t,e),this.aPosition=new Float32Array([0,0,0,1,1,0,1,1]),this.chooseFastestCopyGLTo2DMethod(t,e)},chooseFastestCopyGLTo2DMethod:function(t,e){var i,n=void 0!==window.performance;try{new ImageData(1,1),i=!0}catch(t){i=!1}var r="undefined"!=typeof ArrayBuffer,s="undefined"!=typeof Uint8ClampedArray;if(n&&i&&r&&s){var o=T.util.createCanvasElement(),a=new ArrayBuffer(t*e*4);if(T.forceGLPutImageData)return this.imageBuffer=a,void(this.copyGLTo2D=P);var h,l,c={imageBuffer:a,destinationWidth:t,destinationHeight:e,targetCanvas:o};o.width=t,o.height=e,h=window.performance.now(),k.call(c,this.gl,c),l=window.performance.now()-h,h=window.performance.now(),P.call(c,this.gl,c),l>window.performance.now()-h?(this.imageBuffer=a,this.copyGLTo2D=P):this.copyGLTo2D=k}},createWebGLCanvas:function(t,e){var i=T.util.createCanvasElement();i.width=t,i.height=e;var n={alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1},r=i.getContext("webgl",n);r||(r=i.getContext("experimental-webgl",n)),r&&(r.clearColor(0,0,0,0),this.canvas=i,this.gl=r)},applyFilters:function(t,e,i,n,r,s){var o,a=this.gl;s&&(o=this.getCachedTexture(s,e));var h={originalWidth:e.width||e.originalWidth,originalHeight:e.height||e.originalHeight,sourceWidth:i,sourceHeight:n,destinationWidth:i,destinationHeight:n,context:a,sourceTexture:this.createTexture(a,i,n,!o&&e),targetTexture:this.createTexture(a,i,n),originalTexture:o||this.createTexture(a,i,n,!o&&e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:r},l=a.createFramebuffer();return a.bindFramebuffer(a.FRAMEBUFFER,l),t.forEach((function(t){t&&t.applyTo(h)})),function(t){var e=t.targetCanvas,i=e.width,n=e.height,r=t.destinationWidth,s=t.destinationHeight;i===r&&n===s||(e.width=r,e.height=s)}(h),this.copyGLTo2D(a,h),a.bindTexture(a.TEXTURE_2D,null),a.deleteTexture(h.sourceTexture),a.deleteTexture(h.targetTexture),a.deleteFramebuffer(l),r.getContext("2d").setTransform(1,0,0,1,0,0),h},dispose:function(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()},clearWebGLCaches:function(){this.programCache={},this.textureCache={}},createTexture:function(t,e,i,n){var r=t.createTexture();return t.bindTexture(t.TEXTURE_2D,r),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),n?t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,n):t.texImage2D(t.TEXTURE_2D,0,t.RGBA,e,i,0,t.RGBA,t.UNSIGNED_BYTE,null),r},getCachedTexture:function(t,e){if(this.textureCache[t])return this.textureCache[t];var i=this.createTexture(this.gl,e.width,e.height,e);return this.textureCache[t]=i,i},evictCachesForKey:function(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])},copyGLTo2D:k,captureGPUInfo:function(){if(this.gpuInfo)return this.gpuInfo;var t=this.gl,e={renderer:"",vendor:""};if(!t)return e;var i=t.getExtension("WEBGL_debug_renderer_info");if(i){var n=t.getParameter(i.UNMASKED_RENDERER_WEBGL),r=t.getParameter(i.UNMASKED_VENDOR_WEBGL);n&&(e.renderer=n.toLowerCase()),r&&(e.vendor=r.toLowerCase())}return this.gpuInfo=e,e}}}(),function(){"use strict";var t=function(){};function e(){}T.Canvas2dFilterBackend=e,e.prototype={evictCachesForKey:t,dispose:t,clearWebGLCaches:t,resources:{},applyFilters:function(t,e,i,n,r){var s=r.getContext("2d");s.drawImage(e,0,0,i,n);var o={sourceWidth:i,sourceHeight:n,imageData:s.getImageData(0,0,i,n),originalEl:e,originalImageData:s.getImageData(0,0,i,n),canvasEl:r,ctx:s,filterBackend:this};return t.forEach((function(t){t.applyTo(o)})),o.imageData.width===i&&o.imageData.height===n||(r.width=o.imageData.width,r.height=o.imageData.height),s.putImageData(o.imageData,0,0),o}}}(),T.Image=T.Image||{},T.Image.filters=T.Image.filters||{},T.Image.filters.BaseFilter=T.util.createClass({type:"BaseFilter",vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvoid main() {\nvTexCoord = aPosition;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:"precision highp float;\nvarying vec2 vTexCoord;\nuniform sampler2D uTexture;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\n}",initialize:function(t){t&&this.setOptions(t)},setOptions:function(t){for(var e in t)this[e]=t[e]},createProgram:function(t,e,i){e=e||this.fragmentSource,i=i||this.vertexSource,"highp"!==T.webGlPrecision&&(e=e.replace(/precision highp float/g,"precision "+T.webGlPrecision+" float"));var n=t.createShader(t.VERTEX_SHADER);if(t.shaderSource(n,i),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new Error("Vertex shader compile error for "+this.type+": "+t.getShaderInfoLog(n));var r=t.createShader(t.FRAGMENT_SHADER);if(t.shaderSource(r,e),t.compileShader(r),!t.getShaderParameter(r,t.COMPILE_STATUS))throw new Error("Fragment shader compile error for "+this.type+": "+t.getShaderInfoLog(r));var s=t.createProgram();if(t.attachShader(s,n),t.attachShader(s,r),t.linkProgram(s),!t.getProgramParameter(s,t.LINK_STATUS))throw new Error('Shader link error for "${this.type}" '+t.getProgramInfoLog(s));var o=this.getAttributeLocations(t,s),a=this.getUniformLocations(t,s)||{};return a.uStepW=t.getUniformLocation(s,"uStepW"),a.uStepH=t.getUniformLocation(s,"uStepH"),{program:s,attributeLocations:o,uniformLocations:a}},getAttributeLocations:function(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}},getUniformLocations:function(){return{}},sendAttributeData:function(t,e,i){var n=e.aPosition,r=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,r),t.enableVertexAttribArray(n),t.vertexAttribPointer(n,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,i,t.STATIC_DRAW)},_setupFrameBuffer:function(t){var e,i,n=t.context;t.passes>1?(e=t.destinationWidth,i=t.destinationHeight,t.sourceWidth===e&&t.sourceHeight===i||(n.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(n,e,i)),n.framebufferTexture2D(n.FRAMEBUFFER,n.COLOR_ATTACHMENT0,n.TEXTURE_2D,t.targetTexture,0)):(n.bindFramebuffer(n.FRAMEBUFFER,null),n.finish())},_swapTextures:function(t){t.passes--,t.pass++;var e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e},isNeutralState:function(){var t=this.mainParameter,e=T.Image.filters[this.type].prototype;if(t){if(Array.isArray(e[t])){for(var i=e[t].length;i--;)if(this[t][i]!==e[t][i])return!1;return!0}return e[t]===this[t]}return!1},applyTo:function(t){t.webgl?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},retrieveShader:function(t){return t.programCache.hasOwnProperty(this.type)||(t.programCache[this.type]=this.createProgram(t.context)),t.programCache[this.type]},applyToWebGL:function(t){var e=t.context,i=this.retrieveShader(t);0===t.pass&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(i.program),this.sendAttributeData(e,i.attributeLocations,t.aPosition),e.uniform1f(i.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(i.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,i.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)},bindAdditionalTexture:function(t,e,i){t.activeTexture(i),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)},unbindAdditionalTexture:function(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)},getMainParameter:function(){return this[this.mainParameter]},setMainParameter:function(t){this[this.mainParameter]=t},sendUniformData:function(){},createHelpLayer:function(t){if(!t.helpLayer){var e=document.createElement("canvas");e.width=t.sourceWidth,e.height=t.sourceHeight,t.helpLayer=e}},toObject:function(){var t={type:this.type},e=this.mainParameter;return e&&(t[e]=this[e]),t},toJSON:function(){return this.toObject()}}),T.Image.filters.BaseFilter.fromObject=function(t,e){var i=new T.Image.filters[t.type](t);return e&&e(i),i},function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.ColorMatrix=n(i.BaseFilter,{type:"ColorMatrix",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nuniform mat4 uColorMatrix;\nuniform vec4 uConstants;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor *= uColorMatrix;\ncolor += uConstants;\ngl_FragColor = color;\n}",matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],mainParameter:"matrix",colorsOnly:!0,initialize:function(t){this.callSuper("initialize",t),this.matrix=this.matrix.slice(0)},applyTo2d:function(t){var e,i,n,r,s,o=t.imageData.data,a=o.length,h=this.matrix,l=this.colorsOnly;for(s=0;s<a;s+=4)e=o[s],i=o[s+1],n=o[s+2],l?(o[s]=e*h[0]+i*h[1]+n*h[2]+255*h[4],o[s+1]=e*h[5]+i*h[6]+n*h[7]+255*h[9],o[s+2]=e*h[10]+i*h[11]+n*h[12]+255*h[14]):(r=o[s+3],o[s]=e*h[0]+i*h[1]+n*h[2]+r*h[3]+255*h[4],o[s+1]=e*h[5]+i*h[6]+n*h[7]+r*h[8]+255*h[9],o[s+2]=e*h[10]+i*h[11]+n*h[12]+r*h[13]+255*h[14],o[s+3]=e*h[15]+i*h[16]+n*h[17]+r*h[18]+255*h[19])},getUniformLocations:function(t,e){return{uColorMatrix:t.getUniformLocation(e,"uColorMatrix"),uConstants:t.getUniformLocation(e,"uConstants")}},sendUniformData:function(t,e){var i=this.matrix,n=[i[0],i[1],i[2],i[3],i[5],i[6],i[7],i[8],i[10],i[11],i[12],i[13],i[15],i[16],i[17],i[18]],r=[i[4],i[9],i[14],i[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,n),t.uniform4fv(e.uConstants,r)}}),e.Image.filters.ColorMatrix.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Brightness=n(i.BaseFilter,{type:"Brightness",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBrightness;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += uBrightness;\ngl_FragColor = color;\n}",brightness:0,mainParameter:"brightness",applyTo2d:function(t){if(0!==this.brightness){var e,i=t.imageData.data,n=i.length,r=Math.round(255*this.brightness);for(e=0;e<n;e+=4)i[e]=i[e]+r,i[e+1]=i[e+1]+r,i[e+2]=i[e+2]+r}},getUniformLocations:function(t,e){return{uBrightness:t.getUniformLocation(e,"uBrightness")}},sendUniformData:function(t,e){t.uniform1f(e.uBrightness,this.brightness)}}),e.Image.filters.Brightness.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.Convolute=r(n.BaseFilter,{type:"Convolute",opaque:!1,matrix:[0,0,0,0,1,0,0,0,0],fragmentSource:{Convolute_3_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_3_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[9];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 3.0; h+=1.0) {\nfor (float w = 0.0; w < 3.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_5_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_5_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[25];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 5.0; h+=1.0) {\nfor (float w = 0.0; w < 5.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_7_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_7_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[49];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 7.0; h+=1.0) {\nfor (float w = 0.0; w < 7.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}",Convolute_9_1:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 0);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];\n}\n}\ngl_FragColor = color;\n}",Convolute_9_0:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uMatrix[81];\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = vec4(0, 0, 0, 1);\nfor (float h = 0.0; h < 9.0; h+=1.0) {\nfor (float w = 0.0; w < 9.0; w+=1.0) {\nvec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));\ncolor.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];\n}\n}\nfloat alpha = texture2D(uTexture, vTexCoord).a;\ngl_FragColor = color;\ngl_FragColor.a = alpha;\n}"},retrieveShader:function(t){var e=Math.sqrt(this.matrix.length),i=this.type+"_"+e+"_"+(this.opaque?1:0),n=this.fragmentSource[i];return t.programCache.hasOwnProperty(i)||(t.programCache[i]=this.createProgram(t.context,n)),t.programCache[i]},applyTo2d:function(t){var e,i,n,r,s,o,a,h,l,c,u,f,d,g=t.imageData,p=g.data,v=this.matrix,m=Math.round(Math.sqrt(v.length)),y=Math.floor(m/2),_=g.width,x=g.height,b=t.ctx.createImageData(_,x),C=b.data,S=this.opaque?1:0;for(u=0;u<x;u++)for(c=0;c<_;c++){for(s=4*(u*_+c),e=0,i=0,n=0,r=0,d=0;d<m;d++)for(f=0;f<m;f++)o=c+f-y,(a=u+d-y)<0||a>=x||o<0||o>=_||(h=4*(a*_+o),l=v[d*m+f],e+=p[h]*l,i+=p[h+1]*l,n+=p[h+2]*l,S||(r+=p[h+3]*l));C[s]=e,C[s+1]=i,C[s+2]=n,C[s+3]=S?p[s+3]:r}t.imageData=b},getUniformLocations:function(t,e){return{uMatrix:t.getUniformLocation(e,"uMatrix"),uOpaque:t.getUniformLocation(e,"uOpaque"),uHalfSize:t.getUniformLocation(e,"uHalfSize"),uSize:t.getUniformLocation(e,"uSize")}},sendUniformData:function(t,e){t.uniform1fv(e.uMatrix,this.matrix)},toObject:function(){return i(this.callSuper("toObject"),{opaque:this.opaque,matrix:this.matrix})}}),e.Image.filters.Convolute.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Grayscale=n(i.BaseFilter,{type:"Grayscale",fragmentSource:{average:"precision highp float;\nuniform sampler2D uTexture;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat average = (color.r + color.b + color.g) / 3.0;\ngl_FragColor = vec4(average, average, average, color.a);\n}",lightness:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;\ngl_FragColor = vec4(average, average, average, col.a);\n}",luminosity:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uMode;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 col = texture2D(uTexture, vTexCoord);\nfloat average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;\ngl_FragColor = vec4(average, average, average, col.a);\n}"},mode:"average",mainParameter:"mode",applyTo2d:function(t){var e,i,n=t.imageData.data,r=n.length,s=this.mode;for(e=0;e<r;e+=4)"average"===s?i=(n[e]+n[e+1]+n[e+2])/3:"lightness"===s?i=(Math.min(n[e],n[e+1],n[e+2])+Math.max(n[e],n[e+1],n[e+2]))/2:"luminosity"===s&&(i=.21*n[e]+.72*n[e+1]+.07*n[e+2]),n[e]=i,n[e+1]=i,n[e+2]=i},retrieveShader:function(t){var e=this.type+"_"+this.mode;if(!t.programCache.hasOwnProperty(e)){var i=this.fragmentSource[this.mode];t.programCache[e]=this.createProgram(t.context,i)}return t.programCache[e]},getUniformLocations:function(t,e){return{uMode:t.getUniformLocation(e,"uMode")}},sendUniformData:function(t,e){t.uniform1i(e.uMode,1)},isNeutralState:function(){return!1}}),e.Image.filters.Grayscale.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Invert=n(i.BaseFilter,{type:"Invert",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform int uInvert;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nif (uInvert == 1) {\ngl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);\n} else {\ngl_FragColor = color;\n}\n}",invert:!0,mainParameter:"invert",applyTo2d:function(t){var e,i=t.imageData.data,n=i.length;for(e=0;e<n;e+=4)i[e]=255-i[e],i[e+1]=255-i[e+1],i[e+2]=255-i[e+2]},isNeutralState:function(){return!this.invert},getUniformLocations:function(t,e){return{uInvert:t.getUniformLocation(e,"uInvert")}},sendUniformData:function(t,e){t.uniform1i(e.uInvert,this.invert)}}),e.Image.filters.Invert.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.Noise=r(n.BaseFilter,{type:"Noise",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uStepH;\nuniform float uNoise;\nuniform float uSeed;\nvarying vec2 vTexCoord;\nfloat rand(vec2 co, float seed, float vScale) {\nreturn fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);\n}\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ncolor.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;\ngl_FragColor = color;\n}",mainParameter:"noise",noise:0,applyTo2d:function(t){if(0!==this.noise){var e,i,n=t.imageData.data,r=n.length,s=this.noise;for(e=0,r=n.length;e<r;e+=4)i=(.5-Math.random())*s,n[e]+=i,n[e+1]+=i,n[e+2]+=i}},getUniformLocations:function(t,e){return{uNoise:t.getUniformLocation(e,"uNoise"),uSeed:t.getUniformLocation(e,"uSeed")}},sendUniformData:function(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())},toObject:function(){return i(this.callSuper("toObject"),{noise:this.noise})}}),e.Image.filters.Noise.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Pixelate=n(i.BaseFilter,{type:"Pixelate",blocksize:4,mainParameter:"blocksize",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uBlocksize;\nuniform float uStepW;\nuniform float uStepH;\nvarying vec2 vTexCoord;\nvoid main() {\nfloat blockW = uBlocksize * uStepW;\nfloat blockH = uBlocksize * uStepW;\nint posX = int(vTexCoord.x / blockW);\nint posY = int(vTexCoord.y / blockH);\nfloat fposX = float(posX);\nfloat fposY = float(posY);\nvec2 squareCoords = vec2(fposX * blockW, fposY * blockH);\nvec4 color = texture2D(uTexture, squareCoords);\ngl_FragColor = color;\n}",applyTo2d:function(t){var e,i,n,r,s,o,a,h,l,c,u,f=t.imageData,d=f.data,g=f.height,p=f.width;for(i=0;i<g;i+=this.blocksize)for(n=0;n<p;n+=this.blocksize)for(r=d[e=4*i*p+4*n],s=d[e+1],o=d[e+2],a=d[e+3],c=Math.min(i+this.blocksize,g),u=Math.min(n+this.blocksize,p),h=i;h<c;h++)for(l=n;l<u;l++)d[e=4*h*p+4*l]=r,d[e+1]=s,d[e+2]=o,d[e+3]=a},isNeutralState:function(){return 1===this.blocksize},getUniformLocations:function(t,e){return{uBlocksize:t.getUniformLocation(e,"uBlocksize"),uStepW:t.getUniformLocation(e,"uStepW"),uStepH:t.getUniformLocation(e,"uStepH")}},sendUniformData:function(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}),e.Image.filters.Pixelate.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.extend,n=e.Image.filters,r=e.util.createClass;n.RemoveColor=r(n.BaseFilter,{type:"RemoveColor",color:"#FFFFFF",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uLow;\nuniform vec4 uHigh;\nvarying vec2 vTexCoord;\nvoid main() {\ngl_FragColor = texture2D(uTexture, vTexCoord);\nif(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {\ngl_FragColor.a = 0.0;\n}\n}",distance:.02,useAlpha:!1,applyTo2d:function(t){var i,n,r,s,o=t.imageData.data,a=255*this.distance,h=new e.Color(this.color).getSource(),l=[h[0]-a,h[1]-a,h[2]-a],c=[h[0]+a,h[1]+a,h[2]+a];for(i=0;i<o.length;i+=4)n=o[i],r=o[i+1],s=o[i+2],n>l[0]&&r>l[1]&&s>l[2]&&n<c[0]&&r<c[1]&&s<c[2]&&(o[i+3]=0)},getUniformLocations:function(t,e){return{uLow:t.getUniformLocation(e,"uLow"),uHigh:t.getUniformLocation(e,"uHigh")}},sendUniformData:function(t,i){var n=new e.Color(this.color).getSource(),r=parseFloat(this.distance),s=[0+n[0]/255-r,0+n[1]/255-r,0+n[2]/255-r,1],o=[n[0]/255+r,n[1]/255+r,n[2]/255+r,1];t.uniform4fv(i.uLow,s),t.uniform4fv(i.uHigh,o)},toObject:function(){return i(this.callSuper("toObject"),{color:this.color,distance:this.distance})}}),e.Image.filters.RemoveColor.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass,r={Brownie:[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0],Vintage:[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0],Kodachrome:[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0],Technicolor:[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0],Polaroid:[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],Sepia:[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0],BlackWhite:[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]};for(var s in r)i[s]=n(i.ColorMatrix,{type:s,matrix:r[s],mainParameter:!1,colorsOnly:!0}),e.Image.filters[s].fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric,i=e.Image.filters,n=e.util.createClass;i.BlendColor=n(i.BaseFilter,{type:"BlendColor",color:"#F95C63",mode:"multiply",alpha:1,fragmentSource:{multiply:"gl_FragColor.rgb *= uColor.rgb;\n",screen:"gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);\n",add:"gl_FragColor.rgb += uColor.rgb;\n",diff:"gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);\n",subtract:"gl_FragColor.rgb -= uColor.rgb;\n",lighten:"gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);\n",darken:"gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);\n",exclusion:"gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);\n",overlay:"if (uColor.r < 0.5) {\ngl_FragColor.r *= 2.0 * uColor.r;\n} else {\ngl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);\n}\nif (uColor.g < 0.5) {\ngl_FragColor.g *= 2.0 * uColor.g;\n} else {\ngl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);\n}\nif (uColor.b < 0.5) {\ngl_FragColor.b *= 2.0 * uColor.b;\n} else {\ngl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);\n}\n",tint:"gl_FragColor.rgb *= (1.0 - uColor.a);\ngl_FragColor.rgb += uColor.rgb;\n"},buildSource:function(t){return"precision highp float;\nuniform sampler2D uTexture;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\ngl_FragColor = color;\nif (color.a > 0.0) {\n"+this.fragmentSource[t]+"}\n}"},retrieveShader:function(t){var e,i=this.type+"_"+this.mode;return t.programCache.hasOwnProperty(i)||(e=this.buildSource(this.mode),t.programCache[i]=this.createProgram(t.context,e)),t.programCache[i]},applyTo2d:function(t){var i,n,r,s,o,a,h,l=t.imageData.data,c=l.length,u=1-this.alpha;i=(h=new e.Color(this.color).getSource())[0]*this.alpha,n=h[1]*this.alpha,r=h[2]*this.alpha;for(var f=0;f<c;f+=4)switch(s=l[f],o=l[f+1],a=l[f+2],this.mode){case"multiply":l[f]=s*i/255,l[f+1]=o*n/255,l[f+2]=a*r/255;break;case"screen":l[f]=255-(255-s)*(255-i)/255,l[f+1]=255-(255-o)*(255-n)/255,l[f+2]=255-(255-a)*(255-r)/255;break;case"add":l[f]=s+i,l[f+1]=o+n,l[f+2]=a+r;break;case"diff":case"difference":l[f]=Math.abs(s-i),l[f+1]=Math.abs(o-n),l[f+2]=Math.abs(a-r);break;case"subtract":l[f]=s-i,l[f+1]=o-n,l[f+2]=a-r;break;case"darken":l[f]=Math.min(s,i),l[f+1]=Math.min(o,n),l[f+2]=Math.min(a,r);break;case"lighten":l[f]=Math.max(s,i),l[f+1]=Math.max(o,n),l[f+2]=Math.max(a,r);break;case"overlay":l[f]=i<128?2*s*i/255:255-2*(255-s)*(255-i)/255,l[f+1]=n<128?2*o*n/255:255-2*(255-o)*(255-n)/255,l[f+2]=r<128?2*a*r/255:255-2*(255-a)*(255-r)/255;break;case"exclusion":l[f]=i+s-2*i*s/255,l[f+1]=n+o-2*n*o/255,l[f+2]=r+a-2*r*a/255;break;case"tint":l[f]=i+s*u,l[f+1]=n+o*u,l[f+2]=r+a*u}},getUniformLocations:function(t,e){return{uColor:t.getUniformLocation(e,"uColor")}},sendUniformData:function(t,i){var n=new e.Color(this.color).getSource();n[0]=this.alpha*n[0]/255,n[1]=this.alpha*n[1]/255,n[2]=this.alpha*n[2]/255,n[3]=this.alpha,t.uniform4fv(i.uColor,n)},toObject:function(){return{type:this.type,color:this.color,mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendColor.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric,i=e.Image.filters,n=e.util.createClass;i.BlendImage=n(i.BaseFilter,{type:"BlendImage",image:null,mode:"multiply",alpha:1,vertexSource:"attribute vec2 aPosition;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nuniform mat3 uTransformMatrix;\nvoid main() {\nvTexCoord = aPosition;\nvTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;\ngl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);\n}",fragmentSource:{multiply:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.rgba *= color2.rgba;\ngl_FragColor = color;\n}",mask:"precision highp float;\nuniform sampler2D uTexture;\nuniform sampler2D uImage;\nuniform vec4 uColor;\nvarying vec2 vTexCoord;\nvarying vec2 vTexCoord2;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec4 color2 = texture2D(uImage, vTexCoord2);\ncolor.a = color2.a;\ngl_FragColor = color;\n}"},retrieveShader:function(t){var e=this.type+"_"+this.mode,i=this.fragmentSource[this.mode];return t.programCache.hasOwnProperty(e)||(t.programCache[e]=this.createProgram(t.context,i)),t.programCache[e]},applyToWebGL:function(t){var e=t.context,i=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,i,e.TEXTURE1),this.callSuper("applyToWebGL",t),this.unbindAdditionalTexture(e,e.TEXTURE1)},createTexture:function(t,e){return t.getCachedTexture(e.cacheKey,e._element)},calculateMatrix:function(){var t=this.image,e=t._element.width,i=t._element.height;return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/i,1]},applyTo2d:function(t){var i,n,r,s,o,a,h,l,c,u,f,d=t.imageData,g=t.filterBackend.resources,p=d.data,v=p.length,m=d.width,y=d.height,_=this.image;g.blendImage||(g.blendImage=e.util.createCanvasElement()),u=(c=g.blendImage).getContext("2d"),c.width!==m||c.height!==y?(c.width=m,c.height=y):u.clearRect(0,0,m,y),u.setTransform(_.scaleX,0,0,_.scaleY,_.left,_.top),u.drawImage(_._element,0,0,m,y),f=u.getImageData(0,0,m,y).data;for(var x=0;x<v;x+=4)switch(o=p[x],a=p[x+1],h=p[x+2],l=p[x+3],i=f[x],n=f[x+1],r=f[x+2],s=f[x+3],this.mode){case"multiply":p[x]=o*i/255,p[x+1]=a*n/255,p[x+2]=h*r/255,p[x+3]=l*s/255;break;case"mask":p[x+3]=s}},getUniformLocations:function(t,e){return{uTransformMatrix:t.getUniformLocation(e,"uTransformMatrix"),uImage:t.getUniformLocation(e,"uImage")}},sendUniformData:function(t,e){var i=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,i)},toObject:function(){return{type:this.type,image:this.image&&this.image.toObject(),mode:this.mode,alpha:this.alpha}}}),e.Image.filters.BlendImage.fromObject=function(t,i){e.Image.fromObject(t.image,(function(n){var r=e.util.object.clone(t);r.image=n,i(new e.Image.filters.BlendImage(r))}))}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=Math.pow,n=Math.floor,r=Math.sqrt,s=Math.abs,o=Math.round,a=Math.sin,h=Math.ceil,l=e.Image.filters,c=e.util.createClass;l.Resize=c(l.BaseFilter,{type:"Resize",resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3,getUniformLocations:function(t,e){return{uDelta:t.getUniformLocation(e,"uDelta"),uTaps:t.getUniformLocation(e,"uTaps")}},sendUniformData:function(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)},retrieveShader:function(t){var e=this.getFilterWindow(),i=this.type+"_"+e;if(!t.programCache.hasOwnProperty(i)){var n=this.generateShader(e);t.programCache[i]=this.createProgram(t.context,n)}return t.programCache[i]},getFilterWindow:function(){var t=this.tempScale;return Math.ceil(this.lanczosLobes/t)},getTaps:function(){for(var t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,i=this.getFilterWindow(),n=new Array(i),r=1;r<=i;r++)n[r-1]=t(r*e);return n},generateShader:function(t){for(var e=new Array(t),i=this.fragmentSourceTOP,n=1;n<=t;n++)e[n-1]=n+".0 * uDelta";return i+="uniform float uTaps["+t+"];\n",i+="void main() {\n",i+="  vec4 color = texture2D(uTexture, vTexCoord);\n",i+="  float sum = 1.0;\n",e.forEach((function(t,e){i+="  color += texture2D(uTexture, vTexCoord + "+t+") * uTaps["+e+"];\n",i+="  color += texture2D(uTexture, vTexCoord - "+t+") * uTaps["+e+"];\n",i+="  sum += 2.0 * uTaps["+e+"];\n"})),i+="  gl_FragColor = color / sum;\n",i+="}"},fragmentSourceTOP:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\n",applyTo:function(t){t.webgl?(t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t),t.sourceHeight=t.destinationHeight):this.applyTo2d(t)},isNeutralState:function(){return 1===this.scaleX&&1===this.scaleY},lanczosCreate:function(t){return function(e){if(e>=t||e<=-t)return 0;if(e<1.1920929e-7&&e>-1.1920929e-7)return 1;var i=(e*=Math.PI)/t;return a(e)/e*a(i)/i}},applyTo2d:function(t){var e=t.imageData,i=this.scaleX,n=this.scaleY;this.rcpScaleX=1/i,this.rcpScaleY=1/n;var r,s=e.width,a=e.height,h=o(s*i),l=o(a*n);"sliceHack"===this.resizeType?r=this.sliceByTwo(t,s,a,h,l):"hermite"===this.resizeType?r=this.hermiteFastResize(t,s,a,h,l):"bilinear"===this.resizeType?r=this.bilinearFiltering(t,s,a,h,l):"lanczos"===this.resizeType&&(r=this.lanczosResize(t,s,a,h,l)),t.imageData=r},sliceByTwo:function(t,i,r,s,o){var a,h,l=t.imageData,c=.5,u=!1,f=!1,d=i*c,g=r*c,p=e.filterBackend.resources,v=0,m=0,y=i,_=0;for(p.sliceByTwo||(p.sliceByTwo=document.createElement("canvas")),((a=p.sliceByTwo).width<1.5*i||a.height<r)&&(a.width=1.5*i,a.height=r),(h=a.getContext("2d")).clearRect(0,0,1.5*i,r),h.putImageData(l,0,0),s=n(s),o=n(o);!u||!f;)i=d,r=g,s<n(d*c)?d=n(d*c):(d=s,u=!0),o<n(g*c)?g=n(g*c):(g=o,f=!0),h.drawImage(a,v,m,i,r,y,_,d,g),v=y,m=_,_+=g;return h.getImageData(v,m,s,o)},lanczosResize:function(t,e,o,a,l){var c=t.imageData.data,u=t.ctx.createImageData(a,l),f=u.data,d=this.lanczosCreate(this.lanczosLobes),g=this.rcpScaleX,p=this.rcpScaleY,v=2/this.rcpScaleX,m=2/this.rcpScaleY,y=h(g*this.lanczosLobes/2),_=h(p*this.lanczosLobes/2),x={},b={},C={};return function t(h){var S,w,T,O,k,P,D,j,E,A,M;for(b.x=(h+.5)*g,C.x=n(b.x),S=0;S<l;S++){for(b.y=(S+.5)*p,C.y=n(b.y),k=0,P=0,D=0,j=0,E=0,w=C.x-y;w<=C.x+y;w++)if(!(w<0||w>=e)){A=n(1e3*s(w-b.x)),x[A]||(x[A]={});for(var F=C.y-_;F<=C.y+_;F++)F<0||F>=o||(M=n(1e3*s(F-b.y)),x[A][M]||(x[A][M]=d(r(i(A*v,2)+i(M*m,2))/1e3)),(T=x[A][M])>0&&(k+=T,P+=T*c[O=4*(F*e+w)],D+=T*c[O+1],j+=T*c[O+2],E+=T*c[O+3]))}f[O=4*(S*a+h)]=P/k,f[O+1]=D/k,f[O+2]=j/k,f[O+3]=E/k}return++h<a?t(h):u}(0)},bilinearFiltering:function(t,e,i,r,s){var o,a,h,l,c,u,f,d,g,p=0,v=this.rcpScaleX,m=this.rcpScaleY,y=4*(e-1),_=t.imageData.data,x=t.ctx.createImageData(r,s),b=x.data;for(h=0;h<s;h++)for(l=0;l<r;l++)for(c=v*l-(o=n(v*l)),u=m*h-(a=n(m*h)),g=4*(a*e+o),f=0;f<4;f++)d=_[g+f]*(1-c)*(1-u)+_[g+4+f]*c*(1-u)+_[g+y+f]*u*(1-c)+_[g+y+4+f]*c*u,b[p++]=d;return x},hermiteFastResize:function(t,e,i,o,a){for(var l=this.rcpScaleX,c=this.rcpScaleY,u=h(l/2),f=h(c/2),d=t.imageData.data,g=t.ctx.createImageData(o,a),p=g.data,v=0;v<a;v++)for(var m=0;m<o;m++){for(var y=4*(m+v*o),_=0,x=0,b=0,C=0,S=0,w=0,T=0,O=(v+.5)*c,k=n(v*c);k<(v+1)*c;k++)for(var P=s(O-(k+.5))/f,D=(m+.5)*l,j=P*P,E=n(m*l);E<(m+1)*l;E++){var A=s(D-(E+.5))/u,M=r(j+A*A);M>1&&M<-1||(_=2*M*M*M-3*M*M+1)>0&&(T+=_*d[3+(A=4*(E+k*e))],b+=_,d[A+3]<255&&(_=_*d[A+3]/250),C+=_*d[A],S+=_*d[A+1],w+=_*d[A+2],x+=_)}p[y]=C/x,p[y+1]=S/x,p[y+2]=w/x,p[y+3]=T/b}return g},toObject:function(){return{type:this.type,scaleX:this.scaleX,scaleY:this.scaleY,resizeType:this.resizeType,lanczosLobes:this.lanczosLobes}}}),e.Image.filters.Resize.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Contrast=n(i.BaseFilter,{type:"Contrast",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uContrast;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));\ncolor.rgb = contrastF * (color.rgb - 0.5) + 0.5;\ngl_FragColor = color;\n}",contrast:0,mainParameter:"contrast",applyTo2d:function(t){if(0!==this.contrast){var e,i=t.imageData.data,n=i.length,r=Math.floor(255*this.contrast),s=259*(r+255)/(255*(259-r));for(e=0;e<n;e+=4)i[e]=s*(i[e]-128)+128,i[e+1]=s*(i[e+1]-128)+128,i[e+2]=s*(i[e+2]-128)+128}},getUniformLocations:function(t,e){return{uContrast:t.getUniformLocation(e,"uContrast")}},sendUniformData:function(t,e){t.uniform1f(e.uContrast,this.contrast)}}),e.Image.filters.Contrast.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Saturation=n(i.BaseFilter,{type:"Saturation",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform float uSaturation;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nfloat rgMax = max(color.r, color.g);\nfloat rgbMax = max(rgMax, color.b);\ncolor.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;\ncolor.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;\ncolor.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;\ngl_FragColor = color;\n}",saturation:0,mainParameter:"saturation",applyTo2d:function(t){if(0!==this.saturation){var e,i,n=t.imageData.data,r=n.length,s=-this.saturation;for(e=0;e<r;e+=4)i=Math.max(n[e],n[e+1],n[e+2]),n[e]+=i!==n[e]?(i-n[e])*s:0,n[e+1]+=i!==n[e+1]?(i-n[e+1])*s:0,n[e+2]+=i!==n[e+2]?(i-n[e+2])*s:0}},getUniformLocations:function(t,e){return{uSaturation:t.getUniformLocation(e,"uSaturation")}},sendUniformData:function(t,e){t.uniform1f(e.uSaturation,-this.saturation)}}),e.Image.filters.Saturation.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Blur=n(i.BaseFilter,{type:"Blur",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec2 uDelta;\nvarying vec2 vTexCoord;\nconst float nSamples = 15.0;\nvec3 v3offset = vec3(12.9898, 78.233, 151.7182);\nfloat random(vec3 scale) {\nreturn fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);\n}\nvoid main() {\nvec4 color = vec4(0.0);\nfloat total = 0.0;\nfloat offset = random(v3offset);\nfor (float t = -nSamples; t <= nSamples; t++) {\nfloat percent = (t + offset - 0.5) / nSamples;\nfloat weight = 1.0 - abs(percent);\ncolor += texture2D(uTexture, vTexCoord + uDelta * percent) * weight;\ntotal += weight;\n}\ngl_FragColor = color / total;\n}",blur:0,mainParameter:"blur",applyTo:function(t){t.webgl?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)},applyTo2d:function(t){t.imageData=this.simpleBlur(t)},simpleBlur:function(t){var i,n,r=t.filterBackend.resources,s=t.imageData.width,o=t.imageData.height;r.blurLayer1||(r.blurLayer1=e.util.createCanvasElement(),r.blurLayer2=e.util.createCanvasElement()),i=r.blurLayer1,n=r.blurLayer2,i.width===s&&i.height===o||(n.width=i.width=s,n.height=i.height=o);var a,h,l,c,u=i.getContext("2d"),f=n.getContext("2d"),d=.06*this.blur*.5;for(u.putImageData(t.imageData,0,0),f.clearRect(0,0,s,o),c=-15;c<=15;c++)l=d*(h=c/15)*s+(a=(Math.random()-.5)/4),f.globalAlpha=1-Math.abs(h),f.drawImage(i,l,a),u.drawImage(n,0,0),f.globalAlpha=1,f.clearRect(0,0,n.width,n.height);for(c=-15;c<=15;c++)l=d*(h=c/15)*o+(a=(Math.random()-.5)/4),f.globalAlpha=1-Math.abs(h),f.drawImage(i,a,l),u.drawImage(n,0,0),f.globalAlpha=1,f.clearRect(0,0,n.width,n.height);t.ctx.drawImage(i,0,0);var g=t.ctx.getImageData(0,0,i.width,i.height);return u.globalAlpha=1,u.clearRect(0,0,i.width,i.height),g},getUniformLocations:function(t,e){return{delta:t.getUniformLocation(e,"uDelta")}},sendUniformData:function(t,e){var i=this.chooseRightDelta();t.uniform2fv(e.delta,i)},chooseRightDelta:function(){var t,e=1,i=[0,0];return this.horizontal?this.aspectRatio>1&&(e=1/this.aspectRatio):this.aspectRatio<1&&(e=this.aspectRatio),t=e*this.blur*.12,this.horizontal?i[0]=t:i[1]=t,i}}),i.Blur.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Gamma=n(i.BaseFilter,{type:"Gamma",fragmentSource:"precision highp float;\nuniform sampler2D uTexture;\nuniform vec3 uGamma;\nvarying vec2 vTexCoord;\nvoid main() {\nvec4 color = texture2D(uTexture, vTexCoord);\nvec3 correction = (1.0 / uGamma);\ncolor.r = pow(color.r, correction.r);\ncolor.g = pow(color.g, correction.g);\ncolor.b = pow(color.b, correction.b);\ngl_FragColor = color;\ngl_FragColor.rgb *= color.a;\n}",gamma:[1,1,1],mainParameter:"gamma",initialize:function(t){this.gamma=[1,1,1],i.BaseFilter.prototype.initialize.call(this,t)},applyTo2d:function(t){var e,i=t.imageData.data,n=this.gamma,r=i.length,s=1/n[0],o=1/n[1],a=1/n[2];for(this.rVals||(this.rVals=new Uint8Array(256),this.gVals=new Uint8Array(256),this.bVals=new Uint8Array(256)),e=0,r=256;e<r;e++)this.rVals[e]=255*Math.pow(e/255,s),this.gVals[e]=255*Math.pow(e/255,o),this.bVals[e]=255*Math.pow(e/255,a);for(e=0,r=i.length;e<r;e+=4)i[e]=this.rVals[i[e]],i[e+1]=this.gVals[i[e+1]],i[e+2]=this.bVals[i[e+2]]},getUniformLocations:function(t,e){return{uGamma:t.getUniformLocation(e,"uGamma")}},sendUniformData:function(t,e){t.uniform3fv(e.uGamma,this.gamma)}}),e.Image.filters.Gamma.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.Composed=n(i.BaseFilter,{type:"Composed",subFilters:[],initialize:function(t){this.callSuper("initialize",t),this.subFilters=this.subFilters.slice(0)},applyTo:function(t){t.passes+=this.subFilters.length-1,this.subFilters.forEach((function(e){e.applyTo(t)}))},toObject:function(){return e.util.object.extend(this.callSuper("toObject"),{subFilters:this.subFilters.map((function(t){return t.toObject()}))})},isNeutralState:function(){return!this.subFilters.some((function(t){return!t.isNeutralState()}))}}),e.Image.filters.Composed.fromObject=function(t,i){var n=(t.subFilters||[]).map((function(t){return new e.Image.filters[t.type](t)})),r=new e.Image.filters.Composed({subFilters:n});return i&&i(r),r}}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.Image.filters,n=e.util.createClass;i.HueRotation=n(i.ColorMatrix,{type:"HueRotation",rotation:0,mainParameter:"rotation",calculateMatrix:function(){var t=this.rotation*Math.PI,i=e.util.cos(t),n=e.util.sin(t),r=1/3,s=Math.sqrt(r)*n,o=1-i;this.matrix=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],this.matrix[0]=i+o/3,this.matrix[1]=r*o-s,this.matrix[2]=r*o+s,this.matrix[5]=r*o+s,this.matrix[6]=i+r*o,this.matrix[7]=r*o-s,this.matrix[10]=r*o-s,this.matrix[11]=r*o+s,this.matrix[12]=i+r*o},isNeutralState:function(t){return this.calculateMatrix(),i.BaseFilter.prototype.isNeutralState.call(this,t)},applyTo:function(t){this.calculateMatrix(),i.BaseFilter.prototype.applyTo.call(this,t)}}),e.Image.filters.HueRotation.fromObject=e.Image.filters.BaseFilter.fromObject}(e),function(t){"use strict";var e=t.fabric||(t.fabric={}),i=e.util.object.clone;if(e.Text)e.warn("fabric.Text is already defined");else{var n="fontFamily fontWeight fontSize text underline overline linethrough textAlign fontStyle lineHeight textBackgroundColor charSpacing styles path".split(" ");e.Text=e.util.createClass(e.Object,{_dimensionAffectingProps:["fontSize","fontWeight","fontFamily","fontStyle","lineHeight","text","charSpacing","textAlign","styles","path"],_reNewline:/\r?\n/,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,type:"text",fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:"left",fontStyle:"normal",lineHeight:1.16,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},textBackgroundColor:"",stateProperties:e.Object.prototype.stateProperties.concat(n),cacheProperties:e.Object.prototype.cacheProperties.concat(n),stroke:null,shadow:null,path:null,_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.315,overline:-.88},_fontSizeMult:1.13,charSpacing:0,styles:null,_measuringContext:null,deltaY:0,direction:"ltr",_styleProperties:["stroke","strokeWidth","fill","fontFamily","fontSize","fontWeight","fontStyle","underline","overline","linethrough","deltaY","textBackgroundColor"],__charBounds:[],CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,initialize:function(t,e){this.styles=e&&e.styles||{},this.text=t,this.__skipDimension=!0,this.callSuper("initialize",e),this.path&&this.setPathInfo(),this.__skipDimension=!1,this.initDimensions(),this.setCoords(),this.setupState({propertySet:"_dimensionAffectingProps"})},setPathInfo:function(){var t=this.path;t&&(t.segmentsInfo=e.util.getPathSegmentsInfo(t.path))},getMeasuringContext:function(){return e._measuringContext||(e._measuringContext=this.canvas&&this.canvas.contextCache||e.util.createCanvasElement().getContext("2d")),e._measuringContext},_splitText:function(){var t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t},initDimensions:function(){this.__skipDimension||(this._splitText(),this._clearCache(),this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.saveState({propertySet:"_dimensionAffectingProps"}))},enlargeSpaces:function(){for(var t,e,i,n,r,s,o,a=0,h=this._textLines.length;a<h;a++)if(("justify"===this.textAlign||a!==h-1&&!this.isEndOfWrapping(a))&&(n=0,r=this._textLines[a],(e=this.getLineWidth(a))<this.width&&(o=this.textLines[a].match(this._reSpacesAndTabs)))){i=o.length,t=(this.width-e)/i;for(var l=0,c=r.length;l<=c;l++)s=this.__charBounds[a][l],this._reSpaceAndTab.test(r[l])?(s.width+=t,s.kernedWidth+=t,s.left+=n,n+=t):s.left+=n}},isEndOfWrapping:function(t){return t===this._textLines.length-1},missingNewlineOffset:function(){return 1},toString:function(){return"#<fabric.Text ("+this.complexity()+'): { "text": "'+this.text+'", "fontFamily": "'+this.fontFamily+'" }>'},_getCacheCanvasDimensions:function(){var t=this.callSuper("_getCacheCanvasDimensions"),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t},_render:function(t){this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")},_renderText:function(t){"stroke"===this.paintFirst?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))},_setTextStyles:function(t,e,i){t.textBaseline="alphabetic",t.font=this._getFontDeclaration(e,i)},calcTextWidth:function(){for(var t=this.getLineWidth(0),e=1,i=this._textLines.length;e<i;e++){var n=this.getLineWidth(e);n>t&&(t=n)}return t},_renderTextLine:function(t,e,i,n,r,s){this._renderChars(t,e,i,n,r,s)},_renderTextLinesBackground:function(t){if(this.textBackgroundColor||this.styleHas("textBackgroundColor")){for(var e,i,n,r,s,o,a,h=t.fillStyle,l=this._getLeftOffset(),c=this._getTopOffset(),u=0,f=0,d=this.path,g=0,p=this._textLines.length;g<p;g++)if(e=this.getHeightOfLine(g),this.textBackgroundColor||this.styleHas("textBackgroundColor",g)){n=this._textLines[g],i=this._getLineLeftOffset(g),f=0,u=0,r=this.getValueOfPropertyAt(g,0,"textBackgroundColor");for(var v=0,m=n.length;v<m;v++)s=this.__charBounds[g][v],o=this.getValueOfPropertyAt(g,v,"textBackgroundColor"),d?(t.save(),t.translate(s.renderLeft,s.renderTop),t.rotate(s.angle),t.fillStyle=o,o&&t.fillRect(-s.width/2,-e/this.lineHeight*(1-this._fontSizeFraction),s.width,e/this.lineHeight),t.restore()):o!==r?(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=r,r&&t.fillRect(a,c,f,e/this.lineHeight),u=s.left,f=s.width,r=o):f+=s.kernedWidth;o&&!d&&(a=l+i+u,"rtl"===this.direction&&(a=this.width-a-f),t.fillStyle=o,t.fillRect(a,c,f,e/this.lineHeight)),c+=e}else c+=e;t.fillStyle=h,this._removeShadow(t)}},getFontCache:function(t){var i=t.fontFamily.toLowerCase();e.charWidthsCache[i]||(e.charWidthsCache[i]={});var n=e.charWidthsCache[i],r=t.fontStyle.toLowerCase()+"_"+(t.fontWeight+"").toLowerCase();return n[r]||(n[r]={}),n[r]},_measureChar:function(t,e,i,n){var r,s,o,a,h=this.getFontCache(e),l=i+t,c=this._getFontDeclaration(e)===this._getFontDeclaration(n),u=e.fontSize/this.CACHE_FONT_SIZE;if(i&&void 0!==h[i]&&(o=h[i]),void 0!==h[t]&&(a=r=h[t]),c&&void 0!==h[l]&&(a=(s=h[l])-o),void 0===r||void 0===o||void 0===s){var f=this.getMeasuringContext();this._setTextStyles(f,e,!0)}return void 0===r&&(a=r=f.measureText(t).width,h[t]=r),void 0===o&&c&&i&&(o=f.measureText(i).width,h[i]=o),c&&void 0===s&&(s=f.measureText(l).width,h[l]=s,a=s-o),{width:r*u,kernedWidth:a*u}},getHeightOfChar:function(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")},measureLine:function(t){var e=this._measureLine(t);return 0!==this.charSpacing&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e},_measureLine:function(t){var i,n,r,s,o,a,h=0,l=this._textLines[t],c=new Array(l.length),u=0,f=this.path;for(this.__charBounds[t]=c,f&&(o=e.util.getPointOnPath(f.path,0,f.segmentsInfo),a=f.segmentsInfo[f.segmentsInfo.length-1].length,o.x+=f.pathOffset.x,o.y+=f.pathOffset.y),i=0;i<l.length;i++)n=l[i],s=this._getGraphemeBox(n,t,i,r),f&&(u>a&&(u%=a),this._setGraphemeOnPath(u,s,o)),c[i]=s,h+=s.kernedWidth,u+=s.kernedWidth,r=n;return c[i]={left:s?s.left+s.width:0,width:0,kernedWidth:0,height:this.fontSize},{width:h,numOfSpaces:0}},_setGraphemeOnPath:function(t,i,n){var r=t+i.kernedWidth/2,s=this.path,o=e.util.getPointOnPath(s.path,r,s.segmentsInfo);i.renderLeft=o.x-n.x,i.renderTop=o.y-n.y,i.angle=o.angle},_getGraphemeBox:function(t,e,i,n,r){var s,o=this.getCompleteStyleDeclaration(e,i),a=n?this.getCompleteStyleDeclaration(e,i-1):{},h=this._measureChar(t,o,n,a),l=h.kernedWidth,c=h.width;0!==this.charSpacing&&(c+=s=this._getWidthOfCharSpacing(),l+=s);var u={width:c,left:0,height:o.fontSize,kernedWidth:l,deltaY:o.deltaY};if(i>0&&!r){var f=this.__charBounds[e][i-1];u.left=f.left+f.width+h.kernedWidth-h.width}return u},getHeightOfLine:function(t){if(this.__lineHeights[t])return this.__lineHeights[t];for(var e=this._textLines[t],i=this.getHeightOfChar(t,0),n=1,r=e.length;n<r;n++)i=Math.max(this.getHeightOfChar(t,n),i);return this.__lineHeights[t]=i*this.lineHeight*this._fontSizeMult},calcTextHeight:function(){for(var t,e=0,i=0,n=this._textLines.length;i<n;i++)t=this.getHeightOfLine(i),e+=i===n-1?t/this.lineHeight:t;return e},_getLeftOffset:function(){return"ltr"===this.direction?-this.width/2:this.width/2},_getTopOffset:function(){return-this.height/2},_renderTextCommon:function(t,e){t.save();for(var i=0,n=this._getLeftOffset(),r=this._getTopOffset(),s=0,o=this._textLines.length;s<o;s++){var a=this.getHeightOfLine(s),h=a/this.lineHeight,l=this._getLineLeftOffset(s);this._renderTextLine(e,t,this._textLines[s],n+l,r+i+h,s),i+=a}t.restore()},_renderTextFill:function(t){(this.fill||this.styleHas("fill"))&&this._renderTextCommon(t,"fillText")},_renderTextStroke:function(t){(this.stroke&&0!==this.strokeWidth||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())},_renderChars:function(t,e,i,n,r,s){var o,a,h,l,c,u=this.getHeightOfLine(s),f=-1!==this.textAlign.indexOf("justify"),d="",g=0,p=this.path,v=!f&&0===this.charSpacing&&this.isEmptyStyles(s)&&!p,m="ltr"===this.direction,y="ltr"===this.direction?1:-1;if(e.save(),r-=u*this._fontSizeFraction/this.lineHeight,v)return e.canvas.setAttribute("dir",m?"ltr":"rtl"),e.direction=m?"ltr":"rtl",e.textAlign=m?"left":"right",this._renderChar(t,e,s,0,i.join(""),n,r,u),void e.restore();for(var _=0,x=i.length-1;_<=x;_++)l=_===x||this.charSpacing||p,d+=i[_],h=this.__charBounds[s][_],0===g?(n+=y*(h.kernedWidth-h.width),g+=h.width):g+=h.kernedWidth,f&&!l&&this._reSpaceAndTab.test(i[_])&&(l=!0),l||(o=o||this.getCompleteStyleDeclaration(s,_),a=this.getCompleteStyleDeclaration(s,_+1),l=this._hasStyleChanged(o,a)),l&&(p?(e.save(),e.translate(h.renderLeft,h.renderTop),e.rotate(h.angle),this._renderChar(t,e,s,_,d,-g/2,0,u),e.restore()):(c=n,e.canvas.setAttribute("dir",m?"ltr":"rtl"),e.direction=m?"ltr":"rtl",e.textAlign=m?"left":"right",this._renderChar(t,e,s,_,d,c,r,u)),d="",o=a,n+=y*g,g=0);e.restore()},_applyPatternGradientTransformText:function(t){var i,n=e.util.createCanvasElement(),r=this.width+this.strokeWidth,s=this.height+this.strokeWidth;return n.width=r,n.height=s,(i=n.getContext("2d")).beginPath(),i.moveTo(0,0),i.lineTo(r,0),i.lineTo(r,s),i.lineTo(0,s),i.closePath(),i.translate(r/2,s/2),i.fillStyle=t.toLive(i),this._applyPatternGradientTransform(i,t),i.fill(),i.createPattern(n,"no-repeat")},handleFiller:function(t,e,i){var n,r;return i.toLive?"percentage"===i.gradientUnits||i.gradientTransform||i.patternTransform?(n=-this.width/2,r=-this.height/2,t.translate(n,r),t[e]=this._applyPatternGradientTransformText(i),{offsetX:n,offsetY:r}):(t[e]=i.toLive(t,this),this._applyPatternGradientTransform(t,i)):(t[e]=i,{offsetX:0,offsetY:0})},_setStrokeStyles:function(t,e){return t.lineWidth=e.strokeWidth,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",e.stroke)},_setFillStyles:function(t,e){return this.handleFiller(t,"fillStyle",e.fill)},_renderChar:function(t,e,i,n,r,s,o){var a,h,l=this._getStyleDeclaration(i,n),c=this.getCompleteStyleDeclaration(i,n),u="fillText"===t&&c.fill,f="strokeText"===t&&c.stroke&&c.strokeWidth;(f||u)&&(e.save(),u&&(a=this._setFillStyles(e,c)),f&&(h=this._setStrokeStyles(e,c)),e.font=this._getFontDeclaration(c),l&&l.textBackgroundColor&&this._removeShadow(e),l&&l.deltaY&&(o+=l.deltaY),u&&e.fillText(r,s-a.offsetX,o-a.offsetY),f&&e.strokeText(r,s-h.offsetX,o-h.offsetY),e.restore())},setSuperscript:function(t,e){return this._setScript(t,e,this.superscript)},setSubscript:function(t,e){return this._setScript(t,e,this.subscript)},_setScript:function(t,e,i){var n=this.get2DCursorLocation(t,!0),r=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"fontSize"),s=this.getValueOfPropertyAt(n.lineIndex,n.charIndex,"deltaY"),o={fontSize:r*i.size,deltaY:s+r*i.baseline};return this.setSelectionStyles(o,t,e),this},_hasStyleChanged:function(t,e){return t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.deltaY!==e.deltaY},_hasStyleChangedForSvg:function(t,e){return this._hasStyleChanged(t,e)||t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough},_getLineLeftOffset:function(t){var e=this.getLineWidth(t),i=this.width-e,n=this.textAlign,r=this.direction,s=0,o=this.isEndOfWrapping(t);return"justify"===n||"justify-center"===n&&!o||"justify-right"===n&&!o||"justify-left"===n&&!o?0:("center"===n&&(s=i/2),"right"===n&&(s=i),"justify-center"===n&&(s=i/2),"justify-right"===n&&(s=i),"rtl"===r&&(s-=i),s)},_clearCache:function(){this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]},_shouldClearDimensionCache:function(){var t=this._forceClearCache;return t||(t=this.hasStateChanged("_dimensionAffectingProps")),t&&(this.dirty=!0,this._forceClearCache=!1),t},getLineWidth:function(t){return this.__lineWidths[t]?this.__lineWidths[t]:(e=""===this._textLines[t]?0:this.measureLine(t).width,this.__lineWidths[t]=e,e);var e},_getWidthOfCharSpacing:function(){return 0!==this.charSpacing?this.fontSize*this.charSpacing/1e3:0},getValueOfPropertyAt:function(t,e,i){var n=this._getStyleDeclaration(t,e);return n&&void 0!==n[i]?n[i]:this[i]},_renderTextDecoration:function(t,e){if(this[e]||this.styleHas(e)){for(var i,n,r,s,o,a,h,l,c,u,f,d,g,p,v,m,y=this._getLeftOffset(),_=this._getTopOffset(),x=this.path,b=this._getWidthOfCharSpacing(),C=this.offsets[e],S=0,w=this._textLines.length;S<w;S++)if(i=this.getHeightOfLine(S),this[e]||this.styleHas(e,S)){h=this._textLines[S],p=i/this.lineHeight,s=this._getLineLeftOffset(S),u=0,f=0,l=this.getValueOfPropertyAt(S,0,e),m=this.getValueOfPropertyAt(S,0,"fill"),c=_+p*(1-this._fontSizeFraction),n=this.getHeightOfChar(S,0),o=this.getValueOfPropertyAt(S,0,"deltaY");for(var T=0,O=h.length;T<O;T++)if(d=this.__charBounds[S][T],g=this.getValueOfPropertyAt(S,T,e),v=this.getValueOfPropertyAt(S,T,"fill"),r=this.getHeightOfChar(S,T),a=this.getValueOfPropertyAt(S,T,"deltaY"),x&&g&&v)t.save(),t.fillStyle=m,t.translate(d.renderLeft,d.renderTop),t.rotate(d.angle),t.fillRect(-d.kernedWidth/2,C*r+a,d.kernedWidth,this.fontSize/15),t.restore();else if((g!==l||v!==m||r!==n||a!==o)&&f>0){var k=y+s+u;"rtl"===this.direction&&(k=this.width-k-f),l&&m&&(t.fillStyle=m,t.fillRect(k,c+C*n+o,f,this.fontSize/15)),u=d.left,f=d.width,l=g,m=v,n=r,o=a}else f+=d.kernedWidth;k=y+s+u,"rtl"===this.direction&&(k=this.width-k-f),t.fillStyle=v,g&&v&&t.fillRect(k,c+C*n+o,f-b,this.fontSize/15),_+=i}else _+=i;this._removeShadow(t)}},_getFontDeclaration:function(t,i){var n=t||this,r=this.fontFamily,s=e.Text.genericFonts.indexOf(r.toLowerCase())>-1,o=void 0===r||r.indexOf("'")>-1||r.indexOf(",")>-1||r.indexOf('"')>-1||s?n.fontFamily:'"'+n.fontFamily+'"';return[e.isLikelyNode?n.fontWeight:n.fontStyle,e.isLikelyNode?n.fontStyle:n.fontWeight,i?this.CACHE_FONT_SIZE+"px":n.fontSize+"px",o].join(" ")},render:function(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._shouldClearDimensionCache()&&this.initDimensions(),this.callSuper("render",t)))},_splitTextIntoLines:function(t){for(var i=t.split(this._reNewline),n=new Array(i.length),r=["\n"],s=[],o=0;o<i.length;o++)n[o]=e.util.string.graphemeSplit(i[o]),s=s.concat(n[o],r);return s.pop(),{_unwrappedLines:n,lines:i,graphemeText:s,graphemeLines:n}},toObject:function(t){var e=["text","fontSize","fontWeight","fontFamily","fontStyle","lineHeight","underline","overline","linethrough","textAlign","textBackgroundColor","charSpacing","path","direction"].concat(t),n=this.callSuper("toObject",e);return n.styles=i(this.styles,!0),n.path=this.path&&this.path.toObject(),n},set:function(t,e){this.callSuper("set",t,e);var i=!1,n=!1;if("object"==typeof t)for(var r in t)"path"===r&&this.setPathInfo(),i=i||-1!==this._dimensionAffectingProps.indexOf(r),n=n||"path"===r;else i=-1!==this._dimensionAffectingProps.indexOf(t),n="path"===t;return n&&this.setPathInfo(),i&&(this.initDimensions(),this.setCoords()),this},complexity:function(){return 1}}),e.Text.ATTRIBUTE_NAMES=e.SHARED_ATTRIBUTES.concat("x y dx dy font-family font-style font-weight font-size letter-spacing text-decoration text-anchor".split(" ")),e.Text.DEFAULT_SVG_FONT_SIZE=16,e.Text.fromElement=function(t,n,r){if(!t)return n(null);var s=e.parseAttributes(t,e.Text.ATTRIBUTE_NAMES),o=s.textAnchor||"left";if((r=e.util.object.extend(r?i(r):{},s)).top=r.top||0,r.left=r.left||0,s.textDecoration){var a=s.textDecoration;-1!==a.indexOf("underline")&&(r.underline=!0),-1!==a.indexOf("overline")&&(r.overline=!0),-1!==a.indexOf("line-through")&&(r.linethrough=!0),delete r.textDecoration}"dx"in s&&(r.left+=s.dx),"dy"in s&&(r.top+=s.dy),"fontSize"in r||(r.fontSize=e.Text.DEFAULT_SVG_FONT_SIZE);var h="";"textContent"in t?h=t.textContent:"firstChild"in t&&null!==t.firstChild&&"data"in t.firstChild&&null!==t.firstChild.data&&(h=t.firstChild.data),h=h.replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," ");var l=r.strokeWidth;r.strokeWidth=0;var c=new e.Text(h,r),u=c.getScaledHeight()/c.height,f=((c.height+c.strokeWidth)*c.lineHeight-c.height)*u,d=c.getScaledHeight()+f,g=0;"center"===o&&(g=c.getScaledWidth()/2),"right"===o&&(g=c.getScaledWidth()),c.set({left:c.left-g,top:c.top-(d-c.fontSize*(.07+c._fontSizeFraction))/c.lineHeight,strokeWidth:void 0!==l?l:1}),n(c)},e.Text.fromObject=function(t,n){var r=i(t),s=t.path;return delete r.path,e.Object._fromObject("Text",r,(function(t){s?e.Object._fromObject("Path",s,(function(e){t.set("path",e),n(t)}),"path"):n(t)}),"text")},e.Text.genericFonts=["sans-serif","serif","cursive","fantasy","monospace"],e.util.createAccessors&&e.util.createAccessors(e.Text)}}(e),T.util.object.extend(T.Text.prototype,{isEmptyStyles:function(t){if(!this.styles)return!0;if(void 0!==t&&!this.styles[t])return!0;var e=void 0===t?this.styles:{line:this.styles[t]};for(var i in e)for(var n in e[i])for(var r in e[i][n])return!1;return!0},styleHas:function(t,e){if(!this.styles||!t||""===t)return!1;if(void 0!==e&&!this.styles[e])return!1;var i=void 0===e?this.styles:{0:this.styles[e]};for(var n in i)for(var r in i[n])if(void 0!==i[n][r][t])return!0;return!1},cleanStyle:function(t){if(!this.styles||!t||""===t)return!1;var e,i,n=this.styles,r=0,s=!0,o=0;for(var a in n){for(var h in e=0,n[a]){var l;r++,(l=n[a][h]).hasOwnProperty(t)?(i?l[t]!==i&&(s=!1):i=l[t],l[t]===this[t]&&delete l[t]):s=!1,0!==Object.keys(l).length?e++:delete n[a][h]}0===e&&delete n[a]}for(var c=0;c<this._textLines.length;c++)o+=this._textLines[c].length;s&&r===o&&(this[t]=i,this.removeStyle(t))},removeStyle:function(t){if(this.styles&&t&&""!==t){var e,i,n,r=this.styles;for(i in r){for(n in e=r[i])delete e[n][t],0===Object.keys(e[n]).length&&delete e[n];0===Object.keys(e).length&&delete r[i]}}},_extendStyles:function(t,e){var i=this.get2DCursorLocation(t);this._getLineStyle(i.lineIndex)||this._setLineStyle(i.lineIndex),this._getStyleDeclaration(i.lineIndex,i.charIndex)||this._setStyleDeclaration(i.lineIndex,i.charIndex,{}),T.util.object.extend(this._getStyleDeclaration(i.lineIndex,i.charIndex),e)},get2DCursorLocation:function(t,e){void 0===t&&(t=this.selectionStart);for(var i=e?this._unwrappedTextLines:this._textLines,n=i.length,r=0;r<n;r++){if(t<=i[r].length)return{lineIndex:r,charIndex:t};t-=i[r].length+this.missingNewlineOffset(r)}return{lineIndex:r-1,charIndex:i[r-1].length<t?i[r-1].length:t}},getSelectionStyles:function(t,e,i){void 0===t&&(t=this.selectionStart||0),void 0===e&&(e=this.selectionEnd||t);for(var n=[],r=t;r<e;r++)n.push(this.getStyleAtPosition(r,i));return n},getStyleAtPosition:function(t,e){var i=this.get2DCursorLocation(t);return(e?this.getCompleteStyleDeclaration(i.lineIndex,i.charIndex):this._getStyleDeclaration(i.lineIndex,i.charIndex))||{}},setSelectionStyles:function(t,e,i){void 0===e&&(e=this.selectionStart||0),void 0===i&&(i=this.selectionEnd||e);for(var n=e;n<i;n++)this._extendStyles(n,t);return this._forceClearCache=!0,this},_getStyleDeclaration:function(t,e){var i=this.styles&&this.styles[t];return i?i[e]:null},getCompleteStyleDeclaration:function(t,e){for(var i,n=this._getStyleDeclaration(t,e)||{},r={},s=0;s<this._styleProperties.length;s++)r[i=this._styleProperties[s]]=void 0===n[i]?this[i]:n[i];return r},_setStyleDeclaration:function(t,e,i){this.styles[t][e]=i},_deleteStyleDeclaration:function(t,e){delete this.styles[t][e]},_getLineStyle:function(t){return!!this.styles[t]},_setLineStyle:function(t){this.styles[t]={}},_deleteLineStyle:function(t){delete this.styles[t]}}),function(){function t(t){t.textDecoration&&(t.textDecoration.indexOf("underline")>-1&&(t.underline=!0),t.textDecoration.indexOf("line-through")>-1&&(t.linethrough=!0),t.textDecoration.indexOf("overline")>-1&&(t.overline=!0),delete t.textDecoration)}T.IText=T.util.createClass(T.Text,T.Observable,{type:"i-text",selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,_reSpace:/\s|\n/,_currentCursorOpacity:0,_selectionDirection:null,_abortCursorAnimation:!1,__widthOfSpace:[],inCompositionMode:!1,initialize:function(t,e){this.callSuper("initialize",t,e),this.initBehavior()},setSelectionStart:function(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)},setSelectionEnd:function(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)},_updateAndFire:function(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()},_fireSelectionChanged:function(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})},initDimensions:function(){this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this.callSuper("initDimensions")},render:function(t){this.clearContextTop(),this.callSuper("render",t),this.cursorOffsetCache={},this.renderCursorOrSelection()},_render:function(t){this.callSuper("_render",t)},clearContextTop:function(t){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var e=this.canvas.contextTop,i=this.canvas.viewportTransform;e.save(),e.transform(i[0],i[1],i[2],i[3],i[4],i[5]),this.transform(e),this._clearTextArea(e),t||e.restore()}},renderCursorOrSelection:function(){if(this.isEditing&&this.canvas&&this.canvas.contextTop){var t=this._getCursorBoundaries(),e=this.canvas.contextTop;this.clearContextTop(!0),this.selectionStart===this.selectionEnd?this.renderCursor(t,e):this.renderSelection(t,e),e.restore()}},_clearTextArea:function(t){var e=this.width+4,i=this.height+4;t.clearRect(-e/2,-i/2,e,i)},_getCursorBoundaries:function(t){void 0===t&&(t=this.selectionStart);var e=this._getLeftOffset(),i=this._getTopOffset(),n=this._getCursorBoundariesOffsets(t);return{left:e,top:i,leftOffset:n.left,topOffset:n.top}},_getCursorBoundariesOffsets:function(t){if(this.cursorOffsetCache&&"top"in this.cursorOffsetCache)return this.cursorOffsetCache;var e,i,n,r,s=0,o=0,a=this.get2DCursorLocation(t);n=a.charIndex,i=a.lineIndex;for(var h=0;h<i;h++)s+=this.getHeightOfLine(h);e=this._getLineLeftOffset(i);var l=this.__charBounds[i][n];return l&&(o=l.left),0!==this.charSpacing&&n===this._textLines[i].length&&(o-=this._getWidthOfCharSpacing()),r={top:s,left:e+(o>0?o:0)},"rtl"===this.direction&&(r.left*=-1),this.cursorOffsetCache=r,this.cursorOffsetCache},renderCursor:function(t,e){var i=this.get2DCursorLocation(),n=i.lineIndex,r=i.charIndex>0?i.charIndex-1:0,s=this.getValueOfPropertyAt(n,r,"fontSize"),o=this.scaleX*this.canvas.getZoom(),a=this.cursorWidth/o,h=t.topOffset,l=this.getValueOfPropertyAt(n,r,"deltaY");h+=(1-this._fontSizeFraction)*this.getHeightOfLine(n)/this.lineHeight-s*(1-this._fontSizeFraction),this.inCompositionMode&&this.renderSelection(t,e),e.fillStyle=this.cursorColor||this.getValueOfPropertyAt(n,r,"fill"),e.globalAlpha=this.__isMousedown?1:this._currentCursorOpacity,e.fillRect(t.left+t.leftOffset-a/2,h+t.top+l,a,s)},renderSelection:function(t,e){for(var i=this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,n=this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd,r=-1!==this.textAlign.indexOf("justify"),s=this.get2DCursorLocation(i),o=this.get2DCursorLocation(n),a=s.lineIndex,h=o.lineIndex,l=s.charIndex<0?0:s.charIndex,c=o.charIndex<0?0:o.charIndex,u=a;u<=h;u++){var f,d=this._getLineLeftOffset(u)||0,g=this.getHeightOfLine(u),p=0,v=0;if(u===a&&(p=this.__charBounds[a][l].left),u>=a&&u<h)v=r&&!this.isEndOfWrapping(u)?this.width:this.getLineWidth(u)||5;else if(u===h)if(0===c)v=this.__charBounds[h][c].left;else{var m=this._getWidthOfCharSpacing();v=this.__charBounds[h][c-1].left+this.__charBounds[h][c-1].width-m}f=g,(this.lineHeight<1||u===h&&this.lineHeight>1)&&(g/=this.lineHeight);var y=t.left+d+p,_=v-p,x=g,b=0;this.inCompositionMode?(e.fillStyle=this.compositionColor||"black",x=1,b=g):e.fillStyle=this.selectionColor,"rtl"===this.direction&&(y=this.width-y-_),e.fillRect(y,t.top+t.topOffset+b,_,x),t.topOffset+=f}},getCurrentCharFontSize:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")},getCurrentCharColor:function(){var t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fill")},_getCurrentCharIndex:function(){var t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}}),T.IText.fromObject=function(e,i){if(t(e),e.styles)for(var n in e.styles)for(var r in e.styles[n])t(e.styles[n][r]);T.Object._fromObject("IText",e,i,"text")}}(),C=T.util.object.clone,T.util.object.extend(T.IText.prototype,{initBehavior:function(){this.initAddedHandler(),this.initRemovedHandler(),this.initCursorSelectionHandlers(),this.initDoubleClickSimulation(),this.mouseMoveHandler=this.mouseMoveHandler.bind(this)},onDeselect:function(){this.isEditing&&this.exitEditing(),this.selected=!1},initAddedHandler:function(){var t=this;this.on("added",(function(){var e=t.canvas;e&&(e._hasITextHandlers||(e._hasITextHandlers=!0,t._initCanvasHandlers(e)),e._iTextInstances=e._iTextInstances||[],e._iTextInstances.push(t))}))},initRemovedHandler:function(){var t=this;this.on("removed",(function(){var e=t.canvas;e&&(e._iTextInstances=e._iTextInstances||[],T.util.removeFromArray(e._iTextInstances,t),0===e._iTextInstances.length&&(e._hasITextHandlers=!1,t._removeCanvasHandlers(e)))}))},_initCanvasHandlers:function(t){t._mouseUpITextHandler=function(){t._iTextInstances&&t._iTextInstances.forEach((function(t){t.__isMousedown=!1}))},t.on("mouse:up",t._mouseUpITextHandler)},_removeCanvasHandlers:function(t){t.off("mouse:up",t._mouseUpITextHandler)},_tick:function(){this._currentTickState=this._animateCursor(this,1,this.cursorDuration,"_onTickComplete")},_animateCursor:function(t,e,i,n){var r;return r={isAborted:!1,abort:function(){this.isAborted=!0}},t.animate("_currentCursorOpacity",e,{duration:i,onComplete:function(){r.isAborted||t[n]()},onChange:function(){t.canvas&&t.selectionStart===t.selectionEnd&&t.renderCursorOrSelection()},abort:function(){return r.isAborted}}),r},_onTickComplete:function(){var t=this;this._cursorTimeout1&&clearTimeout(this._cursorTimeout1),this._cursorTimeout1=setTimeout((function(){t._currentTickCompleteState=t._animateCursor(t,0,this.cursorDuration/2,"_tick")}),100)},initDelayedCursor:function(t){var e=this,i=t?0:this.cursorDelay;this.abortCursorAnimation(),this._currentCursorOpacity=1,this._cursorTimeout2=setTimeout((function(){e._tick()}),i)},abortCursorAnimation:function(){var t=this._currentTickState||this._currentTickCompleteState,e=this.canvas;this._currentTickState&&this._currentTickState.abort(),this._currentTickCompleteState&&this._currentTickCompleteState.abort(),clearTimeout(this._cursorTimeout1),clearTimeout(this._cursorTimeout2),this._currentCursorOpacity=0,t&&e&&e.clearContext(e.contextTop||e.contextContainer)},selectAll:function(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this},getSelectedText:function(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")},findWordBoundaryLeft:function(t){var e=0,i=t-1;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i--;for(;/\S/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findWordBoundaryRight:function(t){var e=0,i=t;if(this._reSpace.test(this._text[i]))for(;this._reSpace.test(this._text[i]);)e++,i++;for(;/\S/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},findLineBoundaryLeft:function(t){for(var e=0,i=t-1;!/\n/.test(this._text[i])&&i>-1;)e++,i--;return t-e},findLineBoundaryRight:function(t){for(var e=0,i=t;!/\n/.test(this._text[i])&&i<this._text.length;)e++,i++;return t+e},searchWordBoundary:function(t,e){for(var i=this._text,n=this._reSpace.test(i[t])?t-1:t,r=i[n],s=T.reNonWord;!s.test(r)&&n>0&&n<i.length;)r=i[n+=e];return s.test(r)&&(n+=1===e?0:1),n},selectWord:function(t){t=t||this.selectionStart;var e=this.searchWordBoundary(t,-1),i=this.searchWordBoundary(t,1);this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()},selectLine:function(t){t=t||this.selectionStart;var e=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);return this.selectionStart=e,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this},enterEditing:function(t){if(!this.isEditing&&this.editable)return this.canvas&&(this.canvas.calcOffset(),this.exitEditingOnOthers(this.canvas)),this.isEditing=!0,this.initHiddenTextarea(t),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick(),this.fire("editing:entered"),this._fireSelectionChanged(),this.canvas?(this.canvas.fire("text:editing:entered",{target:this}),this.initMouseMoveHandler(),this.canvas.requestRenderAll(),this):this},exitEditingOnOthers:function(t){t._iTextInstances&&t._iTextInstances.forEach((function(t){t.selected=!1,t.isEditing&&t.exitEditing()}))},initMouseMoveHandler:function(){this.canvas.on("mouse:move",this.mouseMoveHandler)},mouseMoveHandler:function(t){if(this.__isMousedown&&this.isEditing){var e=this.getSelectionStartFromPointer(t.e),i=this.selectionStart,n=this.selectionEnd;(e===this.__selectionStartOnMouseDown&&i!==n||i!==e&&n!==e)&&(e>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=e):(this.selectionStart=e,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===i&&this.selectionEnd===n||(this.restartCursorIfNeeded(),this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}},_setEditingProps:function(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0},fromStringToGraphemeSelection:function(t,e,i){var n=i.slice(0,t),r=T.util.string.graphemeSplit(n).length;if(t===e)return{selectionStart:r,selectionEnd:r};var s=i.slice(t,e);return{selectionStart:r,selectionEnd:r+T.util.string.graphemeSplit(s).length}},fromGraphemeToStringSelection:function(t,e,i){var n=i.slice(0,t).join("").length;return t===e?{selectionStart:n,selectionEnd:n}:{selectionStart:n,selectionEnd:n+i.slice(t,e).join("").length}},_updateTextarea:function(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){var t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}},updateFromTextArea:function(){if(this.hiddenTextarea){this.cursorOffsetCache={},this.text=this.hiddenTextarea.value,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords());var t=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value);this.selectionEnd=this.selectionStart=t.selectionEnd,this.inCompositionMode||(this.selectionStart=t.selectionStart),this.updateTextareaPosition()}},updateTextareaPosition:function(){if(this.selectionStart===this.selectionEnd){var t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}},_calcTextareaPosition:function(){if(!this.canvas)return{x:1,y:1};var t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),i=this.get2DCursorLocation(t),n=i.lineIndex,r=i.charIndex,s=this.getValueOfPropertyAt(n,r,"fontSize")*this.lineHeight,o=e.leftOffset,a=this.calcTransformMatrix(),h={x:e.left+o,y:e.top+e.topOffset+s},l=this.canvas.getRetinaScaling(),c=this.canvas.upperCanvasEl,u=c.width/l,f=c.height/l,d=u-s,g=f-s,p=c.clientWidth/u,v=c.clientHeight/f;return h=T.util.transformPoint(h,a),(h=T.util.transformPoint(h,this.canvas.viewportTransform)).x*=p,h.y*=v,h.x<0&&(h.x=0),h.x>d&&(h.x=d),h.y<0&&(h.y=0),h.y>g&&(h.y=g),h.x+=this.canvas._offset.left,h.y+=this.canvas._offset.top,{left:h.x+"px",top:h.y+"px",fontSize:s+"px",charHeight:s}},_saveEditingProps:function(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}},_restoreEditingProps:function(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor))},exitEditing:function(){var t=this._textBeforeEdit!==this.text,e=this.hiddenTextarea;return this.selected=!1,this.isEditing=!1,this.selectionEnd=this.selectionStart,e&&(e.blur&&e.blur(),e.parentNode&&e.parentNode.removeChild(e)),this.hiddenTextarea=null,this.abortCursorAnimation(),this._restoreEditingProps(),this._currentCursorOpacity=0,this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this.fire("editing:exited"),t&&this.fire("modified"),this.canvas&&(this.canvas.off("mouse:move",this.mouseMoveHandler),this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this},_removeExtraneousStyles:function(){for(var t in this.styles)this._textLines[t]||delete this.styles[t]},removeStyleFromTo:function(t,e){var i,n,r=this.get2DCursorLocation(t,!0),s=this.get2DCursorLocation(e,!0),o=r.lineIndex,a=r.charIndex,h=s.lineIndex,l=s.charIndex;if(o!==h){if(this.styles[o])for(i=a;i<this._unwrappedTextLines[o].length;i++)delete this.styles[o][i];if(this.styles[h])for(i=l;i<this._unwrappedTextLines[h].length;i++)(n=this.styles[h][i])&&(this.styles[o]||(this.styles[o]={}),this.styles[o][a+i-l]=n);for(i=o+1;i<=h;i++)delete this.styles[i];this.shiftLineStyles(h,o-h)}else if(this.styles[o]){n=this.styles[o];var c,u,f=l-a;for(i=a;i<l;i++)delete n[i];for(u in this.styles[o])(c=parseInt(u,10))>=l&&(n[c-f]=n[u],delete n[u])}},shiftLineStyles:function(t,e){var i=C(this.styles);for(var n in this.styles){var r=parseInt(n,10);r>t&&(this.styles[r+e]=i[r],i[r-e]||delete this.styles[r])}},restartCursorIfNeeded:function(){this._currentTickState&&!this._currentTickState.isAborted&&this._currentTickCompleteState&&!this._currentTickCompleteState.isAborted||this.initDelayedCursor()},insertNewlineStyleObject:function(t,e,i,n){var r,s={},o=!1,a=this._unwrappedTextLines[t].length===e;for(var h in i||(i=1),this.shiftLineStyles(t,i),this.styles[t]&&(r=this.styles[t][0===e?e:e-1]),this.styles[t]){var l=parseInt(h,10);l>=e&&(o=!0,s[l-e]=this.styles[t][h],a&&0===e||delete this.styles[t][h])}var c=!1;for(o&&!a&&(this.styles[t+i]=s,c=!0),c&&i--;i>0;)n&&n[i-1]?this.styles[t+i]={0:C(n[i-1])}:r?this.styles[t+i]={0:C(r)}:delete this.styles[t+i],i--;this._forceClearCache=!0},insertCharStyleObject:function(t,e,i,n){this.styles||(this.styles={});var r=this.styles[t],s=r?C(r):{};for(var o in i||(i=1),s){var a=parseInt(o,10);a>=e&&(r[a+i]=s[a],s[a-i]||delete r[a])}if(this._forceClearCache=!0,n)for(;i--;)Object.keys(n[i]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+i]=C(n[i]));else if(r)for(var h=r[e?e-1:1];h&&i--;)this.styles[t][e+i]=C(h)},insertNewStyleBlock:function(t,e,i){for(var n=this.get2DCursorLocation(e,!0),r=[0],s=0,o=0;o<t.length;o++)"\n"===t[o]?r[++s]=0:r[s]++;for(r[0]>0&&(this.insertCharStyleObject(n.lineIndex,n.charIndex,r[0],i),i=i&&i.slice(r[0]+1)),s&&this.insertNewlineStyleObject(n.lineIndex,n.charIndex+r[0],s),o=1;o<s;o++)r[o]>0?this.insertCharStyleObject(n.lineIndex+o,0,r[o],i):i&&(this.styles[n.lineIndex+o][0]=i[0]),i=i&&i.slice(r[o]+1);r[o]>0&&this.insertCharStyleObject(n.lineIndex+o,0,r[o],i)},setSelectionStartEndWithShift:function(t,e,i){i<=t?(e===t?this._selectionDirection="left":"right"===this._selectionDirection&&(this._selectionDirection="left",this.selectionEnd=t),this.selectionStart=i):i>t&&i<e?"right"===this._selectionDirection?this.selectionEnd=i:this.selectionStart=i:(e===t?this._selectionDirection="right":"left"===this._selectionDirection&&(this._selectionDirection="right",this.selectionStart=e),this.selectionEnd=i)},setSelectionInBoundaries:function(){var t=this.text.length;this.selectionStart>t?this.selectionStart=t:this.selectionStart<0&&(this.selectionStart=0),this.selectionEnd>t?this.selectionEnd=t:this.selectionEnd<0&&(this.selectionEnd=0)}}),T.util.object.extend(T.IText.prototype,{initDoubleClickSimulation:function(){this.__lastClickTime=+new Date,this.__lastLastClickTime=+new Date,this.__lastPointer={},this.on("mousedown",this.onMouseDown)},onMouseDown:function(t){if(this.canvas){this.__newClickTime=+new Date;var e=t.pointer;this.isTripleClick(e)&&(this.fire("tripleclick",t),this._stopEvent(t.e)),this.__lastLastClickTime=this.__lastClickTime,this.__lastClickTime=this.__newClickTime,this.__lastPointer=e,this.__lastIsEditing=this.isEditing,this.__lastSelected=this.selected}},isTripleClick:function(t){return this.__newClickTime-this.__lastClickTime<500&&this.__lastClickTime-this.__lastLastClickTime<500&&this.__lastPointer.x===t.x&&this.__lastPointer.y===t.y},_stopEvent:function(t){t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation()},initCursorSelectionHandlers:function(){this.initMousedownHandler(),this.initMouseupHandler(),this.initClicks()},doubleClickHandler:function(t){this.isEditing&&this.selectWord(this.getSelectionStartFromPointer(t.e))},tripleClickHandler:function(t){this.isEditing&&this.selectLine(this.getSelectionStartFromPointer(t.e))},initClicks:function(){this.on("mousedblclick",this.doubleClickHandler),this.on("tripleclick",this.tripleClickHandler)},_mouseDownHandler:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.__isMousedown=!0,this.selected&&(this.inCompositionMode=!1,this.setCursorByClick(t.e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()))},_mouseDownHandlerBefore:function(t){!this.canvas||!this.editable||t.e.button&&1!==t.e.button||(this.selected=this===this.canvas._activeObject)},initMousedownHandler:function(){this.on("mousedown",this._mouseDownHandler),this.on("mousedown:before",this._mouseDownHandlerBefore)},initMouseupHandler:function(){this.on("mouseup",this.mouseUpHandler)},mouseUpHandler:function(t){if(this.__isMousedown=!1,!(!this.editable||this.group||t.transform&&t.transform.actionPerformed||t.e.button&&1!==t.e.button)){if(this.canvas){var e=this.canvas._activeObject;if(e&&e!==this)return}this.__lastSelected&&!this.__corner?(this.selected=!1,this.__lastSelected=!1,this.enterEditing(t.e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection()):this.selected=!0}},setCursorByClick:function(t){var e=this.getSelectionStartFromPointer(t),i=this.selectionStart,n=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(i,n,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())},getSelectionStartFromPointer:function(t){for(var e,i=this.getLocalPointer(t),n=0,r=0,s=0,o=0,a=0,h=0,l=this._textLines.length;h<l&&s<=i.y;h++)s+=this.getHeightOfLine(h)*this.scaleY,a=h,h>0&&(o+=this._textLines[h-1].length+this.missingNewlineOffset(h-1));r=this._getLineLeftOffset(a)*this.scaleX,e=this._textLines[a],"rtl"===this.direction&&(i.x=this.width*this.scaleX-i.x+r);for(var c=0,u=e.length;c<u&&(n=r,(r+=this.__charBounds[a][c].kernedWidth*this.scaleX)<=i.x);c++)o++;return this._getNewSelectionStartFromOffset(i,n,r,o,u)},_getNewSelectionStartFromOffset:function(t,e,i,n,r){var s=t.x-e,o=i-t.x,a=n+(o>s||o<0?0:1);return this.flipX&&(a=r-a),a>this._text.length&&(a=this._text.length),a}}),T.util.object.extend(T.IText.prototype,{initHiddenTextarea:function(){this.hiddenTextarea=T.document.createElement("textarea"),this.hiddenTextarea.setAttribute("autocapitalize","off"),this.hiddenTextarea.setAttribute("autocorrect","off"),this.hiddenTextarea.setAttribute("autocomplete","off"),this.hiddenTextarea.setAttribute("spellcheck","false"),this.hiddenTextarea.setAttribute("data-fabric-hiddentextarea",""),this.hiddenTextarea.setAttribute("wrap","off");var t=this._calcTextareaPosition();this.hiddenTextarea.style.cssText="position: absolute; top: "+t.top+"; left: "+t.left+"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; paddingｰtop: "+t.fontSize+";",T.document.body.appendChild(this.hiddenTextarea),T.util.addListener(this.hiddenTextarea,"keydown",this.onKeyDown.bind(this)),T.util.addListener(this.hiddenTextarea,"keyup",this.onKeyUp.bind(this)),T.util.addListener(this.hiddenTextarea,"input",this.onInput.bind(this)),T.util.addListener(this.hiddenTextarea,"copy",this.copy.bind(this)),T.util.addListener(this.hiddenTextarea,"cut",this.copy.bind(this)),T.util.addListener(this.hiddenTextarea,"paste",this.paste.bind(this)),T.util.addListener(this.hiddenTextarea,"compositionstart",this.onCompositionStart.bind(this)),T.util.addListener(this.hiddenTextarea,"compositionupdate",this.onCompositionUpdate.bind(this)),T.util.addListener(this.hiddenTextarea,"compositionend",this.onCompositionEnd.bind(this)),!this._clickHandlerInitialized&&this.canvas&&(T.util.addListener(this.canvas.upperCanvasEl,"click",this.onClick.bind(this)),this._clickHandlerInitialized=!0)},keysMap:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorRight",36:"moveCursorLeft",37:"moveCursorLeft",38:"moveCursorUp",39:"moveCursorRight",40:"moveCursorDown"},keysMapRtl:{9:"exitEditing",27:"exitEditing",33:"moveCursorUp",34:"moveCursorDown",35:"moveCursorLeft",36:"moveCursorRight",37:"moveCursorRight",38:"moveCursorUp",39:"moveCursorLeft",40:"moveCursorDown"},ctrlKeysMapUp:{67:"copy",88:"cut"},ctrlKeysMapDown:{65:"selectAll"},onClick:function(){this.hiddenTextarea&&this.hiddenTextarea.focus()},onKeyDown:function(t){if(this.isEditing){var e="rtl"===this.direction?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}},onKeyUp:function(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())},onInput:function(t){var e=this.fromPaste;if(this.fromPaste=!1,t&&t.stopPropagation(),this.isEditing){var i,n,r,s,o,a=this._splitTextIntoLines(this.hiddenTextarea.value).graphemeText,h=this._text.length,l=a.length,c=l-h,u=this.selectionStart,f=this.selectionEnd,d=u!==f;if(""===this.hiddenTextarea.value)return this.styles={},this.updateFromTextArea(),this.fire("changed"),void(this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll()));var g=this.fromStringToGraphemeSelection(this.hiddenTextarea.selectionStart,this.hiddenTextarea.selectionEnd,this.hiddenTextarea.value),p=u>g.selectionStart;d?(i=this._text.slice(u,f),c+=f-u):l<h&&(i=p?this._text.slice(f+c,f):this._text.slice(u,u-c)),n=a.slice(g.selectionEnd-c,g.selectionEnd),i&&i.length&&(n.length&&(r=this.getSelectionStyles(u,u+1,!1),r=n.map((function(){return r[0]}))),d?(s=u,o=f):p?(s=f-i.length,o=f):(s=f,o=f+i.length),this.removeStyleFromTo(s,o)),n.length&&(e&&n.join("")===T.copiedText&&!T.disableStyleCopyPaste&&(r=T.copiedTextStyle),this.insertNewStyleBlock(n,u,r)),this.updateFromTextArea(),this.fire("changed"),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())}},onCompositionStart:function(){this.inCompositionMode=!0},onCompositionEnd:function(){this.inCompositionMode=!1},onCompositionUpdate:function(t){this.compositionStart=t.target.selectionStart,this.compositionEnd=t.target.selectionEnd,this.updateTextareaPosition()},copy:function(){this.selectionStart!==this.selectionEnd&&(T.copiedText=this.getSelectedText(),T.disableStyleCopyPaste?T.copiedTextStyle=null:T.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0)},paste:function(){this.fromPaste=!0},_getClipboardData:function(t){return t&&t.clipboardData||T.window.clipboardData},_getWidthBeforeCursor:function(t,e){var i,n=this._getLineLeftOffset(t);return e>0&&(n+=(i=this.__charBounds[t][e-1]).left+i.width),n},getDownCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),n=this.get2DCursorLocation(i),r=n.lineIndex;if(r===this._textLines.length-1||t.metaKey||34===t.keyCode)return this._text.length-i;var s=n.charIndex,o=this._getWidthBeforeCursor(r,s),a=this._getIndexOnLine(r+1,o);return this._textLines[r].slice(s).length+a+1+this.missingNewlineOffset(r)},_getSelectionForOffset:function(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart},getUpCursorOffset:function(t,e){var i=this._getSelectionForOffset(t,e),n=this.get2DCursorLocation(i),r=n.lineIndex;if(0===r||t.metaKey||33===t.keyCode)return-i;var s=n.charIndex,o=this._getWidthBeforeCursor(r,s),a=this._getIndexOnLine(r-1,o),h=this._textLines[r].slice(0,s),l=this.missingNewlineOffset(r-1);return-this._textLines[r-1].length+a-h.length+(1-l)},_getIndexOnLine:function(t,e){for(var i,n,r=this._textLines[t],s=this._getLineLeftOffset(t),o=0,a=0,h=r.length;a<h;a++)if((s+=i=this.__charBounds[t][a].width)>e){n=!0;var l=s-i,c=s,u=Math.abs(l-e);o=Math.abs(c-e)<u?a:a-1;break}return n||(o=r.length-1),o},moveCursorDown:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)},moveCursorUp:function(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorUpOrDown("Up",t)},_moveCursorUpOrDown:function(t,e){var i=this["get"+t+"CursorOffset"](e,"right"===this._selectionDirection);e.shiftKey?this.moveCursorWithShift(i):this.moveCursorWithoutShift(i),0!==i&&(this.setSelectionInBoundaries(),this.abortCursorAnimation(),this._currentCursorOpacity=1,this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorWithShift:function(t){var e="left"===this._selectionDirection?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),0!==t},moveCursorWithoutShift:function(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),0!==t},moveCursorLeft:function(t){0===this.selectionStart&&0===this.selectionEnd||this._moveCursorLeftOrRight("Left",t)},_move:function(t,e,i){var n;if(t.altKey)n=this["findWordBoundary"+i](this[e]);else{if(!t.metaKey&&35!==t.keyCode&&36!==t.keyCode)return this[e]+="Left"===i?-1:1,!0;n=this["findLineBoundary"+i](this[e])}if(void 0!==typeof n&&this[e]!==n)return this[e]=n,!0},_moveLeft:function(t,e){return this._move(t,e,"Left")},_moveRight:function(t,e){return this._move(t,e,"Right")},moveCursorLeftWithoutShift:function(t){var e=!0;return this._selectionDirection="left",this.selectionEnd===this.selectionStart&&0!==this.selectionStart&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e},moveCursorLeftWithShift:function(t){return"right"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):0!==this.selectionStart?(this._selectionDirection="left",this._moveLeft(t,"selectionStart")):void 0},moveCursorRight:function(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)},_moveCursorLeftOrRight:function(t,e){var i="moveCursor"+t+"With";this._currentCursorOpacity=1,e.shiftKey?i+="Shift":i+="outShift",this[i](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())},moveCursorRightWithShift:function(t){return"left"===this._selectionDirection&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection="right",this._moveRight(t,"selectionEnd")):void 0},moveCursorRightWithoutShift:function(t){var e=!0;return this._selectionDirection="right",this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e},removeChars:function(t,e){void 0===e&&(e=t+1),this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()},insertChars:function(t,e,i,n){void 0===n&&(n=i),n>i&&this.removeStyleFromTo(i,n);var r=T.util.string.graphemeSplit(t);this.insertNewStyleBlock(r,i,e),this._text=[].concat(this._text.slice(0,i),r,this._text.slice(n)),this.text=this._text.join(""),this.set("dirty",!0),this._shouldClearDimensionCache()&&(this.initDimensions(),this.setCoords()),this._removeExtraneousStyles()}}),S=T.util.toFixed,w=/  +/g,T.util.object.extend(T.Text.prototype,{_toSVG:function(){var t=this._getSVGLeftTopOffsets(),e=this._getSVGTextAndBg(t.textTop,t.textLeft);return this._wrapSVGTextAndBg(e)},toSVG:function(t){return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,noStyle:!0,withShadow:!0})},_getSVGLeftTopOffsets:function(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}},_wrapSVGTextAndBg:function(t){var e=this.getSvgTextDecoration(this);return[t.textBgRects.join(""),'\t\t<text xml:space="preserve" ',this.fontFamily?'font-family="'+this.fontFamily.replace(/"/g,"'")+'" ':"",this.fontSize?'font-size="'+this.fontSize+'" ':"",this.fontStyle?'font-style="'+this.fontStyle+'" ':"",this.fontWeight?'font-weight="'+this.fontWeight+'" ':"",e?'text-decoration="'+e+'" ':"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",t.textSpans.join(""),"</text>\n"]},_getSVGTextAndBg:function(t,e){var i,n=[],r=[],s=t;this._setSVGBg(r);for(var o=0,a=this._textLines.length;o<a;o++)i=this._getLineLeftOffset(o),(this.textBackgroundColor||this.styleHas("textBackgroundColor",o))&&this._setSVGTextLineBg(r,o,e+i,s),this._setSVGTextLineText(n,o,e+i,s),s+=this.getHeightOfLine(o);return{textSpans:n,textBgRects:r}},_createTextCharSpan:function(t,e,i,n){var r=t!==t.trim()||t.match(w),s=this.getSvgSpanStyles(e,r),o=s?'style="'+s+'"':"",a=e.deltaY,h="",l=T.Object.NUM_FRACTION_DIGITS;return a&&(h=' dy="'+S(a,l)+'" '),['<tspan x="',S(i,l),'" y="',S(n,l),'" ',h,o,">",T.util.string.escapeXml(t),"</tspan>"].join("")},_setSVGTextLineText:function(t,e,i,n){var r,s,o,a,h,l=this.getHeightOfLine(e),c=-1!==this.textAlign.indexOf("justify"),u="",f=0,d=this._textLines[e];n+=l*(1-this._fontSizeFraction)/this.lineHeight;for(var g=0,p=d.length-1;g<=p;g++)h=g===p||this.charSpacing,u+=d[g],o=this.__charBounds[e][g],0===f?(i+=o.kernedWidth-o.width,f+=o.width):f+=o.kernedWidth,c&&!h&&this._reSpaceAndTab.test(d[g])&&(h=!0),h||(r=r||this.getCompleteStyleDeclaration(e,g),s=this.getCompleteStyleDeclaration(e,g+1),h=this._hasStyleChangedForSvg(r,s)),h&&(a=this._getStyleDeclaration(e,g)||{},t.push(this._createTextCharSpan(u,a,i,n)),u="",r=s,i+=f,f=0)},_pushTextBgRect:function(t,e,i,n,r,s){var o=T.Object.NUM_FRACTION_DIGITS;t.push("\t\t<rect ",this._getFillAttributes(e),' x="',S(i,o),'" y="',S(n,o),'" width="',S(r,o),'" height="',S(s,o),'"></rect>\n')},_setSVGTextLineBg:function(t,e,i,n){for(var r,s,o=this._textLines[e],a=this.getHeightOfLine(e)/this.lineHeight,h=0,l=0,c=this.getValueOfPropertyAt(e,0,"textBackgroundColor"),u=0,f=o.length;u<f;u++)r=this.__charBounds[e][u],(s=this.getValueOfPropertyAt(e,u,"textBackgroundColor"))!==c?(c&&this._pushTextBgRect(t,c,i+l,n,h,a),l=r.left,h=r.width,c=s):h+=r.kernedWidth;s&&this._pushTextBgRect(t,s,i+l,n,h,a)},_getFillAttributes:function(t){var e=t&&"string"==typeof t?new T.Color(t):"";return e&&e.getSource()&&1!==e.getAlpha()?'opacity="'+e.getAlpha()+'" fill="'+e.setAlpha(1).toRgb()+'"':'fill="'+t+'"'},_getSVGLineTopOffset:function(t){for(var e,i=0,n=0;n<t;n++)i+=this.getHeightOfLine(n);return e=this.getHeightOfLine(n),{lineTop:i,offset:(this._fontSizeMult-this._fontSizeFraction)*e/(this.lineHeight*this._fontSizeMult)}},getSvgStyles:function(t){return T.Object.prototype.getSvgStyles.call(this,t)+" white-space: pre;"}}),function(t){"use strict";var e=t.fabric||(t.fabric={});e.Textbox=e.util.createClass(e.IText,e.Observable,{type:"textbox",minWidth:20,dynamicMinWidth:2,__cachedLines:null,lockScalingFlip:!0,noScaleCache:!1,_dimensionAffectingProps:e.Text.prototype._dimensionAffectingProps.concat("width"),_wordJoiners:/[ \t\r]/,splitByGrapheme:!1,initDimensions:function(){this.__skipDimension||(this.isEditing&&this.initDelayedCursor(),this.clearContextTop(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),-1!==this.textAlign.indexOf("justify")&&this.enlargeSpaces(),this.height=this.calcTextHeight(),this.saveState({propertySet:"_dimensionAffectingProps"}))},_generateStyleMap:function(t){for(var e=0,i=0,n=0,r={},s=0;s<t.graphemeLines.length;s++)"\n"===t.graphemeText[n]&&s>0?(i=0,n++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[n])&&s>0&&(i++,n++),r[s]={line:e,offset:i},n+=t.graphemeLines[s].length,i+=t.graphemeLines[s].length;return r},styleHas:function(t,i){if(this._styleMap&&!this.isWrapping){var n=this._styleMap[i];n&&(i=n.line)}return e.Text.prototype.styleHas.call(this,t,i)},isEmptyStyles:function(t){if(!this.styles)return!0;var e,i,n=0,r=!1,s=this._styleMap[t],o=this._styleMap[t+1];for(var a in s&&(t=s.line,n=s.offset),o&&(r=o.line===t,e=o.offset),i=void 0===t?this.styles:{line:this.styles[t]})for(var h in i[a])if(h>=n&&(!r||h<e))for(var l in i[a][h])return!1;return!0},_getStyleDeclaration:function(t,e){if(this._styleMap&&!this.isWrapping){var i=this._styleMap[t];if(!i)return null;t=i.line,e=i.offset+e}return this.callSuper("_getStyleDeclaration",t,e)},_setStyleDeclaration:function(t,e,i){var n=this._styleMap[t];t=n.line,e=n.offset+e,this.styles[t][e]=i},_deleteStyleDeclaration:function(t,e){var i=this._styleMap[t];t=i.line,e=i.offset+e,delete this.styles[t][e]},_getLineStyle:function(t){var e=this._styleMap[t];return!!this.styles[e.line]},_setLineStyle:function(t){var e=this._styleMap[t];this.styles[e.line]={}},_wrapText:function(t,e){var i,n=[];for(this.isWrapping=!0,i=0;i<t.length;i++)n=n.concat(this._wrapLine(t[i],i,e));return this.isWrapping=!1,n},_measureWord:function(t,e,i){var n,r=0;i=i||0;for(var s=0,o=t.length;s<o;s++)r+=this._getGraphemeBox(t[s],e,s+i,n,!0).kernedWidth,n=t[s];return r},_wrapLine:function(t,i,n,r){var s=0,o=this.splitByGrapheme,a=[],h=[],l=o?e.util.string.graphemeSplit(t):t.split(this._wordJoiners),c="",u=0,f=o?"":" ",d=0,g=0,p=0,v=!0,m=this._getWidthOfCharSpacing();r=r||0,0===l.length&&l.push([]),n-=r;for(var y=0;y<l.length;y++)c=o?l[y]:e.util.string.graphemeSplit(l[y]),d=this._measureWord(c,i,u),u+=c.length,(s+=g+d-m)>n&&!v?(a.push(h),h=[],s=d,v=!0):s+=m,v||o||h.push(f),h=h.concat(c),g=o?0:this._measureWord([f],i,u),u++,v=!1,d>p&&(p=d);return y&&a.push(h),p+r>this.dynamicMinWidth&&(this.dynamicMinWidth=p-m+r),a},isEndOfWrapping:function(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line},missingNewlineOffset:function(t){return this.splitByGrapheme?this.isEndOfWrapping(t)?1:0:1},_splitTextIntoLines:function(t){for(var i=e.Text.prototype._splitTextIntoLines.call(this,t),n=this._wrapText(i.lines,this.width),r=new Array(n.length),s=0;s<n.length;s++)r[s]=n[s].join("");return i.lines=r,i.graphemeLines=n,i},getMinWidth:function(){return Math.max(this.minWidth,this.dynamicMinWidth)},_removeExtraneousStyles:function(){var t={};for(var e in this._styleMap)this._textLines[e]&&(t[this._styleMap[e].line]=1);for(var e in this.styles)t[e]||delete this.styles[e]},toObject:function(t){return this.callSuper("toObject",["minWidth","splitByGrapheme"].concat(t))}}),e.Textbox.fromObject=function(t,i){return e.Object._fromObject("Textbox",t,i,"text")}}(e),function(){var t=T.controlsUtils,e=t.scaleSkewCursorStyleHandler,i=t.scaleCursorStyleHandler,n=t.scalingEqually,r=t.scalingYOrSkewingX,s=t.scalingXOrSkewingY,o=t.scaleOrSkewActionName,a=T.Object.prototype.controls;if(a.ml=new T.Control({x:-.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:o}),a.mr=new T.Control({x:.5,y:0,cursorStyleHandler:e,actionHandler:s,getActionName:o}),a.mb=new T.Control({x:0,y:.5,cursorStyleHandler:e,actionHandler:r,getActionName:o}),a.mt=new T.Control({x:0,y:-.5,cursorStyleHandler:e,actionHandler:r,getActionName:o}),a.tl=new T.Control({x:-.5,y:-.5,cursorStyleHandler:i,actionHandler:n}),a.tr=new T.Control({x:.5,y:-.5,cursorStyleHandler:i,actionHandler:n}),a.bl=new T.Control({x:-.5,y:.5,cursorStyleHandler:i,actionHandler:n}),a.br=new T.Control({x:.5,y:.5,cursorStyleHandler:i,actionHandler:n}),a.mtr=new T.Control({x:0,y:-.5,actionHandler:t.rotationWithSnapping,cursorStyleHandler:t.rotationStyleHandler,offsetY:-40,withConnection:!0,actionName:"rotate"}),T.Textbox){var h=T.Textbox.prototype.controls={};h.mtr=a.mtr,h.tr=a.tr,h.br=a.br,h.tl=a.tl,h.bl=a.bl,h.mt=a.mt,h.mb=a.mb,h.mr=new T.Control({x:.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"}),h.ml=new T.Control({x:-.5,y:0,actionHandler:t.changeWidth,cursorStyleHandler:e,actionName:"resizing"})}}()},574:()=>{},748:()=>{},246:()=>{}},e={};function i(n){var r=e[n];if(void 0!==r)return r.exports;var s=e[n]={exports:{}};return t[n](s,s.exports,i),s.exports}i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{"use strict";i.r(n),i.d(n,{BaseMockup:()=>a,DesignCanvas:()=>l,DesignManager:()=>h});var t,e=i(676),r=(t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)},function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}),s=!1,o=e.fabric.Canvas.prototype._onTouchStart;e.fabric.util.object.extend(e.fabric.Canvas.prototype,{_onTouchStart:function(t){this.allowTouchScrolling||o.call(this,t)}});var a=function(t){function i(e,i){return t.call(this,e,i)||this}return r(i,t),i.prototype.setDisplaySize=function(t){var e=this.height*(t/this.width);this.setWidth(t),this.setHeight(e);var i=this.backgroundImage;if(i&&"object"==typeof i){var n=i.width*i.scaleX,r=i.height*i.scaleY,s=this.width/n;r*s>this.height&&(s=this.height/r),this.setZoom(s)}},i.prototype.setMockup=function(t){var e=this;return new Promise((function(i){e._loadImg(t).then((function(t){e.setBackgroundImage(t,(function(){if(e.width&&e.height&&t.width&&t.height){var n=e.width/t.width;t.height*n>e.height&&(n=e.height/t.height),e.setZoom(n)}else e.setZoom(1);e.renderAll(),i(t)}))}))}))},i.prototype.setMockupOverlay=function(t){void 0===t&&(t=!0),this.mockupOverlay=t},i.prototype.addPrintSpace=function(t,i){var n=this.getObjectByName("printSpace");n&&this.remove(n);var r=this.getZoom(),s=this.width/r,o=this.height/r,a=.5*s,h=a*i/t;this.strokeWidth=2/r,this.strokeColor="#4e5bf2",this.printSpace=new e.fabric.Rect({name:"printSpace",width:a,height:h,fill:"",top:(o-h)/2,left:(s-a)/2,stroke:this.strokeColor,strokeWidth:this.strokeWidth}),this.printSpace.setControlsVisibility({mt:!1,mb:!1,ml:!1,mr:!1}),this.add(this.printSpace),this.renderAll()},i.prototype.viewDesign=function(t){if(t){var i=this.getObjectByName("printSpace"),n=new e.fabric.Image(t);this.locked&&(n.set("selectable",!this.locked),n.defaultCursor="default"),i?(i.set({stroke:""}),n.scaleToWidth(i.getScaledWidth()),n.height*n.scaleY>i.scaleY*i.height&&n.scaleToHeight(i.getScaledHeight()),n.set({name:"design",top:i.top+(i.getScaledHeight()-n.getScaledHeight())/2,left:i.left+(i.getScaledWidth()-n.getScaledWidth())/2,angle:i.angle})):(n.scaleToWidth(this.width),n.getScaledHeight()>this.height&&n.scaleToHeight(this.height),n.set({name:"design",top:(this.height-n.getScaledHeight())/2,left:(this.width-n.getScaledWidth())/2})),this.mockupOverlay&&n.set({globalCompositeOperation:"destination-over"});var r=this.getObjectByName("design");r&&this.remove(r),this.add(n);var s=this.getObjectByName("mockupColor");s&&this.bringToFront(s),this.renderAll()}},i.prototype.setMockupColor=function(t){var i=this.getObjectByName("mockupColor");i?i.set({fill:t}):(i=new e.fabric.Rect({name:"mockupColor",width:this.width/this.getZoom(),height:this.height/this.getZoom(),fill:t,globalCompositeOperation:"destination-over"}),this.locked&&(i.set("selectable",!this.locked),i.defaultCursor="default"),this.add(i)),this.renderAll()},i.prototype.getObjectByName=function(t){return this.getObjects().find((function(e){return e.name===t}))},i.prototype.loadFromJSONPromise=function(t){var e=this;return new Promise((function(i){e.loadFromJSON(t,(function(){var t=e.backgroundImage;if(t&&"object"==typeof t){var n=t.getElement(),r=t.width*t.scaleX,s=t.height*t.scaleY;t.set({width:n.naturalWidth,height:n.naturalHeight,scaleX:r/n.naturalWidth,scaleY:s/n.naturalHeight});var o=e.width/r;s*o>e.height&&(o=e.height/s),e.setZoom(o)}e.locked&&e.lock(),e.renderAll.bind(e),i(e)}))}))},i.prototype.setDesignGuide=function(t){var e=this;if(this.designGuide&&this.remove(this.designGuide),t)return new Promise((function(i){e._loadImg(t).then((function(t){e.designGuide=t,t.scaleToWidth(e.width/e.getZoom()),e.add(t),e.sendToBack(t),e.renderAll(),i(t)}))}));this.renderAll()},i.prototype.lock=function(t){var e=this;void 0===t&&(t=!0),this.locked=t,this.selection=!this.locked,this.defaultCursor="default";var i=this.getObjectByName("printSpace");i&&i.set({stroke:""}),this.getObjects().forEach((function(t){t.set("selectable",!e.locked),t.defaultCursor="default"})),this.on("mouse:over",(function(t){null!=t.target&&(t.target.hoverCursor=e.defaultCursor)}))},i.prototype.enableToughScroll=function(){s||(s=!0,e.fabric.util.object.extend(e.fabric.Canvas.prototype,{_onTouchStart:function(){}}))},i.prototype.toJSON=function(){return this.setDesignGuide(),t.prototype.toJSON.call(this,["name","mockupOverlay"])},i.prototype.toBlob=function(){var t=this;return new Promise((function(e){t.getElement().toBlob((function(t){e(t)}))}))},i.prototype._loadImg=function(t){return new Promise((function(i,n){t&&void 0!==e.fabric||n(),e.fabric.Image.fromURL(t,(function(t){i(t)}),{crossOrigin:"Anonymous"})}))},i}(e.fabric.Canvas),h=function(){function t(t){this.container=document.getElementById(t),this.designs={},e.fabric.Object.prototype.cornerColor="#727cf5",this.applySameDesign=!0}return t.prototype.loadDesign=function(t){var e;if(this.designs[t.id])return this.designs[t.id];var i=document.createElement("canvas");null===(e=this.container)||void 0===e||e.appendChild(i),t.canvasEl=i;var n=new l(t);return n.manager=this,n.restricted=this.restricted,this.activeDesign?n.hide():this.activeDesign=n,this.designs[t.id]=n,this.setActiveDesign(),n},t.prototype.getDesign=function(t){return!!this.designs[t]&&this.designs[t]},t.prototype.copyDesign=function(t){var e,i=Object.values(this.designs),n=i.find((function(e){return e.key===t.key&&e.hasDesign()}));return n?e=t.load(n.exportJson()):(n=i.find((function(e){return e.printSpace===t.printSpace&&e.hasDesign()})))&&(e=t.addDesign(n.designUrl)),e},t.prototype.getActiveDesign=function(){return this.activeDesign},t.prototype.setActiveDesign=function(t){var e;return t||(t=this.activeDesignId),!!t&&(this.designs[t]?(this.activeDesign!==this.designs[t]&&(null===(e=this.activeDesign)||void 0===e||e.hide(),this.activeDesign=this.designs[t],this.activeDesign.show(),this.activeDesign.renderAll(),this.activeDesign._onupdate(!0)),this.activeDesign):(this.activeDesignId=t,!1))},t.prototype.setRestricted=function(t){void 0===t&&(t=!0),this.restricted=!0===t,Object.values(this.designs).forEach((function(e){e.setRestricted(t)}))},t.prototype._applySameDesign=function(t){t===this.activeDesign&&this.applySameDesign&&Object.values(this.designs).forEach((function(e){e===t||e.key!==t.key||!t.locked&&t.locked!==e.locked||e.load(t.exportJson()).then((function(){}))}))},t.prototype._designUpdated=function(t){this.onupdate&&this.onupdate(t),this._applySameDesign(t)},t.prototype.onupdate=function(t){throw new Error("Method not implemented.")},t.prototype.copyObject=function(t,e){if(t&&e)if("image"===e.type){var i=e;this._copyCustomImage(t,i)}else"i-text"===e.type&&this._copyCustomText(t,e)},t.prototype._copyCustomImage=function(t,e){Object.values(this.designs).forEach((function(i){e.id&&i!==t&&i.key!==t.key&&(i.printSpace===t.printSpace||t.isMain&&i.isMain)&&(i.getObjects().find((function(t){return t.id===e.id}))||i.addCustomImage(e._element.src).then((function(n){var r=t.getZoom()/i.getZoom();n.set({id:e.id,left:e.left*r,top:e.top*r,scaleX:e.scaleX*r,scaleY:e.scaleY*r})})))}))},t.prototype._copyCustomText=function(t,i){Object.values(this.designs).forEach((function(n){var r;if(i.id&&n!==t&&n.key!==t.key&&(n.printSpace===t.printSpace||t.isMain&&n.isMain)){var s=n.getObjects().find((function(t){return t.id===i.id}));if(s)n._saveOldPosition(s,!0),s.set({text:i.text,name:i.name,fontFamily:i.fontFamily,fontUrl:i.fontUrl,fill:i.fill,globalCompositeOperation:i.globalCompositeOperation,dirty:!0,convertText:i.convertText});else{var o=t.getZoom()/n.getZoom();s=new e.fabric.IText(i.text||"",{id:i.id,name:i.name,fontSize:i.fontSize,lockUniScaling:!1,textWidth:i.textWidth,textAlign:i.textAlign,lineHeight:i.lineHeight,editable:!1,maxLength:i.maxLength,fontFamily:i.fontFamily,fontUrl:i.fontUrl,fill:i.fill,width:i.width,height:i.height,left:i.left*o,top:i.top*o,scaleX:i.scaleX*o,scaleY:i.scaleY*o,angle:i.angle,globalCompositeOperation:i.globalCompositeOperation,isCustom:!0,convertText:i.convertText}),n.add(s),null===(r=n.texts)||void 0===r||r.push(s),n._resetTextPosition(s)}n.discardActiveObject(),n._onupdate(!0)}}))},t.prototype._removeObject=function(t,e){Object.values(this.designs).forEach((function(i){if(e.id&&i!==t&&i.key!==t.key&&(i.printSpace===t.printSpace||t.isMain&&i.isMain)){var n=i.getObjects().find((function(t){return t.id===e.id}));n&&(i.removeObject(n,!0),i._onupdate(!0))}}))},t}(),l=function(t){function i(i){var n=t.call(this,i?i.canvasEl||i.id:null,void 0)||this;if(n.texts=[],n.designs=[],n.texts=[],n.onUpdates=[],n.restricted=!0,n.textLimit=2,n.DPI=(null==i?void 0:i.DPI)||300,n.minDPI=(null==i?void 0:i.minDPI)||150,n.unique=(null==i?void 0:i.unique)||!1,!i)return n;n.printSpace=i.name,n.key=i.name+"-"+i.printWidth+"-"+i.printHeight,n.isMain=i.isMain,n.bgColor="",n.printWidth=i.printWidth,n.printHeight=i.printHeight;var r=1.4*Math.max(n.printWidth,n.printHeight),s=i.displayWidth/r;return n.setWidth(i.displayWidth),n.setHeight(i.displayWidth),n.displayWidth=i.displayWidth,n.setZoom(s),n.selection=!1,n.on("object:moving",(function(t){t.target&&n._correctPosition(t.target)})),n.on("object:scaling",(function(t){t.target&&n._correctScale(t.target)})),n.on("mouse:up",(function(t){t.target&&n._correctPosition(t.target),n.hasDesign()&&n._onupdate()})),n.printRect=new e.fabric.Rect({name:"printRect",width:i.printWidth,height:i.printHeight,fill:"",hasControls:!1,selectable:!1,hoverCursor:"default",top:(r-i.printHeight)/2,left:(r-i.printWidth)/2}),n.add(n.printRect),n._grayoutNonPrintArea(),n.strokeWidth=2/s,n.strokeColor="#4e5bf2",n.printRect.clone((function(t){t.set({name:"printStroke",stroke:n.strokeColor,strokeWidth:n.strokeWidth,top:t.top-n.strokeWidth,left:t.left-n.strokeWidth,width:t.width,height:t.height,hasControls:!1,selectable:!1,hoverCursor:"default"}),n.add(t),n.printStroke=t})),n.viewStrokeColor="#00FF00",i.viewWidth&&i.viewHeight&&(n.viewRect=new e.fabric.Rect({name:"viewRect",stroke:n.viewStrokeColor,strokeWidth:n.strokeWidth,strokeDashArray:[100,50],width:i.viewWidth,height:i.viewHeight,fill:"",hasControls:!1,selectable:!1,hoverCursor:"default",top:(r-i.viewHeight)/2-n.strokeWidth,left:(r-i.viewWidth)/2-n.strokeWidth}),n.add(n.viewRect)),n.renderAll(),function(t){for(var i,n,r,s=t.getZoom(),o=t.getWidth()/s,a=t.getHeight()/s,h=o/2,l=a/2,c={},u={},f=100,d="#4e5bf2",g=1,p=t.contextContainer,v=h-f,m=h+f;v<=m;v++)c[Math.round(v)]=!0;for(v=l-f,m=l+f;v<=m;v++)u[Math.round(v)]=!0;function y(t,e,n,r){p&&(p.save(),p.strokeStyle=d,p.lineWidth=g,p.beginPath(),p.moveTo(t*i[0],e*i[3]),p.lineTo(n*i[0],r*i[3]),p.stroke(),p.restore())}t.on("mouse:down",(function(){i=t.viewportTransform||[]})),t.on("object:moving",(function(i){var s=i.target,o=null==s?void 0:s.getCenterPoint();t._currentTransform&&(n=Math.round(null==o?void 0:o.x)in c,((r=Math.round(null==o?void 0:o.y)in u)||n)&&(null==s||s.setPositionByOrigin(new e.fabric.Point(n?h:null==o?void 0:o.x,r?l:null==o?void 0:o.y),"center","center")))})),t.on("before:render",(function(){null==p||p.clearRect(0,0,o,a)})),t.on("after:render",(function(){n&&y(h+.5,0,h+.5,a),r&&y(0,l+.5,o,l+.5)})),t.on("mouse:up",(function(){n=r=!1,t.renderAll()}))}(n),n}return r(i,t),i.prototype.load=function(t,i,n){var r=this;return void 0===i&&(i=0),void 0===n&&(n=!1),t&&(this.json="string"==typeof t?JSON.parse(t):t),new Promise((function(t){if(!r.json||r.loadingJson)return t(!1);r.loadingJson=!0,r.loadFrontPromises=[],"undefined"!=typeof document&&r.json.objects.forEach((function(t){var e;if("i-text"===t.type){var i=t,n=i.fontUrl;n||"Roboto"!==i.fontFamily||(n="/fonts/Roboto.ttf"),n&&(null===(e=r.loadFrontPromises)||void 0===e||e.push(r.loadFont(i.fontFamily,n)))}}));var s=[];Promise.all(r.loadFrontPromises).then((function(){var o;r.clear(),r.designs=[],r.texts=[],r.designUrl=void 0,r.printRect=void 0,e.fabric.util.clearFabricFontCache();var a=null===(o=r.json)||void 0===o?void 0:o.background;r.json.background=void 0,r.loadFromJSON(r.json,(function(){s.length>0&&s.forEach((function(t){r.removeObject(t)}));var i=[];"undefined"==typeof document&&r.getObjects().forEach((function(t){if("i-text"===t.type){var n=t;n.fontFile&&i.push(new Promise((function(i){(void 0).load(n.fontFile).then((function(s){console.log("load font",n.fontFamily,n.fontFile);var o=s.getPath(n.text,0,0,n.fontSize),a=new e.fabric.Path(o.toPathData(20),{top:n.top,left:n.left,width:n.width,height:n.height,scaleX:n.scaleX,scaleY:n.scaleY,angle:n.angle,fill:n.fill,globalCompositeOperation:n.globalCompositeOperation});a.left&&n.width>a.width&&(a.left+=(n.width-a.width)*a.scaleX/2),a.top&&n.height>a.height&&(a.top+=(n.height-a.height)*a.scaleY/2),r.add(a),r.removeObject(t),i(a)})).catch((function(t){return console.log("Error fetching font:",t)}))})))}})),Promise.all(i).then((function(){r.setBackgroundColor(a,(function(){r.renderAll.bind(r),r._onupdate(!0),t(r),r.loadingJson=!1}))}))}),(function(t,e){var o;if("printRect"!==e.name||r.printRect)if("printStroke"===e.name)r.printStroke=e,r.printStroke.hasControls=!1,r.printStroke.selectable=!1;else if("viewRect"===e.name)r.viewRect=e,r.viewRect.hasControls=!1,r.viewRect.selectable=!1;else if("image"===e.type){if(c=e,r.designUrl===c.src)return s.push(c);var a=c.getElement();if(u=c.width*c.scaleX,f=c.height*c.scaleY,c.set({width:a.naturalWidth,height:a.naturalHeight,scaleX:u/a.naturalWidth,scaleY:f/a.naturalHeight}),n){var h=e.adjustedScale||1,l=e.scaleX||1;l*h>r.DPI/r.minDPI&&(h=1/l),e.adjustedScale=h}r.designs.push(c),r.designUrl=c.src}else"i-text"===e.type&&(null===(o=r.texts)||void 0===o||o.push(e));else{var c,u=(c=e).width+2*c.left,f=c.height+2*c.top;if(r.displayWidth)r.setZoom(r.displayWidth/u);else if(i){var d=i/c.width;u*=d,f*=d,r.setZoom(d),r.setWidth(u),r.setHeight(f)}r.printRect=c,r.printWidth=c.width,r.printHeight=c.height,r.printRect.hasControls=!1,r.printRect.selectable=!1}}))}))}))},i.prototype.setDisplaySize=function(t){var e=this.printRect;if(e){var i=e.width+2*e.left;this.setZoom(t/i),this.setWidth(t),this.setHeight(t)}},i.prototype.lockEdit=function(){var t;this.isLockEdit=!0,this.selection=!1,this.defaultCursor="default",this.strokeColor="#4e5bf2",null===(t=this.printStroke)||void 0===t||t.set({stroke:this.strokeColor}),this.getObjects().forEach((function(t){"image"===t.type&&t.isCustom||(t.set("selectable",!1),t.defaultCursor="default")})),this.on("mouse:over",(function(t){null==t.target||"image"===t.target.type&&t.target.isCustom||(t.target.hoverCursor="default")})),this._grayoutNonPrintArea()},i.prototype.setRestricted=function(t){void 0===t&&(t=!0),this.restricted=t,this.hasDesign()&&this.designs.forEach((function(e){e.lockRotation=t}))},i.prototype.cloneActiveObject=function(){var t=this.getActiveObject();if(t){var i=e.fabric.util.object.clone(t);this.add(i),this.setActiveObject(i)}},i.prototype.addDesign=function(t,e){var i=this;return void 0===e&&(e=1),new Promise((function(n){if(i.loadingJson)return n(!1);i.designUrl=t,i.hasDesign()&&i.designs.forEach((function(e){if(e._element.src===t)return n(e)})),i._loadImg(t).then((function(t){t.adjustedScale=e,i._addDesign(t),i.designs.push(t),i._onupdate(),n(t)}))}))},i.prototype.addCustomImage=function(t){var e=this;return new Promise((function(i){e._loadImg(t).then((function(t){t.set({id:"image_"+Date.now(),globalCompositeOperation:"destination-over",isCustom:!0}),e._fitImageToRect(t,e.printRect);var n=e.getCustomImage();n&&e.removeObject(n),e.add(t),e.bringToFront(t),e.setActiveObject(t),e._onupdate(),i(t)}))}))},i.prototype.changeCustomImage=function(t){var i=this;return new Promise((function(n){i._loadImg(t).then((function(t){t.set({id:"image_"+Date.now(),globalCompositeOperation:"destination-over",isCustom:!0}),i.oldImage=i.oldImage||i.getCustomImage();var r=i.oldImage;if(r){var s=r.width*r.scaleX/t.width;t.height*s<r.height*r.scaleY&&(s=r.height*r.scaleY/t.height);var o=r.angle*Math.PI/180,a=(r.width*r.scaleX-t.width*s)/2,h=r.top+a*Math.sin(o),l=r.left+a*Math.cos(o),c=(r.height*r.scaleY-t.height*s)/2;Math.abs(c)>Math.abs(a)&&(h=r.top+c*Math.cos(o),l=r.left-c*Math.sin(o));var u=new e.fabric.Rect({width:r.width*r.scaleX,height:r.height*r.scaleY,angle:r.angle,top:r.top,left:r.left,absolutePositioned:!0});t.set({scaleX:s,scaleY:s,top:h,left:l,angle:r.angle,oldImage:r}),t.clipPath=u}var f=i.getCustomImage();f&&i.removeObject(f),i.add(t),i.bringToFront(t),i.setActiveObject(t),i._onupdate(),n(t)}))}))},i.prototype.getCustomImage=function(){return this.getObjects().find((function(t){return"image"===t.type&&t.isCustom}))},i.prototype.getRequiredSize=function(t){return{width:parseInt((t.width*t.scaleX).toString()),height:parseInt((t.height*t.scaleY).toString())}},i.prototype.hasCustomText=function(){return this.texts.length>0},i.prototype.addText=function(t,i,n,r){var s,o;if(void 0===t&&(t="Hello"),void 0===n&&(n="Roboto"),void 0===r&&(r=null),!(this.texts.length>=this.textLimit||this.loadingJson)){i||(i="text_"+this.getObjects().length);var a=new e.fabric.IText(t,{id:"text_"+Date.now(),name:i,fontSize:100,lockUniScaling:!1,lineHeight:1,textWidth:"limited",textAlign:"center",editable:!1,maxLength:16,fontFamily:n,isCustom:!0});return r&&a.set({fontUrl:r}),a.scaleX=.8*(null===(s=this.printRect)||void 0===s?void 0:s.width)/a.width,a.scaleY=a.scaleX,a.left=this.width/this.getZoom()/2-a.width*a.scaleX/2,a.top=this.height/this.getZoom()/2-a.height*a.scaleY/2,this.add(a),this.setActiveObject(a),null===(o=this.texts)||void 0===o||o.push(a),this.renderAll(),this._onupdate(),a}},i.prototype._resetTextPosition=function(t){var e,i;t.width*t.scaleX>(null===(e=this.printRect)||void 0===e?void 0:e.width)&&(t.scaleX=(null===(i=this.printRect)||void 0===i?void 0:i.width)/t.width,t.scaleY=t.scaleX),this._correctPosition(t)},i.prototype.loadFont=function(t,e){return i=this,n=void 0,s=function(){var i,n;return function(t,e){var i,n,r,s,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(h){return function(a){if(i)throw new TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(o=0)),o;)try{if(i=1,n&&(r=2&a[0]?n.return:a[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,a[1])).done)return r;switch(n=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!((r=(r=o.trys).length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){o.label=a[1];break}if(6===a[0]&&o.label<r[1]){o.label=r[1],r=a;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(a);break}r[2]&&o.ops.pop(),o.trys.pop();continue}a=e.call(t,o)}catch(t){a=[6,t],n=0}finally{i=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,h])}}}(this,(function(r){switch(r.label){case 0:if(!t||!e||"undefined"==typeof document)return[2,!1];i=new FontFace(t,"url("+e+")"),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,i.load()];case 2:return r.sent(),document.fonts.add(i),[3,4];case 3:return n=r.sent(),console.log(n),[3,4];case 4:return[2]}}))},new((r=void 0)||(r=Promise))((function(t,e){function o(t){try{h(s.next(t))}catch(t){e(t)}}function a(t){try{h(s.throw(t))}catch(t){e(t)}}function h(e){var i;e.done?t(e.value):(i=e.value,i instanceof r?i:new r((function(t){t(i)}))).then(o,a)}h((s=s.apply(i,n||[])).next())}));var i,n,r,s},i.prototype.canAddText=function(){return this.texts.length<this.textLimit},i.prototype.updateText=function(t,e){t&&"i-text"===t.type&&(this._saveOldPosition(t),t.text=this.convertText(t,e),this._correctTextPosition(t),this.renderAll(),this._onupdate())},i.prototype.convertText=function(t,e){if(!t||"i-text"!==t.type||!e)return"";var i,n=e;if(t.convertText)switch(t.convertText){case"capitalize":n=(i=e)&&"string"==typeof i?i.split(" ").map((function(t){return t.charAt(0).toUpperCase()+t.slice(1).toLowerCase()})).join(" "):"";break;case"uppercase":n=e.toUpperCase();break;case"lowercase":n=e.toLowerCase()}return n},i.prototype.updateTextName=function(t,e){t&&"i-text"===t.type&&t.set("name",e)},i.prototype._saveOldPosition=function(t,e){void 0===e&&(e=!1),e&&(t.oldWidth=void 0,t.oldScaleX=void 0,t.oldLeft=void 0,t.oldTop=void 0,t.centerX=void 0);var i=t.width*t.scaleX;t.oldWidth=t.oldWidth||i,t.oldScaleX=t.oldScaleX||t.scaleX,t.oldLeft=t.oldLeft||t.left,t.oldTop=t.oldTop||t.top,t.centerX=t.centerX||t.left+i/2},i.prototype._correctTextPosition=function(t){this.renderAll();var e=this.isLockEdit?t.textWidth:"flexible";if(!e&&t.fixWidth&&(e="limited"),"fixed"===e?t.scaleX=t.oldWidth/t.width:"limited"===e&&(t.width*t.oldScaleX>t.oldWidth?t.scaleX=t.oldWidth/t.width:t.scaleX=t.oldScaleX),t.width*t.scaleX>this.printWidth&&(t.scaleX=this.printWidth/t.width),t.width*t.scaleX>this.printWidth&&(t.scaleX=this.printWidth/t.width),0===t.angle)"center"===t.textAlign?t.left=t.centerX-t.width*t.scaleX/2:"right"===t.textAlign&&(t.left=t.oldLeft+t.oldWidth-t.width*t.scaleX);else{var i=0;"center"===t.textAlign?i=(t.oldWidth-t.width*t.scaleX)/2:"right"===t.textAlign&&(i=t.oldWidth-t.width*t.scaleX);var n=t.angle*Math.PI/180;t.left=t.oldLeft+i*Math.cos(n),t.top=t.oldTop+i*Math.sin(n)}},i.prototype.updateFont=function(t,i,n){t&&"i-text"===t.type&&(this._saveOldPosition(t),t.set("fontFamily",i),t.set("fontUrl",n),e.fabric.util.clearFabricFontCache(),this._correctTextPosition(t),this.renderAll(),this._onupdate())},i.prototype.setColor=function(t,e){t&&"i-text"===t.type&&(t.fill=e,this.renderAll(),this._onupdate())},i.prototype.getObjectByName=function(t){return this.getObjects().find((function(e){return e.name===t}))},i.prototype.changeDesignByCharacter=function(t){if(t&&"string"==typeof t&&(t=t.toLowerCase(),this.designUrl))return this.designUrl.includes("custom")?this.replaceDesign(this.designUrl.replace(/\w+(.\w+$)/g,"".concat(t,"$1"))):this.replaceDesign(this.designUrl.replace(/\w+(.\w+$)/g,"custom/".concat(t,"$1")))},i.prototype.replaceDesign=function(t,i){var n=this;return void 0===i&&(i=1),new Promise((function(i){if(0===n.designs.length||n.loadingJson)return i(!1);n.designUrl=t;var r=n.designs[n.designs.length-1];if(r._element.src===t)return i(r);e.fabric.Image.fromURL(t,(function(e){var s=r._element,o=e.width/s.width;Math.abs(s.width/s.height-e.width/e.height)<.005?(s.crossOrigin="Anonymous",s.src=t,s.onload=function(){var t=r.width*r.scaleX,e=r.height*r.scaleY;r.set({width:s.naturalWidth,height:s.naturalHeight,scaleX:t/s.naturalWidth,scaleY:e/s.naturalHeight,adjustedScale:o}),n.renderAll(),n._onupdate(),i(r)}):(n._addDesign(e),n._copyDesignConfig(e,r),n.removeObject(r),n.designs.splice(n.designs.length-1,1),n.designs.push(e),n._onupdate(),i(e))}),{crossOrigin:"Anonymous"})}))},i.prototype._copyDesignConfig=function(t,e){var i,n,r,s=Math.max(null===(i=this.printRect)||void 0===i?void 0:i.width,e.getScaledWidth()),o=Math.max(null===(n=this.printRect)||void 0===n?void 0:n.height,e.getScaledHeight()),a=s/t.width;t.height*a>o&&(a=o/t.height),t.scaleX=t.scaleY=a;var h=Math.min(e.top,null===(r=this.printRect)||void 0===r?void 0:r.top);t.top=Math.max(e.top+(e.getScaledHeight()-t.getScaledHeight())/2,h),t.left=e.left+(e.getScaledWidth()-t.getScaledWidth())/2},i.prototype._addDesign=function(t){var e=this;this.restricted&&(t.lockRotation=!0),this._fitImageToRect(t,this.printRect),this.add(t),this.setActiveObject(t),this.getObjects().forEach((function(t){("i-text"===t.type||"image"===t.type&&t.isCustom)&&e.bringToFront(t)})),this.renderAll()},i.prototype.setBackgroundColor=function(e,i){return t.prototype.setBackgroundColor.call(this,e,i),this.bgColor=e,this.renderAll(),this},i.prototype.reset=function(){var t=this;this.getObjects().forEach((function(e){"image"!==e.type&&"i-text"!==e.type||t.removeObject(e)})),this.designs=[],this.texts=[],this.designUrl=void 0,this.renderAll(),this._onupdate()},i.prototype.removeDesign=function(){var t=this;this.designs.forEach((function(e){t.removeObject(e)})),this.designs=[],this.designUrl=void 0,this.renderAll(),this._onupdate()},i.prototype.removeObject=function(e,i){var n,r,s;(void 0===i&&(i=!1),e)&&(t.prototype.remove.call(this,e),"image"===e.type&&(s=this.designs.indexOf(e))>-1&&this.designs.splice(s,1),"i-text"===e.type&&(s=null===(n=this.texts)||void 0===n?void 0:n.indexOf(e))&&s>-1&&(null===(r=this.texts)||void 0===r||r.splice(s,1)),("i-text"===e.type||e.isCustom)&&this.manager&&!i&&this.manager._removeObject(this,e),this._onupdate())},i.prototype.removeActiveObject=function(){var t=this.getActiveObject();t&&this.removeObject(t)},i.prototype.hasDesign=function(){var t,e;return this.designs.length>0||(null===(t=this.texts)||void 0===t?void 0:t.length)&&(null===(e=this.texts)||void 0===e?void 0:e.length)>0},i.prototype.hasArtwork=function(){return this.designs.length>0},i.prototype._correctPosition=function(t){var e=this.printRect;if(e&&t!==e&&this.restricted&&0===t.angle){var i=t.getScaledWidth(),n=t.getScaledHeight();t.top+n>e.top+e.height&&(t.top=e.top+e.height-n),t.top<e.top&&(t.top=e.top),t.left+i>e.left+e.width&&(t.left=e.left+e.width-i),t.left<e.left&&(t.left=e.left)}"i-text"===t.type&&this._saveOldPosition(t,!0)},i.prototype._correctScale=function(t){var e=this.printRect;if(this.restricted&&e){var i=t.adjustedScale||1,n=t.scaleX;t.width*n>e.width&&(n=e.width/t.width,"i-text"===t.type&&(t.scaleX=n)),t.height*n>e.height&&(n=e.height/t.height,"i-text"===t.type&&(t.scaleY=n)),"image"===t.type&&n*i>this.DPI/this.minDPI&&(n=this.DPI/this.minDPI/i),"image"===t.type&&(t.scaleX=t.scaleY=n),"i-text"===t.type&&this._saveOldPosition(t,!0)}},i.prototype.align=function(t,e){var i,n,r,s,o,a,h,l,c,u,f,d,g,p,v,m,y,_,x,b,C,S,w,T;if(e||(e=this.getActiveObject()),e){var O,k,P=this.printRect;if(e){switch(t){case"left":e.angle<=90&&(O=(null===(i=e.aCoords)||void 0===i?void 0:i.tl.x)-(null===(n=e.aCoords)||void 0===n?void 0:n.bl.x)),e.angle>90&&e.angle<=180&&(O=(null===(r=e.aCoords)||void 0===r?void 0:r.tl.x)-(null===(s=e.aCoords)||void 0===s?void 0:s.br.x)),e.angle>180&&e.angle<=270&&(O=(null===(o=e.aCoords)||void 0===o?void 0:o.tl.x)-(null===(a=e.aCoords)||void 0===a?void 0:a.tr.x)),e.angle>270&&(O=0),e.set({left:((null==P?void 0:P.left)||0)+O});break;case"right":e.angle<=90&&(O=(null===(h=e.aCoords)||void 0===h?void 0:h.tl.x)-(null===(l=e.aCoords)||void 0===l?void 0:l.tr.x)),e.angle>90&&e.angle<=180&&(O=0),e.angle>180&&e.angle<=270&&(O=(null===(c=e.aCoords)||void 0===c?void 0:c.tl.x)-(null===(u=e.aCoords)||void 0===u?void 0:u.bl.x)),e.angle>270&&(O=(null===(f=e.aCoords)||void 0===f?void 0:f.tl.x)-(null===(d=e.aCoords)||void 0===d?void 0:d.br.x)),e.set({left:(null==P?void 0:P.left)+(null==P?void 0:P.width)+O});break;case"top":e.angle<=90&&(k=0),e.angle>90&&e.angle<=180&&(k=(null===(g=e.aCoords)||void 0===g?void 0:g.tl.y)-(null===(p=e.aCoords)||void 0===p?void 0:p.bl.y)),e.angle>180&&e.angle<=270&&(k=(null===(v=e.aCoords)||void 0===v?void 0:v.tl.y)-(null===(m=e.aCoords)||void 0===m?void 0:m.br.y)),e.angle>270&&(k=(null===(y=e.aCoords)||void 0===y?void 0:y.tl.y)-(null===(_=e.aCoords)||void 0===_?void 0:_.tr.y)),e.set({top:(null==P?void 0:P.top)+k});break;case"bottom":e.angle<=90&&(k=(null===(x=e.aCoords)||void 0===x?void 0:x.tl.y)-(null===(b=e.aCoords)||void 0===b?void 0:b.br.y)),e.angle>90&&e.angle<=180&&(k=(null===(C=e.aCoords)||void 0===C?void 0:C.tl.y)-(null===(S=e.aCoords)||void 0===S?void 0:S.tr.y)),e.angle>180&&e.angle<=270&&(k=0),e.angle>270&&(k=(null===(w=e.aCoords)||void 0===w?void 0:w.tl.y)-(null===(T=e.aCoords)||void 0===T?void 0:T.bl.y)),e.set({top:(null==P?void 0:P.top)+(null==P?void 0:P.height)+k});break;case"center":e.viewportCenterH();break;case"vcenter":e.viewportCenterV();break;case"reset":this._fitImageToRect(e,P)}this.renderAll(),this._onupdate()}}},i.prototype.getMinDPI=function(){var t,e,i=this.designs,n=this.DPI;for(e=0;e<i.length;++e){var r=i[e].adjustedScale||1,s=(null!==(t=i[e].scaleX)&&void 0!==t?t:1)*r,o=parseInt((this.DPI/s).toString());o<n&&(n=o)}return n},i.prototype.isGoodDPI=function(){return this.getMinDPI()>=(this.DPI+this.minDPI)/2},i.prototype.isNormalDPI=function(){var t=this.getMinDPI();return t>=this.minDPI&&t<(this.DPI+this.minDPI)/2},i.prototype.isLowDPI=function(){return this.getMinDPI()<this.minDPI},i.prototype.checkDPI=function(){return this.isGoodDPI()?"good":this.isLowDPI()?"low":"normal"},i.prototype._loadImg=function(t){return new Promise((function(i,n){t&&void 0!==e.fabric||n(),e.fabric.Image.fromURL(t||"",(function(t){i(t)}),{crossOrigin:"Anonymous"})}))},i.prototype.exportCanvas=function(t){var i,n,r,s;void 0===t&&(t=1500),this.printWidth/this.printHeight>1.5&&(t*=1.5);var o=this.getZoom(),a=this.getWidth(),h=1.4*Math.max(this.printWidth,this.printHeight);0===t&&(t=h);var l=t/h;this.setWidth(t),this.setHeight(t),this.setZoom(l);var c=this.printWidth*l,u=this.printHeight*l;this.printCanvas||("undefined"==typeof document?this.printCanvas=new e.fabric.StaticCanvas(null,{width:c,height:u}):this.printCanvas=document.createElement("canvas")),this.printCanvas.width=c,this.printCanvas.height=u;var f=this.printCanvas.getContext("2d"),d=this.printRect,g=(null==d?void 0:d.left)*l,p=(null==d?void 0:d.top)*l;return null===(i=this.printStroke)||void 0===i||i.set({stroke:""}),null===(n=this.viewRect)||void 0===n||n.set({stroke:""}),this.setBackgroundColor("",(function(){})),null==f||f.clearRect(0,0,c,u),null==f||f.drawImage(this.toCanvasElement(),g,p,c,u,0,0,c,u),null===(r=this.printStroke)||void 0===r||r.set({stroke:this.strokeColor}),null===(s=this.viewRect)||void 0===s||s.set({stroke:this.viewStrokeColor}),this.setBackgroundColor(this.bgColor||"",(function(){})),this.setZoom(o),this.setWidth(a),this.setHeight(a),this.printCanvas},i.prototype.exportPrint=function(){var t=this,e=this.printRect,i=null==e?void 0:e.left,n=null==e?void 0:e.top,r=this.getObjects(),s=["printRect","printStroke","viewRect"];return r.forEach((function(e){var r;s.includes(null!==(r=e.name)&&void 0!==r?r:"")?t.removeObject(e):(e.top=e.top-n,e.left=e.left-i),"image"!==e.type||e.isCustom||e.set({globalCompositeOperation:"source-over"})})),this.setZoom(1),this.setWidth(null==e?void 0:e.width),this.setHeight(null==e?void 0:e.height),this.renderAll(),this},i.prototype.exportPNG=function(t){var e,i,n,r,s;void 0===t&&(t=!0);var o=this.getZoom(),a=1/o;t||(a=1920/Math.max(this.printWidth,this.printHeight)/o);var h=this.printRect;null===(e=this.printStroke)||void 0===e||e.set({stroke:""}),null===(i=this.viewRect)||void 0===i||i.set({stroke:""}),this.setBackgroundColor("",(function(){}));var l=this.toDataURL({format:"png",multiplier:a,left:(null==h?void 0:h.left)*o,top:(null==h?void 0:h.top)*o,width:(null==h?void 0:h.width)*o,height:(null==h?void 0:h.height)*o});return null===(n=this.printStroke)||void 0===n||n.set({stroke:this.strokeColor}),null===(r=this.viewRect)||void 0===r||r.set({stroke:this.viewStrokeColor}),this.setBackgroundColor(null!==(s=this.bgColor)&&void 0!==s?s:"",(function(){})),l},i.prototype.exportJson=function(){return this.toJSON(["id","name","fontUrl","DPI","adjustedScale","maxLength","textWidth","textAlign","isCustom"])},i.prototype.backup=function(t){this.json=t||this.toJSON(["name"])},i.prototype.show=function(){this.canvasEl&&(this.canvasEl.parentElement.style.display="block")},i.prototype.hide=function(){this.canvasEl&&(this.canvasEl.parentElement.style.display="none")},i.prototype._fitImageToRect=function(t,e,i){void 0===i&&(i=!0);var n=t.adjustedScale||1,r=e.width/t.width;t.height*r>e.height&&(r=e.height/t.height),r*n>this.DPI/this.minDPI&&(r=this.DPI/this.minDPI/n),t.scaleX=t.scaleY=r,t.angle=0,t.top=e.top+(e.height-t.getScaledHeight())/2,t.left=e.left+(e.width-t.getScaledWidth())/2},i.prototype._grayoutNonPrintArea=function(){var t=this;this.on("after:render",(function(){var e=t.contextContainer,i=t.printRect;if(i){var n=i.getBoundingRect(),r=n.left-2,s=n.top-2,o=n.width+2,a=n.height+2;e&&(e.fillStyle=t._hexToRgbA(t.bgColor),e.beginPath(),e.rect(0,0,r,t.height),e.rect(r+o,0,(t.width||0)-r-o,t.height),e.rect(r,0,o,s),e.rect(r,s+a,o,(t.height||0)-s-a),e.fill())}}))},i.prototype._hexToRgbA=function(t,e){var i;return void 0===e&&(e="0.5"),/^#([A-Fa-f0-9]{3}){1,2}$/.test(t)?(3===(i=t.substring(1).split("")).length&&(i=[i[0],i[0],i[1],i[1],i[2],i[2]]),"rgba("+[(i="0x"+i.join(""))>>16&255,i>>8&255,255&i].join(",")+","+e+")"):"rgba(255, 255, 255, "+e+")"},i.prototype.addUpdateCallback=function(t){"function"==typeof t&&this.onUpdates.push(t)},i.prototype._onupdate=function(t){void 0===t&&(t=!1);var e=this;this.onUpdates.length&&this.onUpdates.forEach((function(t){t(e)})),this.onupdate&&this.onupdate(this),this.manager&&!t&&this.manager._designUpdated(this)},i}(e.fabric.Canvas)})(),n})()));