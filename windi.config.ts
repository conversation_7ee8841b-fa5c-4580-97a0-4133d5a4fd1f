import type { Plugin } from 'windicss/types/interfaces'
// colors
import colors from 'windicss/colors'

import { defineConfig } from 'windicss/helpers'

import AspectRatioPlugin from 'windicss/plugin/aspect-ratio'
import FiltersPlugin from 'windicss/plugin/filters'
import LineClampPlugin from 'windicss/plugin/line-clamp'
// plugins
import TypographyPlugin from 'windicss/plugin/typography'

export default defineConfig({
  darkMode: 'class',
  attributify: false,
  extract: {
    include: [
      './components/**/*.{vue,js}',
      './composables/**/*.{js,ts}',
      './content/**/*.md',
      './layouts/**/*.vue',
      './pages/**/*.vue',
      './plugins/**/*.{js,ts}',
      './utils/**/*.{js,ts}',
      './app.vue',
      './error.vue'
    ]
  },
  theme: {
    extend: {
      spacing: {
        'sm': '425px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1300px'
      },
      container: {
        center: true,
        padding: '0.75rem'
      },
      screens: {
        'sm': '425px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1300px'
      },
      colors: {
        'primary': {
          DEFAULT: 'var(--color-primary)',
          lighter: 'var(--color-primary-lighter)',
          light: 'var(--color-primary-light)',
          hover: 'var(--color-primary-hover)',
          focus: 'var(--color-primary-focus)',
          active: 'var(--color-primary-active)'
        },
        'secondary': {
          DEFAULT: 'var(--color-secondary)',
          lighter: 'var(--color-secondary-lighter)',
          light: 'var(--color-secondary-light)',
          hover: 'var(--color-secondary-hover)',
          focus: 'var(--color-secondary-focus)',
          active: 'var(--color-secondary-active)'
        },
        'contrast': 'var(--color-contrast)',
        'green': colors.green,
        'blue': colors.blue,
        'red': colors.red,
        'error': 'var(--color-error)',
        'gray': colors.gray,
        'slate': colors.slate,
        'black': colors.black,
        'muted': 'var(--color-muted)',
        'givehug-gray': {
          DEFAULT: 'rgba(243, 243, 243, 1)',
          hover: 'rgba(230, 230, 230, 1)',
          muted: '#595959'
        },
        'givehug-gray2': {
          DEFAULT: '#EAEAEA',
          hover: '#D9D9D9'
        }
      },
      boxShadow: {
        'custom': '0 0 3px 0 var(--color-primary)',
        'custom2': '0 0 5px 0 var(--color-primary)',
        'custom3': '0 0 15px 0 var(--color-primary)',
        'custom4': 'var(--clg-effect-sem-shadow-elevation-1,0px 1px 2px 0px #0000004d,0px 1px 3px 1px #00000026)',
        'custom5': 'var(--clg-effect-sem-shadow-elevation-3,0px 1px 3px 0px #0000004d,0px 4px 8px 3px #00000026)',
        'custom5VFlip': 'var(--clg-effect-sem-shadow-elevation-3,0px -1px 3px 0px #0000004d,0px -4px 8px 3px #00000026)',
        'color': '0 0 8px 1px var(--color-primary)',
        'color-active': '0 0 0 2px var(--color-primary)'
      }
    }
  },
  shortcuts: {
    'btn': 'cursor-pointer disabled:cursor-not-allowed',
    'btn-text': 'btn hover:text-primary-hover focus:text-primary-focus active:text-primary',
    'btn-border': 'btn border border-gray-300 hover:(text-primary-hover border-primary-hover) active:(text-primary border-primary shadow-custom3)',
    'btn-fill': 'btn bg-primary text-contrast hover:(bg-primary-hover shadow-custom2) active:(bg-primary-active shadow-custom3)',
    'btn-border-fill': 'btn border border-gray-300 hover:(text-contrast bg-primary-hover border-primary-hover) active:(text-contrast bg-primary border-primary)',
    'btn-text-red': 'btn text-red-600 hover:text-red-800 focus:text-red-800 active:text-red',
    'btn-text-black': 'btn hover:text-gray-600 focus:text-gray-800 active:text-gray',
    'btn-border-fill-red': 'btn border text-red-800 border-red-800 hover:(text-white bg-red-800 border-red-800) active:(text-white bg-red border-red)',
    'btn-fill-black': 'bg-gray-600 text-white hover:(bg-gray-700 shadow-custom2) active:(bg-black shadow-custom3)',

    'center-flex': 'flex justify-center items-center',
    'badge': 'h-4 w-4 rounded-full absolute top-0 -right-2 text-white text-[0.75rem] center-flex',
    'text-overflow-hidden': 'text-ellipsis overflow-hidden whitespace-nowrap block',
    'transition-default': 'transition-all duration-200 ease-linear',
    'position-center': 'left-[50%] top-[50%]  transform -translate-x-1/2 -translate-y-1/2 z-2',
    'position-center-x': 'left-[50%] transform -translate-x-1/2 z-2',
    'position-center-y': 'top-[50%] transform -translate-y-1/2 z-2',
    'bottom-fixed': 'fixed bottom-0 left-0',
    'bottom-absolute': 'absolute bottom-0 left-0',
    'cs-container': 'max-w-[1500px] w-full mx-auto px-5',
    'bg-givehug-gray-with-hover': '!bg-givehug-gray hover:!bg-givehug-gray-hover',
    'bg-givehug-gray2-with-hover': '!bg-givehug-gray2 hover:!bg-givehug-gray2-hover'
  },
  plugins: [
    // filters plugin require for navbar blur
    FiltersPlugin as Plugin,
    TypographyPlugin as Plugin,
    AspectRatioPlugin as Plugin,
    LineClampPlugin as Plugin
  ] as Plugin[]
})
