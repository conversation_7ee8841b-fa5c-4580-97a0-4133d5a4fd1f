<script setup lang="ts" >
import type { NuxtError } from 'nuxt/app'

const layoutComponent = getThemeComponent('layout')
const ErrorComponent = getThemeComponent('error')
const props = defineProps({
  error: {
    required: true,
    type: Object as PropType<NuxtError>
  }
})

await useSetup()

const event = useRequestEvent()
setResponseStatus(event, props.error?.statusCode, props.error?.statusMessage)
</script>

<template>
  <div>
    <layout-component>
      <div v-if="useRuntimeConfig().public.appEnv === 'dev'" v-html="error?.stack" />
      <error-component :error="error" />
    </layout-component>
  </div>
</template>

<style scoped>

</style>
