const currentImage = ref<number|undefined>()
const currentImageList = ref<ImageData[]>([])
const totalVideos = ref<number>(0)
const colorsMap = ref(new Map<number, string>()) // Map image at index with color
export function useViewBoxImage () {
  function initState (images: any[], total: number) {
    currentImageList.value = images
    totalVideos.value = total
    colorsMap.value.clear()
  }

  function updateCurrentImage (value: number|undefined) {
    currentImage.value = value
  }

  function updateColorMap (index: number, color: string) {
    colorsMap.value.set(index, color)
  }

  return {
    currentImage,
    updateCurrentImage,
    initState,
    currentImageList,
    totalVideos,
    colorsMap,
    updateColorMap
  }
}
