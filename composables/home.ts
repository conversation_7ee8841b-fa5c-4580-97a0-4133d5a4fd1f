export async function useHomePage() {
  const homepageData = useState<HomePageResponse>('homepageData')
  const listProductData = shallowRef<Array<{ name: string, products: Product[] }>>([])
  const givehugListProductData = shallowRef<Array<{ name: string, products: Product[] }>>([])
  const { $fetchDefault } = useNuxtApp()

  if (!homepageData.value) {
    const { data } = await $fetchDefault<ResponseData<HomePageResponse>>($api.API_HOME_PAGE)
    homepageData.value = data
  }

  if (!homepageData.value) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }

  const {
    best_seller: bestSeller,
    featured,
    new_arrivals: newArrivals,
    other
  } = homepageData.value

  listProductData.value = [
    {
      name: storeInfo().feature_text || 'Featured',
      products: featured
    },
    ...other || [],
    {
      name: 'New arrivals',
      products: newArrivals
    },
    {
      name: 'Best sellers',
      products: bestSeller
    }
  ]

  givehugListProductData.value = other || []

  return {
    listProductData,
    givehugListProductData
  }
}
