import type { LocationQuery } from '#vue-router'
import DOMPurify from 'dompurify'
import { useListingStore } from '~~/store/listing'
import { useUserSession } from '~~/store/userSession'
import { useCampaignStore } from '~/store/campaign'

export function useCampaign(campaignSlug: Slug, isModal = false) {
  const localePath = useLocalePath()
  const { $i18n, $imgUrl, $getRawPrice, $getProductVariantsBySelectedKeyAndCountry, $userCountryForPricing } = useNuxtApp()
  const $route = useRoute()
  const $router = useRouter()

  /**
   * Storefronts Campaign URL Format
   *
   * v3: /[campaignName]?product=[productName]&color=[color]
   * v4: /[campaignName]/[productName]?color=[color]
   *
   * and now we need backward compatibility
   */
  const isV3TypeUrl = !!$route.query?.product

  const {
    getCampaignBySlug,
    getProductStats,
    getPromotion,
    getVariantsByProductId
    // getProductsSimilar,
  } = useCampaignStore()
  const userSession = useUserSession()

  const productStats: ProductStats = reactive({
    add_to_cart: 0,
    visit: 0
  })
  const relatedProducts = ref<Product[]>()
  const relatedCartProducts = ref<Product[]>()

  const campaignPromotions = shallowRef<Promotion[]>()

  const userCampaignOption: UserCampaignOption = shallowReactive<UserCampaignOption>({
    currentProduct: {},
    currentOptions: {},
    optionListFull: {},
    optionList: {},
    imagesList: [],
    currentVariant: undefined,
    currentPrice: 0,
    currentOldPrice: 0,
    quantity: 1,
    optionError: false,
    isAddToCart: false
  })

  const campaignData = shallowReactive<Campaign>({})

  const similarProducts = shallowRef<SimilarProducts[]>([])

  // computed
  const campaignId = computed(() => campaignData.id)
  const isShowProductListDropdown = computed(() => {
    return storeInfo().product_select_type === 'dropdown'
      || storeInfo().store_type === 'express_listing'
  })

  const dataDescription = computed(() => {
    return DOMPurify.sanitize(campaignData?.description || '')
  })

  const dataProductDetail = computed(() => {
    return DOMPurify.sanitize(userCampaignOption.currentProduct?.description || '')
  })

  watch(() => userCampaignOption.currentOptions, optionChange)
  watch(() => $route.query, () => {
    if (userCampaignOption.currentProduct !== undefined) {
      userCampaignOption.currentProduct.currentOptions = getCurrentOptions()
    }
  })

  async function optionChange() {
    const variantKey = Object.keys(userCampaignOption.optionListFull).map(option => (userCampaignOption.currentOptions[option]) || userCampaignOption.optionListFull[option][0]).toString().replace(/ /g, '_').replace(/,/g, '-')
    const productId = userCampaignOption.currentProduct?.id
    const sellerId = campaignData.seller_id ?? storeInfo.seller_id

    const currentProductVariants = productId ? await getVariantsByProductId(productId, sellerId) : (campaignData.variants ?? [])
    const currentVariant = $getProductVariantsBySelectedKeyAndCountry(currentProductVariants, variantKey, productId, $userCountryForPricing())
    if (userCampaignOption.optionListFull?.size?.length === 1) {
      userCampaignOption.currentOptions.size = userCampaignOption.optionListFull.size[0]
    }

    if (userCampaignOption.currentProduct !== undefined) {
      userCampaignOption.currentProduct.currentOptions = userCampaignOption.currentOptions
      userCampaignOption.currentProduct.optionsList = userCampaignOption.optionList
      userCampaignOption.currentProduct.variantsList = currentProductVariants
      userCampaignOption.currentProduct.market_location = userCampaignOption.currentProduct.market_location ?? campaignData.market_location
    }
    userCampaignOption.currentVariant = currentVariant
    userCampaignOption.currentPrice = useTestPrice().getPrice(userCampaignOption.currentProduct, currentVariant)
    userCampaignOption.currentOldPrice = useTestPrice().getOldPrice(userCampaignOption.currentProduct, currentVariant)
  }
  // method
  async function resetData() {
    await getCampaignData()

    if (campaignData.status === 'blocked') {
      return
    }

    if (!campaignData.id || !(campaignData.products?.length)) {
      return showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
    }
    // if (campaignData.id) {
    // similarProducts.value = await getProductsSimilar(campaignData.id || 0) || []
    // }
    await updateProduct(($route.params.productName || $route.query.product) as string)
    if (!campaignData.options?.group) {
      campaignData.options = {
        group: {
          limit: 1,
          name: null,
          extra_custom_fee: 0
        },
        options: []
      }
    }
    if (import.meta.client && campaignData?.id && !isModal) {
      const intervalTracking = setInterval(async () => {
        if (window.userActivity) {
          clearInterval(intervalTracking)
          Object.assign(productStats, await getProductStats(campaignData.id || 0))
          campaignPromotions.value = await getPromotion(campaignData.id || 0)
          preloadImgByColor(userCampaignOption.imagesList, userCampaignOption.optionListFull.color)
        }
      }, 500)
    }
  }

  async function getCampaignData() {
    const data = await getCampaignBySlug(campaignSlug)
    Object.assign(campaignData, toRaw(data))
  }

  async function updateProduct(productName: string, productId?: number) {
    if (!campaignData || !campaignData.id) {
      return
    }
    userCampaignOption.currentProduct = shallowReactive(getCurrentProduct(productName, productId))
    userCampaignOption.optionListFull = JSON.parse(userCampaignOption.currentProduct?.options as string)
    userCampaignOption.optionList = { ...userCampaignOption.optionListFull }

    Object.keys(userCampaignOption.optionList).forEach((key) => {
      if (userCampaignOption.optionList[key].length <= 1) {
        delete userCampaignOption.optionList[key]
      }
    })
    userCampaignOption.currentOptions = getCurrentOptions()
    userCampaignOption.imagesList = getImagesList()
    optionChange()
    await nextTick()
    if (import.meta.client && campaignData?.id) {
      const price = userCampaignOption.currentPrice || 0
      let trackData: {
        campaignId: number
        content_ids: number[]
        content_name: string | undefined
        content_category: string | undefined
        content_value: number[]
        content_quantity: number[]
        content_type: string
        currency: string
        value: number
        klaviyoData?: any
      } = {
        campaignId: campaignData?.id,
        content_ids: [userCampaignOption.currentProduct?.id || 0],
        content_name: campaignData?.name,
        content_category: userCampaignOption.currentProduct?.name,
        content_value: [price],
        content_quantity: [userCampaignOption.quantity],
        content_type: 'product',
        currency: 'USD',
        value: $getRawPrice(price * (Number(userCampaignOption.quantity) || 1), userCampaignOption.currentProduct.currency_code, 'USD')
      }
      // Track viewed_product event for Klaviyo
      const klaviyoData = buildKlaviyoData(userCampaignOption, campaignData)
      if (klaviyoData) {
        trackData = { ...trackData, klaviyoData }
      }
      useTracking().trackEvent({
        event: 'view_content',
        data: trackData
      })
    }

    if (!isModal) {
      $router.replace(getCampaignUrl({
        campaignSlug: campaignData.slug,
        ...((!isV3TypeUrl) ? ({ productName: userCampaignOption.currentProduct.name }) : ({})),
        query: {
          ...$route.query,
          ...userCampaignOption.currentOptions
        }
      }))

      if (campaignData.status === 'blocked') {
        const title = $i18n.t('This campaign is blocked')
        useHead({ title: `${title} | ${storeInfo().name}` })
        return {
          title
        }
      }

      const title = (campaignData.name) || ''
      const description = campaignData.description?.replace(/<[^>]+>/g, '').replace(/\s{2,}/g, ' ') || campaignData.name || ''
      const currentProduct = userCampaignOption.currentProduct

      const color = userCampaignOption.currentOptions.color
      const image = $imgUrl({
        path: currentProduct?.thumb_url || currentProduct?.full_path,
        type: 'share',
        color
      }) || ''

      const keywords = campaignData.collections?.map(item => item.name).join(', ')
      const price = currentProduct?.price
      const currency = currentProduct?.currency_code
      const name = Math.max(...(campaignData.collections?.map(item => item.id)) || [])
      const SKU = campaignData.id
      const jsonData: { meta_data: { meta_title?: string[], meta_description?: string[], meta_keywords?: string[] } } = JSON.parse(campaignData.attributes || '{}')
      const metaTitle = jsonData ? jsonData.meta_data?.meta_title?.slice(-1)[0] : title
      const metaDescription = jsonData ? jsonData.meta_data?.meta_description?.slice(-1)[0] : description
      const metaKeywords = jsonData ? jsonData.meta_data?.meta_keywords?.slice(-1)[0] : keywords

      createSEOMeta({ title: metaTitle ?? title, description: metaDescription ?? description, image, keywords: metaKeywords ?? keywords, price, currency, SKU, name })
    }
  }

  function updateOption({ key, value }: any) {
    if (!userCampaignOption.optionList[key]?.includes(value)) {
      uiManager().createPopup($i18n.t('value not exist on option', { key, value }))
      return
    }

    const newData: any = {}
    newData[key] = value
    const productName = userCampaignOption.currentProduct?.name
    userSession.setBehavior(key, value, productName)

    userCampaignOption.currentOptions = { ...userCampaignOption.currentOptions, ...newData }

    if (key === CAMPAIGN_OPTION.color) {
      userCampaignOption.imagesList = getImagesList()
    }

    if (!isModal) {
      const newQuery = { ...$route.query }
      newQuery[key] = value
      $router.replace(getCampaignUrl({
        campaignSlug: campaignData.slug,
        ...((!isV3TypeUrl) ? ({ productName }) : ({})),
        query: newQuery
      }))
    }
  }

  function getCurrentProduct(productName: string, productId?: number) {
    if (!campaignData.products?.length) {
      return {}
    }

    if (productId) {
      const product = campaignData.products?.find(product => product.id === productId)
      if (product) {
        return product
      }
    }

    if (productName) {
      const product = campaignData.products?.find(product => stringHelperToKey(product.name) === stringHelperToKey(productName))
      if (product) {
        return product
      }
    }

    return campaignData.products?.find(product => product.id === campaignData.default_product_id) || campaignData.products[0]
  }

  function getRelatedProduct() {
    const { currentProduct } = userCampaignOption
    const listProductIdCart: any = {
      filter: []
    }

    const itemProduct: any = {}
    if (currentProduct) {
      itemProduct.product_id = currentProduct.id
      itemProduct.campaign_id = currentProduct.campaign_id

      if (currentProduct.template_id) {
        itemProduct.template_id = currentProduct.template_id
      }
    }

    listProductIdCart.filter.push(itemProduct)
    listProductIdCart.type = 'cart'

    const relatedParam: any = {}
    relatedParam.filter = []
    const paramFilter = {} as any
    paramFilter.campaign_id = campaignData.id

    paramFilter.product_id = currentProduct?.id

    if (currentProduct?.template_id) {
      paramFilter.template_id = currentProduct?.template_id
    }

    relatedParam.filter.push(paramFilter)
    relatedParam.type = 'related'

    useListingStore().getRelatedProduct(relatedParam).then((result) => {
      relatedProducts.value = result
    })
    if (listProductIdCart.filter.length > 0) {
      useListingStore().getRelatedProduct(listProductIdCart).then((result) => {
        relatedCartProducts.value = result
      })
    }
  }

  function getCurrentOptions() {
    const { optionList, currentProduct } = userCampaignOption
    const { default_option: defaultOption } = currentProduct as Product
    const newOption: { [key: string]: string } = {}
    if (optionList && Object.keys(optionList).length > 0) {
      Object.keys(optionList).forEach((item, index) => {
        const lastOption = userSession.getLastOption(item, currentProduct?.name as string)
        if (!isModal && $route.query[item] && optionList[item]?.includes(($route.query[item] as string)?.replace(/-/g, ' '))) {
          newOption[item] = $route.query[item] as string
        }
        else if (lastOption && optionList[item].includes(lastOption.replace(/-/g, ' '))) {
          newOption[item] = lastOption as string
        }
        else if (index === 0 && defaultOption && optionList[item].includes(defaultOption)) {
          newOption[item] = defaultOption
        }
        else if (currentProduct?.full_printed === 5 && currentProduct?.personalized === 0) {
          newOption[item] = optionList[item][0]
        }
        else {
          newOption[item] = optionList[item][0].replace(/ /g, '-')
        }
      })

      if (newOption.print_side) {
        newOption.print_side = newOption.print_side.replace(/-/g, ' ')
      }
    }
    return newOption
  }

  function getImagesList() {
    const { currentProduct, currentOptions } = userCampaignOption
    const imagesList = campaignData.images
    let filterImage: Array<ImageData> = []
    if (imagesList?.length) {
      if (campaignData.system_type === 'combo') {
        filterImage = filterDuplicateImage([{
          product_id: 0,
          token: '',
          campaign_id: campaignData.id as number,
          type: 'image',
          type_detail: null,
          print_space: '',
          id: 0,
          file_url: campaignData.thumb_url as string
        } as ImageData, ...imagesList])
      }
      else if (currentProduct?.id) {
        filterImage = imagesList.filter(image =>
          [currentProduct.id, currentProduct.template_id, null].includes(image.product_id)
        )
      }

      if (currentOptions.color) {
        const imageByColor = filterImage.filter(image => (!image.option || image.option.replace(/ /g, '-') === currentOptions.color))
        if (imageByColor.length) {
          return imageByColor
        }
      }
    }

    return filterImage
  }

  function checkOptions() {
    userCampaignOption.optionError = Object.keys(userCampaignOption.optionList).find((item) => {
      return !userCampaignOption.currentOptions[item] || userCampaignOption.currentOptions[item] === 'false'
    })
    setTimeout(() => {
      userCampaignOption.optionError = false
    }, 3000)
    return userCampaignOption.optionError
  }

  function getCampaignUrl({
    campaignSlug,
    productName,
    query
  }: {
    campaignSlug?: string
    productName?: string
    query: LocationQuery
  }) {
    return localePath({
      path: `/${campaignSlug}/${stringHelperToSlug(productName)}`,
      query
    })
  }

  return {
    campaignId,
    isShowProductListDropdown,
    userCampaignOption,
    campaignData,
    similarProducts,
    relatedProducts,
    relatedCartProducts,

    productStats,
    campaignPromotions,

    dataDescription,
    dataProductDetail,

    getRelatedProduct,
    getCampaignData,
    updateProduct,
    updateOption,
    checkOptions,
    resetData
  }
}

function filterDuplicateImage(images: ImageData[]) {
  const uniqueItems = [] as ImageData[]
  const seenUrls = new Set()
  images.forEach((item) => {
    if (!seenUrls.has(item.file_url)) {
      seenUrls.add(item.file_url)
      uniqueItems.push(item)
    }
  })

  return uniqueItems
}

export function getCampaignUrl({
  campaignSlug,
  productName,
  query
}: {
  campaignSlug?: string
  productName?: string
  query: LocationQuery
}) {
  const localePath = useLocalePath()
  return localePath({
    path: `/${campaignSlug}/${stringHelperToSlug(productName)}`,
    query
  })
}

function preloadImgByColor(imagesList: ImageData[], colorList: string[]) {
  const { $imgUrl } = useNuxtApp()
  imagesList.forEach((image) => {
    colorList?.forEach((color) => {
      const img = new Image()
      img.src = $imgUrl({ path: image.file_url, type: 'full_hd', color, format: 'webp' })
    })
  })
}

// Build viewed_product event data for Klaviyo
function buildKlaviyoData(userCampaignOption: UserCampaignOption, campaignData: Campaign) {
  try {
    const productId = userCampaignOption.currentProduct?.id || 0
    const productPrice = userCampaignOption.currentProduct?.price || 0
    const productName = `${campaignData?.name} - ${campaignData?.name}`
    const categories = userCampaignOption.currentProduct?.name ? [userCampaignOption.currentProduct?.name] : []
    const imageUrl = userCampaignOption.currentProduct?.thumb_url || null
    return {
      product_id: productId,
      product_price: productPrice,
      product_name: productName,
      categories,
      image_url: imageUrl
    }
  }
  catch (error) {
    console.error('Error building Klaviyo data', error)
    return {}
  }
}
