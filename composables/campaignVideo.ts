export default function useCampaignVideo(currentProduct: ref<CurrentProduct>, currentImage: ref<number>) {
    const videos = computed(() => {
        return currentProduct.value.videos ?? []
    })
    const isVideoTab = computed(() => {
        return currentImage.value < modifiedIndex.value
    })
    const modifiedIndex = computed(() => {
        return videos.value.length
    })

    return {
      videos,
      isVideoTab,
      modifiedIndex,
    }
}
