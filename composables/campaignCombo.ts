import { useCampaignStore } from '~/store/campaign'
import { useComboCartStore } from '~/store/comboCart'
import { getDynamicBaseCostIndex } from '~/utils/price'

export function useCampaignCombo(campaign: Campaign) {
  const { getVariantsByProductId } = useCampaignStore()
  const comboStore = useComboCartStore()
  const selected = computed(() => comboStore.selected)
  const products = campaign.products || []
  const sellerId = computed(() => campaign.seller_id || storeInfo().seller_id) as Ref<number>
  const { $getProductVariantsBySelectedKeyAndCountry, $userCountryForPricing } = useNuxtApp()
  const variantPrice = ref(0)

  const calculateVariantPrice = async () => {
    const selectedIds = selected.value.filter(product => product.isSelected).map(product => product.id)
    let total = 0

    for (const id of selectedIds) {
      const product = products.find(item => item.id === id)
      const dynamicBaseCost = getDynamicBaseCostIndex(product as Product)
      const variant = await getVariant(product as Product, selected.value.find(item => item.id === id)?.currentOptions)
      total += (variant?.adjust_price ?? 0) + dynamicBaseCost
    }

    return total
  }

  const totalPrice = ref(0)

  const calculateTotalPrice = async () => {
    let total = 0
    for (const product of selected.value) {
      if (product.isSelected) {
        const foundProduct = products.find(item => item.id === product.id) as Product
        total += await getPrice(foundProduct)
      }
    }
    return total
  }

  watch(selected, async () => {
    variantPrice.value = await calculateVariantPrice()
    totalPrice.value = await calculateTotalPrice()
  }, { immediate: true, deep: true })

  const discountPrice = computed(() => {
    return (campaign.combo_price ?? 0) + variantPrice.value
  }) as Ref<number>

  const sellPrice = computed(() => {
    return isSelectAll.value ? discountPrice.value : totalPrice.value
  }) as Ref<number>

  const isSelectAll = computed(() => {
    return comboStore.isSelectedAll
  })

  async function getVariant(product: Product, currentOptions?: { [key: string]: string }) {
    if (!product || !product.id) {
      return
    }
    const optionsListFull = getProductOptions(product, true)
    const optionKeys = Object.keys(optionsListFull as OptionsList)
    const selectedOption = [] as string[]
    optionKeys.forEach((key) => {
      selectedOption.push(currentOptions?.[key] || optionsListFull[key][0])
    })
    const variantList = await getVariantsByProductId(product.id as number, sellerId.value) as Variant[]

    const result = variantList.find((item) => {
      let searchOptions = []
      if (item.variant_key.includes('_')) {
        searchOptions = item.variant_key.split('_')
      }
      else {
        searchOptions = item.variant_key.split('-')
      }

      return searchOptions.length === selectedOption.length && selectedOption.every((option) => {
        return searchOptions.includes(option)
      })
    })

    const result2 = $getProductVariantsBySelectedKeyAndCountry(variantList, result?.variant_key, result?.product_id, $userCountryForPricing())
    return result2
  }

  function getSelectedOptions(product: Product, optionType: string) {
    const selectedProduct = selected.value.find(item => item.id === product.id)
    return selectedProduct?.currentOptions?.[optionType]
  }

  function isSelectedOption(product: Product, optionType: string, option: string) {
    const modifyProduct = selected.value.find(item => item.id === product.id)
    if (modifyProduct) {
      return modifyProduct.currentOptions?.[optionType] === option
    }
    return false
  }

  async function getPrice(product: Product) {
    if (!product) {
      return 0
    }
    const selectedProduct = selected.value.find(item => item?.id === product?.id)
    const variant = await getVariant(product, selectedProduct?.currentOptions)
    return useTestPrice().getPrice(product, variant) + Number(((product.customFeePrice || 0) * ((product.customOptionGroupNumbers || 1) - 1)))
  }

  function isProductSelected(product: Product) {
    return selected.value.find(item => item.id === product.id)?.isSelected
  }

  function getProductOptions(product: Product, isFull = false) {
    if (!product?.options) {
      return {}
    }
    const options = typeof product.options === 'string' ? JSON.parse(product.options) : product.options
    if (!isFull) {
      for (const key in options) {
        if (options[key].length < 2) {
          delete options[key]
        }
      }
    }

    return options
  }

  return {
    // functions
    getVariant,
    getSelectedOptions,
    isSelectedOption,
    isProductSelected,
    getProductOptions,

    // computed
    variantPrice,
    discountPrice,
    sellPrice,
    totalPrice,
    isSelectAll
  }
}
