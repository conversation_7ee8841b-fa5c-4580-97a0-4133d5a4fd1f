import { useCampaignStore } from '~/store/campaign'
import { useCartStore } from '~~/store/cart'
import { useUserSession } from '~~/store/userSession'
import { BUNDLE_DISCOUNT_VIEW_PLACE } from '~/utils/constant'
import type DefaultCampaignModalConfirmDesign from "~/components/default/campaign/modalConfirmDesign.vue";

const listCartItem = computed(() => useCartStore().listCartItem)

export function useCampaignBundle (ids:ComputedRef<number|Array<number>>, isCartBundle = false, modalConfirmDesign: Ref<InstanceType<typeof DefaultCampaignModalConfirmDesign>>, viewPlace?:number) {
  const {
    getBundleProduct,
  } = useCampaignStore()
  const { $convertPrice } = useNuxtApp()
  const { updateCartItemByID } = useCartStore()

  const bundleProduct = shallowRef<Product[]>([])
  const bundleVariant = shallowRef<Variant[]>([])
  const isFetched = ref(false)

  const currentBundleProduct = shallowRef<Product>()
  const isAddToCart = ref(false)
  const isShowBundleProduct = ref(true)

  const bundlePromotion:BundlePromotion = reactive({
    id: 0,
    discount_code: '',
    discount_percentage: 0,
    number_cart_bundle_product_limit: null
  })

  const totalBundleDiscount = computed(() => {
    return bundleProduct.value.filter((product, index) => isCartBundle ? index < 1 : (index < 3 && product.isSelected))
      .reduce((acc, product) => {
        if (product.personalized === 3 && product.personalizeCustomManager && product.personalizeCustomManager.totalCustomOptionFee.value) {
          acc += product.personalizeCustomManager.totalCustomOptionFee.value
        }
        return acc + $convertPrice(useTestPrice().getPrice(product, product.currentVariant), product.currency_code).value
      }, 0)
  })

  const saveBundleDiscount = computed(() => {
    return totalBundleDiscount.value * bundlePromotion.discount_percentage / 100
  })

  async function getDataBundle (productIds: Array<number> = [], allowReFetchBd: boolean = false) {
    let bundleIds = null
    if (viewPlace === BUNDLE_DISCOUNT_VIEW_PLACE.CART) {
      productIds = listCartItem.value.map(cartItem => cartItem.product_id)
      allowReFetchBd = true
      bundleIds = getBundleIdsBeforeFetching()
    }
    isFetched.value = false
    const result = await getBundleProduct(ids.value, productIds, viewPlace, allowReFetchBd, bundleIds)
    isFetched.value = true
    result?.products.forEach((item) => { item.isBundle = true })
    bundleProduct.value = result?.products || []
    bundleVariant.value = toRaw(result?.variants) || []
    Object.assign(bundlePromotion, toRaw(result?.promotion))
    bundleProduct.value.forEach((product) => {
      let optionsListFull = product.options
      if (typeof optionsListFull === 'string') {
        optionsListFull = JSON.parse(optionsListFull)
      }
      const optionsList:OptionsList = { ...optionsListFull as OptionsList }
      Object.keys(optionsList).forEach((key) => {
        if (optionsList[key].length <= 1) {
          delete optionsList[key]
        }
      })
      const optionKeys = Object.keys(optionsList || {})
      const currentOptions:{[key:string]:string} = {}
      optionKeys.forEach((item, index) => {
        if (index === 0 && product.default_option && optionsList[item].includes(product.default_option)) {
          currentOptions[item] = product.default_option
        } else {
          const lastOption:string = useUserSession().getLastOption(item, product.name)
          if (lastOption && optionsList[item].includes(lastOption)) {
            currentOptions[item] = lastOption
          } else {
            currentOptions[item] = optionsList[item][0]
          }
        }
      })

      product.isSelected = true
      product.variantsList = bundleVariant.value.filter(variant => variant.product_id === product.id)
      product.currentVariantKey = optionKeys.map(item => currentOptions[item]).join(' / ')
      product.existVariantList = getExistVariantList(optionsListFull as OptionsList)
      product.currentVariant = product.variantsList?.find(item => item.variant_key.replace(/_/g, ' ').replaceAll('-', ' / ') === product.currentVariantKey)
      product.optionsList = optionsList
      product.optionsListFull = optionsListFull
      product.currentOptions = currentOptions

      product.currentOptions = currentOptions
      product.currentOptions = currentOptions

      if (product.personalized === 1 || product.personalized === 2) {
        const fakeUserCampaign = shallowReactive<Campaign>({
          personalized: product?.personalized,
          slug: product?.slug,
          products: [product as Product]
        })
        product.personalizeManager = shallowReactive(useCampaignPersonalize(fakeUserCampaign, { currentProduct: product }, true, ref(null)))
      }
      if (product.personalized === 3) {
        const findCustomOption = result?.custom_personalized_options.find((item: { id: number }) => item.id === product.campaign_id)
        if (findCustomOption) {
          let customOptions
          if (typeof findCustomOption.options === 'string') {
            customOptions = JSON.parse(findCustomOption.options)
          } else {
            customOptions = findCustomOption.options
          }
          let commonOptions
          if (typeof findCustomOption.common_options === 'string') {
            commonOptions = JSON.parse(findCustomOption.common_options)
          } else {
            commonOptions = findCustomOption.common_options
          }
          if (customOptions || commonOptions) {
            product.customOptions = customOptions
            product.commonOptions = commonOptions
            product.personalizeCustomManager = shallowReactive(useCampaignPersonalizeCustomOptions(customOptions, true, product.personalized, product.full_printed, commonOptions))
          }
        }
      }
      if (product.personalized === 0 && product.full_printed === 5) {
        let customOptions
        customOptions = product.template_custom_options
        if (typeof customOptions === 'string') {
          customOptions = JSON.parse(customOptions)
        }
        let commonOptions
        commonOptions = product.common_options
        if (typeof commonOptions === 'string') {
          commonOptions = JSON.parse(commonOptions)
        }
        if (customOptions || commonOptions) {
          product.customOptions = customOptions
          product.commonOptions = commonOptions
          product.personalizeCustomManager = shallowReactive(useCampaignPersonalizeCustomOptions(customOptions, true, product.personalized, product.full_printed, commonOptions))
        }
      }
    })

  }


  async function submitBundleProduct (isConfirmDesign = false, forceOpenModal = false) {
    isAddToCart.value = true
    if (!isConfirmDesign) {
      const value = await checkBundleProduct(forceOpenModal)
      if (value.success) {
        if (value?.designDataUrl) {
          modalConfirmDesign.value!.isShowModal = value?.designDataUrl
        }
        currentBundleProduct.value = undefined
      } else {
        return handlePersonalizeError(value)
      }
    }
    const listPersonalize:{[key:string]: Personalize} = {}
    const cartItemBundle = addBundleProductToCart()
    cartItemBundle.forEach((cartItem) => {
      if ((cartItem.personalized === 1 || cartItem.personalized === 2) && cartItem.product.personalizeList?.length) {
        listPersonalize[cartItem.id] = cartItem?.product?.personalizeList[0]
      }
    })
    if (Object.keys(listPersonalize).length) {
      uiManager().$patch({ isUploadFile: true })
      window.loadingUploadImage = getPersonalizeUpload(listPersonalize)
      window.loadingUploadImage.then((uploadUrl) => {
        uiManager().$patch({ isUploadFile: false })
        Object.keys(uploadUrl).forEach((key) => {
          if (uploadUrl[key]) {
            updateCartItemByID(key, { thumb_url: uploadUrl[key] })
          }
        })
      })
    }
  }

  async function handlePersonalizeError ({ product, customItem, errorElementKey, errorType }:CheckProductResult) {
    if (product) {
      currentBundleProduct.value = shallowReactive(product)
    }
    await nextTick()
    if (product?.personalized === 1 && customItem) {
      if (customItem.data.type === 'i-text') {
        const errorItem = document.getElementById(`customText_${customItem.data.name}`)
        errorItem?.focus()
      } else if (customItem.data.type === 'image') {
        const errorItem = document.getElementById('customImage_modal_0')?.parentElement
        errorItem?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
      }
    } else if (product?.personalized === 2) {
      try {
        const errorItem = document.getElementsByClassName('ant-form-explain')[0]
        const isInViewport = checkViewPort(errorItem as HTMLElement)
        if (!isInViewport) {
          errorItem.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      } catch (error) {

        console.log(error)
      }
    } else if (product?.personalized === 3 || product?.full_printed === 5) {
      setTimeout(() => {
        if (errorElementKey) {
          const errorElement:HTMLElement|null = document.getElementById(errorElementKey)
          if (errorElement) {
            if (errorType === CUSTOM_OPTION_TYPE.text) {
              (errorElement as HTMLInputElement).focus()
            }
            if (errorType === CUSTOM_OPTION_TYPE.image) {
              errorElement.parentElement?.scrollIntoView()
            } else {
              errorElement.scrollIntoView()
            }
          }
        }
      }, currentBundleProduct.value === product ? 0 : 400)
    }
  }

  async function checkBundleProduct (forceOpenModal = false):Promise<CheckProductResult> {
    const checkValues = bundleProduct.value.filter((product, index) => isCartBundle ? index < 1 : (index < 3 && product.isSelected)).map((product) => {
      if (product.personalized === 1 || product.personalized === 2) {
        return product.personalizeManager?.checkPersonalize()
      }
      if (product.personalized === 3 || product.full_printed === 5) {
        const result = {
          ...product.personalizeCustomManager?.checkPersonalizeCustomOptions(),
          product
        }
        if (forceOpenModal) {
          result.success = false
        }
        return result
      }

      return {
        success: true
      } as CheckProductResult
    })

    const result = await Promise.all(checkValues)
    const falseResult = result.find(value => !value?.success)

    return (falseResult || {
      success: true,
      designDataUrl: result.filter(value => value?.designDataUrl).reduce((previousValue, currentValue) => previousValue.concat(currentValue?.designDataUrl || []), [] as string[])
    }) as CheckProductResult
  }

  function addBundleProductToCart () {
    const campaignBundleIds: Array<number>|undefined = []
    let campaignBundleData: Array<any>|undefined = []
    if (Array.isArray(ids?.value)) {
      listCartItem.value.forEach((item) => {
        if (!item?.promotion && !item?.campaignBundleId) {
          campaignBundleIds?.push(item.campaign_id)
          campaignBundleData?.push({
            campaign_id: item.campaign_id,
            product_id: item.product_id
          })
        }
      })
    } else {
      const bundleData = listCartItem.value.find((item) => item.campaign_id === ids.value)
      campaignBundleData = [{
        campaign_id: bundleData?.campaign_id,
        product_id: bundleData?.product_id
      }]
      campaignBundleIds[ids.value]
    }

    if (campaignBundleData.length === 0) {
      const bundleData = listCartItem.value.at(-1)
      if (bundleData) {
        campaignBundleData = [{
          campaign_id: bundleData?.campaign_id,
          product_id: bundleData?.product_id
        }]
        campaignBundleIds.push(bundleData.campaign_id)
      } else {
        return;
      }
    }

    return bundleProduct.value.filter((product, index) => isCartBundle ? index < 1 : (index < 3 && product.isSelected)).map(async (product) => {
      // logic cover case product.campaign_id = 0
      let currentProduct = null
      let options = null
      if (!product.campaign_id) {
        const {
          campaignData,
          getCampaignData,
        } = useCampaign(product.slug as string)
        await getCampaignData()
        currentProduct = campaignData.products?.[0]
        options = currentProduct?.options
        if (typeof options === 'string') {
          options = JSON.parse(options)
        }
        const currentOptions = product.currentOptions || {}
        if (options && currentProduct) {
          const keys = Object.keys(options)
          keys.forEach((key) => {
            const lastOption = useUserSession().getLastOption(key, currentProduct.name)
            if (!currentOptions[key]) {
              if (lastOption && options[key].includes(lastOption)) {
                currentOptions[key] = lastOption
              } else
              if (currentProduct?.default_option && options[key].includes(currentProduct.default_option)) {
                currentOptions[key] = currentProduct.default_option
              } else {
                currentOptions[key] = options[key][0]
              }
            }
          })
        }
      }

      return {
        ...useCartStore().addCartItem({
          id: product.campaign_id ?? product.id,
          name: product.campaign_id ? product.campaign_name : product.name,
          slug: product.slug,
          personalized: product.personalized,
          seller_id: product?.seller_id
        }, {
          currentOptions: product.currentOptions || {},
          currentVariant: product.currentVariant,
          currentProduct: currentProduct ?? product,
          quantity: 1,
          imagesList: [],
          optionList: product.optionsList as OptionsList,
          optionListFull: (options ?? product.optionsList) as OptionsList
        }, {
          campaignBundleId: campaignBundleData?.at(-1).campaign_id,
          promotion: bundlePromotion,
          totalCustomOptionFee: product.personalizeCustomManager?.totalCustomOptionFee.value || 0,
          productBundleId: campaignBundleData?.at(-1).product_id,
          userCustomOptions: product.personalizeCustomManager?.userCommonOptions.concat(product.personalizeCustomManager?.userCustomOptions),
          groupCustomOptionsQuantity: (product.personalizeCustomManager?.groupCustomOptionsQuantity.value || 0) + (product.personalizeCustomManager?.userCommonOptions.length || 0),
        }, false),
        product
      }
    })
  }

  function refetchDataBundle (productIdsExclude: Array<number> = []) {
    getDataBundle(productIdsExclude, true)
  }

  function refillBundleProducts () {
    if (getMoreBundleProducts()) {
      getDataBundle()
    }
  }
  function getMoreBundleProducts () {
    if (!bundlePromotion?.number_cart_bundle_product_limit) { return true }
    const productsWithBundle = listCartItem.value.filter((product) => {
      if (product?.promotion && product?.promotion?.id === bundlePromotion?.id) {
        return true
      }
      return false
    })
    if (!bundlePromotion?.number_cart_bundle_product_limit ||
      bundlePromotion?.number_cart_bundle_product_limit > productsWithBundle.length) {
      return true
    }
    return false
  }

  return {
    isAddToCart,
    isShowBundleProduct,
    bundleProduct,
    bundlePromotion,
    currentBundleProduct,
    totalBundleDiscount,
    saveBundleDiscount,
    isFetched,
    getDataBundle,
    checkBundleProduct,
    addBundleProductToCart,
    refetchDataBundle,
    refillBundleProducts,
    getMoreBundleProducts,
    submitBundleProduct,
    handlePersonalizeError,
  }
}

function getExistVariantList (optionsList:OptionsList) {
  if (!optionsList) {
    return []
  }
  if (typeof optionsList !== 'object') { optionsList = JSON.parse(optionsList) }
  const optionKeys = Object.keys(optionsList)
  let existVariantList:{key:string, value:string}[] = []
  optionKeys.forEach((item) => {
    const oldExistVariantList = [...existVariantList]
    existVariantList = []
    if (typeof optionsList[item] === 'object' && existVariantList.length && existVariantList.every(item => item?.key?.length)) {
      return existVariantList
    }
    if (oldExistVariantList.length) {
      oldExistVariantList.forEach((oldValue) => {
        optionsList[item].forEach((optionValue) => {
          existVariantList.push({
            key: `${oldValue.key} / ${optionValue}`,
            value: optionsList[item].length >= 2 ? oldValue.value ? `${oldValue.value} / ${optionValue}` : optionValue : oldValue.value
          })
        })
      })
    } else {
      existVariantList = optionsList[item].map((optionValue) => {
        return {
          key: optionValue,
          value: optionsList[item].length >= 2 ? optionValue : ''
        }
      })
    }
  })
  return existVariantList
}

function getBundleIdsBeforeFetching () {
  const bundleIds = listCartItem.value.map((item) => {
    if (item?.promotion?.id) {
      return item.promotion.id
    }
  })

  return bundleIds.filter(id => id)
}
