import { useUserSession } from '~~/store/userSession'

export function useCampaignCountdown(showCountdown?: number, endTime?: number) {
  const times = reactive([
    { id: 0, text: 'day', time: 0 },
    { id: 1, text: 'hour', time: 0 },
    { id: 2, text: 'minute', time: 0 },
    { id: 3, text: 'second', time: 0 }
  ])

  const changeTime = ref(0)
  const newEndTime = ref(endTime)

  onMounted(() => {
    const { visitInfo } = useUserSession()
    if (showCountdown && endTime) {
      const countdownPeriods = [0, 0, 15 * 60 * 1000, 60 * 60 * 1000, 24 * 60 * 60 * 1000, 7 * 24 * 60 * 60 * 1000]
      const countdownPeriod = countdownPeriods[showCountdown]
      if (!visitInfo.visitTime) {
        visitInfo.visitTime = new Date().getTime()
      }
      let visitTime = visitInfo.visitTime
      newEndTime.value = Date.parse(new Date(endTime).toString())
      if (endTime < visitTime) {
        newEndTime.value = newEndTime.value % countdownPeriod
        if (visitTime % countdownPeriod > newEndTime.value) {
          visitTime += countdownPeriod
        }
        visitTime = Math.round(visitTime / countdownPeriod)
        newEndTime.value = visitTime * countdownPeriod + newEndTime.value
      }
    }

    updateTimer()
    setInterval(updateTimer, 1000)
  })

  function updateTimer () {
    changeTime.value = Date.parse(new Date(endTime2()).toString()) - Date.parse(new Date().toString())
    times[3].time = Math.floor(changeTime.value / 1000 % 60)
    times[2].time = Math.floor(changeTime.value / 1000 / 60 % 60)
    times[1].time = Math.floor(changeTime.value / (1000 * 60 * 60) % 24)
    times[0].time = Math.floor(changeTime.value / (1000 * 60 * 60 * 24))
  }

  function endTime2 () {
    if (newEndTime.value && showCountdown === 1) {
      return newEndTime.value
    }

    const da = new Date()
    const next15 = (Math.floor((da.getMinutes() + 15 - 1) / 15)) * 15
    const distance = (da.getMinutes() === next15) ? (da.getMinutes() + 15) : next15
    switch (showCountdown) {
      case 2:
        // 2 'Repeat every 15 minutes'
        da.setMinutes(distance - 1, 59)
        break
      case 3:
        da.setMinutes(59, 59)
        break
      case 4:
        da.setHours(23, 59, 59)
        break
      case 5:
        // magic number: 0: sunday
        da.setDate(da.getDate() + (0 + (7 - da.getDay())) % 7)
        break
    }
    return da.toISOString()
  }

  return {
    times,
    changeTime,
  }
}
