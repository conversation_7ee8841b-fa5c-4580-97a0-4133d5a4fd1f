export default function useCampaignImage(imagesList: ref<ImagesList>, currentProduct: ref<CurrentProduct>, currentImage: ref<number>, imageCarousel: ref<HTMLElement | null>, viewZone: ref<HTMLElement | null>, color: ref<string>, videoLength: ref<number>, $emit, campaign: ref<Campaign>) {
    const { $imgUrl } = useNuxtApp()
    const isInViewPort = ref(true)

    const viewImagesList = computed(() => {
        return imagesList.value.map(image => $imgUrl({
            path: image?.file_url,
            type: 'full_hd',
            color: color.value
        }))
    })
    const hasVideo = computed(() => {
        return videoLength.value > 0
    })

    watch(currentProduct, checkProduct)
    onMounted(async () => {
        const defaultImage = (storeInfo().store_type === 'express_listing' && campaign.value?.thumb_url) ? campaign.value?.thumb_url : currentProduct.value?.thumb_url || currentProduct.value.full_path
        if (imagesList.value && imagesList.value.length) {
            let index = hasVideo.value ? 0 : imagesList.value.findIndex(item => item.file_url === defaultImage)
            if (!index || index < 0) {
                index = 0
            } else {
                index += videoLength.value
            }
            if (imagesList.value.length > 1) {
                await nextTick()
                slideTo(index)
            }
        }
        checkViewBoxInViewPort()
        document.addEventListener('scroll', checkViewBoxInViewPort)
    })

    onUnmounted(() => {
        document.removeEventListener('scroll', checkViewBoxInViewPort)
    })

    function checkProduct () {
        if (!imagesList.value || hasVideo.value) { return }
        const imageIndex = imagesList.value.findIndex(image => image?.file_url === currentProduct.value.thumb_url)
        if (imageIndex >= 1) {
            slideTo(imageIndex)
        }
    }
    function slideTo (slidingToIndex:number) {
        $emit('changeImage')
        currentImage.value = slidingToIndex
        imageCarousel.value?.go(slidingToIndex)
    }

    function checkViewBoxInViewPort () {
        if (viewZone.value) {
            isInViewPort.value = checkViewPort(viewZone.value, 100)
        }
    }

    function enlargeViewboxImage (isShowDesign: boolean) {
        // for existing image list, and rendered mockup (if it's currently visible to customer)
        const canvas = document.querySelector('canvas[id]')
        const compoundImagesList = [...viewImagesList.value]
        let viewImageIndex = currentImage.value

        if (isShowDesign && canvas) {
            compoundImagesList.push(canvas.toDataURL())
            viewImageIndex += 1
        }

        uiManager().viewImage(compoundImagesList, viewImageIndex)
    }

    return {
        viewImagesList,
        isInViewPort,
        slideTo,
        enlargeViewboxImage,
    }
}
