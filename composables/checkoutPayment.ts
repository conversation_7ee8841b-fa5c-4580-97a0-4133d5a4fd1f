import type { ShallowRef } from 'vue-demi'
import { useCartStore } from '~~/store/cart'
import { useGeneralSettings } from '~~/store/generalSettings'
// import { useStoreInfo } from '~/store/storeInfo'
import { useComboCartStore } from '~/store/comboCart'
import { useStoreInfo } from '~/store/storeInfo'
import { PAYMENT_DISPLAY_TYPE } from '~/utils/constant'

let stripe: any
let stripeCard: any
let stripeElements: any
let stripeBankEl: any

export function useCheckoutPayment(token: string, userInfo: UserInfo, order: Order, paymentGateways: ShallowRef<Gateway[]>, updateOrder: Function) {
  const { $i18n, $fetchDefault, $fetchWrite } = useNuxtApp()

  const userInfoForm = ref<any>()

  const paymentDisplayType: Ref<(typeof PAYMENT_DISPLAY_TYPE)[keyof typeof PAYMENT_DISPLAY_TYPE]> = ref(PAYMENT_DISPLAY_TYPE.UNINITIALIZED)
  const isShowModalConfirmEmail = ref(false)

  const gateways: ListGateway = reactive({})
  const currentGateway: Ref<(typeof PAYMENT_METHOD)[keyof typeof PAYMENT_METHOD]> = ref('')
  const currentGatewayId = computed(() => {
    if (currentGateway.value === PAYMENT_METHOD.paypal && gateways?.paypalGateway?.id) {
      return gateways.paypalGateway.id
    }
    if (currentGateway.value.includes(PAYMENT_METHOD.stripe) && gateways?.stripeGateway?.id) {
      if (currentGateway.value === 'stripe-bank' && stripeBankDetail.value?.paymentGatewayId) {
        return stripeBankDetail.value.paymentGatewayId
      }

      return gateways.stripeGateway.id
    }

    return null
  })

  const iframeContainers: Record<string, Ref<HTMLElement | undefined>> = {
    stripeCard: ref(),
    stripeEwallet: ref()
  }
  const iframes: Record<string, HTMLIFrameElement | null> = {
    paypalSmartCheckout: null,
    stripeCard: null,
    stripeEwallet: null
  }

  // Initializer
  async function initPaymentGateways(errorPaypal = false) {
    gateways.stripeGateway = paymentGateways.value.find(item => item?.gateway === PAYMENT_METHOD.stripe || item?.gateway === PAYMENT_METHOD.stripeCard)
    gateways.paypalGateway = paymentGateways.value.find(item => item?.gateway === PAYMENT_METHOD.paypal)

    const availableGateways = Object.entries(gateways).filter(([_gw, val]) => Boolean(val)).length
    if (!availableGateways) {
      paymentDisplayType.value = PAYMENT_DISPLAY_TYPE.NULL
      return
    }

    if (gateways.paypalGateway?.checkout_domain && gateways.paypalGateway?.clientId && availableGateways === 1) {
      initPaypalSmartCheckoutIframe()
      return
    }
    if (!errorPaypal && gateways.paypalGateway?.clientId && availableGateways === 1) {
      paymentDisplayType.value = PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT
      await nextTick()
      initPaypalSmartCheckout()
    }
    else if (gateways.stripeGateway?.id) {
      paymentDisplayType.value = PAYMENT_DISPLAY_TYPE.LIST

      if (gateways.stripeGateway?.checkout_domain) {
        initStripeIframe()
      }
      else {
        initStripe()
      }
    }
  }
  watch(() => order.fulfill_status, () => {
    initPaymentGateways()
  })

  // Submit Checkout
  async function submitCheckout() {
    useTracking().customTracking({
      event: 'checkout_place_order_button_click'
    })

    if (order.fulfill_status === 'no_ship') {
      return false
    }

    if (!userInfoForm.value.advancedEmailChecking.serverValidate) {
      isShowModalConfirmEmail.value = true
      return false
    }
    if (!userInfoForm.value.checkUserInfo()) {
      return false
    }

    if (paypalDiscount() && !paypalModalConfirmed.value && currentGateway.value === PAYMENT_METHOD.paypal) {
      paypalIsShowModalCreditCardDiscount.value = true
      return false
    }

    const res: ResponseData<{}> = await updateOrder('place_order', {
      order_token: order.access_token,
      user_info: userInfo,
      email: userInfo.email,
      country: order.country || userInfo.country,
      payment_gateway_id: currentGatewayId.value,
      payment_method_name: currentGateway.value,
      verify_address: true, // function called from "Place Your Order" button,
      order_note: userInfo.note
    })

    if (!res.success) {
      return false
    }

    if (uiManager().isLoading) {
      return false
    }

    if (
      paymentDisplayType.value === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT
      || paymentDisplayType.value === PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT_IFRAME
    ) {
      return true
    }

    switch (currentGateway.value) {
      case PAYMENT_METHOD.stripeCard:
        await stripePayWithCard()
        break
      case PAYMENT_METHOD.stripeEwallet:
        await stripePayWithEWallet()
        break
      case PAYMENT_METHOD.paypal:
        await paypalPayWithNormal()
        break
      case PAYMENT_METHOD.stripeBank:
        await stripePayWithBank()
        break
    }
  }

  // Post message handlers
  const paypalIframeFullScreen = ref(false)
  const postMessageHandlerTable: Record<string, Function> = {
    paypal_set_frame_state: (source: MessageEventSource, args: boolean[] = [false]) => {
      const state = args[0]
      const isFullScreen = !state
      paypalIframeFullScreen.value = isFullScreen
      // @ts-ignore
      source.postMessage({ name: 'set_button_container_state', args: [isFullScreen] }, '*')
    },
    paypal_get_client_id: (source: MessageEventSource) => {
      // @ts-ignore
      source.postMessage({ name: 'init_paypal', args: [gateways.paypalGateway?.clientId, gateways.paypalGateway?.paypal_merchant_id] }, '*')
    },
    handle_error: (_source: MessageEventSource, args: any[]) => {
      paypalIframeFullScreen.value = false
      handleError(args?.[0]?.error)
    },
    paypal_transaction_cancelled: (_source: MessageEventSource) => {
      isSubmittingSmartCheckout = false
      paypalIframeFullScreen.value = false
      loading(false)
    },
    paypal_submit_checkout: async (source: MessageEventSource) => {
      isSubmittingSmartCheckout = true
      loading('place_order')
      const isValid = await submitCheckout()

      source.postMessage({
        name: 'set_submitted_checkout',
        args: [isValid]
      // @ts-ignore
      }, '*')
    },
    paypal_verify_transaction: async (_source: MessageEventSource, args: any[]) => {
      const data = args[0]
      const endpoint = `${$api.API_ORDER_PAYPAL_CALLBACK}/${token}?token=${data.orderID}`
      const { success } = await $fetchDefault<ResponseData<{}>>(endpoint)

      if (success) {
        onCreateOrderSucceeded(token)
      }
      else {
        uiManager().createPopup('Transaction failed. Please try again')
      }
    },
    paypal_retrieve_create_order_data: async (source: MessageEventSource) => {
      const data = { orderId: await paypalGetOrderId() }
      source.postMessage({
        name: 'set_create_order_data',
        args: [data]
      // @ts-ignore
      }, '*')
    },
    stripe_retrieve_data: (source: MessageEventSource) => {
      const data = JSON.parse(JSON.stringify(stripeData))
      const i18n = {
        locale: $i18n.locale.value,
        card_number: $i18n.t('Card number'),
        exp_date: $i18n.t('Expiration date'),
        mm_yy: $i18n.t('MM/YY'),
        cvc: $i18n.t('Security code')
      }
      const returnURL = stripeGetReturnURL()

      source.postMessage({
        name: 'stripe_set_data',
        args: [data, i18n, returnURL]
      // @ts-ignore
      }, '*')
    },
    stripe_focus_card: (_source: MessageEventSource, args: string[]) => {
      const type = args[0]
      const allowType = ['card', 'banks', 'ewallet']
      if (!allowType.includes(type)) { return }

      currentGateway.value = `stripe-${type}`
    },
    stripe_card_on_change: (_source: MessageEventSource, args: any[]) => {
      const event = args[0]

      if (event.brand) {
        stripeData.brandCard = event.brand
      }
      if (event.error) {
        showError(event.error?.message)
        handleError(event.error?.message)
      }
    },
    stripe_set_container_height: (_source: MessageEventSource, args: any[]) => {
      const [height, frameType] = args
      const iframeSetHeight
        = (frameType === 'card')
          ? iframes.stripeCard
          : (frameType === 'ewallet')
              ? iframes.stripeEwallet
              : (frameType === 'banks')
                  ? iframes.stripeBanks
                  : null

      if (iframeSetHeight) {
        iframeSetHeight.setAttribute('style', `height: calc(${height}px + 1rem);`)
      }
    },
    stripe_create_order_succeed: (_source: MessageEventSource, _args: any[]) => {
      onCreateOrderSucceeded(order.access_token)
    }
  }
  function attachPostMessageHandler() {
    if (window.addEventListener) {
      window.addEventListener('message', postMessageHandler)
    }
    else {
      // @ts-ignore
      window.attachEvent('onmessage', postMessageHandler)
    }
  }
  function postMessageHandler(messageEvent: MessageEvent) {
    const { data: event, source } = messageEvent

    if (
      typeof event !== 'object'
      || !event.name
      || !event.args || !Array.isArray(event.args)
    ) { return }

    if (Object.hasOwnProperty.call(postMessageHandlerTable, event.name)) {
      postMessageHandlerTable[event.name](source, event.args || [])
    }
  }

  // Common Helper
  function handleError(error: any) {
    loading(false)
    showError(error?.message)

    useOrder(token).updateOrder(false, {
      payment_failed_log: error?.message
    })
    // const {enable_distributed_checkout} = useStoreInfo()
    // const apiExceptionLog = useRuntimeConfig()?.public?.appEnv === 'dev' ? (enable_distributed_checkout === 1 ? $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION : $api.API_CHECKOUT_LOG_EXCEPTION) : $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION
    const apiExceptionLog = $api.API_DISTRIBUTED_CHECKOUT_LOG_EXCEPTION

    $fetchWrite(apiExceptionLog, {
      method: $method.post,
      body: {
        message: error?.message,
        token
      }
    })

    useTracking().newCustomTracking({
      event: 'payment-error',
      data: {
        message: error?.message,
        token
      }
    })
  }
  function showError(message: string) {
    const paymentInfoEl: HTMLElement | null = document.getElementById('paymentInfo')
    paymentInfoEl?.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })

    stripeData.stripeGatewayError = message
    setTimeout(() => {
      stripeData.stripeGatewayError = ''
    }, 10000)
  }
  function useCreditCardDiscount() {
    if (!gateways.stripeGateway) {
      return
    }

    currentGateway.value = PAYMENT_METHOD.stripe
    updateOrder('credit_card_discount', {
      credit_card_discount: true,
      payment_method_name: currentGateway.value,
      payment_gateway_id: currentGatewayId.value
    })
  }

  // Paypal
  const paypalButtonContainer = ref<HTMLElement>()
  const paypalErrorCount = ref(0)
  const paypalIsShowModalCreditCardDiscount = ref(false)
  const paypalModalConfirmed = ref(false)
  let isSubmittingSmartCheckout = false

  // PayPal Smart Checkout in IFrame
  async function initPaypalSmartCheckoutIframe() {
    if (order.fulfill_status === 'no_ship') { return }

    currentGateway.value = gateways.paypalGateway!.gateway

    paymentDisplayType.value = PAYMENT_DISPLAY_TYPE.SMARTCHECKOUT_IFRAME
    attachPostMessageHandler()
    await nextTick()

    let iframeSrc = '/embed/paypal.html'
    if (gateways.paypalGateway?.checkout_domain && !isDevMode().isDev) {
      iframeSrc = (gateways.paypalGateway.checkout_domain.startsWith('https://')) ? `${gateways.paypalGateway.checkout_domain}${iframeSrc}` : `https://${gateways.paypalGateway.checkout_domain}${iframeSrc}`
    }

    iframes.paypalSmartCheckout = generateAndMountIframe({
      src: iframeSrc,
      classes: ['w-full', 'h-full'],
      container: paypalButtonContainer.value
    })
  }

  // Paypal Smart Checkout Initializer
  async function initPaypalSmartCheckout() {
    if (order.fulfill_status === 'no_ship' || isSubmittingSmartCheckout) { return }

    let src = `https://www.paypal.com/sdk/js?client-id=${gateways.paypalGateway?.clientId}&currency=USD&enable-funding=venmo`

    // PayPal: Complete Payments Platform
    if (gateways.paypalGateway?.paypal_merchant_id) {
      src += `&merchant-id=${gateways.paypalGateway?.paypal_merchant_id}`
    }

    useHead({
      script: [{
        src,
        'hid': 'paypal-sdk',
        'data-partner-attribution-id': 'SensePrints_Ecom' // BN Code
      }]
    })
    if (typeof window.paypal === 'undefined') {
      setTimeout(() => {
        initPaypalSmartCheckout()
      }, 1000)
      return
    }

    try {
      currentGateway.value = gateways.paypalGateway!.gateway
      while (paypalButtonContainer.value?.firstElementChild) {
        paypalButtonContainer.value?.firstElementChild.remove()
      }

      await window.paypal.Buttons({
        // Setup the transaction when a payment button is clicked
        createOrder: paypalGetOrderId,

        onClick: async (_data: any, actions: any) => {
          isSubmittingSmartCheckout = true
          const check = await submitCheckout()
          if (check) {
            return actions.resolve()
          }
          else {
            return actions.reject()
          }
        },

        // Finalize the transaction after payer approval
        onApprove: async (data: any) => {
          const endpoint = `${$api.API_ORDER_PAYPAL_CALLBACK}/${token}?token=${data.orderID}`
          const { success } = await $fetchDefault<ResponseData<{}>>(endpoint)

          if (success) {
            onCreateOrderSucceeded(token)
          }
          else {
            uiManager().createPopup('Transaction failed. Please try again')
          }
        },

        onCancel: () => {
          isSubmittingSmartCheckout = false
          loading(false)
        },
        onError: () => {
          isSubmittingSmartCheckout = false
          loading(false)
        }
      }).render('#paypal-button-container')
    }
    catch (error) {
      paypalErrorCount.value++
      if (paypalErrorCount.value <= 3) {
        setTimeout(() => {
          initPaypalSmartCheckout()
        }, 1000)
      }
      else {
        paymentDisplayType.value = PAYMENT_DISPLAY_TYPE.LIST // In case can't initialize smart checkout => fallback to 'list'
        initPaymentGateways(true)
      }
    }
  }

  // Paypal Helper
  function paypalDiscount() {
    return Number.parseFloat(useGeneralSettings().paypal_discount || '0')
  }
  async function paypalGetOrderId() {
    try {
      const { success, data } = await $fetchDefault<ResponseData<string>>($api.API_ORDER_CREATE_PAYPAL, {
        method: $method.post,
        body: {
          order_token: order.access_token,
          return_order_id: true
        }
      })

      if (success) {
        return data // PayPal Order ID
      }

      uiManager().createPopup('Could not initiate PayPal Checkout. Please try again.')
    }
    catch (error) {
      console.error(error)
      uiManager().createPopup('Could not initiate PayPal Checkout.')
    }
  }

  // Paypal Handle Payments
  async function paypalPayWithNormal() {
    loading('place_order')
    if (order.payment_domain) {
      const { domain, isDev } = isDevMode()
      const path = `/payment.html?token=${order.access_token}&ref=${encodeURIComponent(window.btoa(domain))}`

      if (isDev) {
        window.top!.location.href = path
      }
      else {
        window.top!.location.href = `https://${order.payment_domain}${path}`
      }
    }
    const { success, data } = await $fetchDefault<ResponseData<string>>($api.API_ORDER_CREATE_PAYPAL, {
      method: $method.post,
      body: {
        order_token: order.access_token
      }
    })

    if (success) {
      window!.top!.location.href = data
    }
    else {
      handleError(data)
    }
  }
  // Paypal End

  // Stripe
  // let stripeGateway = PAYMENT_METHOD.stripe
  // const payWithCardAble = ref(false)
  const stripeErrorCount = ref(0)
  const elementBanks: any = {}
  let stripeBankClientSecrets: Record<string, string> = {}
  const stripeBankDetail = ref<Partial<GetIntentOrderAdditionResponse>>({})
  let stripeBankReloadCount = 0
  const stripeData = reactive({
    clientSecret: '',
    publishableKey: '',
    brandCard: '',
    currentBank: $stripeBanks[0],
    billingDetails: {
      country: ''
    },
    stripeGatewayError: ''
  })
  const stripeProcessedBanks: Ref<any[]> = ref([])

  // Stripe Iframe Initializer
  async function initStripeIframe() {
    if (order.fulfill_status === 'no_ship') { return }

    if (stripeErrorCount.value > 3) {
      handleError('Can not init stripe')
      return
    }

    const { success, data } = await $fetchWrite<ResponseData<Partial<GetIntentOrderAdditionResponse>>>($api.API_STRIPE_GET_INTENT_ORDER_ADDITION, {
      query: {
        order_token: token,
        payment_methods: 'card',
        gateway_id: gateways.stripeGateway?.id
      }
    })

    if (!success) {
      stripeErrorCount.value++
      setTimeout(initStripe, 1000)
      return
    }

    preProcessStripeBanks()

    attachPostMessageHandler()
    await nextTick()

    Object.assign(stripeData, data)

    let stripeIframeAddr = '/embed/stripe.html'
    if (gateways.stripeGateway?.checkout_domain && !isDevMode().isDev) {
      stripeIframeAddr = (gateways.stripeGateway.checkout_domain.startsWith('https://')) ? `${gateways.stripeGateway.checkout_domain}${stripeIframeAddr}` : `https://${gateways.stripeGateway.checkout_domain}${stripeIframeAddr}`
    }

    const stripeIframeClasses = ['w-full']

    iframes.stripeCard = generateAndMountIframe({
      src: stripeIframeAddr,
      classes: stripeIframeClasses,
      container: iframeContainers.stripeCard.value,
      autoResize: true
    })
    iframes.stripeEwallet = generateAndMountIframe({
      src: `${stripeIframeAddr}?ewallet=true`,
      classes: stripeIframeClasses,
      container: iframeContainers.stripeEwallet.value,
      autoResize: true
    })

    if (!currentGateway.value) {
      currentGateway.value = 'stripe-card'
    }
  }
  // Stripe Initializers
  const reloadGatewayIfEncountered = [
    'payment_intent_authentication_failure',
    'payment_intent_payment_attempt_expired',
    'payment_intent_payment_attempt_failed',
    'payment_method_customer_decline',
    'payment_method_provider_decline',
    'payment_method_provider_timeout',
    'setup_attempt_failed',
    'setup_intent_authentication_failure',
    'setup_intent_setup_attempt_expired'
  ]
  async function initStripe() {
    useHead({
      script: [{
        hid: 'stripe-js',
        src: 'https://js.stripe.com/v3/',
        // @ts-ignore
        body: true
      }]
    })

    if (stripeErrorCount.value > 3) {
      handleError('Can not init stripe')
      return
    }

    if (typeof window.Stripe === 'undefined') {
      setTimeout(initStripe, 1000)
      return
    }

    const { success, data } = await $fetchWrite<ResponseData<Partial<GetIntentOrderAdditionResponse>>>($api.API_STRIPE_GET_INTENT_ORDER_ADDITION, {
      query: {
        order_token: token,
        payment_methods: 'card',
        gateway_id: gateways.stripeGateway?.id
      }
    })

    if (!success) {
      stripeErrorCount.value++
      setTimeout(initStripe, 1000)
      return
    }

    preProcessStripeBanks()

    if (!currentGateway.value) {
      currentGateway.value = 'stripe-card' // WARN: could auto select this gateway if encounter any error @ any payment gateway
    }

    Object.assign(stripeData, data)
    stripe = window.Stripe(stripeData.publishableKey)

    initStripeCard()
    initStripeEWallet()

    if (currentGateway.value === PAYMENT_METHOD.stripeBank) {
      stripeSelectBank(stripeData.currentBank)
    }
  }
  function initStripeCard() {
    // Create Card
    const element = stripe.elements({
      fonts: [
        {
          family: 'Montserrat',
          cssSrc: 'https://fonts.googleapis.com/css?family=Montserrat:400,500'
        }
      ],
      clientSecret: stripeData.clientSecret,
      locale: $i18n.locale.value
    })

    const style = {
      base: {
        color: '#32325d',
        fontFamily: 'Montserrat, sans-serif', // set integrated font family
        fontSmoothing: 'antialiased'
      }
    }

    // cardNumber
    stripeCard = element.create('cardNumber', {
      placeholder: `${$i18n.t('Card number')} *`,
      showIcon: true,
      style
    })

    stripeCard.mount('#card-payment-number')
    // cardExpiry
    const cardExpiry = element.create('cardExpiry', {
      placeholder: `${$i18n.t('Expiration date')} (${$i18n.t('MM/YY')}) *`,
      style
    })
    cardExpiry.mount('#card-payment-expiry')
    // cardCvc
    const cardCvc = element.create('cardCvc', {
      placeholder: `${$i18n.t('Security code')} *`,
      style
    })
    cardCvc.mount('#card-payment-cvc')

    stripeCard.on('change', (event: any) => {
      stripeData.brandCard = event.brand
      if (event.error) {
        showError(event.error?.message)
        handleError(event.error?.message)
      }
    })
    stripeCard.on('focus', () => {
      currentGateway.value = 'stripe-card'
    })
  }
  function initStripeEWallet() {
    const appearance = {
      variables: {
        fontFamily: 'Montserrat, sans-serif',
        borderRadius: '10px',
        colorPrimary: useStoreInfo().primaryColor
      },
      rules: {
        '.Input, .Input:focus': {
          boxShadow: 'none',
          height: '45px'
        }
      }
    }

    stripeElements = stripe.elements({
      clientSecret: stripeData.clientSecret,
      appearance,
      locale: $i18n.locale.value
    })

    // Create eWallet
    const paymentElement = stripeElements.create('payment', {

    })
    paymentElement.mount('#payment-element')

    // update payment method when changed
    paymentElement.on('focus', () => {
      currentGateway.value = 'stripe-ewallet'
    })

    // paymentElement.on('change', (event: any) => {
    //   if (currentGateway.value.includes('stripe-')) {
    //     if (event.value.type !== 'card') {
    //       stripeGateway = `stripe-${event.value.type}`
    //     }
    //   }
    // })
  }

  // Stripe Payment Handler
  async function handleStripePayment(response: any) {
    if (response.error) {
      handleError(response.error)
      loading(false)

      if (reloadGatewayIfEncountered.includes(response.error?.code)) {
        initStripe()
      }

      return
    }

    if (response.paymentIntent.status === 'requires_action') {
      const result = await stripe.confirmCardPayment(stripeData.clientSecret)
      await handleStripePayment(result)
      return
    }

    onCreateOrderSucceeded(order.access_token)
  }
  // Stripe Handle Payments Type
  async function stripePayWithCard() {
    loading('payment')

    if (iframes.stripeCard && iframes.stripeCard.contentWindow) {
      iframes.stripeCard.contentWindow.postMessage({ name: 'stripe_confirm_card', args: [] }, '*')
      return
    }

    const response = await stripe.confirmCardPayment(stripeData.clientSecret, {
      payment_method: {
        card: stripeCard,
        billing_details: {
          name: userInfo.name,
          email: userInfo.email,
          phone: userInfo.phone,
          address: {
            line1: userInfo.address,
            line2: userInfo.address_2,
            city: userInfo.city,
            postal_code: userInfo.zipcode,
            state: userInfo.state,
            country: userInfo.country
          }
        }
      }
    })
    await handleStripePayment(response)
  }
  async function stripePayWithEWallet() {
    loading('payment')

    const returnURL = stripeGetReturnURL()

    if (iframes.stripeEwallet && iframes.stripeEwallet.contentWindow) {
      iframes.stripeEwallet.contentWindow.postMessage({ name: 'stripe_confirm_ewallet', args: [returnURL] }, '*')
      return
    }

    const response = await stripe.confirmPayment({
      elements: stripeElements,
      confirmParams: {
        return_url: returnURL
      },
      redirect: 'if_required'
    })
    await handleStripePayment(response)
  }
  function stripePayWithBank() {
    const returnURL = stripeGetReturnURL()
    const { currentBank, publishableKey: defaultPublishableKey } = stripeData

    const maxReloadCount = 3
    if (typeof window.Stripe === 'undefined' && maxReloadCount <= stripeBankReloadCount) {
      stripeBankReloadCount += 1
      setTimeout(stripePayWithBank, 1000)
      return
    }
    console.log('stripe bank initiated') // TODO: rm this

    if (!stripeBankEl) {
      stripeBankEl = window.Stripe(stripeBankDetail.value?.publishableKey || defaultPublishableKey)
    }

    switch (currentBank.name) {
      case 'Sofort':
        stripeBankEl.confirmSofortPayment(stripeBankClientSecrets[currentBank.currency], {
          payment_method: {
            sofort: {
              country: userInfo.country
            }
          },
          return_url: returnURL
        })
        break
      case 'iDEAL':
        stripeBankEl.confirmIdealPayment(stripeBankClientSecrets[currentBank.currency], {
          payment_method: {
            ideal: {},
            billing_details: {
              name: userInfo.name
            }
          },
          return_url: returnURL
        })
        break
      case 'Bancontact':
        stripeBankEl.confirmBancontactPayment(stripeBankClientSecrets[currentBank.currency], {
          payment_method: {
            billing_details: {
              name: userInfo.name
            }
          },
          return_url: returnURL
        })
        break
      case 'Klarna':
        stripeBankEl.confirmKlarnaPayment(stripeBankClientSecrets[currentBank.currency], {
          payment_method: {
            billing_details: {
              email: userInfo.email,
              address: {
                country: stripeData.billingDetails.country
              }
            }
          },
          return_url: returnURL
        })
        break
    }
  }

  // Stripe Helpers
  function stripeGetReturnURL() {
    const url = new URL(window.location.href)

    let returnURL = `${url.origin}/checkout/stripe`
    returnURL += `?order_token=${order.access_token}`
    returnURL += `&publishable_key=${stripeData.publishableKey}`

    return returnURL
  }
  async function stripeSelectBank(stripeBank: any) {
    // if haven't created transaction for this currency
    if (!stripeBankClientSecrets[stripeBank.currency]) {
      const paymentMethodHasSameCurrency = stripeProcessedBanks.value.reduce((arr, bank) => {
        if (bank.currency === stripeBank.currency) {
          arr.push(bank.payment_method)
        }
        return arr
      }, [] as string[])

      loading('place_order')

      if (!order.access_token || !gateways?.stripeGateway?.id) {
        handleError(`Missing ${(order.access_token) ? 'order token' : 'stripe gateway id'}`)
        return
      }

      // Separated get intent for BANKS ONLY
      const { success, data, message } = await $fetchWrite<ResponseData<GetIntentOrderAdditionResponse>>($api.API_STRIPE_GET_INTENT_ORDER_ADDITION, {
        query: {
          order_token: order.access_token,
          payment_methods: paymentMethodHasSameCurrency?.length ? paymentMethodHasSameCurrency.join(',') : '',
          gateway_id: gateways.stripeGateway!.id,
          currency_code: stripeBank.currency
        }
      })

      loading(false)
      if (!success) {
        handleError(message)
        return
      }

      stripeBankClientSecrets[stripeBank.currency as string] = data.clientSecret

      stripeBankDetail.value = data

      if (!stripeBankEl) {
        stripeBankEl = window.Stripe(stripeBankDetail.value?.publishableKey || stripeData.publishableKey)
      }
    }

    // create bank component
    if (stripeBank.name_component) {
      const options = {
        style: {
          base: {
            'padding': '10px 12px',
            'color': '32325d',
            'fontSize': '16px',
            '::placeholder': {
              color: '#aab7c4'
            }
          }
        },
        placeholder: $i18n.t('Choose a bank')
      }

      elementBanks[stripeBank.name] = (stripeBankEl ?? stripe).elements({
        clientSecret: stripeBankClientSecrets[stripeBank.currency],
        locale: $i18n.locale.value
      }).create(stripeBank.name_component, options)

      nextTick(() => {
        elementBanks[stripeBank.name].mount(`#stripe-${stripeBank.name}`)
      })
    }
  }
  function preProcessStripeBanks() {
    stripeProcessedBanks.value = $stripeBanks.reduce((arr, bank) => {
      const clonedBank: any = _cloneDeep(bank)
      clonedBank.id = gateways.stripeGateway?.id

      let currency = 'EUR'
      if (clonedBank.name === 'Klarna') {
        if (gateways.stripeGateway?.options?.klarna_currency) {
          currency = gateways.stripeGateway.options.klarna_currency
        }
        else {
          return arr
        }
      }
      clonedBank.currency = currency

      arr.push(clonedBank)
      return arr
    }, [] as any[])

    stripeData.currentBank = stripeProcessedBanks.value[0]
    stripeBankClientSecrets = {}
  }
  watch(currentGateway, (newVal) => {
    if (newVal === PAYMENT_METHOD.stripeBank) {
      stripeSelectBank(stripeData.currentBank)
    }
  })
  // Stripe End

  return {
    userInfoForm,

    paymentDisplayType,
    isShowModalConfirmEmail,

    gateways,
    currentGateway,
    currentGatewayId,

    initPaymentGateways,
    submitCheckout,
    useCreditCardDiscount,

    paypalIframeFullScreen,
    paypalButtonContainer,
    paypalIsShowModalCreditCardDiscount,
    paypalModalConfirmed,
    paypalDiscount,
    paypalPayWithNormal,

    stripeIframeContainer: iframeContainers.stripeCard,
    stripeIframeEWalletContainer: iframeContainers.stripeEwallet,
    stripeData,
    stripeBankDetail,
    stripeProcessedBanks,
    stripePayWithCard,
    stripePayWithEWallet,
    stripePayWithBank,
    stripeSelectBank
  }
}

// Helpers
function onCreateOrderSucceeded(orderToken: string, additionalQuery: string = '') {
  const localePath = useLocalePath()
  const $router = useRouter()

  loading('place_order')

  setTimeout(async () => {
    useCartStore().resetCart()
    useComboCartStore().reset()
    await $router.push(localePath(`/order/thank-you/${orderToken}?${additionalQuery}`))
    loading(false) // Clear all loading effects
  }, 1000)
}

function isDevMode() {
  const domain = window!.top!.location.hostname
  const isDev = domain === 'localhost' || domain.endsWith('.dev.senprints.net') // could be changing in v4

  return {
    isDev,
    domain
  }
}

function generateAndMountIframe({ src = '', classes = [], container = undefined, autoResize = false }: { src: string, classes?: string[], container?: HTMLElement, autoResize?: boolean }): HTMLIFrameElement {
  const iframe = document.createElement('iframe')
  iframe.src = src

  if (classes) {
    iframe.classList.add(...classes)
  }
  if (container) {
    container.innerHTML = ''
    container.appendChild(iframe)
  }

  if (autoResize && iframe.contentWindow) {
    iframe.contentWindow.onresize = () => {
      iframe.setAttribute('style', `height: calc(${iframe.contentWindow?.document.body.clientHeight || 100}px + 1rem);`)
    }
  }

  return iframe
}
