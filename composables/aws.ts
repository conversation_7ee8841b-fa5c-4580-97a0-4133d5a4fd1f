import { useUserSession } from "~/store/userSession";
export function useAWSUpload () {
  const { $i18n } = useNuxtApp()
  const userSession = useUserSession()
  /**
   * @param fileName
   * @returns {{ext: *, name: *}}
   */
  const parseFileInfo = (fileName:string) => {
    const els = fileName.split('.')
    return {
      name: els.slice(0, -1).join('.'),
      ext: els.pop()
    }
  }

  /**
   * @param file
   * @returns {Promise<*|null>}
   */
  const createSignedURL = async (file:File) => {
    await userSession.getCSRFToken()
    const currentTime = new Date()
    const fileInfo = parseFileInfo(file.name)
    const fileDate = currentTime.getFullYear() + '|' + (currentTime.getMonth() + 1) + '|' + currentTime.getDate() + '|' + currentTime.getTime()
    const fileHash = await sha256(fileInfo.name + '|' + fileDate) + '.' + fileInfo.ext
    const fileName = 'tmp/' + fileHash
    const response = await $fetch('/api/public/pre-signed-url', {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-session-id": userSession.visitInfo.session_id as string,
        "x-csrf-token": userSession.visitInfo.csrfToken as string
      },
      body: JSON.stringify({
        fileName: fileName
      }),
    })
    // @ts-ignore
    const { success, data } = response
    if (success) {
      return data
    }
    return null
  }

  /**
   * @param file
   * @returns {Promise<{Key}|boolean>}
   */
  const AWSUpload = async (file:File) => {
    const preSignedResult = await createSignedURL(file)
    if (!preSignedResult) {
      return false
    }
    try {
      const { filePath, preSignedUrl } = preSignedResult
      const response = await fetch(preSignedUrl, {
        method: "PUT",
        headers: {
          "Content-Type": file.type
        },
        body: file,
      })
      if (response.status === 200) {
        if (typeof window !== 'undefined' && typeof CustomEvent !== 'undefined') {
          window.dispatchEvent(new CustomEvent('sp_file_uploaded', { detail: filePath }))
        }
        return filePath
      }
      uiManager().createPopup($i18n.t('Cannot upload image'))
      return
    } catch (error) {
      uiManager().createPopup($i18n.t('Cannot upload image'))
      return
    }
  }

  return {
    AWSUpload,
  }
}
