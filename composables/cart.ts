/* eslint-disable no-case-declarations */
import { useCartStore } from '~~/store/cart'
import { useCampaignStore } from '~/store/campaign'
import { useUserSession } from '~/store/userSession'
import {$getProductUrl} from "~/utils/helper"
import { useComboCartStore } from '~/store/comboCart'

export function useCartItem (cartItem:CartItem) {
  const cartStore = useCartStore()
  const index = computed(() => cartStore.listCartItem.findIndex(item => item === cartItem))
  const cartInfo = computed(() => cartStore.listCartItem[index.value])
  const totalCustomOptionFee = computed(() => {
    return cartInfo.value?.extra_custom_fee || 0
  })
  const extraCustomFee = cartItem.extra_custom_fee || 0
  const isEditCart = ref(false)
  const isShowModalDeleteCartItem = ref(false)
  const currentVariant = ref<Partial<Variant> | undefined>({})
  const comboStore = useComboCartStore()
  const isHasCampaignData = ref(true)
  const { $getProductVariantsBySelectedKeyAndCountry, $userCountryForPricing } = useNuxtApp()

  const {
    campaignData,
    getCampaignData
  } = useCampaign(cartItem?.campaign_slug as string)
  const {
    getVariantsByProductId,
  } = useCampaignStore()

  setTimeout(async () => {
    await getCampaignData()
    if (!campaignData?.id) {
      isHasCampaignData.value = false
    }
    let variantKey
    if (cartItem.options) {
      variantKey = `${cartItem.options?.color?.replace(/_/g, '').replace(/ /g, '_').replace(/,/g, '-')}-${cartItem?.options?.size}`
    }
    const productId = cartItem.product_id
    const sellerId = cartItem.seller_id

    const currentProductVariants = productId ? await getVariantsByProductId(productId, sellerId) : (campaignData.variants ?? [])
    currentVariant.value = $getProductVariantsBySelectedKeyAndCountry(currentProductVariants, variantKey, productId, $userCountryForPricing())
    const product = {
      id: cartItem.product_id,
      name: cartItem.product_name,
      currentOptions: cartItem.options,
      optionsList: cartItem.optionList,
      variantsList: currentProductVariants,
      market_location: campaignData.market_location,
      currency_code: cartItem.currency_code,
      price: cartItem.price,
      pricing_mode: campaignData.pricing_mode
    }

    cartItem.variantPrice = useTestPrice().getPrice(product, currentVariant.value as Variant)
    if (cartItem.full_printed === 5 || cartItem.personalized === 3) {
      campaignData.products = campaignData.products?.filter(item => item.id === cartItem.product_id)
    } else {
      campaignData.products = campaignData.products?.filter((item) => {
        return item.full_printed !== 5 || cartItem.personalized === 3
      })
    }
  })

  async function updateCartItem (key:string, value:any) {
    if (!key || !value) {
      return
    }
    const newData:Partial<CartItem> = {}
    const params = new URLSearchParams()

    switch (key) {
      case 'product':
        if (value.id === cartItem.product_id) {
          return
        }
        value as Product
        const optionList = JSON.parse(value.options)

        newData.product_id = value.id
        newData.product_name = value.name
        newData.price = useTestPrice().getPrice(value) + extraCustomFee
        newData.thumb_url = value.thumb_url
        newData.full_printed = value.full_printed
        newData.optionList = optionList
        newData.options = {}
        if (optionList) {
          Object.keys(optionList).forEach((el, index) => {
            if (newData.options) {
              const lastOption:string = useUserSession().getLastOption(el, value.name) as string
              if (lastOption && optionList[el].includes(lastOption)) {
                newData.options[el] = lastOption
              } else {
                newData.options[el] = lastOption ?? optionList[el][0]
                if (value.default_option && index === 0) {
                  newData.options[el] = value.default_option
                }
              }
              params.set(el, newData.options[el])
            }
          })
        }
        break
      case 'option':
        const { optionName, optionItem } = value
        newData.thumb_url = cartItem.thumb_url
        newData.product_name = cartItem.product_name
        newData.options = { ...cartItem.options }
        newData.options[optionName] = optionItem
        useUserSession().setBehavior(optionName, optionItem, cartItem.product_name)
        Object.keys(cartItem.optionList as Object).forEach((el) => {
          params.set(el, (newData.options && newData.options[el]) as string)
        })

        break
      default:
        return
    }

    if (newData.product_id || newData.options) {
      const currentProduct = campaignData.products?.find(product => product.id === newData.product_id || product.name === newData.product_name)
      if (newData.options.color) {
        newData.thumb_url = newData?.thumb_url?.replace(/co_rgb:.{6}/, `co_rgb:${colorVal(newData.options.color.replace(/-/g, ' '))?.replace('#', '')}`)
      }
      let variantKey
      if (newData.options) {
        variantKey = `${newData.options?.color?.replace(/_/g, '').replace(/ /g, '_').replace(/,/g, '-')}-${newData.options?.size?.replace(/-/g, '')}`
      }
      const productId = currentProduct?.id
      const sellerId = campaignData.seller_id ?? storeInfo.seller_id
      const currentProductVariants = productId ? await getVariantsByProductId(productId, sellerId) : (campaignData.variants ?? [])

      currentVariant.value = $getProductVariantsBySelectedKeyAndCountry(currentProductVariants, variantKey, productId, $userCountryForPricing())
      if (currentProduct) {
        const currentOptions = newData.options
        currentOptions.color = currentOptions.color?.replace(/_/g, '').replace(/ /g, '_').replace(/,/g, '-')
        currentProduct.currentOptions = currentOptions
        currentProduct.variantsList = currentProductVariants
      }
      newData.variantPrice = useTestPrice().getPrice(currentProduct, currentVariant.value as Variant)
      newData.product_url = `/${cartItem.campaign_slug}/${stringHelperToSlug(newData.product_name)}?${params.toString()}`
    }
    cartStore.updateCartItem(cartItem, newData)

    return newData
  }

  function updateQuantity (number:number) {
    if (number === 0) {
      isShowModalDeleteCartItem.value = true
    }
    const quantity = number < 1 ? 1 : number > 50 ? 50 : number
    cartStore.updateCartItem(cartItem, { quantity })
  }

  function duplicateCartItem () {
    cartStore.duplicateCartItem(index.value)
  }

  function removeCartItem () {
    handleCampaignBundleInRemoveCartItem()
    comboStore.removeItem(cartItem)
    cartStore.removeCartItem(index.value)
    isShowModalDeleteCartItem.value = false
  }

  const  isBundleProduct = computed(() => {
    const campaignBundleId = Array.isArray(cartItem.campaignBundleId) ? cartItem.campaignBundleId?.at(-1) : cartItem.campaignBundleId
    return !!cartStore.listCartItem?.find(item => item.campaign_id === campaignBundleId)
  })

  const realPrice = computed(() => {
    return cartItem?.variantPrice || cartItem?.price || 0
  })

  const priceWithCustomOptionFee = computed(() => {
    return realPrice.value + totalCustomOptionFee.value
  })

  const totalPriceWithCustomOptionFee = computed(() => {
    return priceWithCustomOptionFee.value * cartItem.quantity
  })

  const totalPriceBundleWithCustomOptionFee = computed(() => {
    return isBundleProduct.value ? totalPriceWithCustomOptionFee.value * discountRate.value : 0
  })

  const discountRate = computed(() => {
    return (100 - (cartItem?.promotion?.discount_percentage || 0)) / 100
  })

  const totalPrice = computed(() => {
    return realPrice.value * cartItem.quantity
  })

  const totalPriceBundle = computed(() => {
    return isBundleProduct.value ? totalPrice.value * discountRate.value : 0
  })

  function handleCampaignBundleInRemoveCartItem () {
    const campaignId = cartItem?.campaign_id
    const productId = cartItem?.product_id
    const followProduct = cartStore.listCartItem.find((item) => {
      if (item?.promotion &&
        item?.campaignBundleId === campaignId &&
        item?.productBundleId === productId
      ) {
        return true
      }
    })

    const promotion = followProduct?.promotion

    let allowRemovePromotionInFollowProduct = false;
    if (promotion?.is_same_campaign) {
      if (!cartItem?.campaignBundleId || !cartItem?.promotion) {
        allowRemovePromotionInFollowProduct = true
      }
    } else {
      allowRemovePromotionInFollowProduct = true
    }

    if (allowRemovePromotionInFollowProduct) {
      cartStore.listCartItem.forEach((cartItem) => {
        if (cartItem?.campaignBundleId && cartItem?.campaignBundleId === campaignId) {
          cartItem.campaignBundleId = undefined
          cartItem.promotion = undefined
          cartItem.productBundleId = undefined
          cartStore.updateCartItemByID(cartItem.id, cartItem)
        }
      })
    }
  }

  function getProductUrl (url?:String) {
    return $getProductUrl(url)
  }

  return {
    isHasCampaignData,
    isBundleProduct,
    campaignData,
    realPrice,
    totalPrice,
    totalPriceBundle,
    isEditCart,
    isShowModalDeleteCartItem,
    currentVariant,
    getProductUrl,
    updateQuantity,
    updateCartItem,
    duplicateCartItem,
    removeCartItem,
    priceWithCustomOptionFee,
    totalPriceBundleWithCustomOptionFee,
    totalPriceWithCustomOptionFee,
    handleCampaignBundleInRemoveCartItem
  }
}

// function getVariant (cartItem: Partial<CartItem>, variants?:Array<Variant>) {
//   const variantKey = Object.keys(cartItem.optionList || {}).map(option => cartItem.options?.[option] || cartItem.optionList?.[option][0]).toString().replace(/-/g, '_').replace(/,/g, '-')
//   return variants?.find(variant => variant.variant_key === variantKey && variant.product_id === cartItem.product_id)
// }
