interface CustomPageData {
  title: string
  description: string
  content: string
  link_json: Product[]
  data_json: Product[]
}

export async function usePageCustom() {
  const fullPath = useRoute().fullPath
  let paths: any = fullPath.split('/')
  paths = `custom-${paths[paths.length - 2]}/${paths[paths.length - 1]}`
  const customPageData = useState<CustomPageData>(`customPageData-${paths}`)
  const { $fetchDefault } = useNuxtApp()

  try {
    if (!customPageData.value) {
      const { success, data } = await $fetchDefault<ResponseData<CustomPageData>>($api.API_GENERATE_SEO, {
        params: {
          path: paths
        }
      })

      if (success && data) {
        customPageData.value = data
      }
    }
  }
  catch (error) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }

  if (!customPageData.value) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }

  return {
    customPageData
  }
}
