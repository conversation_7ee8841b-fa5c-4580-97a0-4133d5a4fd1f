export function useProductDesign(product: Ref<Product>, isModal: boolean) {
  const { $imgUrl } = useNuxtApp()

  async function preLoadDesign(personalize: Personalize) {
    if (personalize.baseMockup && !personalize.imagePreloaded) {
      preloadImgDesign(personalize.baseMockup)
    }

    if (personalize.personalized === 1 && personalize.customDesign) {
      const designData = preloadImgDesign(personalize.customDesign)
      designData.objects.forEach((designObject: any) => {
        if (designObject.type === 'i-text') {
          const checkHasTextItem = product.value?.customTextList?.find(customTextItem => customTextItem.data.name === designObject.name)
          if (checkHasTextItem) {
            checkHasTextItem.subPersonalize = personalize
          }
          else {
            const customTextItem = shallowReactive({
              data: shallowReactive(designObject),
              personalize,
              product: product.value
            })
            product.value?.customTextList?.push(customTextItem)
            personalize?.customTextList?.push(customTextItem)
          }
        }
        if (designObject.type === 'image' && designObject.isCustom) {
          const customImageItem = shallowReactive({
            data: shallowReactive(designObject),
            personalize,
            product: product.value,
            canvasObject: undefined
          })

          product.value?.customImageList?.push(customImageItem)
          personalize?.customImageList?.push(customImageItem)
        }
      })
      personalize.customItemList = personalize.customTextList?.concat(personalize.customImageList || [])

      if (!personalize?.designCanvas) {
        personalize.designCanvas = await $initDesignCanvas()

        if (personalize.mockupCanvas) {
          personalize.mockupCanvas.viewDesign(personalize.designCanvas.exportCanvas())
        }
        personalize.designCanvas.onupdate = () => {
          personalize?.mockupCanvas?.viewDesign(personalize.designCanvas.exportCanvas())
        }

        personalize.designCanvas.set({ enableRetinaScaling: false })
        await personalize.designCanvas.load(personalize.customDesign.design_json, 900)
        personalize.loaded = true
        personalize.designCanvas.lockEdit()
      }

      return resetPersonalizeCustomData(personalize, product.value)
    }
  }

  async function initPersonalizePb(personalize: Personalize) {
    useHead({
      link: [{
        rel: 'stylesheet',
        href: 'https://apis.personalbridge.com/style.css'
      }],
      script: [{
        hid: 'personal-bridge',
        src: useRuntimeConfig().public.personalBridgeUrl,
        // @ts-ignore
        body: true
      }]
    })

    if (window.pbsdk) {
      const result = await window.pbsdk.init({
        key: personalize.personalizeKey,
        artworkId: personalize.pbArtwork?.pb_artwork_id,
        $formEl: isModal ? '#artwork_form_selector_modal' : '#artwork_form_selector',
        $canvasEl: '',
        onChange: (pbCustomInfo) => {
          personalize.pbCustomInfo = pbCustomInfo
          if (personalize.mockupCanvas) {
            personalize.mockupCanvas.viewDesign(personalize.designCanvas)
          }
        }
      })
      personalize.designCanvas = result[0]
      personalize.pbPrintUrl = result[1]
      if (personalize.mockupCanvas) {
        personalize.mockupCanvas.viewDesign(personalize.designCanvas)
      }
    }
    else {
      setTimeout(() => {
        initPersonalizePb(personalize, isModal)
      }, 500)
    }
  }

  function resetPersonalizeCustomData(personalize: Personalize, product: Product) {
    let checkHasData = false
    let textCustomDesign = ''
    const queryParams = useRoute().query

    personalize.customTextList?.forEach((customTextItem) => {
      customTextItem.data.placeholder = customTextItem.data.placeholder || customTextItem.data.text || $i18n.t('Enter text')
      if (!isModal) {
        customTextItem.data.text = queryParams[(customTextItem.data.name && customTextItem.data.name.replaceAll(' ', '-')) as string] as string || ''
      }
      else {
        customTextItem.data.text = ''
      }
      if (customTextItem.data.text) {
        checkHasData = true
        textCustomDesign = customTextItem.data.text && customTextItem.data.text.toLowerCase().charAt(0)
      }
    })

    personalize.customImageList?.forEach((customImageItem) => {
      const currentFileUploadUrl = (() => {
        if (isModal) { return '' }
        try {
          return atob(queryParams['custom-image'] as string)
        }
        catch {
          return queryParams['custom-image'] as string
        }
      })()

      if (currentFileUploadUrl) {
        customImageItem.currentFileUploadUrl = currentFileUploadUrl
      }
      if (!isModal && currentFileUploadUrl) {
        checkHasData = true
      }
    })

    personalize.designCanvas.getObjects().forEach((item: any) => {
      if (item.type === 'i-text') {
        if (!isModal) {
          personalize.designCanvas.updateText(item, queryParams[item.name && item.name.replaceAll(' ', '-')] || item.text)
        }
        else {
          personalize.designCanvas.updateText(item, item.text)
        }
      }
    })

    personalize?.customImageList?.forEach((item) => {
      if (item.currentFileUploadUrl) {
        personalize.designCanvas.changeCustomImage($imgUrl({
          path: item.currentFileUploadUrl,
          type: 'full'
        })).then(() => {
          item.canvasObject = shallowReactive(personalize.designCanvas.getCustomImage())
        })
      }
    })

    if (personalize?.designCanvas?.customDesign && $customDesignArray[personalize?.designCanvas?.customDesign]) {
      const customDesignType = personalize?.designCanvas?.customDesign
      product!.customDesignType = customDesignType
      product!.personalizeCustomDesign = personalize
      if (customDesignType === 'custom') {
        $customDesignArray.custom = personalize?.designCanvas?.customDesignList
      }
      product!.customDesignList = $customDesignArray[customDesignType]
      if (queryParams.customdesign && product?.customDesignList.includes(queryParams.customdesign as string)) {
        product!.selectedCustomDesign = queryParams.customdesign as string
        checkHasData = true
      }
      else if (checkHasData && product?.customDesignList.includes(textCustomDesign)) {
        product!.selectedCustomDesign = textCustomDesign
      }
      else {
        product!.selectedCustomDesign = product?.customDesignList[0]
      }
      personalize?.designCanvas?.changeDesignByCharacter(product!.selectedCustomDesign)
    }
    return checkHasData
  }

  function preloadImgDesign(design: DesignItem) {
    const { $preloadImg } = useNuxtApp()
    const designData = JSON.parse(design?.design_json || '')

    if (designData.backgroundImage?.src) {
    // resize image
      if (window.isSupportWebp) {
        designData.backgroundImage.src = designData.backgroundImage.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900,q_90,ofmt_webp/')
      }
      else {
        designData.backgroundImage.src = designData.backgroundImage.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900/')
      }
      $preloadImg({ path: designData.backgroundImage.src })
    }

    designData.objects.forEach((item: any) => {
      if (item.type === 'image') {
        if (window.isSupportWebp) {
          item.src = item.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900,q_90,ofmt_webp/')
        }
        else {
          item.src = item.src.replace(/\/rx\/[a-z0-9_,]+\//g, '/rx/900/')
        }
      // $preloadImg({ path: item.src })
      }
    })
    design.design_json = JSON.stringify(designData)
    return designData
  }

  return {
    preLoadDesign,
    initPersonalizePb,
    resetPersonalizeCustomData,
    preloadImgDesign
  }
}
