import { useProductReviewsStore } from '~/store/productReviews'
import { useStoreInfo } from '~/store/storeInfo'

export function useCampaignReview (currentProduct: Ref<Product | undefined>) {
  const productReviewStore = useProductReviewsStore()
  const reviewDisplay = useStoreInfo().product_review_display

  const isLoadingReviews = ref(false)
  const starCounts: ('five_star_count' | 'four_star_count' | 'three_star_count' | 'two_star_count' | 'one_star_count')[] = ['five_star_count', 'four_star_count', 'three_star_count', 'two_star_count', 'one_star_count']
  const filtersList: ({ value: ProductReviewFilter, text: string, icon?: string })[] = [
    { value: 'helpful', text: 'Helpful', icon: undefined },
    { value: 'newest', text: 'Newest', icon: undefined },
    { value: 'five_star', text: '5', icon: 'icon-sen-star-fill text-[#ffc107]' },
    { value: 'four_star', text: '4', icon: 'icon-sen-star-fill text-[#ffc107]' },
    { value: 'three_star', text: '3', icon: 'icon-sen-star-fill text-[#ffc107]' },
    { value: 'two_star', text: '2', icon: 'icon-sen-star-fill text-[#ffc107]' },
    { value: 'one_star', text: '1', icon: 'icon-sen-star-fill text-[#ffc107]' },
  ]
  const defaultReviewSummary: ProductReviewSummary = {
    summary: {
      average_rating: 0,
      review_count: 0,
      five_star_count: 0,
      four_star_count: 0,
      three_star_count: 0,
      two_star_count: 0,
      one_star_count: 0
    },
    files: []
  }
  const productReviews: Ref<ProductReviewComposable> = ref({
    reviewSummary: { ...defaultReviewSummary },
    reviews: {
      data: [],
      current_page: 0,
      per_page: 0,
      last_page: 0,
      total: 0,
      from: 0,
      to: 0,
      first_page_url: '',
      last_page_url: '',
      next_page_url: '',
      prev_page_url: '',
      links: [{ url: '', label: '' }]
    },
    filter: 'helpful',
    page: 1
  })

  function getReviewSummary (campaignId: number, templateId: number) {
    productReviewStore.getReviewSummary({ campaignId, templateId }).then((response) => {
      productReviews.value.reviewSummary = response?.data || { ...defaultReviewSummary }
    })
  }
  function getReviews (campaignId: number, templateId: number) {
    isLoadingReviews.value = true
    productReviewStore.getReviews({ campaignId, templateId, filter: productReviews.value.filter, page: productReviews.value.page }).then((response) => {
      productReviews.value.reviews = response?.data || []
      isLoadingReviews.value = false
    })
  }

  function changePage (page: number | string | null) {
    if (!page || parseInt(page as string) > productReviews.value.reviews.last_page) { return }

    productReviews.value.page = page
    currentProduct.value?.campaign_id && currentProduct.value?.template_id && getReviews(currentProduct.value.campaign_id, currentProduct.value.template_id)
    if (import.meta.client) {
      document.querySelector('#productReviewBox')!.scrollIntoView()
    }
  }
  function changeFilter (filter: ProductReviewFilter) {
    productReviews.value.filter = filter
    productReviews.value.page = 1
    currentProduct.value?.campaign_id && currentProduct.value?.template_id && getReviews(currentProduct.value.campaign_id, currentProduct.value.template_id)
  }

  function fetchCampaignReviews () {
    if (reviewDisplay !== 'disable' && currentProduct.value?.campaign_id && currentProduct.value?.template_id) {
      getReviewSummary(currentProduct.value.campaign_id, currentProduct.value.template_id)
      getReviews(currentProduct.value.campaign_id, currentProduct.value.template_id)
    }
  }

  watch(currentProduct, () => {
    productReviews.value.filter = 'helpful'
    productReviews.value.page = 1

    fetchCampaignReviews()
  })
  onMounted(() => {
    fetchCampaignReviews()
  })

  return {
    isLoadingReviews,

    starCounts,
    filtersList,
    reviewDisplay,
    productReviews,

    changePage,
    changeFilter,
  }
}
