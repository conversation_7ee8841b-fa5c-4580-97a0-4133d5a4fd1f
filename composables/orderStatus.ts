import { onMounted } from 'vue'
import { useListingStore } from '~~/store/listing'
import { useStoreInfo } from '~~/store/storeInfo'
import { useUserSession } from '~~/store/userSession'
import { countryByCode } from './common'

export async function useOrderStatusPage() {
  const { $i18n, $fetchDefault } = useNuxtApp()
  const relatedProducts = ref<Product[]>()

  onMounted(async () => {
    loading(false) // Clear all loading effects

    const listRelatedProducts: RelatedProductPostData = {
      type: 'post_sale',
      filter: []
    }
    listRelatedProducts.filter = []
    for (const key of orderStatuses) {
      for (const product of tracking?.[key as keyof Fulfillments] as OrderProduct[]) {
        if (!product.product_id) {
          return
        }
        const item = {
          product_id: product.product_id,
          campaign_id: product.campaign_id,
          template_id: product.template_id
        }
        listRelatedProducts.filter.push(item)
      }
    }

    if (listRelatedProducts.filter.length > 0) {
      if (storeInfo.smart_remarketing && order.id) {
        listRelatedProducts.order_id = Number.parseInt(order.id as unknown as string)
        listRelatedProducts.source = 'order_status'
      }
      relatedProducts.value = await listingStore.postRelatedProduct(listRelatedProducts)
    }
  })

  const { params, query } = useRoute()
  const { success, data } = await $fetchDefault<ResponseData<GetOrderResponse>>(`${$api.API_GET_ORDER}/${params.token}/detail`)

  if (!(success || data)) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }
  const { order, fulfillments: tracking, timeframe } = data
  const { currentShippingMethod } = extractOrderDataResponse(data)

  const storeInfo = useStoreInfo()
  const listingStore = useListingStore()

  const isShowModal = ref({
    editInfo: false,
    pendingCustomerAction: false,
    requestCancelOrder: false,
    confirmDeleteRequest: false
  })

  const cancelOrderMsgs = ref({
    submitted: false,
    expired: false,
    cannotCancel: false
  })
  cancelOrderMsgs.value.submitted = (query.error && Number.parseInt(query.error as string) === 1) as boolean
  cancelOrderMsgs.value.expired = (query.error && Number.parseInt(query.error as string) === 2) as boolean

  function getOrderStatus(status: FulfillStatus) {
    const table: Record<FulfillStatus, {
      className: string
      text: string
    }> = {
      unfulfilled: {
        className: 'secondary',
        text: 'Unfulfilled'
      },
      cancelled: {
        className: 'text-red-600',
        text: 'Cancelled'
      },
      processing: {
        className: 'info',
        text: 'In production'
      },
      on_delivery: {
        className: 'success',
        text: 'On Delivery'
      },
      fulfilled: {
        className: 'success',
        text: 'Delivered'
      }
    }
    const defaultRes = {
      className: 'primary',
      text: 'Processing'
    }

    const parsedStatus = status.split('_')?.[0] as FulfillStatus

    return table[parsedStatus] || defaultRes
  }
  function getTimeTracking(timeString?: string) {
    const time = new Date(timeString || '')
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec']
    const value = `${time.getDate()} ${$i18n.t(monthNames[time.getMonth()])}`
    return value
  }
  function compareTrackingTime(time?: string, compare: string = '') {
    const now = (compare) ? Date.parse(compare) : Date.now()
    const fromTimeframe = Date.parse(time || '')
    return now >= fromTimeframe
  }

  function showConfirmDeleteRequestCancelOrder() {
    isShowModal.value.pendingCustomerAction = false
    isShowModal.value.confirmDeleteRequest = true
  }
  function showPendingCustomerAction() {
    isShowModal.value.confirmDeleteRequest = false
    isShowModal.value.pendingCustomerAction = true
  }
  async function resumeOrder() {
    const { visitInfo } = useUserSession()
    const { success, message } = await $fetchDefault<ResponseData<any>>(`${$api.API_CONFIRM_CANCEL_ORDER}/${order.access_token}`, {
      method: 'DELETE',
      headers: {
        'X-User-Id': visitInfo?.user_id?.toString() ?? ''
      }
    })
    if (success) {
      order.request_cancel = null
      isShowModal.value.confirmDeleteRequest = false
      isShowModal.value.pendingCustomerAction = false
      cancelOrderMsgs.value.submitted = false
      useRouter().go(0)
    }
    else {
      uiManager().createPopup(message)
    }
  }

  async function cancelOrder() {
    const { visitInfo } = useUserSession()
    const { success, message } = await $fetchDefault<ResponseData<any>>(`${$api.API_CONFIRM_CANCEL_ORDER}/${order.access_token}`, {
      method: 'PUT',
      headers: {
        'X-User-Id': visitInfo?.user_id?.toString() ?? ''
      }
    })
    if (success) {
      order.request_cancel = {
        order_id: order.id,
        status: 'pending',
        sent_email: 0
      }
      isShowModal.value.requestCancelOrder = false
      cancelOrderMsgs.value.submitted = true
      useRouter().go(0)
    }
    else {
      uiManager().createPopup(message)
    }
  }

  function getTotalTrackingItemText(trackingItem: OrderProduct[]) {
    let total = 0
    for (let i = 0; i < trackingItem.length; i++) {
      total += trackingItem[i].quantity
    }

    return $i18n.t('item', { count: total })
  }

  const currentCountry = countryByCode(order.country)
  const orderStatuses = Object.keys(tracking || {}) as FulfillStatus[]
  const canEditAddressInfo = computed(() => {
    const DAY = 86400000
    const numberDay = order.type === 'regular' && order.address_verified === 'invalid' ? 2 : 1
    return Date.parse(order.paid_at || '') + DAY * numberDay > Date.now()
  })
  const hasMailboxNumber = computed(() => order.mailbox_number && order.mailbox_number.trim().length > 0)
  const hasHouseNumber = computed(() => order.house_number && order.house_number.trim().length > 0)

  return {
    relatedProducts,

    getOrderStatus,
    getTimeTracking,
    compareTrackingTime,

    showConfirmDeleteRequestCancelOrder,
    showPendingCustomerAction,
    resumeOrder,
    cancelOrder,
    getTotalTrackingItemText,

    order,
    tracking,
    timeframe,

    hasMailboxNumber,
    hasHouseNumber,
    currentCountry,
    orderStatuses,
    cancelOrderMsgs,
    isShowModal,
    canEditAddressInfo,
    currentShippingMethod
  }
}
