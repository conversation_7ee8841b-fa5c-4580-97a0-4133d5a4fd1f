import { useGeneralSettings } from '~/store/generalSettings'

export function useCountryFormConfig (code: Ref<string | undefined> | string | undefined) {
  const generalSettings = useGeneralSettings()
  const currentCountryFormMap = computed(() => {
    const codeValue = isRef(code) ? code.value : code
    if (!codeValue) {
      return null
    }
    const config = generalSettings.getCheckoutFormConfig as CheckoutFormConfig
    return config[codeValue === 'GB' ? 'UK' : codeValue] ?? null
  })

  function isRequired (key: CheckoutFormConfigItemKey, defaultValue = true) {
    return currentCountryFormMap.value?.[key]?.required ?? defaultValue
  }

  return {
    currentCountryFormMap,
    isRequired
  }
}
