import { useUserSession } from '~/store/userSession'
import { getDynamicBaseCostIndex } from '~/utils/price'

export function useTestPrice() {
  const userSession = useUserSession()

  function getPrice(product?: Product, variant?: Variant) {
    let adjustBaseCost = 0
    if (product !== undefined) {
      adjustBaseCost = getDynamicBaseCostIndex(product)
    }
    if (userSession.isTestPriceApply === true && product?.r_price && product.seller_id === storeInfo().seller_id) {
      return (variant?.r_price || product.r_price || 0) + adjustBaseCost
    }
    return (variant?.price || product?.price || 0) + adjustBaseCost
  }

  function getOldPrice(product?: Product, variant?: Variant) {
    if (userSession.isTestPriceApply === true && product?.r_old_price && product.seller_id === storeInfo().seller_id) {
      return variant?.r_old_price || product.r_old_price || 0
    }
    return variant?.old_price || product?.old_price || 0
  }

  function initTestPrice() {
    userSession.isTestPriceApply = !!(Math.floor(Math.random() * 100) % 2)
  }

  return {
    initTestPrice,
    getPrice,
    getOldPrice
  }
}
