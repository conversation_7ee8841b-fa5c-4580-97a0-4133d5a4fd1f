import { useUserSession } from '~/store/userSession'

interface CommonModalEmits {
  (e: 'isShowModal', state: boolean): void
}
interface CommonModalEmitsWithRefresh extends CommonModalEmits {
  (e: 'refreshOrder'): void
}

export function useModalEditInfo(order: Order, $emit: CommonModalEmitsWithRefresh, userInfoForm: Ref) {
  const { $i18n, $fetchWrite } = useNuxtApp()
  const currentCountry = countryByCode(order.country)
  const isConfirmAddr = ref(false)
  const isValidOrderNumber = ref(false)
  const userInfo = ref({
    name: order.customer_name || '',
    email: order.customer_email || '',
    order_number: order.order_number || '',
    address: order.address || '',
    address_2: order.address_2 || '',
    city: order.city || '',
    state: order.state || '',
    zipcode: order.postcode || '',
    phone: order.customer_phone || '',
    country: order.country || '',
    mailbox_number: order.mailbox_number || '',
    house_number: order.house_number || '',
    note: order.order_note || ''
  })

  async function updateCustomerData() {
    if (!userInfo.value.order_number) {
      return uiManager().createPopup($i18n.t('Invalid Order Number'))
    }
    else {
      isValidOrderNumber.value = true
    }

    if (!userInfoForm.value.checkUserInfo()) {
      return
    }

    const mappedOrderInfo = {
      customer_name: userInfo.value.name,
      customer_email: userInfo.value.email,
      order_number: userInfo.value.order_number,
      address: userInfo.value.address,
      address_2: userInfo.value.address_2,
      city: userInfo.value.city,
      state: userInfo.value.state,
      postcode: userInfo.value.zipcode,
      customer_phone: userInfo.value.phone,
      country: order.country,
      confirm_address: isConfirmAddr.value,
      mailbox_number: userInfo.value.mailbox_number,
      house_number: userInfo.value.house_number,
      order_note: userInfo.value.note
    }
    const { visitInfo } = useUserSession()

    const { success } = await $fetchWrite<ResponseData<any>>(`${$api.API_CUSTOMER_INFO}`, {
      method: 'PUT',
      body: mappedOrderInfo,
      headers: {
        'X-User-Id': visitInfo?.user_id?.toString() ?? ''
      }
    })

    if (success) {
      uiManager().createPopup($i18n.t('Update info success'), 'success')
      setTimeout(() => {
        $emit('refreshOrder')
        $emit('isShowModal', false)
      }, 1000)
    }
    else {
      uiManager().createPopup($i18n.t('Server busy'))
    }
  }

  return {
    currentCountry,
    userInfo,
    isConfirmAddr,
    isValidOrderNumber,

    updateCustomerData
  }
}

interface ModalConfirmAddressEmit extends CommonModalEmits {
  (e: 'showEditAddress'): void
}
export function useModalConfirmAddress(order: Order, $emit: ModalConfirmAddressEmit) {
  const { $fetchWrite } = useNuxtApp()
  const currentCountry = countryByCode(order.country)

  function confirmAddress() {
    $fetchWrite(`${$api.API_CONFIRM_ADDRESS}/${order.access_token}`, { method: 'PUT' }).finally(() => {
      $emit('isShowModal', false)
    })
  }

  async function showModalEditAddress() {
    $emit('isShowModal', false)
    await nextTick()
    $emit('showEditAddress')
  }

  return {
    currentCountry,
    confirmAddress,
    showModalEditAddress
  }
}

interface ProductReviewUploadedFile {
  type: 'image' | 'video'
  url: string
  thumb?: string
}
interface ProductReviewResponse {
  success: boolean
  data: {
    average_rating: number
  }
  message: {
    [key: string]: []
  }
}
interface CheckReviewOrderResponse {
  success: boolean
  data: object
  message: {
    [key: string]: []
  }
}
export function useModalProductReview(orderStatus: string, pos: number, item: OrderProduct, customerName: string) {
  onMounted(() => {
    if (averageRating.value === 0) {
      if (reviewFor.value && Number.parseInt(reviewFor.value) === item.id) {
        isShowModalReviewProduct.value = true
      }
      else if (reviewFor === null && pos === 0) {
        isShowModalReviewProduct.value = true
      }
    }
  })

  const { $i18n, $fetchWrite } = useNuxtApp()

  const errorMessages = ref<any>([])
  const uploadErrorMessages = ref<any>([])
  const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'mov', 'wmv', 'avi', 'webm', 'mkv']
  const allowedTypes = ['image/jpg', 'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/mov', 'video/wmv', 'video/avi', 'video/webm', 'video/mkv']
  const uploadMaxSize = 50

  const averageRating = ref((item.product_review && item.product_review.average_rating) || 0)
  const form: Ref<{
    order_product_id: number
    print_quality_rating: number
    product_quality_rating: number
    customer_support_rating: number
    comment: string
    assets: ProductReviewUploadedFile[] | []
  }> = ref({
    order_product_id: item.id,
    print_quality_rating: 5,
    product_quality_rating: 5,
    customer_support_rating: 5,
    comment: '',
    assets: []
  })
  const isShowModalReviewProduct = ref(false)
  const isShowModalReviewCoupon = ref(false)
  const isFormLoading = ref(false)

  watch(isShowModalReviewProduct, (newVal) => {
    if (newVal) {
      useRouter().push({ hash: `#review-${item.id}` })
    }
  })

  // Computed
  const storeDetail = storeInfo()
  const shopNowPath = computed(() => {
    let customerFirstName = stringHelperToSlug(customerName).split('-')[0]

    if (customerFirstName && customerFirstName.length > 0) {
      customerFirstName = customerFirstName[0].toUpperCase() + customerFirstName.substring(1)
    }
    else {
      customerFirstName = 'Your_Name'
    }

    const slug = ((storeDetail.id === 1) ? `/custom-name/${customerFirstName}-unisex_standard_tshirt` : '/collection')
    return useLocalePath()(slug) + (storeDetail.product_review_coupon ? `?discount=${storeDetail.product_review_coupon}` : '')
  })
  const options = computed(() => {
    return JSON.parse(item.options)
  })
  const reviewFor = computed(() => {
    const hash = useRoute().hash
    return (hash === '#review') ? null : hash.replace('#review-', '')
  })
  const commentState = computed(() => {
    return form.value.comment.length > 300
  })
  const canSubmitReview = computed(() => {
    return orderStatus === 'completed'
  })
  const disableSubmit = computed(() => {
    return (commentState.value || errorMessages.value.length || isLoading.value || isFormLoading.value)
  })

  function resetForm() {
    form.value = {
      order_product_id: item.id,
      print_quality_rating: 5,
      product_quality_rating: 5,
      customer_support_rating: 5,
      comment: '',
      assets: []
    }

    errorMessages.value = []
    uploadErrorMessages.value = []
    useRouter().push({ hash: undefined })
  }
  function getVariant(item2get: OrderProduct) {
    const parsedItemOptions = JSON.parse(item2get.options)
    if (item2get.options && Object.keys(parsedItemOptions).length > 0) {
      return `${Object.values(parsedItemOptions).toString().replace(/,/g, ' / ').toUpperCase()}`
    }
    else {
      return item2get.product_name
    }
  }
  function removeUploadedItem(index: number) {
    form.value.assets.splice(index, 1)
  }
  async function submit() {
    loading(true)
    errorMessages.value = []
    uploadErrorMessages.value = []

    await $fetchWrite<ProductReviewResponse>($api.API_CREATE_PRODUCT_REVIEW, {
      method: $method.post,
      body: form.value
    })
      .then(({ data, success, message }) => {
        if (success) {
          averageRating.value = data.average_rating

          isShowModalReviewProduct.value = false
          uiManager().createPopup($i18n.t('Thanks for your review!'), 'success')
          useRouter().push({ hash: undefined })

          if (storeDetail.product_review_coupon && form.value.assets.length) {
            isShowModalReviewCoupon.value = true
          }
        }
        else {
          Object.values(message).forEach((k) => {
            Object.values(k).forEach((m) => {
              errorMessages.value.push(m)
            })
          })
        }
      })
      .catch((error) => {
        errorMessages.value.push(error)
      })

    loading(false)
  }

  function fileInputOnChangeState(state: { isUploading: boolean }) {
    isFormLoading.value = state.isUploading
  }

  const msgTable: { [key: string]: string } = {
    exceed_limit: $i18n.t('Upload limit is 5 files.'),
    invalid_extension: $i18n.t('Supported file formats') + allowedExtensions.join(', '),
    exceed_file_size_limit: $i18n.t('The upload file may not be greater than MB', { size: uploadMaxSize })
  }
  function fileInputOnValidatorError(error: string) {
    uploadErrorMessages.value.push(msgTable[error] || $i18n.t('Please try again later'))
  }

  return {
    errorMessages,
    uploadErrorMessages,

    allowedExtensions,
    allowedTypes,
    uploadMaxSize,

    averageRating,
    form,
    isShowModalReviewProduct,
    isShowModalReviewCoupon,
    isFormLoading,

    storeDetail,
    shopNowPath,
    options,
    reviewFor,
    commentState,
    disableSubmit,
    canSubmitReview,

    resetForm,
    getVariant,
    removeUploadedItem,
    submit,

    fileInputOnChangeState,
    fileInputOnValidatorError
  }
}

export function useModalCreateOrderWithRecaptcha() {
  const isShowModal = computed(() => {
    return uiManager().createOrderNeedRecaptcha
  })
  const isVerified = ref(false)

  function onReCaptchaConfirm(token: string) {
    isVerified.value = true

    setTimeout(() => {
      createOrder({
        recaptcha_token: token
      }).then(() => {
        uiManager().viewModalCreateOrderRecaptcha(false)
      })
    }, 1000)
  }

  return {
    isShowModal,
    isVerified,
    onReCaptchaConfirm
  }
}

export function useModalCheckReviewOrder() {
  const errorMessages = ref<any>([])
  const errorSubmitted = ref(false)
  const localePath = useLocalePath()
  const form: Ref<{
    email: string
    order_number: string
  }> = ref({
    email: '',
    order_number: ''
  })
  const isShowModalCheckReviewOrder = ref(false)
  const disableSubmit = computed(() => {
    return !!(!form.value.email.length || !form.value.order_number.length || isLoading.value)
  })
  const { $fetchWrite } = useNuxtApp()

  function resetForm() {
    form.value = {
      email: '',
      order_number: ''
    }
    errorMessages.value = []
  }
  async function submit() {
    loading(true)
    errorMessages.value = []
    errorSubmitted.value = false
    await $fetchWrite<CheckReviewOrderResponse>($api.API_PRE_CHECK_REVIEW_ORDER, {
      method: $method.post,
      body: form.value
    })
      .then(({ data, success, message }) => {
        // @ts-ignore
        if (success && data?.products?.length) {
          isShowModalCheckReviewOrder.value = false
          // @ts-ignore
          useRouter().push(localePath(`/order/status/${data?.access_token}#review-${data.products[0].id}`))
        }
        else if (message) {
          Object.values(message).forEach((k) => {
            Object.values(k).forEach((m) => {
              errorMessages.value.push(m)
            })
          })
        }
        else {
          errorSubmitted.value = true
        }
      })
      .catch((error) => {
        errorMessages.value.push(error)
      })
    loading(false)
  }

  return {
    errorMessages,
    form,
    isShowModalCheckReviewOrder,
    disableSubmit,
    errorSubmitted,
    resetForm,
    submit
  }
}
