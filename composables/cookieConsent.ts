import { useUserSession } from '~/store/userSession'

const EU_COUNTRIES = [
  'AT', // Austria
  'BE', // Belgium
  'BG', // Bulgaria
  'HR', // Croatia
  'CY', // Republic of Cyprus
  'CZ', // Czech Republic
  'DK', // Denmark
  'EE', // Estonia
  'FI', // Finland
  'FR', // France
  'DE', // Germany
  'GR', // Greece
  'HU', // Hungary
  'IE', // Ireland
  'IT', // Italy
  'LV', // Latvia
  'LT', // Lithuania
  'LU', // Luxembourg
  'MT', // Malta
  'NL', // Netherlands
  'PL', // Poland
  'PT', // Portugal
  'RO', // Romania
  'SK', // Slovakia
  'SI', // Slovenia
  'ES', // Spain
  'SE', // Sweden
  'UK', // United Kingdom,
  'GB', // United Kingdom,
]
export function useCookieConsent () {
  const { userBehavior } = useUserSession()
  const userCountryCode = computed(() => useUserSession().visitInfo.country)
  const isShowCookieConsent = ref(false)

  function confirmCookieConsent () {
    isShowCookieConsent.value = false
    userBehavior.cookieConsentConfirmed = true
  }
  function showCookieConsent (retries = 0) {
    if (!userCountryCode.value) {
      setTimeout(() => {
        if (retries < 11) {
          showCookieConsent(retries + 1)
        }
      }, 1000)
      return
    }

    const isEu = EU_COUNTRIES.includes(userCountryCode.value)
    isShowCookieConsent.value = isEu
  }

  onMounted(() => {
    if (!userBehavior.cookieConsentConfirmed) {
      showCookieConsent()
    }
  })

  return {
    isShowCookieConsent,
    confirmCookieConsent
  }
}
