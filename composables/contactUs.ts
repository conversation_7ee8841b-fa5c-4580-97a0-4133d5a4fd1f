import type { Ref } from 'nuxt/dist/app/compat/capi'
import { useUserSession } from '~~/store/userSession'

interface FormInvalid {
  email_required?: boolean
  email_invalid?: boolean
  message_required?: boolean
  message_length?: boolean
}

export function useContactUsForm(fileSelector: Ref) {
  const { $fetchWrite } = useNuxtApp()

  const { $i18n } = useNuxtApp()
  const userSession = useUserSession()
  const userInfo = computed(() => userSession.userInfo)

  const optionsSelect: Ref<string[]> = ref([
    'Where is my order?',
    'Change my order',
    'Return my order',
    'Payment/Discount issue',
    'Other questions',
    'Item not as describe',
    'Item not received'
  ])
  const contactForm = reactive({
    type: '',
    fullName: '',
    email: '',
    order: '',
    message: '',
    attachFile: [] as string[]
  })
  const submitted = ref(false)
  const isUploadingFiles = ref(false)
  const recaptcha = ref()

  const successForm = ref(false)
  const warningForm = ref(false)
  const errorMsg = ref('')

  const invalids = computed(() => {
    const list: FormInvalid = {}

    const msgLen = contactForm.message.trim().length

    list.email_required = (contactForm.email.length === 0)
    list.email_invalid = !isValidEmail(contactForm.email)
    list.message_required = (msgLen === 0)
    list.message_length = (msgLen > 0 && msgLen < 10)

    return list
  })

  function showError(msg: string) {
    warningForm.value = true
    errorMsg.value = $i18n.t(msg)
    window.scrollTo(0, 0)
  }

  async function handleSubmit() {
    submitted.value = true
    const ivl = invalids.value
    if (ivl.email_invalid || ivl.email_required || ivl.message_required || ivl.message_length) {
      return
    }

    let token = null
    try {
      token = await recaptcha.value.getResponse()
    }
    catch (e) {
      showError('Please check the reCAPTCHA box')
      return
    }

    try {
      isUploadingFiles.value = true
      await $fetchWrite<ResponseData<{}>>($api.API_CONTACT_FORM, {
        method: $method.post,
        body: {
          name: contactForm.fullName,
          subject: contactForm.type,
          email: contactForm.email,
          order_number: contactForm.order,
          message: contactForm.message,
          attached_files: contactForm.attachFile,
          token
        }
      }).then((response) => {
        isUploadingFiles.value = false
        if (!response.success) {
          throw response
        }

        submitted.value = false
        warningForm.value = false
        successForm.value = true

        contactForm.message = ''
        contactForm.attachFile = []
        fileSelector.value.resetFilesInput()

        recaptcha.value.reset()
      }).catch((err) => {
        isUploadingFiles.value = false
        throw err
      })

      useTracking().trackEvent({
        event: 'contact',
        data: {
          name: contactForm.fullName,
          email: contactForm.email,
          subject: contactForm.type,
          order_number: contactForm.order,
          message: contactForm.message
        }
      })
    }
    catch (error) {
      showError('Please try again later')
    }
  }

  function handleStateChange(state: object) {
    if ('isUploading' in state) {
      isUploadingFiles.value = state.isUploading as boolean
    }
  }

  onMounted(() => {
    const val = userInfo.value

    contactForm.fullName = val!.name
    contactForm.email = val!.email
  })

  return {
    optionsSelect,
    contactForm,
    submitted,
    invalids,
    isUploadingFiles,
    recaptcha,

    successForm,
    warningForm,
    errorMsg,

    handleSubmit,
    handleStateChange
  }
}
