import { useStoreInfo } from '~~/store/storeInfo'

export async function useSetup () {
  const { initTracking } = useTracking()
  const { $imgUrl, $getMobileOperatingSystem } = useNuxtApp()
  const { banners, name, favicon, default_color: defaultColor, style, theme } = useStoreInfo()

  const title = name || ''
  const image = $imgUrl({
    path: (banners[0] && banners[0].banner_url) || '',
    type: 'share'
  })

  createSEOMeta({
    title,
    image
  })

  const link = [{
    hid: 'icon',
    rel: 'icon',
    type: 'image/x-icon',
    href: $imgUrl({ path: favicon, type: 'icon' }) || ''
  },
  {
    hid: 'apple-touch-icon',
    rel: 'apple-touch-icon',
    href: $imgUrl({ path: favicon, type: 'icon-xl' }) || ''
  },
  {
    hid: 'shortcut-icon',
    rel: 'shortcut icon',
    href: $imgUrl({ path: favicon, type: 'icon-lg' }) || ''
  }]

  const headerStyle = []
  const isPrimaryLightColor = isLightColor(defaultColor)

  if (defaultColor !== '#ffffff') {
    headerStyle.push({
      children: `:root {          
        --color-primary: ${defaultColor || '#333333'};
        --color-primary-lighter: ${isPrimaryLightColor ? adjustHexColor(defaultColor, 55) : adjustHexColor(defaultColor, 70)};
        --color-primary-light: ${isPrimaryLightColor ? adjustHexColor(defaultColor, 30) : adjustHexColor(defaultColor, 45)};
        --color-primary-hover: ${isPrimaryLightColor ? adjustHexColor(defaultColor, -10) : adjustHexColor(defaultColor, 20)};
        --color-primary-focus: ${isPrimaryLightColor ? adjustHexColor(defaultColor, -15) : adjustHexColor(defaultColor, 10)};
        --color-primary-active: ${isPrimaryLightColor ? adjustHexColor(defaultColor, -20) : adjustHexColor(defaultColor, -5)};
        --color-contrast: ${isPrimaryLightColor ? '#333333' : '#ffffff'};
      }`
    })
  }

  if (isPrimaryLightColor) {
    headerStyle.push({
      children: `
        .btn-outline {
          --color-primary: #333333;
        }
      `
    })
  }

  onMounted(() => {
    console.log(useRuntimeConfig().public.version)
    useHead({
      link: [
        {
          rel: 'stylesheet',
          href: `${cdnURL.value}css/vuetelinput.css`,
        },
        (
          (window.matchMedia(`only screen and (-webkit-min-device-pixel-ratio: 2),
          only screen and (min--moz-device-pixel-ratio: 2),
          only screen and (-o-min-device-pixel-ratio: 2),
          only screen and (min-device-pixel-ratio: 2),
          only screen and (min-resolution: 192dpi),
          only screen and (min-resolution: 2dppx)`).matches)
            ? ({ rel: 'stylesheet', href: `${cdnURL.value}css/vuetelinput_extended.css` })
            : ({})
        )
      ]
    })

    const body = document.body
    const _html = document.documentElement
    const scrollUp = 'scroll-up'
    const scrollDown = 'scroll-down'
    const pseudoScroll = 'pseudo-scroll'
    const bodyFixFixed = 'bodyFixFixed'
    let lastScroll = 0
    let tracker = false
    const _pet = window.innerHeight
    const hWindow = Math.max(body.scrollHeight, body.offsetHeight, _html.clientHeight, _html.scrollHeight, _html.offsetHeight)
    const _dri = $getMobileOperatingSystem()
    body.classList.add(scrollUp, pseudoScroll)
    if (style) {
      body.classList.add(style)
    }
    if (theme && theme !== 'default') {
      body.classList.remove('default')
      body.classList.add(theme)
    }

    window.addEventListener('scroll', () => {
      const currentScroll = window.pageYOffset
      if (!tracker) {
        tracker = true
        setTimeout(() => {
          tracker = false
        }, 100)
        body.classList.remove(pseudoScroll)
        if (currentScroll <= 0 || (currentScroll < lastScroll && body.classList.contains(scrollDown))) {
          body.classList.remove(scrollDown)
          body.classList.add(scrollUp)
        } else if (currentScroll > lastScroll && !body.classList.contains(scrollDown)) {
          body.classList.remove(scrollUp)
          body.classList.add(scrollDown)
        }

        if (_dri === 'iOS') {
          if (((currentScroll + _pet) >= hWindow)) {
            body.classList.remove(bodyFixFixed)
          } else if (currentScroll <= 0) {
            body.classList.add(bodyFixFixed)
          } else if (currentScroll > lastScroll) {
            body.classList.remove(bodyFixFixed)
          } else {
            body.classList.add(bodyFixFixed)
          }
        }
      }
      lastScroll = currentScroll
    })
  })

  await initTracking()

  useHead({
    link,
    style: headerStyle
  })
}
