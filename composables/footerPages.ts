export async function useCommonFooterPage(slug: string) {
  const pageData = useState<PageData>(`pageData-${slug}`)
  const { $fetchDefault } = useNuxtApp()

  if (!pageData.value) {
    const { data, success } = await $fetchDefault<ResponseData<PageData>>(`${$api.API_GET_PAGE}/${slug}`)
    if (success && data) {
      pageData.value = data
    }
  }

  if (!pageData.value) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }

  const pageTitle = `${pageData.value.title} | ${storeInfo().name || ''}`
  createSEOMeta({
    title: pageTitle
  })

  return {
    pageData
  }
}

export async function useFAQPage() {
  const faqData = useState<ProcessedFAQResponse[]>('faq')
  const { $fetchDefault } = useNuxtApp()

  let usefulLinkList = [
    { title: 'Track Order', url: '/order/track' },
    { title: 'Shipping Policy', url: '/page/shipping-policy' },
    { title: 'Return/Refund Policy', url: '/page/return-policy' },
    { title: 'Contact support', url: '/page/contact-us' }
  ]

  if (storeInfo().theme === 'givehug') {
    usefulLinkList = [
      { title: 'Track Order', url: '/order/track' },
      { title: 'Shipping Policy', url: '/page/shipping-policy' },
      { title: 'Return & Refund Policy', url: '/page/return-refund-policy' },
      { title: 'Contact support', url: '/page/contact-us' }
    ]
  }

  const { $i18n } = useNuxtApp()
  let locale = ''
  if ($i18n.localeProperties.value.code) {
    locale = $i18n.localeProperties.value.code
  }

  if (!faqData.value) {
    const { data } = await $fetchDefault<ResponseData<FAQResponse>>(`${$api.API_FAQ}?locale=${locale}`)
    const faqCategory = (data.faq_category as unknown as ProcessedFAQResponse[]).map((cat: ProcessedFAQResponse) => {
      cat.faqList = data.faq.filter(fq => fq.category_id === cat.id)
      return cat
    })

    faqData.value = faqCategory
  }

  return {
    usefulLinkList,
    faqData
  }
}
