export default function useCampaignComboUI(products: Ref<OrderProduct[]>, order: Ref<Order>) {
  const { $formatPriceByCurrency } = useNuxtApp()

  const comboProducts = computed(() => products.value.filter(p => p.combo_id))

  const combos = computed(() => {
    const comboMap = new Map<string, any>()

    comboProducts.value.filter(p => p.combo_id).forEach((item) => {
      if (!comboMap.has(item.combo_id as string)) {
        comboMap.set(item.combo_id as string, {
          id: item.combo_id,
          campaign_id: item.campaign_id,
          quantity: item.quantity,
          thumb_url: item.campaign?.thumb_url,
          title: item.campaign?.name,
          items: [] as OrderProduct[],
        })
      }
      const comboItem = comboMap.get(item.combo_id as string)!
      comboItem.items.push({
        ...item,
        options: typeof item.options === 'string' ? JSON.parse(item.options) : item.options,
      });
    })

    const result = Array.from(comboMap.values())
    return result.map(combo => {
      return {
        ...combo,
        combo_price: $formatPriceByCurrency(combo.items.reduce((acc: number, item: OrderProduct) => {
          return acc + item.total_amount
        }, 0), order.value.currency_rate, order.value.currency_code),
      }
    })
  })

  const productIdsInCombo = computed(() => {
    return comboProducts.value.reduce((acc: number[], item: OrderProduct) => {
      acc.push(item.id)
      return acc
    }
    , [])
  })

  return {
    combos,
    productIdsInCombo,
  }
}
