
const discountErrorCode = ref<number|undefined>()
export function useDiscount () {
  const updateDiscountError = (value: number|undefined) => {
    discountErrorCode.value = value
  }
  return {
    discountErrorCode,
    updateDiscountError
  }
}

export function useRouteDiscountCode() {
  if (import.meta.server) { return }

  /**
   * Initially for campaigns route only
   * might add it to other route in the future
   */
  const route = useRoute()
  const discountCode = (route.query.discount_code || route.query.discount) as string | undefined

  if (discountCode) {
    const cookie = useCookie('discount_code')
    cookie.value = discountCode
  }
}
