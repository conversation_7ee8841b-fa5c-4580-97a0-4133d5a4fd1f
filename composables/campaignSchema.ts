export function useCampaignSchema (campaignData: Campaign, userCampaignOption: UserCampaignOption) {
  const { $imgUrl } = useNuxtApp()
  const campaignDescription = computed(() => {
    return campaignData?.description?.replace(/<[^>]+>/g, '').replace(/\s\s+/g, ' ') || campaignData.name || ''
  })
  const color = userCampaignOption.currentOptions.color || ''
  const currentProduct = userCampaignOption.currentProduct
  const thumbnail = computed(() => {
    return $imgUrl({
      path: currentProduct?.thumb_url || currentProduct?.full_path,
      type: 'share',
      color
    }) || ''
  })

  const reviews = campaignData.reviews
  const averageRating = reviews && reviews.average_rating ? reviews.average_rating === 0 ? 4.5 : reviews.average_rating : 4.5
  const bestRating = reviews && reviews.best_rating ? reviews.best_rating : ''
  const worstRating = reviews && reviews.worst_rating ? reviews.worst_rating : ''
  const ratingCount = reviews && reviews.review_count ? reviews.review_count === 0 ? 5 : reviews.review_count : 5
  const storeName = campaignData?.store_name ?? ''
  useSchemaOrg([
    defineProduct({
      name: campaignData?.name ?? '',
      url: `https://${storeInfo().domain}/${campaignData.slug}`,
      productID: campaignData.id,
      id: campaignData.id,
      image: thumbnail,
      description: campaignDescription,
      brand: storeName,
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: averageRating,
        bestRating,
        worstRating,
        ratingCount
      },
      offers: [{
        price: currentProduct?.price,
        priceCurrency: currentProduct?.currency_code,
        itemCondition: 'http://schema.org/NewCondition',
        availability: 'http://schema.org/InStock',
      }]
    })
  ])

  return {
    campaignDescription,
    thumbnail,
    reviewSummary: reviews,
  }
}
