export function headTagFilter(objects: HeadTag[]): HeadTag[] {
  if (!objects || objects.length === 0) {
    return []
  }

  const route = useRoute()
  let basePath = route.path
  const locale = basePath.split('/')[1]
  if (locale.length === 2) {
    basePath = basePath.replace(`/${locale}`, '')
  }

  return objects.filter(obj => {
    if (obj.path === null) {
      return true;
    } else {
      const paths = obj.path.split(',').map(p => p.trim());
      return paths.some(path => path === basePath)
    }
  })
}
