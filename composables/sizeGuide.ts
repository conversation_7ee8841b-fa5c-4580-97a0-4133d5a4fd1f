import { useUserSession } from '~~/store/userSession'

export function useSizeGuide () {
  const cmCountryList = ['BE', 'BG', 'CZ', 'DK', 'DE', 'EE', 'IE', 'EL', 'ES', 'FR', 'HR', 'IT', 'CY', 'LV', 'LT', 'LU', 'HU', 'MT', 'NL', 'AT', 'PL', 'PT', 'RO', 'SI', 'SK', 'FI', 'SE']

  const currentSizeGuideUnit = ref<'in' | 'cm'>(cmCountryList.includes(useUserSession().visitInfo?.country as string) ? 'cm' : 'in')

  const sizeGuideProperties:Array<{
    key: keyof SizeGuide
    title: string
  }> = [{
    key: 'width',
    title: 'Chest Width'
  }, {
    key: 'length',
    title: 'Shirt Length'
  }, {
    key: 'sleeve',
    title: 'Sleeve Length'
  }, {
    key: 'height',
    title: 'Height'
  }, {
    key: 'weight',
    title: 'Weight'
  }, {
    key: 'waist',
    title: 'Waist'
  }, {
    key: 'hip',
    title: 'Hip'
  }, {
    key: 'note',
    title: 'Note'
  }]

  const currentTemplate = computed(() => {
    return uiManager().sizeGuide
  })

  const converseData = computed(() => {
    return (key?:string, value?:string | number | null, unit?:string) => {
      if (!key || !value) {
        return ''
      }
      let converseSizeNumber = 1
      let converseWeightNumber = 1
      if (unit === 'cm' && currentSizeGuideUnit.value === 'in') {
        converseSizeNumber = 0.3937
        converseWeightNumber = 2.2046
      }
      if (unit === 'in' && currentSizeGuideUnit.value === 'cm') {
        converseSizeNumber = 2.54
        converseWeightNumber = 0.45359237
      }
      switch (key) {
        case 'width':
        case 'length':
        case 'sleeve':
        case 'waist':
        case 'hip':
          return `${currentSizeGuideUnit.value === unit ? value : Math.round(Number(value) * converseSizeNumber)} ${currentSizeGuideUnit.value}`
        case 'height':
          return `${currentSizeGuideUnit.value === unit ? (value as string)?.replace(',', '-') : (value as string)?.split(',').map(height => Math.round(Number(height) * converseSizeNumber)).join('-')} ${currentSizeGuideUnit.value}`
        case 'weight':
          return currentSizeGuideUnit.value === unit ? (value as string)?.replace(',', '-') : `${(value as string)?.split(',').map(weight => Math.round(Number(weight) * converseWeightNumber)).join('-')} kg`
        case 'note':
          return value || ''
      }
    }
  })

  const sizeGuideList = computed(() => {
    const newSizeGuildList = currentTemplate.value?.sizeGuideList?.filter(item => item.unit === currentSizeGuideUnit.value)
    if (newSizeGuildList && newSizeGuildList.length) {
      return newSizeGuildList
    } else {
      return currentTemplate.value?.sizeGuideList
    }
  })

  return {
    currentTemplate,
    sizeGuideProperties,
    currentSizeGuideUnit,
    sizeGuideList,
    converseData
  }
}
