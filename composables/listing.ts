import type { RouteParams } from '@intlify/vue-router-bridge'
import { v4 as uuidV4 } from 'uuid'
import { useGeneralSettings } from '~/store/generalSettings'
import { useListingStore } from '~/store/listing'

type ListingPageType = 'collection' | 'category' | 'collectionSlug' | 'search' | 'tag' | 'artist' | undefined
type SortType = 'featured' | 'relevant' | 'popular' | 'newest' | 'highest_price' | 'lowest_price' | 'a_z' | 'z_a'

export function useListing() {
  const { categories, colors } = useGeneralSettings()
  const { $i18n: { t: $t } } = useNuxtApp()
  watch(() => useRoute().query, resetData, { deep: true })

  const filterValue = reactive({
    title: '',
    pageType: '' as ListingPageType,
    category: '',
    template: '',
    color: null as Color | null,
    priceFilter: [0, 0] as [number, number],
    sort: 'popular' as SortType,
    sortTypeList: [] as Array<{ text: string, value: string }>,
    filterCategories: [] as Array<Category>,
    filterTemplates: [] as Array<Template>
  })

  const listingProduct: Omit<ListingProduct, 'bannerUrl'> & { bannerUrl: string | null } = reactive({
    products: [],
    links: [],
    currentPage: 1,
    perPage: 0,
    lastPage: 0,
    total: 0,
    from: 0,
    to: 0,
    bannerUrl: null
  })

  const filterProduct: FilterProduct = reactive({
    category_id_group: [],
    template_id_group: [],
    collection_group: [],
    color_group: [],
    min_price: 0,
    max_price: 0
  })

  async function resetData() {
    const { $formatPriceNoUnit } = useNuxtApp()
    const { query, params } = useRoute()
    const pageType = getListingPageType()
    const data = await getListingData(params, query, pageType)

    uiManager().isSearching = false

    Object.assign(listingProduct, data.listingProduct)
    Object.assign(filterProduct, data.filterProduct)
    filterProduct.min_price = filterProduct.min_price ? (Math.ceil(filterProduct.min_price / 5) - 1) * 5 : 0
    filterProduct.max_price = filterProduct.max_price ? (Math.ceil(filterProduct.max_price / 5) + 1) * 5 : 50
    Object.assign(filterValue, {
      title: title(params, query, pageType),
      pageType,
      color: colors.find(item => (query.color as string)?.replace(/-/g, ' ') === item.name),
      category: query.sub_cat || query.category,
      sort: getCurrentSortType(query, pageType),
      sortTypeList: getSortTypeList(pageType),
      template: getTemplate(params, query, pageType),
      priceFilter: [(query.price && (query.price as string).split('-')[0]) || 0, (query.price && (query.price as string).split('-')[1]) || filterProduct.max_price],
      filterCategories: getFilterCategories(params, categories, pageType, filterProduct),
      filterTemplates: getFilterTemplates(filterProduct?.template_id_group)
    })
    if (import.meta.client) {
      let event = 'view_item_list'
      let klaviyoData = {}
      if (pageType === 'collectionSlug' || pageType === 'collection' || pageType === 'category') {
        klaviyoData = {
          'URL': window.location.href,
          'Activity ID': uuidV4(),
          'CollectionName': filterValue ? filterValue.title : '',
          'CollectionID': params.collection || ''
        }
      }
      if (pageType === 'search') {
        event = 'search'
        klaviyoData = {
          'URL': window.location.href,
          'Activity ID': uuidV4(),
          'SearchQuery': query.s ? query.s.toString() : '',
          'SearchQueryParts': query.s ? query.s.toString().split(' ') : []
        }
      }
      let data: any = {
        items: listingProduct.products.map((item) => {
          return {
            item_id: item.id || item.campaign_id,
            item_name: item.campaign_name,
            item_category: item.name,
            price: Number($formatPriceNoUnit(item.price || 0, item.currency_code, 'USD'))
          }
        })
      }
      if (klaviyoData) {
        data = { ...data, klaviyoData }
      }

      useTracking().trackEvent({
        event,
        data
      })
    }

    const pageTitle = `${filterValue.title} | ${storeInfo().name}`

    createSEOMeta({
      title: pageTitle
    })
  }

  const currentSortType = computed(() => {
    return filterValue.sortTypeList.find(item => item.value === filterValue.sort) || filterValue.sortTypeList[0]
  })

  const filtersCount = computed(() => {
    let count = 0

    if (filterValue?.product) {
      count += 1
    }
    if (filterValue?.color) {
      count += 1
    }
    if (filterValue?.category) {
      count += 1
    }
    if (filterValue.sort !== 'popular') {
      count += 1
    }
    if (Number.parseFloat(filterValue.priceFilter[0]) > 0 || Number.parseFloat(filterValue.priceFilter[1]) !== filterProduct.max_price) {
      count += 1
    }

    return count
  })

  const currentProductCategory = computed(() => {
    if (!filterValue.template) { return '' }

    return filterValue.filterTemplates.find(template => template.id === Number.parseInt(filterValue.template))?.name
  })

  const currentPriceRange = computed(() => {
    const val = {
      start: Number.parseFloat(filterValue.priceFilter[0]),
      end: Number.parseFloat(filterValue.priceFilter[1]),
      show: false
    }
    val.show = (val.start) || val.end !== filterProduct.max_price

    return val
  })

  function updatePrice() {
    useRouter().push(getFilterUrl.value('price', filterValue.priceFilter.join('-')))
  }

  function title(params: RouteParams, query: any, pageType: ListingPageType) {
    if (pageType === 'category') {
      return $t(getTitle(categories, (params.category as string)))
    }
    else if (pageType === 'collection') {
      return $t('All products')
    }
    else if (pageType === 'collectionSlug') {
      const collectionName = $t((params.collection as string).replace(/-/g, ' '))
      return collectionName.charAt(0).toUpperCase() + collectionName.slice(1)
    }
    else if (pageType === 'search') {
      return `${$t('Search result for')}: ${query.s}`
    }
    else if (pageType === 'tag') {
      return `${$t('Tag')}: ${$t((params.tag as string).replace(/[_-]/g, ' '))}`
    }
    else if (pageType === 'artist') {
      const artistName = params.artist as string
      const parts = artistName.split('-')
      parts.pop()
      const capitalizedParts = parts.map(part =>
        part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
      )
      return `${$t('Designed by')} ${capitalizedParts.join(' ')}`
    }
  }
  return {
    filterValue,
    listingProduct,
    filterProduct,
    currentSortType,
    resetData,
    updatePrice,
    filtersCount,
    currentProductCategory,
    currentPriceRange
  }
}

export function getListingPageType() {
  const { name, params } = useRoute()
  const routeName = name as string
  if (routeName.startsWith('category-category___')) {
    return 'category'
  }
  else if (routeName.startsWith('collection-collection___')) {
    if (params.collection) {
      return 'collectionSlug'
    }
    return 'collection'
  }
  else if (routeName.startsWith('search___')) {
    return 'search'
  }
  else if (routeName.startsWith('tag-tag___')) {
    return 'tag'
  }
  else if (routeName.startsWith('artist-artist___')) {
    return 'artist'
  }
}

export const getFilterUrl = computed(() => {
  return function (key?: string, value?: string | number) {
    const { query, path } = useRoute()
    const localePath = useLocalePath()
    const pageType: ListingPageType = getListingPageType()
    const { templates } = useGeneralSettings()
    let newPath = path
    const newQuery = { ...query }
    if (key === 'product' && pageType === 'tag' && value) {
      const selectTemplate = templates.find(item => item.id === Number(value))
      const keyWord = path.split('-')
      newPath = [keyWord[0], selectTemplate?.name.replace(/\s/g, '_')].join('-')
      delete newQuery.product
    }
    else if (key && value && newQuery[key] !== value) {
      newQuery[key] = value as string
    }
    else if (key) {
      delete newQuery[key]
    }

    if (key !== 'page' && newQuery.page) {
      // im assuming filter change should reset page to 1
      // but if we doing something else then this should be change
      delete newQuery.page
    }

    return localePath({ path: newPath, query: newQuery })
  }
})

export const getCategoryFilterUrl = computed(() => {
  return (categorySlug?: string) => {
    const localePath = useLocalePath()
    const { query, path } = useRoute()
    const newQuery = { ...query }
    const pageType = getListingPageType()
    if (categorySlug) {
      newQuery.page = '1'
      if (pageType === 'category') {
        if (!categorySlug) {
          delete newQuery.sub_cat
        }
        else {
          newQuery.sub_cat = categorySlug
        }
      }
      else if (!categorySlug) {
        delete newQuery.category
      }
      else {
        newQuery.category = categorySlug
      }
      return localePath({ path, query: newQuery })
    }
    else {
      return path
    }
  }
})
// helpers

function getTitle(categories: Array<Category>, categorySlug: string) {
  let categoryName = ''
  categories.some((category) => {
    if (category.slug === categorySlug) {
      categoryName = category.name
      return true
    }
    else if (category.child_menu && category.child_menu.length) {
      categoryName = getTitle(category.child_menu, categorySlug)
      if (categoryName) {
        return true
      }
    }
    return false
  })
  return categoryName
}

function getListingData(params: RouteParams, query: any, pageType: ListingPageType): Promise<{ listingProduct: ListingProduct, filterProduct: FilterProduct }> {
  const listingStore = useListingStore()
  const apiQuery = getApiQuery(params, query, pageType)
  const apiQueryFilter = getApiQueryFilter(params, query, pageType)
  return Promise.all([listingStore.getListProductByUrl(apiQuery), listingStore.getFilterProductByUrl(apiQueryFilter)]).then((result) => {
    return {
      listingProduct: result[0] || {
        products: [],
        perPage: 0,
        lastPage: 0,
        total: 0,
        from: 0,
        to: 0,
        bannerUrl: null
      },
      filterProduct: result[1] || {
        category_id_group: [],
        template_id_group: [],
        collection_group: [],
        color_group: [],
        min_price: 0,
        max_price: 0
      }
    }
  })
}

function getApiQuery(params: RouteParams, query: any, pageType: ListingPageType): string {
  const param = new URLSearchParams()

  if (query?.page) {
    param.set('page', query?.page)
  }

  if (query.s) {
    param.set('s', query.s)
  }

  if (pageType === 'collectionSlug' && params.collection) {
    param.set('collection_slug', params.collection as string)
  }

  if (pageType === 'tag' && typeof params.tag === 'string') {
    const keyWord = params.tag.split('-')
    if (keyWord[1]) {
      const productTemPlate = generalSettings().templates.find((item) => {
        return flatWord(item.name).includes(flatWord(keyWord[1]))
      })
      if (productTemPlate) {
        param.set('product', productTemPlate.id.toString())
      }
    }
    param.set('s', keyWord[0].replace(/_/g, ' ') || params.tag)
  }

  if (pageType === 'artist' && params.artist) {
    param.set('nickname', params.artist as string)
  }

  if (pageType === 'category') {
    if (query.sub_cat) {
      param.set('category_slug', query.sub_cat)
    }
    else if (pageType === 'category') {
      param.set('category_slug', params.category as string)
    }
  }

  if (query.category) {
    param.set('category_slug', query.category)
  }

  if (query.collection) {
    param.set('collection_slug', query.collection)
  }

  if (query.color) {
    param.set('color', query.color.replace(/-/g, ' '))
  }

  if (query.price) {
    param.set('filter_price', query.price)
  }

  if (query.product) {
    param.set('product', query.product)
  }

  if (!query.sort) {
    if (pageType === 'collectionSlug') {
      param.set('sort', 'featured')
    }
    else if (pageType === 'search') {
      param.set('sort', 'relevant')
    }
    else {
      param.set('sort', 'popular')
    }
  }
  else {
    param.set('sort', query.sort)
  }

  return param.toString()
}

function getApiQueryFilter(params: RouteParams, query: any, pageType: ListingPageType): string {
  const param = new URLSearchParams()

  if (query.s) {
    param.set('s', query.s)
  }

  if (pageType === 'collectionSlug' && params.collection) {
    param.set('collection_slug', params.collection as string)
  }

  if (pageType === 'tag' && typeof params.tag === 'string') {
    const keyWord = params.tag.split('-')
    param.set('s', keyWord[0].replace(/_/g, ' ') || params.tag)
  }

  if (pageType === 'category') {
    param.set('category_slug', params.category as string)
  }

  if (query.collection) {
    param.set('collection_slug', query.collection)
  }

  return param.toString()
}

function getCurrentSortType(query: any, pageType: ListingPageType): SortType {
  if (query.sort as SortType) {
    return query.sort as SortType
  }
  else if (pageType === 'collectionSlug') {
    return 'featured'
  }
  else if (pageType === 'search') {
    return 'relevant'
  }
  else {
    return 'popular'
  }
}

function getSortTypeList(pageType: ListingPageType): Array<{ text: string, value: string }> {
  const sortTypeList: Array<{ text: string, value: SortType }> = []
  if (pageType === 'collectionSlug') {
    sortTypeList.push({
      text: 'Featured',
      value: 'featured'
    })
  }
  if (pageType === 'search' || pageType === 'tag') {
    sortTypeList.push({
      text: 'Relevant',
      value: 'relevant'
    })
  }

  return sortTypeList.concat([{
    text: 'Popular',
    value: 'popular'
  }, {
    text: 'Newest',
    value: 'newest'
  }, {
    text: 'Price: High To Low',
    value: 'highest_price'
  }, {
    text: 'Price: Low To High',
    value: 'lowest_price'
  }, {
    text: 'Alphabet, A-Z',
    value: 'a_z'
  }, {
    text: 'Alphabet, Z-A',
    value: 'z_a'
  }])
}

function getTemplate(params: RouteParams, query: any, pageType: ListingPageType) {
  if (pageType === 'tag') {
    const keyWord = params.tag.toString().split('-')
    if (keyWord[1]) {
      const productTemPlate = generalSettings().templates.find((item) => {
        return flatWord(item.name).includes(flatWord(keyWord[1]))
      })
      if (productTemPlate) {
        return productTemPlate.id.toString()
      }
    }
  }
  return query.product || ''
}

function getFilterTemplates(templateIdsFilterGroup?: Array<number>) {
  const { templates } = useGeneralSettings()
  return templates.filter(t => templateIdsFilterGroup?.includes(t.id))
}

function getFilterCategories(params: RouteParams, categories: Category[], pageType: ListingPageType, filterProduct?: FilterProduct) {
  const categoryGroupFilter = filterProduct?.category_id_group || []
  let firstArr: Array<Category> | null | undefined = []
  if (pageType === 'category') {
    const category = params.category
    firstArr = getCategoryBySlug(categories, category)
    if (!firstArr) {
      firstArr = []
    }
  }
  else {
    firstArr = categories
  }

  if (firstArr.length > 0) {
    firstArr = firstArr.filter(item => categoryGroupFilter.includes(item.id))
  }

  const filterCategories: Array<Category> = []
  firstArr.forEach((item) => {
    const clonedItem = Object.assign({}, item)
    filterCategories.push(clonedItem)
    clonedItem.child_menu = clonedItem?.child_menu?.filter(el => categoryGroupFilter.includes(el.id))
  })
  return filterCategories
}

function getCategoryBySlug(categories?: Category[] | null, slug?: string | string[]): Category[] | null | undefined {
  const result = categories?.find(item => item.slug === slug)
  if (result) {
    return result.child_menu
  }
  else {
    let filter: any = false
    categories?.every((category) => {
      const check = getCategoryBySlug(category?.child_menu, slug)
      if (check) {
        filter = check
      }
      return check
    })
    return filter
  }
}
