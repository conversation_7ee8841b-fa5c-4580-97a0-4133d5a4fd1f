export function usePromotion(promotion: Ref<Promotion | undefined>) {
  const { $i18n, $formatPrice } = useNuxtApp()

  const isCopy = ref(false)

  const rules = computed(() => {
    if (promotion?.value?.rules) {
      return JSON.parse(promotion.value.rules)
    }
    // if (promotion?.rules) {
    //   return JSON.parse(promotion.rules)
    // }
    return {}
  })

  const type = computed(() => {
    if (rules.value.type === 'TD') {
      return $i18n.t('Tiered Discount')
    }
    if (rules.value.type === 'FS') {
      const fsPercent = Number.parseInt(rules.value?.fs_percent)
      const fsMaxAmount = Number.parseInt(rules.value?.fs_max_amount)
      let stringRule = ''
      if (fsPercent && fsPercent < 100 && fsPercent > 0) {
        stringRule = `${$i18n.t('Discount')} ${fsPercent}% ${$i18n.t('of shipping')}`
      }
      else {
        stringRule = $i18n.t('Free shipping')
      }
      return stringRule
    }
    if (rules.value.type === 'BXGY') {
      const discountString = (rules.value?.get?.discount_percentage)
        ? (rules.value.get.discount_percentage === 100)
            ? $i18n.t('free')
            : `${$i18n.t('with')} ${rules.value.get.discount_percentage}% ${$i18n.t('discount')}`
        : ''

      const bxgyMin = (rules.value?.buy?.minimum_quantity)
        ? `${rules.value.buy.minimum_quantity}`
        : (rules.value?.buy?.minimum_amount) ? `${$i18n.t('a minimum of')} ${$formatPrice(rules.value.buy.minimum_amount)}` : ''

      return `${$i18n.t('Buy')} ${bxgyMin} ${$i18n.t('get')} ${rules.value?.get?.quantity || ''} ${discountString}`
    }

    let stringRule = ''

    if (rules.value.type === 'PD' || rules.value.type === 'FS') {
      stringRule = $i18n.t('Discount')

      if (rules.value.discount_percentage) {
        stringRule += ` ${rules.value.discount_percentage}%`
      }
      if (rules.value.discount_amount) {
        stringRule += ` ${$formatPrice(rules.value.discount_amount)}`
      }
    }

    return stringRule
  })

  const condition = computed(() => {
    if (rules.value.type === 'TD') {
      return ''
    }
    if (rules.value.type === 'BXGY') {
      return ''
    }

    let stringRule = ''

    if (rules.value.minimum_amount) {
      stringRule += `${$i18n.t('For order of')} ${$formatPrice(rules.value.minimum_amount)}`
    }
    else if (rules.value.minimum_quantity) {
      stringRule += `${$i18n.t('For order of')} ${$i18n.t('item', { count: rules.value.minimum_quantity })}`
    }
    else {
      stringRule += `${$i18n.t('For any product')}`
    }

    return stringRule
  })

  const countries = computed(() => {
    if (!rules.value.countries) {
      return false
    }

    if (rules.value.countries === '*') {
      return $i18n.t('Worldwide')
    }

    const countryList = generalSettings().countries || []
    const countryCodeList = rules.value.countries.split(',') as string[]
    const countryDiscountList = countryCodeList.map((countryCode) => {
      const country = countryList.find(country => country.code === countryCode)
      return country?.name
    })
    return countryDiscountList.filter(item => item).join(', ')
  })

  const stringRule = computed(() => {
    let stringRule: string | null = null
    if (storeInfo().theme === 'givehug' && promotion.value) {
      stringRule = $getPromotionGivehugStringRule(promotion.value)
    }

    if (stringRule) {
      return stringRule
    }

    const currentType = promotion.value?.type
    if (currentType === 'TD' || currentType === 'BXGY') {
      return type.value // the computed value above
    }

    let fsMaxAmountStringRule = ''

    if (currentType === 'FS') {
      const fsPercent = Number.parseInt(rules.value?.fs_percent)
      const fsMaxAmount = Number.parseInt(rules.value?.fs_max_amount)

      if (fsPercent && fsPercent < 100 && fsPercent > 0) {
        stringRule = `${$i18n.t('Discount')} ${fsPercent}% ${$i18n.t('of shipping')}`
      }
      else {
        stringRule = $i18n.t('Free shipping')
      }

      if (fsMaxAmount && fsMaxAmount > 0) {
        fsMaxAmountStringRule = ` ${$i18n.t('with maximum')}  ${$formatPrice(fsMaxAmount)}`
      }
    }
    else if (currentType === 'PD' || currentType === 'FD') {
      stringRule = $i18n.t('Discount')

      if (rules.value.discount_percentage) {
        stringRule += ` ${rules.value.discount_percentage}%`
      }
      if (rules.value.discount_amount) {
        stringRule += ` ${$formatPrice(rules.value.discount_amount)}`
      }
    }

    if (rules.value.minimum_amount) {
      stringRule += ` ${$i18n.t('for order of')} ${$formatPrice(rules.value.minimum_amount)}`
    }
    else if (rules.value.minimum_quantity) {
      stringRule += ` ${$i18n.t('for order of')} ${$i18n.t('item', { count: rules.value.minimum_quantity })}`
    }
    else {
      stringRule += ` ${$i18n.t('for any product')}`
    }

    if (fsMaxAmountStringRule.length > 0) {
      stringRule += fsMaxAmountStringRule
    }

    return stringRule
  })

  function copyCode() {
    copyToClipBoard(promotion.value?.discount_code as string)
    isCopy.value = true
    setTimeout(() => {
      isCopy.value = false
    }, 1000)
  }

  return {
    isCopy,
    rules,
    type,
    condition,
    countries,
    stringRule,
    copyCode
  }
}
