import { useUserSession } from '~/store/userSession'

export function useCampaignReportPage(emailConfirm: Ref, countryDropdown: Ref) {
  const { $i18n, $fetchDefault } = useNuxtApp()
  const { query } = useRoute()

  const tabsList = [
    { name: 'Intellectual Property Claim', value: 'intellectual' },
    { name: 'Policy Violation', value: 'policy' }
  ]
  const selectedTab = ref('intellectual')

  // Form
  const campaignSlug = ref(query.campaign || '')
  const isFileUploading = ref(false)

  const userCountry = ref(useUserSession().userInfo.country)
  const currentCountry = computed(() => {
    return generalSettings().getCountryByCode(userCountry.value)
  })
  const filterCountryText = ref('')
  const filterCountryArray = computed(() => {
    return generalSettings().countries.filter(item => item.name.toLowerCase().trim().includes(filterCountryText.value.toLowerCase().trim()))
  })
  const formIntellectual = ref({
    userType: '1',
    firstName: '',
    lastName: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    zipcode: '',
    country: '',
    email: '',
    emailConfirm: '',
    phone: '',
    legalName: '',
    additionalInfo: {
      specificConcern: '',
      originalWork: '',
      otherInfo: ''
    },
    acceptStatement: false,
    attachFile: [] as string[]
  })
  const errors = ref({
    unmatchedEmail: false,
    noCountry: false
  })
  const formPolicy = ref({
    hate: false,
    sexual: false,
    violence: false,
    misleading: false,
    illegal: false
  })

  watch(currentCountry, (newVal) => {
    formIntellectual.value.country = newVal?.name || ''
  })

  function handleStateChange(state: object) {
    if ('isUploading' in state) {
      isFileUploading.value = state.isUploading as boolean
    }
  }
  function resetForm() {
    campaignSlug.value = ''
    userCountry.value = useUserSession().userInfo.country
    formIntellectual.value = {
      userType: '1',
      firstName: '',
      lastName: '',
      address1: '',
      address2: '',
      state: '',
      city: '',
      zipcode: '',
      country: '',
      email: '',
      emailConfirm: '',
      phone: '',
      legalName: '',
      additionalInfo: {
        specificConcern: '',
        originalWork: '',
        otherInfo: ''
      },
      acceptStatement: false,
      attachFile: []
    }
    formPolicy.value = {
      hate: false,
      sexual: false,
      violence: false,
      misleading: false,
      illegal: false
    }
  }
  async function submit() {
    let formData

    if (selectedTab.value === 'intellectual') {
      const intellectualForm = formIntellectual.value
      if (!intellectualForm.acceptStatement) {
        return
      }
      if (!intellectualForm.country) {
        errors.value.noCountry = true
        countryDropdown.value.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        })
        window.scrollBy(0, -(window.innerHeight / 2))
        return
      }
      if (intellectualForm.email !== intellectualForm.emailConfirm) {
        errors.value.unmatchedEmail = true
        emailConfirm.value.focus()
        return
      }

      const tempForm = {
        reason: 'copyright',
        campaign_slug: campaignSlug.value as string,
        name: `${intellectualForm.firstName} ${intellectualForm.lastName}`,
        phone: intellectualForm.phone,
        email: intellectualForm.email,
        address: intellectualForm.address1,
        address_2: intellectualForm.address2,
        city: intellectualForm.city,
        state: intellectualForm.state,
        zipcode: intellectualForm.zipcode,
        country: intellectualForm.country,
        additional_info: {
          report_by: intellectualForm.userType === '1' ? 'right_owner' : 'agent',
          legal_name: intellectualForm.legalName,
          specific_concern: intellectualForm.additionalInfo.specificConcern,
          original_work: intellectualForm.additionalInfo.originalWork,
          other_info: intellectualForm.additionalInfo.otherInfo,
          attach_files: intellectualForm.attachFile
        }
      }
      formData = tempForm
    }
    else if (selectedTab.value === 'policy') {
      const tempForm = {
        campaign_slug: campaignSlug.value as string,
        reason: [] as string[]
      }
      for (const [name, truthy] of Object.entries(formPolicy.value)) {
        if (truthy) {
          tempForm.reason.push(name)
        }
      }
      formData = tempForm
    }
    else {
      return
    }

    isFileUploading.value = true
    const { success, message } = await $fetchDefault<ResponseData<any>>($api.API_REPORT_FORM, { method: $method.post, body: formData })

    isFileUploading.value = false
    if (success) {
      resetForm()
      uiManager().createPopup($i18n.t('Send report success'), 'success')
    }
    else {
      uiManager().createPopup(`Error: ${message}`, 'error')
    }
  }

  return {
    tabsList,
    selectedTab,
    campaignSlug,
    isFileUploading,

    userCountry,
    currentCountry,
    filterCountryText,
    filterCountryArray,
    formIntellectual,
    formPolicy,

    handleStateChange,
    submit,

    errors
  }
}
