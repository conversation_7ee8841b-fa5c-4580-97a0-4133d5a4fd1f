import { h, render } from 'vue'
import { useCartStore } from '~~/store/cart'
import { useListingStore } from '~~/store/listing'
import { useStoreInfo } from '~~/store/storeInfo'
import { useUserSession } from '~~/store/userSession'
import { countryByCode } from './common'

export async function useThankYouPage() {
  const { $fetchDefault, $fetchWrite } = useNuxtApp()
  const relatedProducts = ref<Product[]>()

  const { params } = useRoute()
  const { success, data } = await $fetchDefault<ResponseData<GetOrderResponse>>(`${$api.API_GET_ORDER}/${params.token}?ref=thank_you`)

  if (!(success || data)) {
    showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
  }

  const { order } = data

  const storeInfo = useStoreInfo()
  const userSession = useUserSession()
  const cartStore = useCartStore()
  const listingStore = useListingStore()

  const { currentShippingMethod } = extractOrderDataResponse(data)

  const currentCountry = countryByCode(order.country)

  const canEditAddressInfo = computed(() => {
    return Date.parse(order.paid_at || '') + 86400000 > Date.now()
  })

  const hasHouseNumber = computed(() => {
    return order.house_number && order.house_number.trim() !== ''
  })

  const hasMailboxNumber = computed(() => {
    return order.mailbox_number && order.mailbox_number.trim() !== ''
  })

  const hasNote = computed(() => {
    return order.order_note && order.order_note.trim() !== ''
  })

  // Modals
  const isShowModalConfirmAddress = ref(false)
  const isShowModalEditInfo = ref(false)

  if (import.meta.client && order.order_number !== userSession.lastOrder?.order_number) {
    if (storeInfo.id === 1) {
      render(h('img', {
        width: 1,
        height: 1,
        src: `https://www.shareasale.com/sale.cfm?tracking=${order.order_number}&amount=${order.total_product_amount}&merchantID=116236&transtype=sale`
      }), document.head)
    }

    const googleAdsThankyou = storeInfo?.tracking_code?.google_ads_gtag_thank_you
    if (googleAdsThankyou) {
      useHead({
        script: [{
          type: 'text/javascript',
          innerHTML: `gtag('event', 'conversion', {'send_to': '${googleAdsThankyou}','value': ${order.total_amount},'currency': 'USD','transaction_id': ''})`
        }]
      })
    }

    userSession.setLastOrderDetail({
      order_number: order.order_number,
      order_token: order.access_token
    })
  }

  onMounted(async () => {
    if (
      (order.type === 'regular' || order.type === 'custom')
      && (!order.paid_at
        || (
          order.paid_at && (new Date(order.paid_at).getTime() + 3e5 > (new Date()).getTime())
        )
      )
    ) {
      setTimeout(() => {
        useTracking().trackEvent({
          event: 'purchase',
          data: {
            order_token: order.order_number || order.id,
            content_ids: order.products.map((item: OrderProduct) => item.product_id),
            content_name: order.products.map((item: OrderProduct) => item.campaign_title).join(','),
            content_category: order.products.map((item: OrderProduct) => item.product_name).join(','),
            content_value: order.products.map((item: OrderProduct) => item.price),
            content_quantity: order.products.map((item: OrderProduct) => item.quantity),
            content_type: 'product',
            num_items: order.total_quantity,
            currency: 'USD',
            country: order.country,
            value: order.total_amount
          }
        })
      }, 500)
    }

    userSession.setLastOrderDetail({
      order_number: order.order_number,
      order_token: order.access_token
    })

    const listRelatedProducts: RelatedProductPostData = {
      type: 'post_sale',
      filter: order.products.map((item: OrderProduct) => {
        return {
          product_id: item.product_id,
          campaign_id: item.campaign_id,
          template_id: item.template_id
        }
      })
    }
    if (storeInfo.smart_remarketing && order.id) {
      listRelatedProducts.order_id = Number.parseInt(order.id as unknown as string)
      listRelatedProducts.source = 'thank_you'
    }

    relatedProducts.value = await listingStore.postRelatedProduct(listRelatedProducts)

    // Save Coupon to Cart
    if (storeInfo.promotion_title && storeInfo.discount_code) {
      cartStore.setDiscountCode(storeInfo.discount_code)
    }

    setTimeout(() => {
      if (order.address_verified === 'invalid' && (order.paid_at && new Date(order.paid_at).getTime() + 43200000 >= new Date().getTime())) {
        isShowModalConfirmAddress.value = true
      }
    }, 1000)

    const orderKey = computed(() => useUserSession().orderKey)
    if (orderKey.value && orderKey.value.cart_key) {
      $fetchWrite($api.API_CART_KEY, {
        method: $method.post,
        body: {
          orderToken: orderKey.value.order_token,
          cartKey: orderKey.value.cart_key
        }
      })
      useUserSession().setOrderKey('', '')
    }
  })

  return {
    relatedProducts,
    currentCountry,
    currentShippingMethod,
    order,

    canEditAddressInfo,
    hasHouseNumber,
    hasMailboxNumber,
    hasNote,

    isShowModalConfirmAddress,
    isShowModalEditInfo
  }
}
