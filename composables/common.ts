import { addListener, launch } from 'devtools-detector'
import { useCampaignStore } from '~/store/campaign'
import { useCartStore } from '~/store/cart'
import { useGeneralSettings } from '~/store/generalSettings'
import { useStoreInfo } from '~/store/storeInfo'
import { useUiManager } from '~/store/uiManager'
import { useUserSession } from '~/store/userSession'

export function storeInfo() {
  return useStoreInfo()
}

export function generalSettings() {
  return useGeneralSettings()
}

export function campaignStore() {
  return useCampaignStore()
}

export const cdnURL = computed(() => useRuntimeConfig().app.cdnURL)

export function uiManager() {
  return useUiManager()
}

export function getThemeComponent(component: string) {
  return defineAsyncComponent(async () => {
    try {
      if (!storeInfo().theme) {
        throw new Error('Theme not found')
      }

      await import(`~/components/${storeInfo().theme}/${component}/index.vue`)
      return import(`~/components/${storeInfo().theme}/${component}/index.vue`)
    }
    catch (e) {
      return import(`~/components/default/${component}/index.vue`)
    }
  })
}

export function colorVal(val?: string) {
  const DEFAULT = '#333333'
  if (!val) {
    return DEFAULT
  }
  const { colors } = useGeneralSettings()
  const color = colors.find(item => val.replace('__', '') === item.name)

  if (!color) {
    return DEFAULT
  }

  // For multi-color mode, return the first color for backward compatibility
  if (color.multi_color_mode === 1 && color.hex_code.includes('-')) {
    return color.hex_code.split('-')[0] || DEFAULT
  }

  return color.hex_code || DEFAULT
}

export function getColorInfo(val?: string) {
  const DEFAULT_COLOR = {
    hex_code: '#333333',
    multi_color_mode: 0,
    is_heather: 0
  }

  if (!val) {
    return DEFAULT_COLOR
  }

  const { colors } = useGeneralSettings()
  const color = colors.find(item => val.replace('__', '') === item.name)

  if (!color) {
    return DEFAULT_COLOR
  }

  return {
    hex_code: color.hex_code,
    multi_color_mode: color.multi_color_mode || 0,
    is_heather: color.is_heather || 0
  }
}

export function getColorForImageTransform(val?: string) {
  const colorInfo = getColorInfo(val)

  // For multi-color mode, use the first color for image transformation
  if (colorInfo.multi_color_mode === 1 && colorInfo.hex_code.includes('-')) {
    return colorInfo.hex_code.split('-')[0]
  }

  // For single color mode, return the hex code
  return colorInfo.hex_code
}

export function totalQuantity() {
  const { getTotalQuantity } = useCartStore()
  return computed(() => {
    return getTotalQuantity
  })
}

export function countryByCode(code: string) {
  return useGeneralSettings().countries.find(country => country.code === code)
}

export const isLoading = computed(() => useUiManager().isLoading)

export function loading(value: boolean | string = false) {
  return useUiManager().$patch({
    isLoading: value
  })
}

export const isCheckoutPage = computed(() => useRoute().name?.toString().startsWith('checkout-token__'))
export const isOrderPage = computed(() => useRoute().name?.toString().startsWith('checkout-token__') || useRoute().name?.toString().startsWith('order-status-token__') || useRoute().name?.toString().startsWith('order-thank-you-token__'))
export const isCategoryPage = computed(() => useRoute().name?.toString().startsWith('category-category__'))
export const isCampaignPage = computed(() => useRoute().name?.toString().startsWith('campaignSlug-productName__'))
export const isCartPage = computed(() => useRoute().name?.toString().startsWith('cart__'))

export const isDebug = computed(() => useRoute().query.debug === 'true')

export function getHost(): any {
  const { ssrContext } = useNuxtApp()
  if (import.meta.client) {
    return window.top?.location.hostname
  }
  return ssrContext?.event.node.req.headers.host
}

export function createSEOMeta({ title = '', description = '', image, keywords, price, currency = 'USD', SKU = '', name = '' }: any) {
  const host = getHost()

  const storeInfo = useStoreInfo()
  const route = useRoute()
  const url = `https://${host}${route?.fullPath || ''}`

  if (!title) {
    title = storeInfo.name
  }
  if (!description) {
    description = storeInfo.description || storeInfo.name || ''
  }
  const meta = [
    { hid: 'description', name: 'description', content: description },
    { hid: 'title', name: 'title', content: title },
    // google
    { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: title },

    // facebook
    { hid: 'og:title', property: 'og:title', content: title },
    { hid: 'og:description', property: 'og:description', content: description },
    { hid: 'og:url', property: 'og:url', content: url },
    { hid: 'og:type', property: 'og:type', content: 'website' },
    { hid: 'og:site_name', property: 'og:site_name', content: storeInfo.name || '' },

    // twitter
    { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
    { hid: 'twitter:site', name: 'twitter:site', content: storeInfo.name || '' },
    { hid: 'twitter:title', name: 'twitter:title', content: title },
    { hid: 'twitter:description', name: 'twitter:description', content: description },
    { hid: 'twitter:creator', name: 'twitter:creator', content: storeInfo.name },
    { hid: 'twitter:url', name: 'twitter:url', content: url }
  ]

  if (storeInfo.store_type === 'google_ads') {
    meta.push(
      { hid: 'description', property: 'description', content: description },
      { hid: 'title', name: 'title', content: title }
    )

    if (keywords) {
      meta.push(
        { hid: 'keywords', property: 'keywords', content: keywords }
      )
    }
  }

  if (image) {
    meta.push(
      { hid: 'og:image:alt', property: 'og:image:alt', content: description },
      { hid: 'og:image', property: 'og:image', content: image },
      { hid: 'twitter:image:src', name: 'twitter:image:src', content: image }
    )
  }

  if (price) {
    meta.push(
      { hid: 'product:price:amount', property: 'product:price:amount', content: price },
      { hid: 'product:brand', property: 'product:brand', content: storeInfo.name || '' },
      { hid: 'product:price:currency', property: 'product:price:currency', content: currency },
      { hid: 'product:availability', property: 'product:availability', content: 'in stock' },
      { hid: 'product:retailer_item_id', name: 'product:retailer_item_id', content: SKU },
      { hid: 'product:item_group_id', name: 'product:item_group_id', content: name },
      { hid: 'product:condition', name: 'product:condition', content: 'new' }
    )
  }

  if (storeInfo.tracking_code && storeInfo.tracking_code.google_merchant_verification) {
    meta.push({ hid: 'google-site-verification', name: 'google-site-verification', content: storeInfo.tracking_code.google_merchant_verification })
  }
  if (storeInfo.tracking_code && storeInfo.tracking_code.facebook_meta_tag) {
    meta.push({ hid: 'facebook-domain-verification', name: 'facebook-domain-verification', content: storeInfo.tracking_code.facebook_meta_tag })
  }
  if (storeInfo.tracking_code && storeInfo.tracking_code.pinterest_meta_tag) {
    meta.push({ hid: 'p:domain_verify', name: 'p:domain_verify', content: storeInfo.tracking_code.pinterest_meta_tag })
  }

  useHead({
    title,
    meta
  })
}

export function handleDevtoolDetector() {
  if (!useRoute().query.ddd && useRuntimeConfig()?.public?.appEnv === 'production') {
    addListener((isOpen) => {
      const { visitInfo } = useUserSession()
      visitInfo.dd = isOpen
      visitInfo.device = isOpen ? 'desktop' : (clientInfo.device && clientInfo.device.type)
    })

    launch()
  }
}
