import { useStoreInfo } from '~~/store/storeInfo'

export default function useCustomHeadTag() {
    const tags = useStoreInfo().getStoreHeadTags?.head

    if (tags) {
        const headTags = headTagFilter(tags).map((tag) => {
            const match = tag.code.match(/<(\w+)\s+(.+?)>/)
            if (match) {
                const attributes = match[2]
                const tagObject = {}
                const attrMatches = attributes.match(/(\w+)="([^"]*?)"/g)
                if (attrMatches) {
                    attrMatches.forEach((attr) => {
                        const [key, value] = attr.split('=')
                        tagObject[key] = value.replace(/"/g, '')
                    })
                }
                return tagObject
            }
            return null
        }).filter(Boolean)

        useHead({
          meta: headTags
        })
    }
}
