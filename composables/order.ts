import { useUiManager } from '~~/store/uiManager'
import { useUserSession } from '~~/store/userSession'
import { useAWSUpload } from '~/composables/aws'
// import { useStoreInfo } from '~~/store/storeInfo'
import { useDiscount } from '~/composables/useDiscount'
import { useCartStore } from '~/store/cart'
import { useGeneralSettings } from '~/store/generalSettings'
import { $api, $method } from '~/utils/constant'
import { base64ToFile } from '~/utils/helper'

function useHelper() {
  return {
    base64ToFile
  }
}

export function useOrder(token?: string) {
  const order = shallowReactive<Partial<Order>>({})
  const paymentGateways = shallowRef<Array<Gateway>>([])
  const shippingMethods = shallowRef<Array<ShippingMethod>>([])
  const fulfillments = shallowRef<Fulfillments>({ unfulfilled: [], cancelled: [], processing: [], on_delivery: [], fulfilled: [] })
  const timeframe = shallowRef<Timeframe>({ received: '', validate: '', print: '', package: '', ship: '', delivered: null })

  const { $fetchWrite } = useNuxtApp()

  async function getOrderInfo(force = false) {
    const orderData = useState<GetOrderResponse>(`orderData-${token}`)
    if (!orderData.value || force) {
      // const {enable_distributed_checkout} = useStoreInfo()
      // const getOrderAPI = useRuntimeConfig()?.public?.appEnv === 'dev' ? (enable_distributed_checkout === 1 ? $api.API_GET_DISTRIBUTED_ORDER : $api.API_GET_ORDER) : $api.API_GET_DISTRIBUTED_ORDER
      const getOrderAPI = $api.API_GET_DISTRIBUTED_ORDER

      const { success, data } = await $fetchWrite<ResponseData<GetOrderResponse>>(`${getOrderAPI}/${token}`, {
        params: {
          token
        }
      })
      if (success) {
        orderData.value = data
      }
    }

    if (!orderData.value) {
      showError({ statusCode: 404, statusMessage: 'Page Not Found', fatal: true })
      return
    }

    Object.assign(order, orderData.value?.order)
    paymentGateways.value = orderData.value?.payment_gateways || []
    shippingMethods.value = orderData.value?.shipping_methods || []
  }

  async function updateOrder(key: boolean | string, updateData?: object, payload?: object) {
    const { updateDiscountError } = useDiscount()
    loading(key)
    // const {enable_distributed_checkout} = useStoreInfo()
    // const updateOrderAPI = useRuntimeConfig()?.public?.appEnv === 'dev' ? (enable_distributed_checkout === 1 ? $api.API_UPDATE_DISTRIBUTED_ORDER : $api.API_UPDATE_ORDER) : $api.API_UPDATE_DISTRIBUTED_ORDER
    const updateOrderAPI = $api.API_UPDATE_DISTRIBUTED_ORDER

    const result = await $fetchWrite<ResponseData<Partial<GetOrderResponse>>>(`${updateOrderAPI}`, {
      method: $method.post,
      body: {
        order_token: token,
        ...updateData
      }
    })
    const { success, data, message } = result
    if (message === 'Order is using others discount code') {
      updateDiscountError(3)
    }
    if (message === 'Your discount code is not available.') {
      updateDiscountError(1)
    }
    loading(false)
    if (success && data) {
      if (data.order) {
        Object.assign(order, data.order)
      }
      if (data.payment_gateways) {
        paymentGateways.value = data.payment_gateways
      }
      if (data.shipping_methods) {
        shippingMethods.value = data.shipping_methods
      }
      if (key === 'user-info' && payload) {
        useUserSession().updateUserInfo(payload)
      }
    }

    return result
  }

  return {
    order: order as Order,
    paymentGateways,
    shippingMethods,
    fulfillments,
    timeframe,
    getOrderInfo,
    updateOrder
  }
}

export async function createOrder(data?: object) {
  const { $fetchWrite } = useNuxtApp()

  useUiManager().$patch({
    isLoading: 'createOrder'
  })
  const $router = useRouter()
  const localePath = useLocalePath()
  const { AWSUpload } = useAWSUpload()
  const { base64ToFile } = useHelper()

  const { token, discountCode, listCartItem } = useCartStore()
  const {
    userInfo,
    visitInfo: { dd, csrfToken, ...filteredVisitInfo },
    userBehavior
  } = useUserSession()

  // Upload AI design images to AWS if they exist
  for (const item of listCartItem) {
    if (item.campaign_system_type === 'ai_mockup' && item.design_image_base64 && !item.aws_uploaded) {
      try {
        // Convert base64 to File object
        const fileName = `ai_design_${item.campaign_id}_${item.product_id}_${Date.now()}.png`
        const fileObject = base64ToFile(item.design_image_base64, fileName)

        const filePath = await AWSUpload(fileObject)

        if (filePath) {
          // Save original thumbnail URL if not already saved
          if (!item.original_thumb_url) {
            item.original_thumb_url = item.thumb_url
          }

          // Update the thumbnail URL with the new AWS path
          item.thumb_url = filePath
          item.aws_uploaded = true
        }
      }
      catch (error) {
        console.error('Error uploading design image:', error)
        // Continue with checkout process even if upload fails
      }
    }
  }

  const generalSettingsStore = useGeneralSettings()
  const body = {
    ...{
      email: userInfo?.email,
      products: listCartItem,
      country: generalSettingsStore.getCountryDisabledCheckout.includes(userInfo?.country as string) ? 'US' : userInfo?.country || filteredVisitInfo?.country || 'US',
      token,
      discount_code: discountCode,
      currency_code: userBehavior?.currencyCode ?? '',
      user_info: userInfo,
      visit_info: filteredVisitInfo,
      correct_price: userBehavior?.correct_price === true
    },
    ...data
  }
  // const {enable_distributed_checkout} = useStoreInfo()
  // const createOrderAPI = useRuntimeConfig()?.public?.appEnv === 'dev' ? (enable_distributed_checkout === 1 ? $api.API_CREATE_DISTRIBUTED_ORDER : $api.API_CREATE_ORDER) : $api.API_CREATE_DISTRIBUTED_ORDER
  const createOrderAPI = $api.API_CREATE_DISTRIBUTED_ORDER

  return await $fetchWrite<ResponseData<string>>(`${createOrderAPI}`, {
    method: $method.post,
    body
  }).then((result) => {
    const { success, message, data } = result

    if (success && data) {
      useCartStore().$patch({ token: data })
      $router.push(localePath({ path: `/checkout/${data}` }))
    }
    else if (message === 'need_verify') {
      uiManager().viewModalCreateOrderRecaptcha(true)
    }
    else if (message.email) {
      uiManager().createPopup(message.email)
    }
    else {
      uiManager().createPopup(message)
    }

    useUiManager().$patch({
      isLoading: false
    })

    return result
  }).catch(() => {
    uiManager().createPopup('Something went wrong, please try again later')
    useUiManager().$patch({
      isLoading: false
    })
  })
}
