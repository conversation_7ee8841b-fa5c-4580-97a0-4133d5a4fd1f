import type { Ref } from 'vue'
import { useCampaignStore } from '~~/store/campaign'
import { useUserSession } from '~~/store/userSession'
import { useCountryFormConfig } from '~/composables/countryFormConfig'
import { useCartStore } from '~/store/cart'
import { useGeneralSettings } from '~/store/generalSettings'

const validate: { [key in UserInfo as string]: string } = reactive({
  email: '',
  name: '',
  address: '',
  address_2: '',
  city: '',
  state: '',
  zipcode: '',
  phone: '',
  country: '',
  mailbox_number: '',
  house_number: ''
})

export async function useCheckout(token: string) {
  const localePath = useLocalePath()

  const {
    order,
    paymentGateways,
    shippingMethods,
    getOrderInfo,
    updateOrder
  } = useOrder(token)
  const { updateDiscountError } = useDiscount()
  const userInfo: ComputedRef<UserInfo> = computed(() => useUserSession().userInfo)
  const currentShippingMethod = ref<string>('')
  const isShowOrderSummary = ref(Boolean(storeInfo().always_show_order_summary))
  const countryDisabledCheckout = computed(() => useGeneralSettings().getCountryDisabledCheckout)

  watch(currentShippingMethod, (newShippingMethod: string, oldShippingMethod: string) => {
    if (!oldShippingMethod) {
      return
    }
    updateOrder(`shipping_fee_${newShippingMethod}`, { shipping_method: newShippingMethod })
  })

  const isNotOrderService = computed(() => {
    /**
     * This will hide/disable some elements in the checkout page
     * to make the page more concise
     *
     * Elements will be hidden:
     * - 'Return to cart'
     * - Countdown
     * - Shipping
     * - Insurance
     * - Discount
     * - Tip
     * - Product thumbnail
     */
    return order.type !== 'service'
  })

  onMounted(() => {
    useTracking().trackEvent({
      event: 'initiate_checkout',
      data: {
        order_token: order.order_number || order.id,
        content_ids: order.products?.map(item => item.product_id),
        content_name: order.products?.map(item => item.campaign_title).join(','),
        content_category: order.products?.map(item => item.product_name).join(','),
        content_value: order.products?.map(item => item.price),
        content_quantity: order.products?.map(item => item.quantity),
        content_type: 'product',
        num_items: order.total_quantity,
        currency: 'USD',
        country: order.country,
        value: order.total_amount,
        klaviyoData: {
          order
        }
      }
    })
  })

  await getOrderInfo(true)

  resetData()

  function resetData() {
    const userSession = useUserSession()
    const visitInfo = userSession.visitInfo
    userSession.updateUserInfo({
      name: order.customer_name || userInfo.value.name || '',
      email: order.customer_email || userInfo.value.email || '',
      address: order.address || userInfo.value.address || '',
      address_2: order.address_2 || userInfo.value.address_2 || '',
      city: order.city || userInfo.value.city || '',
      state: order.state || userInfo.value.state || '',
      zipcode: order.postcode || userInfo.value.postcode || '',
      phone: order.customer_phone || userInfo.value.phone || '',
      country: order.country || userInfo.value.country || visitInfo.country || userSession.clientInfo.country || 'US',
      subscribed: order.country === 'US' ? 1 : 0,
      house_number: order.house_number || userInfo.value.house_number || '',
      mailbox_number: order.mailbox_number || userInfo.value.mailbox_number || '',
      note: order.order_note || userInfo.value.note || ''
    })
    currentShippingMethod.value = order.shipping_method as string
  }

  async function updateCheckoutDiscount(discountCode: string, reEvaluateFlag: Ref<boolean>) {
    updateDiscountError(undefined)
    const updateBody = {
      ref: 'apply_discount_code',
      discount_code: discountCode
    }
    await updateOrder('discount_code', updateBody)
    if (reEvaluateFlag) {
      reEvaluateFlag.value = !reEvaluateFlag.value
    }
  }

  async function reloadOrder() {
    if (!document.hidden) {
      loading(true)
      await getOrderInfo(true)
      loading(false)
    }
  }

  async function removeProduct(product: OrderProduct) {
    const newListProducts = [...order.products || []]
    const index = newListProducts.indexOf(product)
    if (index > -1) {
      newListProducts.splice(index, 1)
    }
    useCartStore().removeCartItem(index)

    if (!useCartStore().listCartItem.length) {
      useRouter().push(localePath('/cart'))
      return
    }

    await createOrder()
    reloadOrder()
    // TODO: update for new api remove product
  }

  return {
    order,
    reloadOrder,
    isShowOrderSummary,
    countryDisabledCheckout,
    paymentGateways,
    shippingMethods,
    userInfo: userInfo.value,
    currentShippingMethod,
    isNotOrderService,
    updateCheckoutDiscount,
    resetData,
    getOrderInfo,
    updateOrder,
    removeProduct
  }
}

export function useCheckoutTip(tipList = [0, 0.02, 0.05, 0.1]) {
  const currentTip = ref(0)
  const currentTipIndex = ref<number | boolean>(0)
  const userTriggerTipping = ref(false)

  return {
    currentTip,
    tipList,
    currentTipIndex,
    userTriggerTipping
  }
}

export function useCheckoutForm(userInfo: Partial<UserInfo>) {
  const { $i18n } = useNuxtApp()
  const $t = $i18n.t

  const advancedEmailChecking = ref({
    isDisposable: false,
    hasMistyped: false,
    serverValidate: true,
    mistypedAutocorrect: ''
  })

  const isShowCountrySelect = ref(false)
  const filterCountryText = ref('')

  const countryDropdown = ref()
  const phoneInput = ref('')
  const warningAddress = ref(false)

  const isShowValidate = ref(false)

  Object.keys(validate).forEach(key => checkInfo(key as keyof UserInfo))
  watch(userInfo, () => {
    Object.keys(validate).forEach(key => checkInfo(key as keyof UserInfo))
  })

  function updateUserInfo(data: Partial<UserInfo>) {
    Object.assign(userInfo, data)
    useUserSession().update('userInfo', data)
    Object.keys(data).forEach(key => checkInfo(key as keyof UserInfo))
  }

  const filterCountryArray = computed(() => {
    return generalSettings().countries.filter((item: Country) => item.name.toLowerCase().trim().includes(filterCountryText.value.toLowerCase().trim()))
  })

  const currentCountry = computed(() => {
    return generalSettings().getCountryByCode(userInfo?.country)
  })

  function selectCountry(countryCode: string) {
    if (generalSettings().getCountryDisabledCheckout.includes(countryCode)) {
      return
    }
    userInfo.country = countryCode
    countryDropdown.value.hideData()
    filterCountryText.value = ''
  }

  const zipcodeText = computed(() => {
    if (['IE'].includes(currentCountry.value?.code as string)) {
      return 'Eircode'
    }
    else if (['AU', 'DE', 'GB'].includes(currentCountry.value?.code as string)) {
      return 'Postcode'
    }
    else if (['CA', 'FR', 'ES'].includes(currentCountry.value?.code as string)) {
      return 'Postal code'
    }
    else if (['IN'].includes(currentCountry.value?.code as string)) {
      return 'Pincode'
    }
    else {
      return 'Zip code'
    }
  })
  const isCountryUsingAlphanumericZipcode: ComputedRef<boolean> = computed(() => {
    return true // temporary skipping this check, allow every country to use both text + number as zipcode

    const countries = ['CA', 'GB', 'NL', 'IE', 'AU', 'MT', 'CH', 'IT', 'BR', 'AR', 'BE', 'PT', 'SE']
    return countries.includes(userInfo.country as string)
  })

  // @ts-expect-error ts(2349)
  const computedStateSelect = computed({
    set(state: string) {
      updateUserInfo({ state })
    },
    get() {
      return userInfo.state
    }
  })

  const stateText = computed(() => {
    if (['AU'].includes(currentCountry.value?.code as string)) {
      return 'State/Territory'
    }
    else if (['DE', 'IE', 'GB'].includes(currentCountry.value?.code as string)) {
      return 'County'
    }
    else if (['US'].includes(currentCountry.value?.code as string)) {
      return 'State'
    }
    else {
      return 'State/Province/Region'
    }
  })

  const countryState = computed(() => {
    return (COUNTRY_STATE_LIST as any)[currentCountry.value?.code as string]
  })

  function checkPhone(string: string, { valid, number }: any, { isPhoneNumberRequired }: { isPhoneNumberRequired: boolean }) {
    if (!isPhoneNumberRequired) {
      validate.phone = ''
      return
    }

    if (string) {
      if (valid) {
        userInfo.phone = number
        validate.phone = ''
      }
      else {
        validate.phone = 'Phone number is invalid'
      }
    }
    else {
      validate.phone = 'Phone number is required'
    }
  }
  function checkInfo(key: keyof UserInfo) {
    if (!userInfo) {
      return
    }
    const { isRequired } = useCountryFormConfig(userInfo.country)
    const checkKeyArray = ['email', 'name']
    if (isRequired('address_1')) {
      checkKeyArray.push('address')
    }
    if (isRequired('city')) {
      checkKeyArray.push('city')
    }
    if (isRequired('postCode')) {
      checkKeyArray.push('zipcode')
    }
    if (isRequired('houseNumber', false)) {
      checkKeyArray.push('house_number')
    }

    if (checkKeyArray.includes(key)) {
      const text = key === 'house_number' ? 'House number' : capitalizeFirstLetter(key)
      if (!userInfo[key]) {
        validate[key] = `${text} is required`
      }
      else {
        validate[key] = ''
      }
    }

    if (key === 'email' && userInfo.email) {
      if (!isValidEmail(userInfo.email as string)) {
        validate.email = 'Email is invalid'
        return
      }

      const mailTypo = Mailcheck().run({ email: userInfo.email })
      if (mailTypo && userInfo.email !== mailTypo.full) {
        advancedEmailChecking.value.hasMistyped = true
        advancedEmailChecking.value.mistypedAutocorrect = mailTypo.full
      }
      else {
        advancedEmailChecking.value.hasMistyped = false
        advancedEmailChecking.value.mistypedAutocorrect = ''
      }
    }
  }

  function onChangeEmailAddress() {
    if (!userInfo.email || validate.email) {
      return
    }

    serverValidateEmail(userInfo.email).then((res) => {
      // @ts-expect-error ts(2349)
      advancedEmailChecking.value.serverValidate = res
    })
  }

  const computedEmailValidateText = computed(() => {
    const obj: {
      level?: string | boolean
      message?: string
    } = {
      level: undefined,
      message: ''
    }

    if (isShowValidate.value && validate.email) {
      obj.level = !validate.email
      obj.message = $t(validate.email)
      return obj
    }

    if (advancedEmailChecking.value.isDisposable) {
      obj.level = true
      obj.message = $t('To ensure timely and reliable delivery of order updates, we ask that you do not use temporary email services.')
      return obj
    }
    if (!advancedEmailChecking.value.serverValidate) {
      obj.level = 'warning'
      obj.message = $t('Your email may not be valid')
      return obj
    }
    if (advancedEmailChecking.value.hasMistyped) {
      obj.level = 'warning'
      obj.message = $t('Did you mean {email}? Click to correct', { email: advancedEmailChecking.value.mistypedAutocorrect })
      return obj
    }

    return obj
  })

  return {
    advancedEmailChecking,
    computedEmailValidateText,

    isShowCountrySelect,
    filterCountryText,
    countryDropdown,
    phoneInput,
    warningAddress,
    filterCountryArray,
    isShowValidate,
    validate,

    currentCountry,
    stateText,
    zipcodeText,
    isCountryUsingAlphanumericZipcode,
    computedStateSelect,
    countryState,
    checkPhone,

    selectCountry,
    updateUserInfo,
    onChangeEmailAddress
  }
}

export function useCheckoutCountTimer() {
  const timer = ref(600)
  const parsedTimer = computed(() => {
    const minutes: number = Math.floor(timer.value / 60)
    const seconds: number = timer.value - minutes * 60

    return {
      minutes,
      seconds
    }
  })

  function runTimer() {
    const trackerInterval = setInterval(() => {
      timer.value--
      if (!timer.value) {
        clearInterval(trackerInterval)
      }
    }, 1000)
  }

  return {
    parsedTimer,
    timer,
    runTimer
  }
}

export function useCheckoutDiscount(order: Order) {
  const $viewport = useViewport()

  const discountList = shallowRef<Promotion[]>([])
  const { updateDiscountError, discountErrorCode } = useDiscount()
  function getDiscountList() {
    if (order.products) {
      const campaignIds = order.products.map(item => item.campaign_id)
      // @ts-expect-error ts(2349)
      useCampaignStore().getPromotion(campaignIds).then((discounts: Promotion[]) => {
        discountList.value = discounts as Promotion[]
        if (!$viewport.isLessThan(VIEWPORT.desktop) || !!discountCode.value) {
          promotionShow.value = !!discountCode.value || !!(discounts && discounts.length)
        }
      })
    }
  }

  const DISCOUNT_RETURN_CODE = {
    SUCCESS: 0,
    INVALID: 1,
    CONDITION_NOT_MET: 2,
    USING_OTHER_DISCOUNT: 3
  }

  const userTrigger = ref(false)
  const isShowModalDiscountList = ref(false)
  const promotionShow = ref(false)
  const discountInput = ref<HTMLElement>()

  watch(promotionShow, async (newShowState: boolean) => {
    if (newShowState && userTrigger.value) {
      await nextTick()
      discountInput.value?.focus()
    }
    userTrigger.value = false
  })

  const discountCode = ref('')
  if (order.discount_code) {
    discountCode.value = order.discount_code
  }

  // const discountErrorCode: Ref<number | null> = ref(null)

  function evaluateDiscountCode() {
    if (!discountCode.value) {
      updateDiscountError(undefined)
      // discountErrorCode.value = null
      return
    }
    if (discountErrorCode.value === DISCOUNT_RETURN_CODE.USING_OTHER_DISCOUNT) {
      return
    }
    if (discountCode.value !== order.discount_code) {
      updateDiscountError(DISCOUNT_RETURN_CODE.INVALID)
      // discountErrorCode.value = DISCOUNT_RETURN_CODE.INVALID
      return
    }
    if (discountCode.value === order.discount_code && !order.total_discount) {
      updateDiscountError(DISCOUNT_RETURN_CODE.CONDITION_NOT_MET)
      // discountErrorCode.value = DISCOUNT_RETURN_CODE.CONDITION_NOT_MET
      return
    }
    updateDiscountError(DISCOUNT_RETURN_CODE.SUCCESS)
    // discountErrorCode.value = DISCOUNT_RETURN_CODE.SUCCESS
  }

  return {
    discountList,

    DISCOUNT_RETURN_CODE,

    userTrigger,
    promotionShow,
    isShowModalDiscountList,
    discountErrorCode,
    discountCode,
    discountInput,
    getDiscountList,
    evaluateDiscountCode
  }
}

export function useCheckoutDynamicForm(userInfo: Partial<UserInfo>, isPhoneNumberRequired: boolean, isHasRequiredLabel: boolean = true) {
  const { $i18n } = useNuxtApp()
  const $t = $i18n.t
  const { countryState, stateText, zipcodeText } = useCheckoutForm(userInfo)
  const { currentCountryFormMap, isRequired } = useCountryFormConfig(computed(() => userInfo.country))
  const currentCountryFormMapPosition = computed(() => {
    return currentCountryFormMap.value?.position ?? ['address', 'city', 'state', 'postCode']
  })

  const cityLabel = computed(() => {
    const DEFAULT = $t('City')
    const label = getLabel('city')
    const text = label ? $t(label) : DEFAULT
    return isHasRequiredLabel ? text + getRequiredSuffix('city') : text
  })
  const addressLabel1 = computed(() => {
    const DEFAULT = `${$t('Address')} (${$t('House number and street name')})`
    const label = getLabel('address_1')
    const text = label ? $t(label) : DEFAULT
    return isHasRequiredLabel ? text + getRequiredSuffix('address_1') : text
  })
  const addressLabel2 = computed(() => {
    const DEFAULT = `${$t('Apt / Suite / Other')}`
    const label = getLabel('address_2')
    return label ? $t(label) : DEFAULT
  })
  const stateLabel = computed(() => {
    const DEFAULT = stateText.value
    const label = getLabel('state')
    const text = label ? $t(label) : DEFAULT
    return isHasRequiredLabel ? text + getRequiredSuffix('state') : text
  })
  const zipcodeLabel = computed(() => {
    const DEFAULT = `${$t(zipcodeText.value)}`
    const label = getLabel('postCode')
    const text = label ? $t(label) : DEFAULT
    return isHasRequiredLabel ? text + getRequiredSuffix('postCode') : text
  })
  const phoneLabel = computed(() => {
    const DEFAULT = isPhoneNumberRequired ? `${$t('Phone number')} *` : $t('Phone number (Optional)')
    const label = getLabel('phoneNumber')
    const suffix = isPhoneNumberRequired ? ' *' : ` (${$t('Optional')})`
    return label ? `${label} ${suffix}` : DEFAULT
  })
  const houseNumberLabel = computed(() => {
    const DEFAULT = $t('House number')
    const label = getLabel('houseNumber')
    const suffix = isRequired('houseNumber', false) ? ' *' : ''
    const text = label ? $t(label) : DEFAULT
    return isHasRequiredLabel ? text + suffix : text
  })
  const mailboxLabel = computed(() => {
    const DEFAULT = $t('Mailbox number')
    const label = getLabel('mailboxNumber')
    return label ? $t(label) : DEFAULT
  })

  function getClass(inputType: CheckoutFormConfigItemKey): string {
    const posIndex = currentCountryFormMapPosition.value.indexOf(inputType.startsWith('address') ? 'address' : inputType)
    let orderClass = ''
    switch (posIndex) {
      case 0:
        orderClass = 'order-1'
        break
      case 1:
        orderClass = 'order-2'
        break
      case 2:
        orderClass = 'order-3'
        break
      case 3:
        orderClass = 'order-4'
        break
      case 4:
        orderClass = 'order-5'
        break
      case 5:
        orderClass = 'order-6'
        break
      case 6:
        orderClass = 'order-7'
        break
      case 7:
        orderClass = 'order-8'
        break
      case 8:
        orderClass = 'order-9'
        break
      case 9:
        orderClass = 'order-10'
        break
      case 10:
        orderClass = 'order-11'
        break
      case 11:
        orderClass = 'order-12'
        break
    }
    let widthClass = ''
    let paddingClass = ''
    if (!currentCountryFormMap.value) { // default
      switch (inputType) {
        case 'city':
          widthClass = 'col-span-12 lg:col-span-4'
          paddingClass = 'md:mr-1'
          break
        case 'postCode':
          paddingClass = 'ml-1'
          widthClass = 'col-span-6 lg:col-span-4'
          break
        case 'state':
          paddingClass = 'mr-1 md:ml-1'
          widthClass = 'col-span-6 lg:col-span-4'
          break
        case 'address_1':
        case 'address_2':
          widthClass = 'col-span-12'
          break
      }
    }
    else {
      const width = currentCountryFormMap.value?.[inputType]?.width ?? 100
      const nextKey = currentCountryFormMapPosition.value?.[posIndex + 1]
      let nextWidth = 100
      if (nextKey !== 'address') {
        nextWidth = currentCountryFormMap.value?.[nextKey as CheckoutFormConfigItemKey]?.width ?? 100
      }
      switch (width) {
        case 30:
          widthClass = 'col-span-4'
          if (nextWidth >= 60) {
            paddingClass = 'pr-2'
          }
          else {
            paddingClass = 'px-2'
          }
          break
        case 40:
          widthClass = 'col-span-5'
          break
        case 50:
          widthClass = 'col-span-6'
          if (nextWidth === 50) {
            paddingClass = 'pr-2'
          }
          else {
            paddingClass = 'pl-2'
          }
          break
        case 60:
          widthClass = 'col-span-7'
          paddingClass = 'pl-2'
          break
        case 70:
          widthClass = 'col-span-8'
          paddingClass = 'pl-2'
          break
        case 100:
          widthClass = 'col-span-12'
          break
      }
    }
    return `${paddingClass} ${widthClass} ${orderClass}`
  }
  function getLabel(inputType: CheckoutFormConfigItemKey): string | undefined {
    return currentCountryFormMap.value?.[inputType]?.label
  }
  function getRequiredSuffix(inputType: CheckoutFormConfigItemKey) {
    let isStateRequired = true
    if (inputType === 'state') {
      isStateRequired = countryState.value && countryState.value.length
    }
    return isRequired(inputType) && isStateRequired ? ' *' : ''
  }
  function isHasField(inputType: CheckoutFormConfigItemKey, defaultValue = true) {
    if (!currentCountryFormMap.value) {
      return defaultValue
    }
    return !!currentCountryFormMap.value[inputType]
  }

  return {
    cityLabel,
    addressLabel1,
    addressLabel2,
    stateLabel,
    zipcodeLabel,
    phoneLabel,
    mailboxLabel,
    houseNumberLabel,

    getClass,
    isHasField,
    isRequired
  }
}
