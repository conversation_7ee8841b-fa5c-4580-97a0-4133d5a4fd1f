import { useUserSession } from '~/store/userSession'

interface IP2LocattionResponse {
  status: string;
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  zip: string;
  lat: number;
  lon: number;
  timezone: string;
  isp: string;
  org: string;
  as: string;
  query: string;
}

export function useDeliverToLocation () {
  const location = ref('')
  async function ip2location (ip: string): Promise<Partial<IP2LocattionResponse>> {
    try {
      return await $fetch<IP2LocattionResponse>(`https://ip.cloudimgs.net/json/${ip}`)
    } catch (e) {
      return {
        countryCode: '',
        city: '',
        region: '',
        regionName: '',
        country: '',
      } as Partial<IP2LocattionResponse>
    }
  }

  async function initDeliverTo () {
    const { visitInfo, userInfo, clientInfo } = useUserSession()
    location.value = '' // Reset location

    if (!visitInfo.clientIp) {
      setTimeout(initDeliverTo, 500)
      return
    }
    if (generalSettings().getCountryDisabledCheckout.includes(visitInfo.country as string)) {
      return
    }

    try {
      if (userInfo.country) {
        const countryName = (new Intl.DisplayNames(['en'], { type: 'region' })).of(userInfo.country) as string
        const userCity = userInfo.city

        if (userCity) {
          location.value = `${userCity}, ${countryName}`
          return
        }

        location.value = countryName
        return
      }

      const tracedLocation = await ip2location(visitInfo.clientIp)
      if (!tracedLocation.countryCode) {
        throw new Error('no country code')
      }

      const tempLocation = (tracedLocation.countryCode === 'US') ? `${tracedLocation.city}, ${tracedLocation.region}` : `${tracedLocation.regionName}, ${tracedLocation.country}`
      if (tempLocation.includes('undef')) {
        throw new Error('undef')
      }

      location.value = tempLocation
      visitInfo.country = tracedLocation.countryCode
      clientInfo.country = tracedLocation.countryCode
    } catch (err) {
      const userCountry = visitInfo.country || ''
      location.value = (new Intl.DisplayNames(['en'], { type: 'region' })).of(userCountry) as string
    }
  }

  return {
    location,
    ip2location,
    initDeliverTo,
  }
}
