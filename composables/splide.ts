import { Grid } from '@splidejs/splide-extension-grid'
import { type Options } from '@splidejs/vue-splide'

export const splideExtensions = { Grid }

export const splideSetting:Options = {
  type: 'loop',
  classes: {
    arrow: 'splide__arrow z-1 absolute position-center-y btn-fill-black opacity-50 rounded-full fill-white bg-gray-400 p-1 h-10 w-10 center-flex <md:(w-7 h-7)',
    prev: 'splide__arrow--prev left-1 rotate-180',
    next: 'splide__arrow--next right-1',

    pagination: 'splide__pagination py-3 !md:hidden',
    page: 'splide__pagination__page btn bg-gray-300 hover:bg-gray-800 rounded-full h-2 w-2 mx-1',
  }
}

export const collectionBannerSplideSetting:Options = {
  ...splideSetting,
  pagination: false,
  // focus: 'center',
  perPage: 5,
  padding: '1rem',
  breakpoints: {
    767: {
      perPage: 2,
      padding: '0.5rem',
    },
    1023: {
      perPage: 3,
    }
  },
  grid: false,
}

export const productSplideSetting:Options = {
  ...splideSetting,
  pagination: false,
  perPage: 4,
  gap: '1rem',
  breakpoints: {
    490: {
      perPage: 1,
      grid: { rows: 2, cols: 2 },
    },
    768: {
      perPage: 3,
      gap: '0.25rem',
    },
    1024: {
      perPage: 4,
      gap: '0.5rem',
    },
  },
  grid: false,
}

export const vintageSplideSetting: Options = {
  type: 'loop',
  arrowPath: 'M 16 28 a 1 1 0 0 1 -0.664 -1.747 L 23.5 19 L 15.336 11.747 A 1 1 0 0 1 16.664 10.253 L 26.5 19 L 16.664 27.747 A 0.994 0.994 0 0 1 16 28 Z',
  classes: {
    arrow: 'splide__arrow absolute position-center-y p-1 border bg-white rounded-full hover:bg-[#eaeaea] transition',
    prev: 'splide__arrow--prev left-1 rotate-180 shadow-custom5VFlip',
    next: 'splide__arrow--next right-1 shadow-custom5',

    pagination: 'splide__pagination py-3 !md:hidden',
    page: 'splide__pagination__page btn bg-gray-300 hover:bg-gray-800 rounded-full h-2 w-2 mx-1',
  }
}

export const productReviewSplideSetting:Options = {
  ...vintageSplideSetting,
  type: 'slide',
  pagination: false,
  perPage: 4,
  gap: '1rem',
  breakpoints: {
    768: {
      perPage: 3,
      gap: '0.25rem',
    },
    1024: {
      perPage: 4,
      gap: '0.5rem',
    },
  },
  grid: false,
}

export const basicSplideSetting: Options = {
  type: 'loop',
  arrowPath: 'M 26.616 20.2 l -9.648 11.2 l -2.384 -2.144 L 22.392 20.2 L 14.584 11.144 L 16.968 9 l 9.648 11.2 z',
  classes: {
    arrow: 'splide__arrow absolute position-center-y transition bg-[var(--slide-arrow-background)] hover:(bg-[var(--slide-arrow-background-hover)] fill-white)',
    prev: 'splide__arrow--prev left-1 rotate-180',
    next: 'splide__arrow--next right-1',

    pagination: 'splide__pagination py-3 !md:hidden',
    page: 'splide__pagination__page',
  }
}

export const basicSplideCollectionBannerSplideSetting = {
  ...basicSplideSetting,
  pagination: false,
  focus: 'center',
  perPage: 5,
  padding: '1rem',
  breakpoints: {
    767: {
      perPage: 2,
      padding: '0.5rem',
    },
    1023: {
      perPage: 3,
    }
  },
  grid: false,
}

export const basicProductSplideSetting: Options = {
  ...basicSplideSetting,
  pagination: false,
  perPage: 4,
  gap: '1rem',
  type: 'slide',
  breakpoints: {
    490: {
      perPage: 2,
      arrows: false,
    },
    768: {
      perPage: 3,
      gap: '0.25rem',
    },
    1024: {
      perPage: 4,
      gap: '0.5rem',
    },
  },
  grid: false,
}