import { createLogger } from 'winston'
import DiscordTransport from 'winston-discord-transport'
import WinstonGraylog2 from 'winston-graylog2'

export function ErrorLoggers() {
  const runtimeConfig = useRuntimeConfig()
  const loggerSampleRate = Number.parseInt(runtimeConfig.loggerSampleRate)

  const loggers = {
    Discord: runtimeConfig.discordWebhook
      ? createLogger({
          transports: [
            // @ts-expect-error ts(2349)
            new (DiscordTransport.default || DiscordTransport)({
              webhook: runtimeConfig.discordWebhook,
              defaultMeta: {
                environment: `v4-${runtimeConfig.public.appEnv}`
              }
            })
          ]
        })
      : undefined,
    Graylog: createLogger({
      transports: [
        // @ts-expect-error ts(2349)
        new WinstonGraylog2({
          name: 'Graylog',
          level: 'error',
          graylog: {
            servers: [
              { host: 'graylog.senprints.net', port: 12201 }
            ],
            facility: runtimeConfig.public.appEnv
          },
          staticMeta: {
            application_name: 'storefront_v4'
          }
        })
      ]
    }),

    logAllThrottled(...args: any[]) {
      const shouldSendLog = Math.random() < loggerSampleRate
      if (!shouldSendLog) {
        return
      }

      // @ts-expect-error ts(2349)
      this.logAll(...args)
    },

    logAll(error: Error, meta = {}) {
      try {
        this.Graylog.error({
          message: error?.stack || error.message
        })

        this.Discord?.log({
          level: 'error',
          message: error.message,
          error,
          meta
        })
      }
      catch (err) {
        console.log(err)
        console.log('error sending log')
      }
    }
  }

  return loggers
}
