import { useUserSession } from '~~/store/userSession'

export function useFooter() {
  const { status, foot_line: footLine, currentDomain, tags, footerMenu } = storeInfo()
  const { $i18n, $isBot, $fetchWrite } = useNuxtApp()
  const userSession = useUserSession()
  const { t } = useI18n()

  const { contactPhoneData, contactPhoneData2 } = $footerContactPhoneData
  const contactPhoneList = status === 'verified' ? contactPhoneData : contactPhoneData2
  const currentContactPhoneIndex = ref<number>(0)
  const emailNewsletter = ref<UserInfo['email']>()

  const copyrightText = computed(() => {
    if (!footLine) {
      return `Copyright © ${currentDomain} - All rights reserved.`
    }

    // render template variables
    // input: {copyright} {year} SenPrints
    // output: &copy; <current-year> SenPrints

    let footlineBak = footLine

    if (footlineBak.includes('{year}')) {
      footlineBak = footlineBak.replace('{year}', `2020-${new Date().getFullYear()}`)
    }

    if (footlineBak.includes('{copyright}')) {
      footlineBak = footlineBak.replace('{copyright}', '&copy;')
    }

    return footlineBak
  })

  const userCountryCode = computed(() => userSession.visitInfo.country)

  const enableContactForm = computed(() => {
    return storeInfo().enable_contract_form
  })

  const enableCustomPhone = computed(() => {
    return storeInfo().enable_custom_phone
  })

  const canHasExtraInfo = computed(() => {
    return enableCustomPhone.value || tags?.includes('custom phone') || tags?.includes('custom contact info')
  })

  function getFooters() {
    const copiedFooter = _cloneDeep($menuFooter)

    const { footerMenu } = storeInfo()

    for (const item of footerMenu) {
      if (!item.url) { continue }
      const urlKey = item.url as keyof typeof $menuFooterUrls
      if (Object.prototype.hasOwnProperty.call($menuFooterUrls, urlKey)) {
        const indexedFooterItem = $menuFooterUrls[urlKey]
        const menuKey = indexedFooterItem[0] as keyof typeof copiedFooter
        const menuIndex = indexedFooterItem[1] as number

        if (menuKey in copiedFooter
          && Array.isArray(copiedFooter[menuKey].menuData)
          && item.name) {
          copiedFooter[menuKey].menuData[menuIndex] = {
            name: item.name,
            url: item.url
          }
        }
        continue
      }
      if (item.name) {
        copiedFooter.menu1.menuData.push({
          name: item.name,
          url: item.url
        })
      }
    }

    return copiedFooter
  }

  onMounted(() => {
    updateCurrentContactPhoneIndex()
  })

  watch(userCountryCode, () => {
    updateCurrentContactPhoneIndex()
  })

  function updateCurrentContactPhoneIndex() {
    if (userCountryCode.value) {
      const index = contactPhoneList.findIndex(element => element.countryCode.includes(userCountryCode.value as string))
      if (index === -1) {
        currentContactPhoneIndex.value = contactPhoneList.length - 1
      }
      else {
        currentContactPhoneIndex.value = index
      }
    }
  }

  async function subscribeEmail() {
    if (!emailNewsletter.value) {
      return
    }

    await userSession.getCSRFToken()

    const { success, message } = await $fetchWrite<ResponseData<any>>($api.API_GET_SUBSCRIBE, {
      method: $method.post,
      body: {
        email: emailNewsletter.value,
        token: userSession.visitInfo.csrfToken
      },
      headers: {
        'x-session-id': userSession.visitInfo.session_id
      } as { 'x-session-id': string }
    })

    if (success) {
      uiManager().createPopup($i18n.t('Thank you for your support!'), 'success')
    }
    else {
      uiManager().createPopup(`${message}`, 'error')
    }
  }
  function getSocialLink(link: string | null) {
    return $getSocialLink(link)
  }

  function encode(str: string) {
    if (str && import.meta.client) {
      return btoa(str)
    }
    return ''
  }

  function decode(str: string) {
    if (str && import.meta.client) {
      if ($isBot()) {
        return ''
      }
      return atob(str)
    }
    return ''
  }

  return {
    contactPhoneList,
    currentContactPhoneIndex,
    emailNewsletter,
    enableContactForm,
    enableCustomPhone,
    canHasExtraInfo,
    userCountryCode,
    copyrightText,
    getFooters,

    subscribeEmail,
    getSocialLink,
    encode,
    decode
  }
}

export function useHeader() {
  const $router = useRouter()
  const route = useRoute()
  const $localePath = useLocalePath()

  const searchKeyword = ref<string>('')
  const searchInputRef = ref<HTMLElement | null>()

  const activeMenu = ref<number | boolean>()
  const showMenu = computed(() => uiManager().showHeaderMenu)

  const isSearching = computed(() => uiManager().isSearching)

  function searchAction(): void {
    if (searchKeyword.value) {
      if (searchKeyword.value !== route.query?.s) {
        uiManager().isSearching = true
      }

      const query = {
        s: searchKeyword.value
      }
      $router.push($localePath({
        path: '/search',
        query
      }))
    }
    else {
      $router.push($localePath({
        path: '/collection'
      }))
    }
  }

  return {
    isSearching,
    searchKeyword,
    searchInputRef,
    activeMenu,
    showMenu,
    searchAction
  }
}
