export function useHiddenBottomButton() {
  const isHiddenBottomButton = ref(true)
  function checkBottomButton () {
    if (import.meta.server) { return }
    if (window.innerWidth >= 768) { return }
    const buttonRect = document.getElementById('quantityDropdown')?.getBoundingClientRect()
    isHiddenBottomButton.value = (buttonRect?.top || 0) >= 0
  }
  onMounted(() => {
    document.addEventListener('scroll', checkBottomButton)
  })
  onUnmounted(() => {
    document.removeEventListener('scroll', checkBottomButton)
  })

  return {
    isHiddenBottomButton,
  }
}
