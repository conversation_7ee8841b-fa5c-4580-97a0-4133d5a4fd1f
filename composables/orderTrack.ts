import { useUserSession } from '~~/store/userSession'

export function useLastOrder() {
  const lastOrderStore = useUserSession()
  const lastOrder = computed(() => lastOrderStore.lastOrder)
  const lastOrderUrl = computed(() => {
    if (lastOrder!.value!.order_token) {
      return `/order/status/${lastOrder!.value!.order_token}`
    }
    return null
  })

  return {
    lastOrder,
    lastOrderUrl
  }
}

export function useOrderTrack() {
  const isShowValidate = ref(false)
  const router = useRouter()
  const localePath = useLocalePath()
  const { $i18n, $fetchDefault } = useNuxtApp()
  const email = ref('')
  const orderNumber = ref('')

  function resendConfirmation() {
    isShowValidate.value = true
    if (!email.value && !isValidEmail(email.value)) {
      return
    }
    $fetchDefault('/public/order/resend-confirm-email', { method: $method.post, body: { email: email.value } }).finally(() => {
      uiManager().createPopup($i18n.t('Please check your inbox'), 'success')
    })
  }
  async function onSubmit() {
    if ((!email.value && !isValidEmail(email.value)) || !orderNumber) {
      return
    }

    const { data, success } = await $fetchDefault<ResponseData<string>>('/public/order/track', { method: $method.post, body: { email: email.value, order_number: orderNumber.value } })

    if (success) {
      router.push(localePath(`/order/status/${data}`))
    }
  }

  return {
    isShowValidate,
    email,
    orderNumber,

    resendConfirmation,
    onSubmit
  }
}
