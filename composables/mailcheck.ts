export function Mailcheck() {
  const domainThreshold = 2
  const secondLevelThreshold = 2
  const topLevelThreshold = 2

  const defaultDomains = ['msn.com', 'bellsouth.net', 'telus.net', 'comcast.net', 'optusnet.com.au', 'earthlink.net', 'qq.com', 'sky.com', 'icloud.com', 'mac.com', 'sympatico.ca', 'googlemail.com', 'att.net', 'xtra.co.nz', 'web.de', 'cox.net', 'gmail.com', 'ymail.com', 'aim.com', 'rogers.com', 'verizon.net', 'rocketmail.com', 'google.com', 'optonline.net', 'sbcglobal.net', 'aol.com', 'me.com', 'btinternet.com', 'apple.com', 'web.de', '1und1.de', 't-online.de', 'freenet.de', 'charter.net', 'shaw.ca']

  const defaultSecondLevelDomains = ['yahoo', 'hotmail', 'mail', 'live', 'outlook', 'gmx']
  const defaultTopLevelDomains = ['com', 'com.au', 'com.tw', 'ca', 'co.nz', 'co.uk', 'de', 'fr', 'it', 'ru', 'net', 'org', 'edu', 'gov', 'jp', 'nl', 'kr', 'se', 'eu', 'ie', 'co.il', 'us', 'at', 'be', 'dk', 'hk', 'es', 'gr', 'ch', 'no', 'cz', 'in', 'net', 'net.au', 'info', 'biz', 'mil', 'co.jp', 'sg', 'hu', 'uk']

  const dictionary = {
    'web.de.de': 'web.de',
    'gxm.net': 'gmx.net',
    'gxm.com': 'gmx.com',
    'gmx.ta': 'gmx.at',
    'yaho.com': 'yahoo.com',
    'yaho.de': 'yahoo.de',
    'yahoo.net': 'yahoo.com',
    'yahoo.org': 'yahoo.com',
    'yahoomail.com': 'yahoo.com'
  }

  // Helpers
  function encodeEmail(email: string) {
    let result = encodeURI(email)
    result = result.replace('%20', ' ').replace('%25', '%').replace('%5E', '^').replace('%60', '`').replace('%7B', '{').replace('%7C', '|').replace('%7D', '}')
    return result
  }

  function splitEmail(email: string) {
    // @ts-ignore
    email = email !== null ? (email.replace(/^\s*/, '').replace(/\s*$/, '')) : null // trim() not exist in old IE!
    const parts = email.split('@')

    if (parts.length < 2 || parts.includes('')) {
      return false
    }

    const domain = parts.pop()
    // @ts-ignore
    const domainParts = domain.split('.')
    let sld = ''
    let tld = ''

    if (domainParts.length === 0) {
      // The address does not have a top-level domain
      return false
    }
    else if (domainParts.length === 1) {
      // The address has only a top-level domain (valid under RFC)
      tld = domainParts[0]
    }
    else {
      // The address has a domain and a top-level domain
      sld = domainParts[0]
      for (let j = 1; j < domainParts.length; j++) {
        tld += `${domainParts[j]}.`
      }
      tld = tld.substring(0, tld.length - 1)
    }

    return {
      topLevelDomain: tld,
      secondLevelDomain: sld,
      domain,
      address: parts.join('@')
    }
  }

  function sift4Distance(s1: string, s2: string, maxOffset: number) {
    // sift4: https://siderite.blogspot.com/2014/11/super-fast-and-accurate-string-distance.html
    if (maxOffset === undefined) {
      maxOffset = 5 // default
    }

    if (!s1 || !s1.length) {
      if (!s2) {
        return 0
      }
      return s2.length
    }

    if (!s2 || !s2.length) {
      return s1.length
    }

    const l1 = s1.length
    const l2 = s2.length

    let c1 = 0 // cursor for string 1
    let c2 = 0 // cursor for string 2
    let lcss = 0 // largest common subsequence
    let localCS = 0 // local common substring
    let trans = 0 // number of transpositions ('ab' vs 'ba')
    const offsetArr = [] // offset pair array, for computing the transpositions

    while ((c1 < l1) && (c2 < l2)) {
      if (s1.charAt(c1) === s2.charAt(c2)) {
        localCS++
        let isTrans = false
        // see if current match is a transposition
        let i = 0
        while (i < offsetArr.length) {
          // @ts-ignore
          const ofs = offsetArr[i]

          if (c1 <= ofs.c1 || c2 <= ofs.c2) {
            // when two matches cross, the one considered a transposition is the one with the largest difference in offsets
            isTrans = Math.abs(c2 - c1) >= Math.abs(ofs.c2 - ofs.c1)
            if (isTrans) {
              trans++
            }
            else if (!ofs.trans) {
              ofs.trans = true
              trans++
            }
            break
          }
          else if (c1 > ofs.c2 && c2 > ofs.c1) {
            offsetArr.splice(i, 1)
          }
          else {
            i++
          }
        }
        offsetArr.push({
          c1,
          c2,
          trans: isTrans
        })
      }
      else {
        lcss += localCS
        localCS = 0
        if (c1 !== c2) {
          c1 = c2 = Math.min(c1, c2) // using min allows the computation of transpositions
        }
        // if matching characters are found, remove 1 from both cursors (they get incremented at the end of the loop)
        // so that we can have only one code block handling matches
        for (let j = 0; j < maxOffset && (c1 + j < l1 || c2 + j < l2); j++) {
          if ((c1 + j < l1) && (s1.charAt(c1 + j) === s2.charAt(c2))) {
            c1 += j - 1
            c2--
            break
          }
          if ((c2 + j < l2) && (s1.charAt(c1) === s2.charAt(c2 + j))) {
            c1--
            c2 += j - 1
            break
          }
        }
      }
      c1++
      c2++
      // this covers the case where the last match is on the last token in list, so that it can compute transpositions correctly
      if ((c1 >= l1) || (c2 >= l2)) {
        lcss += localCS
        localCS = 0
        c1 = c2 = Math.min(c1, c2)
      }
    }
    lcss += localCS
    return Math.round(Math.max(l1, l2) - lcss + trans) // add the cost of transpositions to the final result
  }

  function findClosestDomain(domain: string, domains: string[], distanceFunction: Function, threshold: number) {
    threshold = threshold || topLevelThreshold
    let dist
    let minDist = Infinity
    let closestDomain = null

    if (!domain || !domains) {
      return false
    }
    if (!distanceFunction) {
      distanceFunction = sift4Distance
    }

    for (let i = 0; i < domains.length; i++) {
      if (domain === domains[i]) {
        return domain
      }
      dist = distanceFunction(domain, domains[i])
      if (dist < minDist) {
        minDist = dist
        closestDomain = domains[i]
      }
    }

    if (minDist <= threshold && closestDomain !== null) {
      return closestDomain
    }
    else {
      return false
    }
  }

  // Main
  function suggest(email: string, domains: string[], secondLevelDomains: string[], topLevelDomains: string[], distanceFunction: Function) {
    email = email.toLowerCase()

    const emailParts = splitEmail(email)

    if (!emailParts) {
      return
    }

    if (secondLevelDomains && topLevelDomains) {
      // If the email is a valid 2nd-level + top-level, do not suggest anything.
      if (secondLevelDomains.includes(emailParts.secondLevelDomain) && topLevelDomains.includes(emailParts.topLevelDomain)) {
        return false
      }
    }

    // @ts-ignore
    let closestDomain = (emailParts.domain in dictionary && dictionary[emailParts.domain]) || findClosestDomain(emailParts.domain, domains, distanceFunction, domainThreshold)

    if (closestDomain) {
      if (closestDomain === emailParts.domain) {
        // The email address exactly matches one of the supplied domains; do not return a suggestion.
        return false
      }
      else {
        // The email address closely matches one of the supplied domains; return a suggestion
        return { address: emailParts.address, domain: closestDomain, full: `${emailParts.address}@${closestDomain}` }
      }
    }

    // The email address does not closely match one of the supplied domains
    const closestSecondLevelDomain = findClosestDomain(emailParts.secondLevelDomain, secondLevelDomains, distanceFunction, secondLevelThreshold)
    const closestTopLevelDomain = findClosestDomain(emailParts.topLevelDomain, topLevelDomains, distanceFunction, topLevelThreshold)

    if (emailParts.domain) {
      closestDomain = emailParts.domain
      let rtrn = false

      if (closestSecondLevelDomain && closestSecondLevelDomain !== emailParts.secondLevelDomain) {
        // The email address may have a mispelled second-level domain; return a suggestion
        closestDomain = closestDomain.replace(emailParts.secondLevelDomain, closestSecondLevelDomain)
        rtrn = true
      }

      if (closestTopLevelDomain && closestTopLevelDomain !== emailParts.topLevelDomain && emailParts.secondLevelDomain !== '') {
        // The email address may have a mispelled top-level domain; return a suggestion
        closestDomain = closestDomain.replace(new RegExp(`${emailParts.topLevelDomain}$`), closestTopLevelDomain)
        rtrn = true
      }

      if (rtrn) {
        return { address: emailParts.address, domain: closestDomain, full: `${emailParts.address}@${closestDomain}` }
      }
    }

    /* The email address exactly matches one of the supplied domains, does not closely
     * match any domain and does not appear to simply have a mispelled top-level domain,
     * or is an invalid email address; do not return a suggestion.
     */
    return false
  }

  function run(opts: { email: string, domains?: string[], secondLevelDomains?: string[], topLevelDomains?: string[], distanceFunction?: Function }) {
    opts.domains = opts.domains || defaultDomains
    opts.secondLevelDomains = opts.secondLevelDomains || defaultSecondLevelDomains
    opts.topLevelDomains = opts.topLevelDomains || defaultTopLevelDomains
    opts.distanceFunction = opts.distanceFunction || sift4Distance

    return suggest(encodeEmail(opts.email), opts.domains, opts.secondLevelDomains, opts.topLevelDomains, opts.distanceFunction)
  }

  return {
    domainThreshold,
    secondLevelThreshold,
    topLevelThreshold,
    defaultDomains,
    defaultSecondLevelDomains,
    defaultTopLevelDomains,
    dictionary,
    encodeEmail,
    splitEmail,
    sift4Distance,
    findClosestDomain,
    suggest,
    run
  }
}

export async function serverValidateEmail(email: string): Promise<boolean> {
  const { $fetchWrite } = useNuxtApp()

  const response = await $fetchWrite<ResponseData<null>>($api.API_EMAIL_VALIDATION, {
    method: $method.post,
    body: { email }
  })
  return response.success
}
