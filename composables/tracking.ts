import { useCampaignStore } from '~~/store/campaign'
import { useStoreInfo } from '~~/store/storeInfo'
import { useUiManager } from '~~/store/uiManager'
import { useUserSession } from '~~/store/userSession'

interface TrackingData {
  campaignTrackingCode: any | false
  storeTrackingCode: any | false
  sellerTrackingCode: any | false
}

export function useTracking() {
  const { $localeHead, $isBot, $imgUrl } = useNuxtApp()
  const storeInfo = useStoreInfo()
  const $route = useRoute()

  const {
    userInfo,
    visitInfo,
    clientInfo,
    currentSellerId,
    initClientInfo,
    initSessionId
  } = useUserSession()

  const {
    trackingUrl,
    newTrackingUrl
  } = useRuntimeConfig().public

  async function initTracking() {
    if (import.meta.server) {
      const trackingData = await getTrackingData()
      initFacebookPixel(trackingData)
      initGoogleAnalytic(trackingData)
      initGoogleTagManager(trackingData)
      initOtherTrackingScrip(trackingData)
      initHeadTag()
    }

    if (import.meta.client) {
      if (window.trackingInit) {
        return
      }
      window!.eventQueueList = []
      window!.userActivity = 0

      await initClientInfo()
      await initSessionId()

      window.trackingInit = true

      initEvent()

      watch(() => $route.path, () => {
        trackEvent({ event: 'page_view' })
        initHeadTag()
      })

      trackEvent({ event: 'page_view' })
    }
  }

  async function getTrackingData() {
    const campaignStore = useCampaignStore()
    const trackingData: TrackingData = {
      campaignTrackingCode: false,
      storeTrackingCode: false,
      sellerTrackingCode: false
    }

    try {
      // campaign
      if (($route.name as string)?.startsWith('campaignSlug___')) {
        const campaignData = await campaignStore.getCampaignBySlug($route.params.campaignSlug as string)
        let campaignTrackingCode: any = campaignData?.tracking_code
        if (campaignTrackingCode) {
          if (typeof campaignTrackingCode !== 'object') {
            campaignTrackingCode = JSON.parse(campaignTrackingCode)
          }
          trackingData.campaignTrackingCode = campaignTrackingCode
        }
      }
      // store
      let storeTrackingCode = storeInfo.tracking_code
      if (storeTrackingCode) {
        if (typeof storeTrackingCode !== 'object') {
          storeTrackingCode = JSON.parse(storeTrackingCode)
        }
        trackingData.storeTrackingCode = storeTrackingCode
      }
      // seller
      let sellerTrackingCode = storeInfo.trackingCode
      if (sellerTrackingCode) {
        try {
          if (typeof sellerTrackingCode !== 'object') {
            sellerTrackingCode = JSON.parse(sellerTrackingCode)
          }
          trackingData.sellerTrackingCode = sellerTrackingCode
        }
        catch (e) {
          // noop
        }
      }
    }
    catch (error) {
      useUiManager().createPopup((error as Error).message)
    }

    return trackingData
  }

  function initFacebookPixel(trackingData: TrackingData) {
    const pixelList: Array<string> = []
    Object.keys(trackingData).forEach((key) => {
      pixelList.push(...(trackingData[key as keyof TrackingData]?.facebook_pixel?.split(',') || []))
    })

    if (pixelList) {
      const customerData = {
        em: (userInfo.email || '').toLowerCase(),
        fn: (userInfo.name || '').toLowerCase(),
        ph: (userInfo.phone || '').toLowerCase(),
        ct: (userInfo.city || '').toLowerCase(),
        st: (userInfo.state || '').toLowerCase(),
        zp: (userInfo.zipcode || '').toLowerCase(),
        country: (userInfo.country || '').toLowerCase()
      }

      const facebookScript = [{
        type: 'text/javascript',
        hid: 'facebook-pixel',
        innerHTML: `
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
        `,
        // @ts-ignore
        body: true,
        async: true
      }]

      pixelList?.forEach((pixel) => {
        if (pixel.length === 0) {
          return
        }
        facebookScript.push({
          type: 'text/javascript',
          hid: `facebook-pixel-${pixel}`,
          innerHTML: `
            fbq('init', ${pixel}, ${JSON.stringify(customerData)})
          `,
          // @ts-ignore
          body: true,
          async: true
        })
      })

      useHead({ script: facebookScript })
    }
  }

  function initGoogleAnalytic({ storeTrackingCode, sellerTrackingCode }: TrackingData) {
    const googleAdsGtag = storeTrackingCode?.google_ads_gtag
    const googleAnalytics = storeTrackingCode?.google_analytics
    const googleAdwordsId = storeTrackingCode?.google_adwords_id

    const googleAnalyticsAccountLevel = sellerTrackingCode?.google_analytics
    const googleAdwordsIdAccountLevel = sellerTrackingCode?.google_adwords
    const googleTagAccountLevel = sellerTrackingCode?.google_gtag

    const googleIds = [googleAdsGtag, googleAnalytics, googleAdwordsId, googleAnalyticsAccountLevel, googleAdwordsIdAccountLevel, googleTagAccountLevel].filter((item, index, array) => item && array.indexOf(item) === index)
    const script: Array<Record<string, any>> = []

    if (googleIds && googleIds.length) {
      let gtagScriptExists = false
      googleIds.forEach((item) => {
        if (!gtagScriptExists) {
          script.push({
            type: 'text/javascript',
            hid: 'gtm',
            src: 'https://www.googletagmanager.com/gtag/js',
            body: true,
            async: true
          })
          script.push({
            type: 'text/javascript',
            hid: 'gtag',
            innerHTML: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
              `,
            body: true,
            async: true
          })
          gtagScriptExists = true
        }
        script.push({
          type: 'text/javascript',
          hid: `gtag-${item}`,
          innerHTML: ` gtag('config', '${item}');`,
          body: true,
          async: true
        })
      })
    }

    useHead({
      script
    })
  }

  function initHeadTag() {
    const style: Array<Record<string, any>> = []
    const script: Array<Record<string, any>> = []
    if (storeInfo.head_tags) {
      const headtags = headTagFilter(storeInfo.head_tags)

      headtags.forEach((tag) => {
        switch (tag.tag) {
          case 'script':
            script.push(createTagObj(tag))
            break

          case 'script_src': {
            const adp = (typeof tag?.additional_properties === 'string') ? JSON.parse(tag.additional_properties) : (tag?.additional_properties || {})

            // Create script object with src and position
            const scriptObj = {
              src: tag.code,
              body: (tag.position === 'body')
            } as any

            // Add async and defer if present
            if (adp?.async) {
              scriptObj.async = adp.async
            }
            if (adp?.defer) {
              scriptObj.defer = adp.defer
            }

            // Add all other additional properties as custom attributes
            Object.keys(adp).forEach((key) => {
              if (key !== 'async' && key !== 'defer') {
                scriptObj[key] = adp[key]
              }
            })

            script.push(scriptObj)
            break
          }

          case 'style':
            style.push(createTagObj(tag))
            break
        }
      })
    }

    useHead({ script, style })
  }

  function initOtherTrackingScrip({ storeTrackingCode }: TrackingData) {
    const tiktokPixel = storeTrackingCode?.tiktok_pixel
    const pinterestTagId = storeTrackingCode?.pinterest_tag_id
    const snapPixel = storeInfo?.tracking_code?.snapchat_pixel
    const quoraPixel = storeInfo?.tracking_code?.quora_pixel
    const bingPixel = storeInfo?.tracking_code?.bing_pixel
    const klaviyoPixel = storeInfo?.tracking_code?.klaviyo_public_key
    const redditPixel = storeInfo?.tracking_code?.reddit_pixel

    const script: Array<Record<string, any>> = []

    if (klaviyoPixel) {
      script.push({
        type: 'text/javascript',
        async: true,
        src: `//static.klaviyo.com/onsite/js/klaviyo.js?company_id=${klaviyoPixel}`,
        body: true
      })
    }

    if (tiktokPixel) {
      script.push({
        type: 'text/javascript',
        hid: `tiktok-${tiktokPixel}`,
        innerHTML: `
          !function (w, d, t) {
            w.TiktokAnalyticsObject=t;var ttq=w[t]=w[t]||[];ttq.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"],ttq.setAndDefer=function(t,e){t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}};for(var i=0;i<ttq.methods.length;i++)ttq.setAndDefer(ttq,ttq.methods[i]);ttq.instance=function(t){for(var e=ttq._i[t]||[],n=0;n<ttq.methods.length;n++)ttq.setAndDefer(e,ttq.methods[n]);return e},ttq.load=function(e,n){var i="https://analytics.tiktok.com/i18n/pixel/events.js";ttq._i=ttq._i||{},ttq._i[e]=[],ttq._i[e]._u=i,ttq._t=ttq._t||{},ttq._t[e]=+new Date,ttq._o=ttq._o||{},ttq._o[e]=n||{};var o=document.createElement("script");o.type="text/javascript",o.async=!0,o.src=i+"?sdkid="+e+"&lib="+t;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(o,a)};

            ttq.load('${tiktokPixel}');
          }(window, document, 'ttq');
        `,
        body: true,
        async: true

      })
    }

    if (pinterestTagId) {
      script.push({
        type: 'text/javascript',
        hid: `pinterest-${pinterestTagId}`,
        innerHTML: `
        !function(e){if(!window.pintrk){window.pintrk=function(){window.pintrk.queue.push(
          Array.prototype.slice.call(arguments))};var
          n=window.pintrk;n.queue=[],n.version="3.0";var
          t=document.createElement("script");t.async=!0,t.src=e;var
          r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(t,r)}}("https://s.pinimg.com/ct/core.js");
          pintrk('load', '${pinterestTagId}');
          pintrk('page');
        `,
        body: true,
        async: true
      })
    }

    if (snapPixel) {
      script.push({
        type: 'text/javascript',
        innerHTML: `
        (function(e,t,n){if(e.snaptr)return;var a=e.snaptr=function()
          {a.handleRequest?a.handleRequest.apply(a,arguments):a.queue.push(arguments)};
          a.queue=[];var s='script';r=t.createElement(s);r.async=!0;
          r.src=n;var u=t.getElementsByTagName(s)[0];
          u.parentNode.insertBefore(r,u);})(window,document,
          'https://sc-static.net/scevent.min.js');
          snaptr('init', '${snapPixel}');
        `
      })
    }

    if (quoraPixel) {
      script.push({
        type: 'text/javascript',
        innerHTML: `
        !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
        qp('init', '${quoraPixel}');
        `
      })
    }

    if (bingPixel) {
      script.push({
        type: 'text/javascript',
        innerHTML: `(function(w,d,t,r,u){var f,n,i;w[u]=w[u]||[] ,f=function(){var o={ti:"${bingPixel}", enableAutoSpaTracking: true}; o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")} ,n=d.createElement(t),n.src=r,n.async=1,n.onload=n .onreadystatechange=function() {var s=this.readyState;s &&s!=="loaded"&& s!=="complete"||(f(),n.onload=n. onreadystatechange=null)},i= d.getElementsByTagName(t)[0],i. parentNode.insertBefore(n,i)})(window,document,"script"," //bat.bing.com/bat.js","uetq");`
      })
    }

    if (redditPixel) {
      script.push({
        type: 'text/javascript',
        innerHTML: `
          !function(w,d){if(!w.rdt){var p=w.rdt=function(){p.sendEvent?p.sendEvent.apply(p,arguments):p.callQueue.push(arguments)};p.callQueue=[];var t=d.createElement("script");t.src="https://www.redditstatic.com/ads/pixel.js",t.async=!0;var s=d.getElementsByTagName("script")[0];s.parentNode.insertBefore(t,s)}}(window,document);
          rdt('init','${redditPixel}');`
      })
    }

    if (storeInfo.id === 1) {
      script.push({
        type: 'text/javascript',
        hid: 'dwin1',
        src: 'https://www.dwin1.com/19038.js',
        defer: 'defer',
        body: true,
        async: true
      })
    }

    if (storeInfo.enable_crisp_support) {
      const { $getHost } = useNuxtApp()
      const host = $getHost()
      const crispWebsiteId = 'c17a453b-7e4b-4914-9cb6-747a900f787f'
      script.push({
        type: 'text/javascript',
        innerHTML: `
          window.$crisp=[];
          window.CRISP_WEBSITE_ID="${crispWebsiteId}";
          (function(){d=document;s=d.createElement("script");s.src="https://client.crisp.chat/l.js";s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})();
          (function(){
            function setSegmentPushed(crispUserId, value) {
              const data = {
                value: value,
                timestamp: Date.now()
              };
              localStorage.setItem(getKey(crispUserId), JSON.stringify(data));
            }

            function getKey(crispUserId) {
              return 'crisp_segment_pushed_${host}_' + crispUserId;
            }

            function getSegmentPushed(crispUserId) {
              const key = getKey(crispUserId);
              try {
                const stored = localStorage.getItem(key);
                if (!stored) return false;

                const data = JSON.parse(stored);

                return data.value;
              } catch (e) {
                localStorage.removeItem(key);
                return false;
              }
            }

            function logToBackend(message) {
              try {
                fetch('https://${host}/api/public/init-segment-live-chat', {
                  method: 'POST',
                  body: JSON.stringify({
                    crisp_data: message,
                    host: '${host}'
                  }),
                  headers: {
                    'Content-Type': 'application/json'
                  }
                })
              }
              catch (e) {
                console.log('error:pushing segment to backend', e);
              }
            }

            $crisp.push(["on", "message:sent", function (message) {
              const crispUserId = message.user.user_id;
              const segmentPushed = getSegmentPushed(crispUserId);
              if (!segmentPushed) {
                window.$crisp.push(["set", "session:segments", [["live_chat"]]])
                setSegmentPushed(crispUserId, true);
                logToBackend(message);
              }
            }]);
            window.$crisp.push(['on','chat:closed',()=>{ document.querySelector('.crisp-client.opened').classList.remove('opened'); }]);
            ${storeInfo.theme === 'givehug' ? 'window.$crisp.push(["config", "color:theme", ["orange"]])' : ''}
            window.onload=()=>{d=document;
            s=d.createElement('span');s.innerHTML="Live Chat";s.onclick=()=>{document.querySelector('.crisp-client').classList.add('opened');window.$crisp.push(['do', 'chat:open']);};
            e=d.createElement('div');e.setAttribute('class', "livechatbtn hidden");e.appendChild(s);d.body.appendChild(e);
            }
          })();
        `
      })
    }

    const i18nHeadProperties = $localeHead({ addSeoAttributes: true })
    if (i18nHeadProperties.link) {
      for (const lnk of i18nHeadProperties.link) {
        /**
         * Explicitly appending domain based on guide:
         * https://developers.google.com/search/docs/specialty/international/localized-versions
         */
        lnk.href = `https://${storeInfo.domain || storeInfo.currentDomain}${lnk.href}`
      }
    }

    useHead({
      htmlAttrs: i18nHeadProperties.htmlAttrs,
      link: i18nHeadProperties.link,
      meta: i18nHeadProperties.meta,
      script
    })

    return script
  }

  function initGoogleTagManager({ storeTrackingCode, sellerTrackingCode }: TrackingData) {
    const gtmCode = [storeTrackingCode.google_tag, sellerTrackingCode.google_tag_manager].filter(item => item)

    if (gtmCode && gtmCode.length) {
      useHead({
        script: gtmCode.map((item) => {
          return {
            type: 'text/javascript',
            hid: `gtm-script-${item}`,
            innerHTML: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${item}');
          `,
            async: true
          }
        }),
        noscript: gtmCode.map((item) => {
          return {
            type: 'text/javascript',
            hid: `gtm-noscript-${item}`,
            innerHTML: `<iframe src="https://www.googletagmanager.com/ns.html?id=${item}"
            height="0" width="0" style="display:none;visibility:hidden"></iframe>
          `,
            body: true,
            async: true
          }
        })
      })
    }
  }

  function trackEvent(trackData?: TrackEventData) {
    if ($isBot()) {
      return
    }

    const routeName = $route.name as string
    if (window.userActivity < 2) {
      if (trackData) {
        window.eventQueueList?.length
          ? window.eventQueueList.push(trackData)
          : window.eventQueueList = [trackData]
      }
      return
    }

    if ((trackData?.event && !['page_view', 'view_content'].includes((trackData?.event as string))) || routeName.startsWith('checkout-token') || routeName.startsWith('order')) {
      window.userActivity = 3
    }

    if (window.eventQueueList?.length) {
      window.eventQueueList.forEach(track)
      window.eventQueueList = []
    }

    if (trackData) {
      track(trackData)
    }
  }

  function track(data: TrackEventData) {
    const eventName = TRACKING_EVENT_LIST[data.event]
    customTracking({ ...data, event: eventName.custom })
    newCustomTracking(data)
    facebookTracking({ ...data, event: eventName.facebook })
    pinterestTracking({ ...data, event: eventName.pintrk })
    tiktokTracking({ ...data, event: eventName.ttq })
    gtagTracking({ ...data, event: eventName.gtag })
    snapTracking({ ...data, event: eventName.snap })
    quoraTracking({ ...data, event: eventName.quora })
    bingTracking({ ...data, event: eventName.bing })
    klaviyoTracking({ ...data, event: eventName.klaviyo })
    redditTracking({ ...data, event: eventName.reddit })
  }

  function customTracking(data: TrackEventData) {
    if (!data.event) {
      return
    }

    try {
      $fetch(trackingUrl, {
        method: 'PUT',
        // signal: AbortSignal.timeout(5000),
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          appId: 'customer',
          client: clientInfo,
          events: {
            sellerId: visitInfo.seller_id || currentSellerId || storeInfo.seller_id,
            storeId: storeInfo.id,
            ...data.data,
            track: data.event.toLowerCase()
          },
          sessionId: visitInfo.session_id
        })
      })
    }
    catch (_e) {}
  }

  async function newCustomTracking({ event, elementName, actionName, data = {} }: TrackEventData) {
    if (!window.userActivity) {
      window.userActivity = 1
      return
    }
    if (window.userActivity === 1) {
      window.userActivity = 2
      return
    }
    if (window.userActivity === 2) {
      window.userActivity = 3
      trackEvent()
      return
    }

    if (!event) {
      return
    }
    // campaign
    if (($route.name as string)?.startsWith('campaignSlug___')) {
      const campaignData = await useCampaignStore().getCampaignBySlug($route.params.campaignSlug as string)
      if (campaignData?.id) {
        data.campaignId = campaignData.id
      }
    }

    try {
      $fetch(newTrackingUrl, {
        method: 'PUT', // or 'PUT'
        signal: AbortSignal.timeout(15000),
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'event',
          event: actionName || event,
          sessionId: visitInfo.session_id,
          elementName,
          url: window.top?.location.href,
          ...data
        })
      })
    }
    catch (_e) {}
  }

  async function facebookTracking({ event, data }: TrackEventData) {
    if (!event) {
      return
    }
    const eventID = `${storeInfo.name.replaceAll(' ', '_')}.${event}.${Date.now()}`
    const currentTime = Math.round(Date.now() / 1000)
    // facebook tracking
    if (window.fbq) {
      window.fbq('track', event, data, { eventID })
    }
    const fbc = useCookie('_fbc')?.value
    const fbp = useCookie('_fbp')?.value
    // facebook conversion api
    const facebookConversionsData = [{
      event_id: eventID,
      event_name: event,
      event_time: currentTime,
      custom_data: data,
      user_data: {
        em: await sha256(visitInfo.email ? visitInfo.email.toLowerCase() : ''),
        ct: await sha256(visitInfo.city ? visitInfo.city.toLowerCase() : ''),
        fbc: fbc || `fb.1.${currentTime}.${useRoute().query.fbclid || visitInfo.ad_id || ''}`,
        fbp: fbp || `fb.1.${currentTime}.${visitInfo.device_id}`,
        country: await sha256(visitInfo.country ? visitInfo.country.toLowerCase() : ''),
        client_ip_address: clientInfo.ip,
        client_user_agent: clientInfo.useragent
      },
      event_source_url: clientInfo.url,
      action_source: 'website'
    }]

    const API_VERSION = storeInfo?.tracking_code?.facebook_pixel_version || 'v13.0'
    const pixelIds = (storeInfo?.tracking_code?.facebook_pixel || '').split(',')
    const tokens = (storeInfo?.tracking_code?.facebook_conversion_token || '').split(',')
    pixelIds.forEach((PIXEL_ID, index) => {
      const TOKEN = tokens[index]
      if (API_VERSION && PIXEL_ID && TOKEN) {
        const trackingUrl = `https://graph.facebook.com/${API_VERSION}/${PIXEL_ID}/events?access_token=${TOKEN}`
        $fetch(trackingUrl, {
          method: $method.post, // or 'PUT'
          body: { data: facebookConversionsData }
        })
      }
    })
  }

  function gtagTracking({ event, data }: TrackEventData) {
    if (!event || !window.dataLayer) {
      return
    }
    let gtagData: any = {}
    switch (event) {
      case 'page_view':
        gtagData = {
          event_category: 'Ecommerce'
        }
        return
      case 'view_item':
      case 'add_to_cart':
      case 'begin_checkout':
      case 'purchase':
        gtagData = {
          event_category: 'Ecommerce',
          currency: data?.currency,
          value: data?.value,
          items: data?.content_ids.map((item: any, index: any) => {
            return {
              item_id: item,
              item_name: data?.content_name?.split(',')[index],
              item_category: data?.content_category?.split(',')?.[index] || '',
              price: data?.content_value[index],
              currency: data?.currency,
              quantity: data?.content_quantity[index]
            }
          })
        }

        if (data?.order_token) {
          gtagData.order_token = data?.order_token
        }
        break
      case 'view_item_list':
      case 'select_item':
        gtagData = {
          event_category: 'Ecommerce',
          items: data?.items
        }
        break
    }

    if (storeInfo?.id === 1) {
      gtagData.seller_id = visitInfo.seller_id || 1
    }
    window.dataLayer.push({ event, ...gtagData })
  }

  function snapTracking({ event, data }: TrackEventData) {
    if (!event || !window.snaptr) {
      return
    }
    let snapData: any = {}
    switch (event) {
      case 'VIEW_CONTENT':
      case 'ADD_CART':
      case 'START_CHECKOUT':
      case 'PURCHASE':
        snapData = {
          currency: data?.currency,
          price: data?.value,

          item_ids: data?.content_ids,
          item_name: data?.content_name,
          item_category: data?.content_category,
          number_items: data?.content_quantity
        }

        if (data?.order_token) {
          snapData.transaction_id = data?.order_token
        }
        break
    }

    window.snaptr('track', event, snapData)
  }

  function quoraTracking({ event, data }: TrackEventData) {
    if (!event || !window.qp) {
      return
    }
    let quoraData: any = {}
    switch (event) {
      case 'ViewContent':
        quoraData = {
          campaign_id: data?.campaignId
        }
        break
      case 'Contact':
        quoraData = data
        break
      case 'AddToCart':
      case 'InitiateCheckout':
      case 'Purchase':
        quoraData = {
          currency: data?.currency,
          value: data?.value,
          item_id: data?.content_ids,
          item_name: data?.content_name?.split(','),
          item_category: data?.content_category,
          price: data?.content_value,
          quantity: data?.content_quantity
        }
        break
    }
    window.qp('track', event, quoraData)
  }

  function klaviyoTracking({ event, data }: TrackEventData) {
    if (!event || typeof window.klaviyo === 'undefined') {
      return
    }
    window.klaviyo = window.klaviyo || []
    let klaviyoData: any = {}
    const email = userInfo?.email
    if (email) {
      window.klaviyo.push(['identify', { $email: email }])
    }
    const domain = window.location.hostname
    const port = window.location.port ? `:${window.location.port}` : ''
    const baseUrl = `https://${domain}${port}`
    switch (event) {
      case 'Active on Site': {
        klaviyoData = {
          ...klaviyoData,
          event: 'Active on Site',
          time: new Date().toISOString()
        }
        break
      }
      case 'Viewed Product': {
        const klaviyoInfo = data?.klaviyoData || {}
        if (!klaviyoInfo) {
          return
        }
        klaviyoData = {
          ...klaviyoData,
          ProductName: klaviyoInfo.product_name,
          ProductID: klaviyoInfo.product_id,
          Categories: klaviyoInfo.categories,
          URL: document.location.href,
          Price: klaviyoInfo.product_price,
          ImageUrl: $imgUrl({ path: klaviyoInfo.image_url, type: 'full_hd' })
        }
        window.klaviyo.push(['trackViewedItem', {
          Title: klaviyoInfo.product_name,
          ItemId: klaviyoInfo.product_id,
          Categories: klaviyoInfo.categories,
          Url: document.location.href,
          ImageUrl: $imgUrl({ path: klaviyoInfo.image_url, type: 'full_hd' }),
          Metadata: {
            Price: klaviyoInfo.product_price
          }
        }])
        break
      }
      case 'Added to Cart': {
        const cartUrl = `${baseUrl}/cart`
        const addedToCartData = data?.klaviyoData
        if (!addedToCartData) {
          return
        }
        const addedItem = {
          AddedItemProductName: addedToCartData?.item_name || '',
          AddedItemProductID: addedToCartData.item_id || '',
          AddedItemCategories: addedToCartData.categories,
          AddedItemURL: baseUrl + addedToCartData.item_url || '',
          AddedItemPrice: addedToCartData.item_price || 0,
          AddedItemQuantity: addedToCartData.item_quantity || 0,
          ImageURL: $imgUrl({ path: addedToCartData.item_image_url, type: 'full_hd' }),
          CheckoutURL: cartUrl,
          $value: addedToCartData.$value || 0
        }
        const addedItems = addedToCartData?.items?.map((item: any) => {
          return {
            ProductID: item?.item_id || '',
            ProductName: item?.item_name || '',
            Quantity: item?.item_quantity || 0,
            ItemPrice: item?.item_price || 0,
            RowTotal: item?.item_price || 0,
            ProductURL: baseUrl + item?.item_url || '',
            ImageURL: $imgUrl({ path: item?.item_image_url, type: 'full_hd' }),
            ProductCategories: item?.categories
          }
        })
        klaviyoData = {
          ...klaviyoData,
          ...addedItem,
          Items: addedItems
        }
        break
      }
      case 'Started Checkout': {
        const order = data?.klaviyoData.order || {}
        if (!order) {
          return
        }
        const checkoutUrl = `${baseUrl}/checkout/${order?.access_token}`
        const itemNames = order?.products
          .map((item: any) => item.product_name)
          .filter((value: any, index: any, self: any) => self.indexOf(value) === index)
        const totalPrice = order?.total_amount
        const categories = order?.products
          .map((item: any) => item.campaign_title)
          .filter((value: any, index: any, self: any) => self.indexOf(value) === index)
        klaviyoData = {
          ...klaviyoData,
          $event_id: order?.order_number,
          $value: totalPrice,
          ItemsNames: itemNames,
          CheckoutURL: checkoutUrl,
          Categories: categories,
          Items: order?.products.map((item: any) => {
            return {
              ProductID: item?.product_id,
              ProductName: item?.product_name,
              Quantity: item?.quantity,
              ItemPrice: item?.price,
              RowTotal: item?.price * item?.quantity,
              ProductURL: baseUrl + item?.product_url,
              ImageURL: $imgUrl({ path: item?.thumb_url, type: 'full_hd' }),
              ProductCategories: [item?.campaign_title]
            }
          })
        }
        break
      }
      default:
        return
    }
    window.klaviyo.push(['track', event, klaviyoData])
  }

  function redditTracking({ event, data }: TrackEventData) {
    if (!event || !window.rdt) {
      return
    }
    let rdtData: any = {}
    switch (event) {
      case 'Contact':
        event = 'Custom'
        rdtData = {
          customEventName: 'Contact',
          ...data
        }
        break
      case 'InitiateCheckout':
        event = 'Custom'
        rdtData = {
          customEventName: 'InitiateCheckout',
          ...data
        }
        break
      case 'AddToCart':
      case 'Purchase':
        rdtData = {
          ...data,
          /**
           * force value to match
           * - "order.total_amount" at InitiateCheckout / Purchase
           * - "totalPrice" at Cart
           */
          content_value: data?.value
        }
        break
    }
    if (rdtData.klaviyoData) {
      delete rdtData.klaviyoData
    }
    window.rdt('track', event, rdtData)
  }

  function bingTracking({ event, data }: TrackEventData) {
    if (!event || typeof window.uetq === 'undefined') {
      return
    }
    window.uetq = window.uetq || []
    let bingData: any = {}
    switch (event) {
      case 'page_view':
        bingData = {
          ecomm_pagetype: correctEcommPageType(event)
        }
        event = ''
        return
      case 'view_item':
      case 'view_content':
        data = {
          ecomm_pagetype: correctEcommPageType(event),
          ecomm_prodid: [data?.campaignId]
        }
        event = ''
        return
      case 'add_to_cart':
      case 'begin_checkout':
      case 'purchase':
        bingData = {
          currency: data?.currency,
          ecomm_pagetype: correctEcommPageType(event),
          ecomm_totalvalue: data?.value,
          ecomm_prodid: data?.content_ids,
          items: data?.content_ids.map((item: any, index: any) => {
            return {
              item_id: item,
              item_name: data?.content_name?.split(',')[index],
              item_category: data?.content_category?.split(',')?.[index] || '',
              price: data?.content_value[index],
              currency: data?.currency,
              quantity: data?.content_quantity[index]
            }
          })
        }
        if (data?.order_token) {
          bingData.order_token = data?.order_token
        }
        break
    }
    if (storeInfo?.id === 1) {
      bingData.seller_id = visitInfo.seller_id || 1
    }
    window.uetq.push('event', event, bingData)
  }

  function correctEcommPageType(event: string) {
    switch (event) {
      case 'page_view':
        return 'home'
      case 'view_item':
      case 'view_content':
        return 'product'
      case 'add_to_cart':
        return 'cart'
      case 'purchase':
        return 'purchase'
      default:
        return 'other'
    }
  }

  function initEvent() {
    let scrollTimeoutInterval: any
    let mouseMoveEndedInterval: any

    window.addEventListener('sp_file_uploaded', (e) => {
      newCustomTracking({
        event: 'file_uploaded',
        data: { value: (e as CustomEvent).detail } // file path will be supplied in "detail" field
      })
    })

    window.addEventListener('sp_custom_input_change', (e) => {
      newCustomTracking({
        event: 'custom_input_change',
        data: { value: (e as CustomEvent).detail }
      })
    })

    window.addEventListener('click', (e) => {
      newCustomTracking({
        event: 'click',
        data: {
          actionName: getActionName(e.target as HTMLElement),
          elementName: getElementName(e.target as HTMLElement)
        }
      })
    })
    window.addEventListener('change', (e) => {
      newCustomTracking({
        event: 'input_change',
        actionName: getActionName(e.target as HTMLElement),
        elementName: getElementName(e.target as HTMLElement),
        data: {
          value: e.target?.value
        }
      })
    })
    // window.addEventListener('keydown', (e) => {
    //   newCustomTracking({
    //     event: 'keydown',
    //     data: {
    //       actionName: getActionName(e.target as HTMLElement),
    //       elementName: getElementName(e.target as HTMLElement),
    //       value: e.key
    //     }
    //   })
    // })
    window.addEventListener('scroll', () => {
      if (scrollTimeoutInterval) {
        clearInterval(scrollTimeoutInterval)
      }
      scrollTimeoutInterval = setTimeout(() => {
        newCustomTracking({
          event: 'scroll',
          data: {
            point: [window.scrollX, window.scrollY]
          }
        })
      }, 300)
    })
    window.addEventListener('mousemove', (e) => {
      if (mouseMoveEndedInterval) {
        clearInterval(mouseMoveEndedInterval)
      }
      mouseMoveEndedInterval = setTimeout(() => {
        newCustomTracking({
          event: 'mousemove',
          actionName: getActionName(e.target as HTMLElement),
          elementName: getElementName(e.target as HTMLElement),
          data: {
            point: [e.clientX, e.clientY]
          }
        })
      }, 300)
    })
  }

  return {
    initTracking,
    trackEvent,
    newCustomTracking,
    customTracking
  }
}

function createTagObj(tag: HeadTag) {
  // ignore errors to keep the storefront running
  const injectTag: Record<string, any> = {
    innerHTML: `try {${tag.code}} catch(e) {/* noop */}`
  }

  if (tag.tag === 'style') {
    injectTag.innerHTML = tag.code
  }

  if (tag.position === 'body') {
    injectTag.body = true
  }

  return injectTag
}

function pinterestTracking({ event, data }: TrackEventData) {
  if (!event || !window.pintrk) {
    return
  }

  let pintrkData: any = {}

  switch (event) {
    case 'PageVisit':
      pintrkData = {
        product_id: data?.campaignId
      }
      break
    case 'Contact':
      pintrkData = data
      break
    case 'AddToCart':
      pintrkData = {
        product_id: data?.content_ids,
        value: data?.value,
        order_quantity: data?.num_items,
        currency: data?.currency,
        content_name: data?.content_name
      }
      break
    case 'Checkout':
    case 'Purchase':
      pintrkData = {
        order_id: data?.order_token,
        product_id: data?.content_ids,
        value: data?.value,
        order_quantity: data?.num_items,
        currency: 'USD',
        line_items: data?.content_ids.map((item: any, index: any) => {
          return {
            product_id: item,
            product_name: data?.content_name?.split(',')[index],
            product_category: data?.content_category.split(',')[index]
          }
        })
      }
      break
  }
  window.pintrk('track', event, pintrkData)
}

function tiktokTracking({ event, data }: TrackEventData) {
  if (!event || !window.ttq) {
    return
  }
  let tiktokData: any = {}
  switch (event) {
    case 'page_view':
      window.ttq.page()
      return
    case 'ViewContent':
      tiktokData = {
        campaignId: data?.campaignId
      }
      break
    case 'Contact':
      tiktokData = data
      break
    case 'AddToCart':
    case 'InitiateCheckout':
    case 'CompletePayment':
      tiktokData = {
        content_id: data?.content_ids,
        content_type: 'product',
        content_name: data?.content_name,
        quantity: data?.num_items,
        value: data?.value,
        currency: data?.currency
      }
      break
  }
  window.ttq.track(event, tiktokData)
}

function getElementAttribute(elm?: HTMLElement | null, attributes?: Array<string>): string {
  if (!elm || !attributes) {
    return ''
  }

  for (const attributeName of attributes) { // You can use `let` instead of `const` if you like
    let attribute = elm?.getAttribute(attributeName)

    if (attribute != null) {
      attribute = attribute.replace(/^\s+|\s+$/gm, '') || ''
    }

    if (attribute != null && attribute.length > 0) {
      return attribute
    }
  }

  const parent = elm?.parentElement
  if (parent == null) {
    return ''
  }
  else {
    return getElementAttribute(parent, attributes)
  }
}

function getActionName(elm?: HTMLElement | null) {
  return getElementAttribute(elm, ['sp-action'])
}

function getElementName(elm?: HTMLElement | null) {
  const maxLength = 50
  let text = elm?.textContent || ''
  if (text.length > maxLength) {
    text = `${text.slice(0, maxLength)}...`
  }
  if (text.length !== 0) {
    return text.replace(/^\s+|\s+$/gm, '')
  }
  else {
    return getElementAttribute(elm, ['sp-name', 'name', 'id', 'title', 'alt'])
  }
}
