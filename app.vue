<script lang="ts" setup>
const layoutComponent = getThemeComponent('layout')
const ModalSizeGuide = defineAsyncComponent(() => {
  if (storeInfo().theme === 'givehug') {
    return import('@/components/givehug/global/modalSizeGuide.client.vue')
  }
  return import('@/components/Common/modalSizeGuide.client.vue')
})

await useSetup()

const WHITELISTED_PATHS = ['/order/track', '/page/contact-us', '/order/status']
const isAccessingWhitelistedPage = computed(() => {
  for (let i = 0; i < WHITELISTED_PATHS.length; i++) {
    if (useRoute().path.includes(WHITELISTED_PATHS[i])) {
      return true
    }
  }
  return false
})

const {
  isShowModal,
  isVerified,
  onReCaptchaConfirm
} = useModalCreateOrderWithRecaptcha()

useCustomHeadTag()

const savedY = ref(0)
const isShowCartDrawler = computed(() => uiManager().isShowCartDrawler)
watch(isShowCartDrawler, (opened) => {
  console.log('isShowCartDrawler', isShowCartDrawler.value)
  const body = document.body
  if (opened) {
    savedY.value = window.scrollY
    body.style.cssText = `
      position: fixed;
      top: -${savedY.value}px;
      left: 0; right: 0;
      width: 100%;
      overflow-y: scroll;           /* vẫn hiện “rãnh” cuộn */
      padding-right: var(--sbw, 0px);
    `
    // Tính bề rộng scrollbar & set biến CSS cho padding-right
    document.documentElement.style.setProperty(
      '--sbw',
      `${window.innerWidth - document.documentElement.clientWidth}px`
    )
  }
  else {
    body.style.cssText = ''
    window.scrollTo({ left: 0, top: savedY.value })
  }
})
</script>

<template>
  <div v-if="storeInfo().status !== 'blocked'">
    <common-indicator color="var(--color-primary)" />
    <common-sitewide />
    <layout-component>
      <NuxtPage />
      <common-html-head-tag for-position="body" />
      <lazy-common-popup />
      <ModalSizeGuide />
      <LazyGivehugGlobalCartDrawler v-if="storeInfo().theme === 'givehug'" />
      <lazy-common-modal-view-image />
      <lazy-common-modal
        modal-id="confirm-recaptcha"
        modal-class="<md:(w-full h-full) <lg:(w-[80vw]) lg:w-[798px] max-h-30vh"
        :title="$t('Confirm')"
        title-class="text-xl font-medium absolute top-2 left-2"
        :model-value="isShowModal"
        @close-modal="isShowModal = false"
      >
        <div class="mt-12 mb-6 px-3" align="center">
          <h1 class="text-xl font-medium text-center transition">
            {{ (isVerified) ? $t('Taking you to the checkout page...') : $t('We noticed something unusual from your network. Please confirm to continue!') }}
          </h1>
          <lazy-common-recaptcha
            class="mt-6"
            @success="onReCaptchaConfirm"
          />
        </div>
      </lazy-common-modal>
    </layout-component>
  </div>
  <div v-else class="h-screen flex flex-col">
    <NuxtPage v-if="isAccessingWhitelistedPage" />
    <div v-else class="flex flex-1 items-center justify-center px-4">
      <h2 class="font-medium text-3xl text-gray-700">
        {{ $t('This store is suspended due to policy violation.') }}
      </h2>
    </div>
    <default-footer-blocked v-if="isAccessingWhitelistedPage" />
  </div>
</template>
